{"name": "@q-centrix/q-components-react", "description": "shared react components", "version": "1.3.25", "private": false, "repository": "git://github.com/q-centrix/q-components-react.git", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist", "src"], "engines": {"node": ">=10"}, "scripts": {"start-styleguidist": "styleguidist server", "start-watch": "tsdx watch", "start-playground": "cd example && yarn start", "start": "yarn start-styleguidist & yarn start-watch & storybook dev -p 6006 & npx tailwindcss -i ./tailwind.css -o ./src/styles/tw-output.css --watch", "build": "npx tailwindcss -i ./tailwind.css -o ./src/styles/tw-output.css && tsdx build", "test": "tsdx test --passWithNoTests --coverage --maxWorkers 2", "test:watch": "tsdx test --watch", "lint": "tsdx lint", "install:playground": "cd example && yarn install && cd ..", "prepare": "tsdx build", "format": "prettier --write '{src,test}/**/*.{js,tsx}'", "validate": "npm-run-all lint test", "storybook": "storybook dev -p 6006", "storybook-docs": "storybook dev --docs", "build-storybook": "storybook build"}, "peerDependencies": {"classnames": "^2.x", "humps": "^2.x", "ramda": "^0.27.x", "react": ">=16", "react-intl": "^6.4.0", "react-redux": "^7.x", "recompose": "^0.30.x", "redux": "^4.x", "redux-thunk": "^2.x"}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "module": "dist/q-components-react.esm.js", "devDependencies": {"@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "@babel/preset-react": "^7.9.4", "@rollup/plugin-image": "^3.0.2", "@storybook/addon-essentials": "^7.0.12", "@storybook/addon-interactions": "^7.0.12", "@storybook/addon-links": "^7.0.12", "@storybook/addon-mdx-gfm": "^7.0.12", "@storybook/addon-styling": "^1.0.8", "@storybook/blocks": "^7.0.12", "@storybook/react": "^7.0.12", "@storybook/react-webpack5": "^7.0.12", "@storybook/testing-library": "^0.1.0", "@types/react": "^16.9.35", "@types/react-dom": "^16.9.8", "babel-loader": "^9.1.2", "babel-plugin-polyfill-regenerator": "^0.2.0", "classnames": "^2.2.6", "css-loader": "6.7.1", "dotenv": "^10.0.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-storybook": "^0.6.12", "file-loader": "^6.2.0", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jest-enzyme": "^7.1.2", "n": "^6.5.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-loader": "^7.2.4", "postcss-url": "^8.0.0", "prop-types": "^15.7.2", "ramda": "^0.27.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-intl": "^6.4.0", "react-redux": "^7.2.0", "react-styleguidist": "12.0.0", "react-test-renderer": "^16.13.1", "recompose": "^0.30.0", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0", "rollup-plugin-postcss": "^4.0.2", "sass": "^1.62.0", "sass-loader": "^13.2.2", "storybook": "^7.0.12", "style-loader": "3.3.1", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.6", "tsdx": "^0.13.2", "tslib": "^2.0.0", "typescript": "^3.9.2", "waait": "^1.0.4", "webpack": "5.72.0", "webpack-cli": "4.9.2"}, "dependencies": {"@apollo/client": "^3.7.17", "@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/react-fontawesome": "^0.1.11", "@popmotion/popcorn": "^0.4.4", "@radix-ui/react-toast": "^1.1.4", "@reduxjs/toolkit": "^1.8.6", "@storybook/react-docgen-typescript-plugin": "^1.0.1", "axios": "^0.26.1", "axios-mock-adapter": "^1.20.0", "babel-plugin-module-resolver": "^5.0.0", "class-variance-authority": "^0.7.0", "classnames": "^2.2.6", "date-fns": "^2.28.0", "date-fns-tz": "^2.0.0", "dompurify": "^3.1.3", "framer-motion": "^6.3.4", "graphql": "^16.7.1", "helper": "^0.0.13", "helpers": "^0.0.6", "humps": "^2.0.1", "react-datepicker": "6.6.0", "react-loader-spinner": "^5.3.4", "react-modal": "^3.16.1", "react-select": "^5.7.4", "react-tooltip": "^5.28.0", "tailwind-merge": "^1.12.0"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}