module.exports = {
  setupFilesAfterEnv: ["./src/setupTests.js"],
  testMatch: ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
  moduleNameMapper: {
    "\\.(css|less|sass|scss)$": "identity-obj-proxy"
  },
  coveragePathIgnorePatterns: [
    "/node_modules/",
    "src/helpers/redux",
    "src/hooks/",
    "src/helpers/components/icons",
    "mockedServerRequest",
    "serverRequest",
    "Navbar/actions",
    "src/index.tsx"
  ]
};
