{"extends": ["eslint:recommended", "plugin:react/recommended", "prettier/babel", "prettier/react", "prettier/standard", "plugin:storybook/recommended", "prettier"], "plugins": ["jest", "prettier", "react", "react-hooks", "standard"], "parserOptions": {"sourceType": "module", "ecmaFeatures": {"jsx": true}, "ecmaVersion": 2020}, "env": {"es6": true, "node": true, "jest": true}, "rules": {"prettier/prettier": "error", "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "no-console": "warn"}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["*-test.js", "*.spec.js"], "rules": {"jest/lowercase-name": ["error", {"ignore": ["describe"]}]}}]}