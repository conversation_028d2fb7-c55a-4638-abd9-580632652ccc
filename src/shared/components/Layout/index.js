/* eslint-disable react/sort-prop-types */
import PropTypes from "prop-types";
import {
  Topbar,
  SecondaryToolbar,
  SideNavMenu
} from "@q-centrix/q-components-react";
import "styles/layout.scss";

const Layout = ({
  tbChildren,
  tbOnClick,
  stTitle,
  stChildren,
  stContainerClassName,
  stTitleClassName,
  stChildrenClassName,
  children,
  client,
  onBackArrowClick,
  onLogoClick
}) => {
  const shouldDisplaySecondaryToolbar = stTitle || stChildren;

  return (
    <div className="tw-flex tw-min-h-[inherit]">
      <SideNavMenu />
      <section className="tw-flex tw-flex-1 tw-flex-col">
        <header className="layout-header tw-z-50 tw-shadow-qc">
          <Topbar
            onClick={tbOnClick}
            client={client}
            onBackArrowClick={onBackArrowClick}
            onLogoClick={onLogoClick}
          >
            {tbChildren}
          </Topbar>
          {shouldDisplaySecondaryToolbar && (
            <SecondaryToolbar
              title={stTitle}
              containerClassName={stContainerClassName}
              titleClassName={stTitleClassName}
              childrenClassName={stChildrenClassName}
            >
              {stChildren}
            </SecondaryToolbar>
          )}
        </header>
        <main className="tw-h-full">{children}</main>
      </section>
    </div>
  );
};

Layout.propTypes = {
  tbChildren: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  stChildren: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  stTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  tbOnClick: PropTypes.func,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node
  ]),
  onBackArrowClick: PropTypes.func,
  onLogoClick: PropTypes.func
};

export default Layout;
