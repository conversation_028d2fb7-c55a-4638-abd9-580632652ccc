import {
  Button,
  ConfirmationModal,
  Spinner
} from "@q-centrix/q-components-react";
import { isEmpty } from "ramda";
import { useRef } from "react";
import useOnClickOutside from "shared/hooks/useOnClickOutside";
import <PERSON><PERSON>r<PERSON>and<PERSON> from "../ErrorHandler";
import { useFacilitySelector } from "./hooks";

const FacilitySelector = ({
  onFacilityChange,
  confirmationRequired = true,
  className = ""
}) => {
  const dropdownRef = useRef(null);

  const {
    formattedFacility,
    options,
    loading,
    error,
    isDropdownOpen,
    isConfirmationModalOpen,
    confirmationMessage,
    handleFacilitySelect,
    handleConfirmSelection,
    handleCancelSelection,
    handleSearchChange,
    toggleDropdown,
    setIsDropdownOpen
  } = useFacilitySelector({
    onFacilityChange,
    confirmationRequired
  });

  useOnClickOutside(dropdownRef, () => setIsDropdownOpen(false));

  if (loading) return <Spinner />;
  if (error) return <ErrorHandler error={error} />;

  const currentFacilityName = formattedFacility?.label || "Select Facility";

  const handleOptionClick = option => {
    handleFacilitySelect(option);
  };

  const handleKeyDown = (event, option) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleOptionClick(option);
    }
  };

  const handleSelectorKeyDown = event => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      toggleDropdown();
    }
  };

  return (
    <div
      className={`tw-relative tw-inline-block ${className}`}
      ref={dropdownRef}
    >
      {/* Main Facility Selector Button */}
      <button
        onClick={toggleDropdown}
        onKeyDown={handleSelectorKeyDown}
        className="tw-flex tw-items-center tw-space-x-2 tw-bg-blue-600 tw-text-white tw-px-4 tw-py-2 tw-rounded-full tw-border-none tw-cursor-pointer tw-transition-all tw-duration-200 hover:tw-bg-blue-700 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-blue-500 focus:tw-ring-offset-2"
        aria-expanded={isDropdownOpen}
        aria-haspopup="listbox"
        aria-label={`Current facility: ${currentFacilityName}. Click to change facility.`}
        type="button"
      >
        <i className="fa-solid fa-location-dot tw-text-sm" aria-hidden="true" />
        <span className="tw-text-sm tw-font-medium tw-max-w-48 tw-truncate">
          {currentFacilityName}
        </span>
        <i
          className={`fa-solid fa-chevron-down tw-text-xs tw-transition-transform tw-duration-200 ${
            isDropdownOpen ? "tw-rotate-180" : ""
          }`}
          aria-hidden="true"
        />
      </button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <div className="tw-absolute tw-top-full tw-left-0 tw-mt-1 tw-w-72 tw-bg-white tw-border tw-border-gray-200 tw-rounded-lg tw-shadow-lg tw-z-50 tw-max-h-80 tw-overflow-hidden">
          {/* Search Input */}
          <div className="tw-p-3 tw-border-b tw-border-gray-100">
            <div className="tw-relative">
              <i
                className="fa-solid fa-search tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-text-gray-400 tw-text-sm"
                aria-hidden="true"
              />
              <input
                type="text"
                placeholder="Search facilities..."
                className="tw-w-full tw-pl-10 tw-pr-4 tw-py-2 tw-border tw-border-gray-300 tw-rounded-md tw-text-sm focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-blue-500 focus:tw-border-blue-500"
                onChange={e => handleSearchChange(e.target.value)}
                autoFocus
              />
            </div>
          </div>

          {/* Options List */}
          <div className="tw-max-h-60 tw-overflow-y-auto" role="listbox">
            {isEmpty(options) ? (
              <div className="tw-p-4 tw-text-center tw-text-gray-500 tw-text-sm">
                No facilities found
              </div>
            ) : (
              options.map(option => (
                <div
                  key={option.value}
                  onClick={() => handleOptionClick(option)}
                  onKeyDown={e => handleKeyDown(e, option)}
                  className={`tw-flex tw-items-center tw-space-x-3 tw-px-4 tw-py-3 tw-cursor-pointer tw-transition-colors tw-duration-150 hover:tw-bg-blue-50 focus:tw-bg-blue-50 focus:tw-outline-none ${
                    formattedFacility?.value === option.value
                      ? "tw-bg-blue-100 tw-text-blue-900"
                      : "tw-text-gray-700"
                  }`}
                  role="option"
                  aria-selected={formattedFacility?.value === option.value}
                  tabIndex={0}
                >
                  <i
                    className="fa-solid fa-building tw-text-gray-400 tw-text-sm"
                    aria-hidden="true"
                  />
                  <span className="tw-flex-1 tw-text-sm">{option.label}</span>
                  {formattedFacility?.value === option.value && (
                    <i
                      className="fa-solid fa-check tw-text-blue-600 tw-text-sm"
                      aria-hidden="true"
                    />
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isConfirmationModalOpen}
        title="Confirm Facility Change"
        appElement=".main"
      >
        <div className="tw-space-y-4">
          <div className="tw-flex tw-items-start tw-space-x-3">
            <i
              className="fa-solid fa-circle-exclamation tw-text-yellow-500 tw-mt-1"
              aria-hidden="true"
            />
            <p className="tw-text-gray-700">{confirmationMessage}</p>
          </div>
        </div>

        <div className="tw-flex tw-justify-end tw-space-x-3 tw-mt-6">
          <Button
            onClick={handleCancelSelection}
            variant="outline"
            className="tw-px-4 tw-py-2"
          >
            <i className="fa-solid fa-xmark tw-mr-2" aria-hidden="true" />
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSelection}
            variant="primary"
            className="tw-px-4 tw-py-2"
          >
            <i className="fa-solid fa-check tw-mr-2" aria-hidden="true" />
            Confirm
          </Button>
        </div>
      </ConfirmationModal>
    </div>
  );
};

export default FacilitySelector;
