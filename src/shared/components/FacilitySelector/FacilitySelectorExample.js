import FacilitySelector from "./index";

const FacilitySelectorExample = () => {
  const handleFacilityChange = () => {
    console.log("Facility changed");
    // Handle facility change logic here
  };

  return (
    <div className="tw-p-6 tw-max-w-6xl tw-mx-auto tw-space-y-6">
      <div className="tw-space-y-2">
        <h2 className="tw-text-2xl tw-font-bold tw-text-gray-900">
          Facility Selector - Top Bar Integration
        </h2>
        <p className="tw-text-gray-600">
          This component replaces the blue left-side navigation facility
          selector with a modern pill-shaped selector designed for the top bar.
        </p>
      </div>

      {/* Mock Top Bar Layout */}
      <div className="tw-bg-white tw-border tw-border-gray-200 tw-rounded-lg tw-p-4">
        <h3 className="tw-text-lg tw-font-semibold tw-mb-4">
          Example Top Bar Layout
        </h3>

        {/* Top Bar */}
        <div className="tw-flex tw-items-center tw-justify-between tw-bg-gray-50 tw-p-4 tw-rounded-lg tw-border">
          <div className="tw-flex tw-items-center tw-space-x-4">
            {/* Logo/Brand */}
            <div className="tw-flex tw-items-center tw-space-x-2">
              <div className="tw-w-8 tw-h-8 tw-bg-blue-600 tw-rounded tw-flex tw-items-center tw-justify-center">
                <i className="fa-solid fa-hospital tw-text-white tw-text-sm" />
              </div>
              <span className="tw-font-semibold tw-text-gray-900">
                Q-Centrix
              </span>
            </div>

            {/* Facility Selector */}
            <FacilitySelector
              onFacilityChange={handleFacilityChange}
              confirmationRequired={true}
            />

            {/* Page Title */}
            <div className="tw-flex tw-items-center tw-space-x-2">
              <span className="tw-text-gray-400">|</span>
              <h1 className="tw-text-xl tw-font-semibold tw-text-gray-900">
                Dashboard
              </h1>
            </div>
          </div>

          {/* Right side items */}
          <div className="tw-flex tw-items-center tw-space-x-4">
            <button className="tw-p-2 tw-text-gray-500 hover:tw-text-gray-700">
              <i className="fa-solid fa-bell" />
            </button>
            <button className="tw-p-2 tw-text-gray-500 hover:tw-text-gray-700">
              <i className="fa-solid fa-user-circle" />
            </button>
          </div>
        </div>
      </div>

      {/* Standalone Examples */}
      <div className="tw-bg-gray-50 tw-p-4 tw-rounded-lg">
        <h3 className="tw-text-lg tw-font-semibold tw-mb-4">
          Standalone Examples
        </h3>

        <div className="tw-space-y-4">
          {/* With Confirmation */}
          <div>
            <h4 className="tw-font-medium tw-mb-2">With Confirmation Modal:</h4>
            <FacilitySelector
              onFacilityChange={handleFacilityChange}
              confirmationRequired={true}
            />
          </div>

          {/* Without Confirmation */}
          <div>
            <h4 className="tw-font-medium tw-mb-2">
              Without Confirmation Modal:
            </h4>
            <FacilitySelector
              onFacilityChange={handleFacilityChange}
              confirmationRequired={false}
            />
          </div>

          {/* Custom Styling */}
          <div>
            <h4 className="tw-font-medium tw-mb-2">With Custom Styling:</h4>
            <FacilitySelector
              onFacilityChange={handleFacilityChange}
              confirmationRequired={true}
              className="tw-shadow-md"
            />
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="tw-bg-white tw-border tw-border-gray-200 tw-rounded-lg tw-p-4">
        <h3 className="tw-text-lg tw-font-semibold tw-mb-2">
          Usage Instructions
        </h3>
        <div className="tw-space-y-2 tw-text-sm tw-text-gray-600">
          <p>• Place this component in your top bar before the page title</p>
          <p>
            • The component will automatically fetch and display available
            facilities
          </p>
          <p>
            • When a user selects a facility, a confirmation modal will appear
            (if enabled)
          </p>
          <p>• The component integrates with Redux to manage facility state</p>
          <p>• Search functionality is built-in for easy facility finding</p>
        </div>
      </div>
    </div>
  );
};

export default FacilitySelectorExample;
