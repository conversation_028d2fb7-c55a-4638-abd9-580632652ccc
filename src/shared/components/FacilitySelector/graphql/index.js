import { gql } from "@apollo/client";

export const GET_USER_AVAILABLE_FACILITIES = gql`
  query currentUserAvailableFacilities(
    $perPage: Int
    $page: Int
    $search: String
  ) {
    currentUserAvailableFacilities(
      perPage: $perPage
      page: $page
      search: $search
    ) {
      userAvailableFacilities {
        id
        name
      }
      count
    }
  }
`;

export const UPDATE_USER_FACILITY = gql`
  mutation updateUserFacility($facilityId: ID!) {
    updateUserFacility(facilityId: $facilityId) {
      success
      message
    }
  }
`;
