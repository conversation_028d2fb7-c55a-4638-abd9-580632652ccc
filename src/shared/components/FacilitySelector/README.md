# FacilitySelector Component

A modern facility selector component designed for the top bar that replaces the blue left-side navigation facility selector. Features a sleek pill-shaped design with dropdown functionality and confirmation modal.

## Features

- **Pill-shaped Design**: Clean blue button with location icon matching the provided screenshot
- **Top Bar Integration**: Designed specifically for placement in the top bar before page titles
- **Single Facility Selection**: Select one facility at a time (not multi-select)
- **Confirmation Modal**: Optional confirmation dialog when changing facilities
- **Search Functionality**: Real-time search through available facilities
- **Redux Integration**: Connects to existing facility state management
- **Accessibility**: Full keyboard navigation and screen reader support
- **Responsive Design**: Adapts to different screen sizes

## Usage

```jsx
import FacilitySelector from "shared/components/FacilitySelector";

const TopBar = () => {
  const handleFacilityChange = () => {
    // Handle facility change - component automatically updates Redux state
    console.log("Facility changed");
  };

  return (
    <div className="top-bar">
      <div className="logo">Q-Centrix</div>
      <FacilitySelector
        onFacilityChange={handleFacilityChange}
        confirmationRequired={true}
      />
      <h1>Dashboard</h1>
    </div>
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onFacilityChange` | `Function` | `undefined` | Callback function called after facility selection is confirmed |
| `confirmationRequired` | `Boolean` | `true` | Whether to show confirmation modal for changes |
| `className` | `String` | `""` | Additional CSS classes for the container |

## Design

The component matches the design shown in the provided screenshot:

- **Blue pill-shaped button** with rounded corners
- **Location pin icon** on the left
- **Facility name** in the center (truncated if too long)
- **Dropdown arrow** on the right that rotates when opened
- **Hover effects** and focus states for accessibility

## Dropdown Features

- **Search input** at the top for filtering facilities
- **Scrollable list** of available facilities
- **Current selection indicator** with checkmark
- **Building icons** for each facility option
- **Keyboard navigation** support

## State Management

The component integrates with the existing Redux facility state:

- Connects to `state.facility` for current facility
- Dispatches `facilitySlice.actions.requestFacility()` on mount
- Updates facility via `UPDATE_USER_FACILITY` GraphQL mutation
- Triggers `facilitySlice.actions.setSelectedFacility()` on successful update

## GraphQL Integration

Uses two GraphQL operations:

### Query: GET_USER_AVAILABLE_FACILITIES
```graphql
query currentUserAvailableFacilities($perPage: Int, $page: Int, $search: String) {
  currentUserAvailableFacilities(perPage: $perPage, page: $page, search: $search) {
    userAvailableFacilities {
      id
      name
    }
    count
  }
}
```

### Mutation: UPDATE_USER_FACILITY
```graphql
mutation updateUserFacility($facilityId: ID!) {
  updateUserFacility(facilityId: $facilityId) {
    success
    message
  }
}
```

## Accessibility Features

- **Keyboard Navigation**: Full support for arrow keys, Enter, and Space
- **ARIA Labels**: Proper labeling for screen readers
- **Focus Management**: Logical tab order and focus indicators
- **Role Attributes**: Proper listbox and option roles
- **Screen Reader Support**: Announces current selection and changes

## Examples

### Basic Top Bar Usage
```jsx
<div className="top-bar">
  <FacilitySelector onFacilityChange={handleChange} />
</div>
```

### Without Confirmation Modal
```jsx
<FacilitySelector
  onFacilityChange={handleChange}
  confirmationRequired={false}
/>
```

### With Custom Styling
```jsx
<FacilitySelector
  onFacilityChange={handleChange}
  className="custom-facility-selector"
/>
```

## Migration from Side Navigation

This component replaces the q-components `SideNavMenu/FacilitySelector`. Key differences:

1. **Single select** instead of potentially multi-select
2. **Top bar placement** instead of side navigation
3. **Pill design** instead of traditional dropdown
4. **Built-in confirmation** modal functionality
5. **Integrated search** in dropdown

## Dependencies

- `@apollo/client` - For GraphQL operations
- `react-redux` - For Redux state management
- `@q-centrix/q-components-react` - For UI components (Button, ConfirmationModal, Spinner)
- `ramda` - For functional programming utilities
- `shared/hooks/useOnClickOutside` - For dropdown click-outside behavior

## Styling

Uses Tailwind CSS with the `tw-` prefix. Key design elements:

- **Blue primary color** (`tw-bg-blue-600`)
- **Rounded pill shape** (`tw-rounded-full`)
- **Smooth transitions** for interactive states
- **Drop shadows** for elevation
- **Proper spacing** and typography