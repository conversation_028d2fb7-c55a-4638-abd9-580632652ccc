import { useMutation, useQuery } from "@apollo/client";
import { facilitySlice } from "modules/app/redux/slices/facilitySlice";
import {
  always,
  both,
  identity,
  ifElse,
  map,
  pathOr,
  pipe,
  prop,
  sortBy
} from "ramda";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import formatSelectOption from "utils/fp/formatSelectOption";
import { GET_USER_AVAILABLE_FACILITIES, UPDATE_USER_FACILITY } from "./graphql";

const formatFacility = ifElse(
  both(identity, pipe(prop("id"), identity)),
  formatSelectOption(["id", "name"]),
  always(undefined)
);

const getOptions = ({ data, queryObjectNamePath, fields }) =>
  pipe(
    pathOr([], queryObjectNamePath),
    ifElse(
      () => fields === false,
      map(element => ({ label: element, value: element })),
      map(formatSelectOption(fields))
    ),
    sortBy(prop("label"))
  )(data);

export const useFacilitySelector = ({
  onFacilityChange,
  confirmationRequired = true
}) => {
  const dispatch = useDispatch();
  const facility = useSelector(state => state.facility);
  const formattedFacility = useMemo(() => formatFacility(facility), [facility]);

  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [pendingFacility, setPendingFacility] = useState(null);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);

  useEffect(() => {
    dispatch(facilitySlice.actions.requestFacility());
  }, [dispatch]);

  const { data, loading, error } = useQuery(GET_USER_AVAILABLE_FACILITIES, {
    variables: { page, perPage: 25, search }
  });

  const [handleUpdateFacility] = useMutation(UPDATE_USER_FACILITY, {
    refetchQueries: [
      {
        query: GET_USER_AVAILABLE_FACILITIES,
        variables: {}
      }
    ]
  });

  const options = getOptions({
    data,
    queryObjectNamePath: [
      "currentUserAvailableFacilities",
      "userAvailableFacilities"
    ],
    fields: ["id", "name"]
  });

  const totalCount = pathOr(0, ["currentUserAvailableFacilities", "count"])(
    data
  );

  const handleFacilitySelect = useCallback(
    selectedOption => {
      if (confirmationRequired) {
        setPendingFacility(selectedOption);
        setIsConfirmationModalOpen(true);
        setIsDropdownOpen(false);
      } else {
        performFacilityUpdate(selectedOption);
      }
    },
    [confirmationRequired]
  );

  const performFacilityUpdate = useCallback(
    selectedOption => {
      handleUpdateFacility({
        variables: {
          facilityId: selectedOption.value
        },
        onCompleted: () => {
          dispatch(facilitySlice.actions.setSelectedFacility());
          if (typeof onFacilityChange === "function") {
            onFacilityChange();
          }
        }
      });
    },
    [handleUpdateFacility, dispatch, onFacilityChange]
  );

  const handleConfirmSelection = useCallback(() => {
    if (pendingFacility) {
      performFacilityUpdate(pendingFacility);
    }
    setIsConfirmationModalOpen(false);
    setPendingFacility(null);
  }, [pendingFacility, performFacilityUpdate]);

  const handleCancelSelection = useCallback(() => {
    setIsConfirmationModalOpen(false);
    setPendingFacility(null);
  }, []);

  const handlePageChange = useCallback(page => setPage(page), []);
  const handleSearchChange = useCallback(search => setSearch(search), []);

  const toggleDropdown = useCallback(() => {
    setIsDropdownOpen(!isDropdownOpen);
  }, [isDropdownOpen]);

  const confirmationMessage = useMemo(() => {
    if (!pendingFacility) return "";
    return `Are you sure you want to switch to ${pendingFacility.label}?`;
  }, [pendingFacility]);

  return {
    formattedFacility,
    options,
    page,
    totalCount,
    loading,
    error,
    isDropdownOpen,
    isConfirmationModalOpen,
    confirmationMessage,
    handleFacilitySelect,
    handleConfirmSelection,
    handleCancelSelection,
    handlePageChange,
    handleSearchChange,
    toggleDropdown,
    setIsDropdownOpen
  };
};
