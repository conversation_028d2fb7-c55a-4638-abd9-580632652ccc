// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Comments renders Comment loading state 1`] = `
Array [
  <div
    className="tw-px-5"
  >
    <div
      className="tw-flex tw-justify-center tw-p-10"
    >
      <Spinner />
    </div>
  </div>,
  <DeleteModal
    body="Are you sure you want to permanently delete this comment?"
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Comment"
  />,
  <form
    autoComplete="off"
    className="tw-flex tw-flex-col tw-gap-y-5 tw-p-5 tw-bg-white tw-border-t tw-border-black-29"
  >
    <TextArea
      disabled={false}
      placeholder="Write any comments about this case"
      textareaClassName="tw-min-h-[120px]"
      value=""
    />
    <Button
      bg="main"
      customStyle="tw-right-5 tw-top-5 tw-flex tw-gap-2.5 tw-items-center tw-self-start"
      type="submit"
    >
      <i
        className="fa-light fa-message-medical"
      />
      Add comment
    </Button>
  </form>,
]
`;

exports[`Comments renders Comments 1`] = `
Array [
  <div
    className="tw-px-5"
  >
    <Comment
      commentBody="First comment"
      commenterName="Russell Reas (me)"
      confirmDeleteComment={[Function]}
      createdAt="2023-10-20T18:16:44-04:00"
      date="2023-10-20T18:16:44-04:00"
      disabled={false}
      editMode={false}
      id="86"
      onCancelEditing={[Function]}
      onDoneEditing={[Function]}
      onEdit={[Function]}
      permissionToEdit={true}
      updatedAt="2023-10-20T18:16:44-04:00"
    />
    <Comment
      commentBody="Second comment"
      commenterName="Russell Reas (me)"
      confirmDeleteComment={[Function]}
      createdAt="2023-10-20T18:16:25-04:00"
      date="2023-10-20T18:16:25-04:00"
      disabled={false}
      editMode={false}
      id="85"
      onCancelEditing={[Function]}
      onDoneEditing={[Function]}
      onEdit={[Function]}
      permissionToEdit={true}
      updatedAt="2023-10-20T18:16:25-04:00"
    />
    <Comment
      commentBody="Third comment"
      commenterName="Russell Reas (me)"
      confirmDeleteComment={[Function]}
      createdAt="2023-10-20T18:00:50-04:00"
      date="2023-10-20T18:00:50-04:00"
      disabled={false}
      editMode={false}
      id="83"
      onCancelEditing={[Function]}
      onDoneEditing={[Function]}
      onEdit={[Function]}
      permissionToEdit={true}
      updatedAt="2023-10-20T18:00:50-04:00"
    />
  </div>,
  <DeleteModal
    body="Are you sure you want to permanently delete this comment?"
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Comment"
  />,
  <form
    autoComplete="off"
    className="tw-flex tw-flex-col tw-gap-y-5 tw-p-5 tw-bg-white tw-border-t tw-border-black-29"
  >
    <TextArea
      disabled={false}
      placeholder="Write any comments about this case"
      textareaClassName="tw-min-h-[120px]"
      value=""
    />
    <Button
      bg="main"
      customStyle="tw-right-5 tw-top-5 tw-flex tw-gap-2.5 tw-items-center tw-self-start"
      type="submit"
    >
      <i
        className="fa-light fa-message-medical"
      />
      Add comment
    </Button>
  </form>,
]
`;

exports[`Comments renders Comments error state 1`] = `
<p
  className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500"
>
  Error: Could not retrieve the data.
</p>
`;
