import {
  ApolloClient,
  ApolloLink,
  InMemoryCache,
  from,
  split
} from "@apollo/client";
import { createUploadLink } from "apollo-upload-client";
import { getMainDefinition } from "@apollo/client/utilities";
import { onError } from "@apollo/client/link/error";
import { getCSRFToken } from "utils/serverRequest";
import possibleTypes from "./possibleTypes.json";
import redirectToLogin from "../utils/redirectToLogin";
import { createConsumer } from "@rails/actioncable";
import ActionCableLink from "graphql-ruby-client/subscriptions/ActionCableLink";
import { wsUri } from "./constants";

const cable = wsUri ? createConsumer(wsUri) : createConsumer();

/**
 * Afterware to handle Apollo request errors.
 * For now only 401 is handled to redirect to login
 */
const errorLink = onError(({ networkError }) => {
  if (networkError && networkError.statusCode === 401) {
    redirectToLogin();
  }
});

/**
 * Middleware that adds the XSRF token to header on every Apollo request
 */
const middlewareLink = new ApolloLink((operation, forward) => {
  operation.setContext({
    headers: {
      "X-CSRF-Token": getCSRFToken()
    }
  });
  return forward(operation);
});

const getLinkChain = uri => {
  const httpLink = createUploadLink({
    uri,
    credentials: "same-origin"
  });

  // The split function takes three parameters:
  //
  // A function that's called for each operation to execute
  // The Link to use for an operation if the function returns a "truthy" value
  // The Link to use for an operation if the function returns a "falsy" value
  const splitLink = split(
    ({ query }) => {
      const definition = getMainDefinition(query);

      return (
        definition.kind === "OperationDefinition" &&
        definition.operation === "subscription"
      );
    },
    new ActionCableLink({ cable }),
    httpLink
  );

  return from([errorLink, middlewareLink, splitLink]);
};

// Creates Cache to hold objects
export const cache = new InMemoryCache({
  possibleTypes
});

export const apolloClient = uri =>
  new ApolloClient({
    link: getLinkChain(uri),
    cache
  });

export default apolloClient;
