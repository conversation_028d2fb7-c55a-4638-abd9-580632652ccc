import Layout from "shared/components/Layout";
import Hours from "shared/widgets/Hours";
import LastLogInTile from "shared/widgets/LastLogInTile";
import redirectToRoot from "utils/redirectToRoot";
import QPoints from "shared/widgets/QPoints";
import { ApolloProvider } from "@apollo/client";
import apolloClient from "base/apolloClient";

export const client = apolloClient("/qapps/graphql");

const topbarChildren = (
  <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
    <h1 className="tw-text-2xl tw-font-semibold">Page Not Found</h1>
    <div className="tw-flex tw-items-center tw-gap-5">
      <Hours />
      <QPoints />
      <LastLogInTile />
    </div>
  </div>
);

const Page404 = () => (
  <ApolloProvider client={client}>
    <Layout tbChildren={topbarChildren} onLogoClick={redirectToRoot}>
      <div>Oops! The page you are looking for doesn&apos;t exist</div>
    </Layout>
  </ApolloProvider>
);

export default Page404;
