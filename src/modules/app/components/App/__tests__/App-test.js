import { act, create } from "react-test-renderer";
import wait from "waait";
import App from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Toaster: "Toaster",
  <PERSON><PERSON>: "But<PERSON>",
  Spinner: "Spinner"
}));
jest.mock("../../404", () => "Page404");

describe("App", () => {
  function render() {
    return create(<App />);
  }

  test("it renders spinner before token call", () => {
    const app = render(<App />);

    expect(app).toMatchSnapshot();
  });

  test("it renders component after token call", async () => {
    const app = render(<App />);

    await act(() => wait(200));

    expect(app).toMatchSnapshot();
  });
});
