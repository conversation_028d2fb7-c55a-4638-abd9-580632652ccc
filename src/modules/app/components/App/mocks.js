import abstractorMocks from "modules/abstractor/graphql/mocks";
import caseReviewMocks from "modules/case-review/graphql/mocks";
import samplingSettingManagerDataMock from "modules/sampling-manager/graphql/mocks";
import skipLogicManagerMocks from "modules/skip-logic-manager/graphql/mocks";
import populationDataMock from "modules/population-data/graphql/mocks";
import finalizationDataMock from "modules/sample-finalization/graphql/mocks";

export default [
  ...abstractorMocks,
  ...caseReviewMocks,
  ...samplingSettingManagerDataMock,
  ...skipLogicManagerMocks,
  ...populationDataMock,
  ...finalizationDataMock
];
