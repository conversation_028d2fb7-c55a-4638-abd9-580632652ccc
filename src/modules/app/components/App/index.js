import { useEffect, useState } from "react";
import axios from "axios";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON>, Routes, Route } from "react-router-dom";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
// import { ApolloProvider } from "@apollo/client";
import { IntlProvider } from "react-intl";
import { basePath } from "base/constants";
import configureStore from "base/configureStore";
// import apolloClient from "base/apolloClient";
import "@q-centrix/q-components-react/dist/q-components-react.cjs.development.css";
import localeData from "base/locales/data";
import { Toaster, Spinner } from "@q-centrix/q-components-react";
import Abstractor from "modules/abstractor/components/Abstractor";
import ReviewQuestionnaire from "modules/case-review/components/ReviewQuestionnaire";
import SkipLogicManager from "modules/skip-logic-manager/components/SkipLogicManager";
import SkipLogicHistory from "modules/skip-logic-manager/components/SkipLogicHistory";
import SampleFinalization from "modules/sample-finalization/components/SampleFinalization";
import Cases from "modules/cases/components/Cases";
import CaseCreate from "modules/cases/components/CaseCreate";
import FacilityGroups from "modules/facility-groups/components/FacilityGroups";
import FacilityGroupListing from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupsListing";
import FacilityGroupsDetails from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails";
import FacilityGroupDetails from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/Details";
import FacilityGroupActiveMeasures from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/ActiveMeasures";
import FacilityGroupPopulationData from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/PopulationData";
import FacilityGroupSamplingManager from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/SamplingManager";
import FacilityGroupSampleFinalization from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/SampleFinalization";
import FacilityGroupPsychiatricPatientCensus from "modules/facility-groups/components/FacilityGroups/Content/FacilityGroupDetails/PsychiatricPatientCensus";
import CaseDetails from "modules/cases/components/CaseDetails";
import Submission from "modules/submission/components/Submission";
import Hours from "modules/hours/components/Hours";
import InfectionPrevention from "modules/infection-prevention/components/InfectionPrevention";
import { defaultTo, identity, ifElse, path, pipe } from "ramda";
import redirectToLogin from "utils/redirectToLogin";
import Upload from "modules/upload/components/Upload";
import Page404 from "../404";
import Facility from "modules/facilities/components/Facility";
import MeasuresAndRegistries from "modules/measures-and-registries/components/MeasuresAndRegistries";
import Activity from "modules/activity/components/Activity";
import System from "modules/system/components/System";
import GlobalRoles from "modules/system/components/GlobalRoles";
import PriceModifiers from "modules/facilities/components/PriceModifiers";

const { store, persistor } = configureStore();
const locale = "en";
const messages = localeData[locale] || localeData.en;

function App() {
  const [tokenLoaded, setTokenLoaded] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await axios({
          method: "get",
          url: "/xsrf-token",
          headers: {
            Accept: "application/json"
          }
        });

        pipe(
          defaultTo({}),
          path(["data", "token"]),
          ifElse(
            identity,
            token => window.sessionStorage.setItem("csrf", token),
            () => redirectToLogin()
          )
        )(response);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.log("error", e);
        redirectToLogin();
      }
      setTokenLoaded(true);
    })();
  }, []);

  if (!tokenLoaded) return <Spinner />;

  return (
    <IntlProvider locale={locale} messages={messages}>
      <Provider store={store}>
        <PersistGate persistor={persistor}>
          <div className="app-container">
            <BrowserRouter basename={basePath}>
              <div className="tw-min-h-screen tw-overflow-y-auto">
                <Routes>
                  <Route path="/abstractor" element={<Abstractor />} />
                  <Route
                    path="/case-review/:reviewId"
                    element={<ReviewQuestionnaire />}
                  />
                  <Route
                    path="/skip-logic-manager"
                    element={<SkipLogicManager />}
                  />
                  <Route
                    path="/skip-logic-history"
                    element={<SkipLogicHistory />}
                  />
                  <Route
                    path="/sample-finalization"
                    element={<SampleFinalization groupPage={false} />}
                  />
                  <Route path="/:type/new" element={<CaseCreate />} />
                  <Route path="/submission" element={<Submission />} />
                  <Route path="/cases" element={<Cases />} />
                  <Route path="/cases/:id" element={<CaseDetails />} />
                  <Route path="/hours/:id" element={<Hours />} />
                  <Route path="/activities/:id" element={<Activity />} />
                  <Route path="/facility-groups" element={<FacilityGroups />}>
                    <Route index element={<FacilityGroupListing />} />
                    <Route path=":id" element={<FacilityGroupsDetails />}>
                      <Route
                        path="details"
                        element={<FacilityGroupDetails />}
                      />
                      <Route
                        path="active-measures"
                        element={<FacilityGroupActiveMeasures />}
                      />
                      <Route
                        path="population-data"
                        element={<FacilityGroupPopulationData />}
                      />
                      <Route
                        path="sampling-manager"
                        element={<FacilityGroupSamplingManager />}
                      />
                      <Route
                        path="sample-finalization"
                        element={<FacilityGroupSampleFinalization />}
                      />
                      <Route
                        path="psychiatric-patient-census"
                        element={<FacilityGroupPsychiatricPatientCensus />}
                      />
                    </Route>
                  </Route>
                  <Route
                    path="/infection-prevention"
                    element={<InfectionPrevention />}
                  >
                    <Route path=":id" element={<InfectionPrevention />} />
                  </Route>
                  <Route path="/upload/*" element={<Upload />} />
                  <Route path="/facilities/*" element={<Facility />} />
                  <Route
                    path="/system/measures_and_registries/*"
                    element={<MeasuresAndRegistries />}
                  />
                  <Route path="/system/facilities/:id/price_modifiers" element={<PriceModifiers />} />
                  <Route path="/system/admin/users/*" element={<System />} />
                  <Route path="/users/*" element={<System />} />
                  <Route
                    path="/system/global_roles/*"
                    element={<GlobalRoles />}
                  />
                  <Route path="*" element={<Page404 />} />
                </Routes>
              </div>
            </BrowserRouter>
          </div>
          <Toaster />
        </PersistGate>
      </Provider>
    </IntlProvider>
  );
}

export default App;
