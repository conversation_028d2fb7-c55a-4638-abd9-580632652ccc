import { gql } from "@apollo/client";

export const GET_CURRENT_USER_AVAILABLE_FACILITIES = gql`
  query currentUserAvailableFacilities(
    $page: Int
    $perPage: Int
    $search: String
  ) {
    currentUserAvailableFacilities(
      page: $page
      perPage: $perPage
      search: $search
    ) {
      userAvailableFacilities {
        id
        name
      }
      count
      page
      perPage
    }
  }
`;

export const GET_AVAILABLE_APPS_DATA = gql`
  query userApps {
    userApps {
      name
      key
      path
      description
      enabled
    }
  }
`;
