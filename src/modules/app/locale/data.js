export default {
  buttons: {
    cancelChanges: "Cancel Changes",
    deleteThisBatch: "Delete This Batch",
    downloadFile: "Download File",
    saveChanges: "Save Changes",
    upload: "Upload",
    deleteRows: "Delete Selected Rows"
  },
  automatedCaseUpload: {
    columns: {
      batchID: "Batch ID",
      fileName: "File Name",
      numCreatedRecords: "# of Created Records",
      uploadDateTime: "Upload Date/Time",
      numErrors: "# of Errors",
      status: "Status",
      email: "Email Upload Log"
    },
    messages: {
      selectOptions: "Please select an upload type and file to upload",
      massUpdateErrorMessage:
        "{errorCount, plural, one {# error} other {# errors}} prevented your changes"
    },
    statuses: {
      deleted: "Batch Deleted",
      // eslint-disable-next-line camelcase
      deleted_partial: "Batch Partially Deleted",
      error: "Error",
      failed: "Failed",
      processing: "Processing",
      success: "Success",
      waiting: "Waiting",
      // eslint-disable-next-line camelcase
      partial_error: "Partial Error"
    }
  },
  caseUpload: {
    multiFacility: "Multiple Facilities",
    columns: {
      batchID: "Batch ID",
      user: {
        id: "User ID",
        fullName: "Uploaded By"
      },
      createdAt: "Time of Upload",
      errors: "Errors",
      facility: "Facility",
      lastUpdated: "Last Updated",
      numberOfCases: "# of cases",
      status: "Status"
    },
    labels: {
      multiFacility: "OR upload cases for multiple facilities "
    },
    messages: {
      selectOptions: "Please select facility and file to upload",
      massUpdateErrorMessage:
        "{errorCount, plural, one {# error} other {# errors}} prevented your changes"
    },
    detailsTable: {
      errorMessage:
        "You have {errorCount, plural, one {# error} other {# errors}} in this CSV. Please correct {errorCount, plural, one {it} other {them}} to proceed with the upload.",
      updateHeader: "Change all filtered {field} fields",
      tableHeaders: {
        caseTypeName: "Case Type Name",
        arrivalDate: "Arrival Date",
        admissionDate: "Admission Date",
        procedureDate: "Procedure Date",
        dischargeDate: "Discharge Date",
        dropDate: "Drop Date",
        clientDeadline: "Deadline",
        patientMrn: "Patient MRN",
        visitNumber: "Visit Number",
        userEmail: "User Email",
        facility: "Facility",
        shell: "Shell",
        bornOn: "DOB",
        gender: "Sex"
      },
      deleteMessage:
        "{deletedCount, plural, =0 {No cases were} one {# case was} other {# cases were}} deleted by {name}.",
      failedDeleteMessage:
        "These cases could not be deleted because there is another record associated with this case."
    },
    errorTable: {
      valid: "{valid, plural, one {Valid case} other {Valid cases}}",
      invalid: "{invalid, plural, one {Invalid case} other {Invalid cases}}",
      errorType: "Error Type",
      rows: "Rows",
      columnName: "Column Name",
      select: "Select"
    },
    statuses: {
      deleted: "Batch Deleted",
      // eslint-disable-next-line camelcase
      deleted_partial: "Batch Partially Deleted",
      error: "Error",
      // eslint-disable-next-line camelcase
      partial_error: "Partial Error",
      // eslint-disable-next-line camelcase
      failed_create: "Upload failed",
      // eslint-disable-next-line camelcase
      failed_update: "Update failed",
      processing: "Processing",
      success: "Success",
      waiting: "Waiting"
    }
  },
  labels: {
    chooseFacility: "Choose Facility",
    percentage: "Percentage",
    status: "Status",
    taskType: "Task Type"
  },
  payrollDataUpload: {
    titles: {
      managingPayPeriod: "MANAGING PAY PERIOD",
      payrollUploadErrors: "Payroll Upload Errors",
      payrollDataUploadTool: "Payroll Data Upload Tool"
    },
    labels: {
      dataType: "Choose Data Type"
    },
    columns: {
      dataType: "Data Type",
      uploadDateTime: "Uploaded Date/Time",
      numberOfRecords: "# of Records",
      errors: "Errors",
      status: "Status",
      rowNumber: "Row Number",
      reason: "Reason"
    },
    messages: {
      selectOptions: "Please select a data type and file to upload"
    }
  },
  titles: {
    batchId: "BATCH ID",
    caseUploadTool: "Case Upload Tool",
    priceModifers: "Price Modifiers"
  },
  caseFinding: {
    assignModal: {
      messages: {
        success: "Case finding record assignment - {response}",
        error: "User cannot be assigned without facility access. {errors}",
        errors: "Error occurred. Try again."
      }
    }
  }
};
