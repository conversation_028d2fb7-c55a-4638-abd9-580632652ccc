import { fetchData } from "utils/serverRequest";
import {
  requestAccountSettings,
  successAccountSettings,
  failureAccountSettings
} from "../slices/accountSettingsSlice";
import {
  requestFacility,
  successFacility,
  failureFacility,
  setSelectedFacility
} from "../slices/facilitySlice";

export function fetchAccountSettings() {
  return fetchData({
    uri: "/api/user/v2/account_settings",
    requestFunction: requestAccountSettings,
    successFunction: successAccountSettings,
    failureFunction: failureAccountSettings
  });
}

export function fetchFacility() {
  return fetchData({
    uri: "/api/user/v2/facility",
    requestFunction: requestFacility,
    successFunction: successFacility,
    failureFunction: failureFacility
  });
}

export function changeCurrentFacility(facilityId) {
  return fetchData({
    method: "PUT",
    uri: `/facilities/${facilityId}/set`,
    requestData: { no_redirect: "true" }, // eslint-disable-line
    requestFunction: requestFacility,
    successFunction: setSelectedFacility,
    failureFunction: failureFacility
  });
}
