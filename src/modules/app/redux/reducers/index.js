import { combineReducers } from "redux";
import { persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { reducers } from "@q-centrix/q-components-react";
import caseReview from "modules/case-review/redux/slice";
import populationData from "modules/population-data/redux/slice";
import psychiatricPatientCensus from "modules/psychiatric-patient-census/redux/slice";
import accountSettings from "modules/app/redux/slices/accountSettingsSlice";
import sampleFinalization from "modules/sample-finalization/redux/slice";
import facility from "modules/app/redux/slices/facilitySlice";
import cases from "modules/cases/redux/slice";
import upload from "modules/upload/redux/slice";
import facilityGroups from "modules/facility-groups/redux/slice";
import activeMeasures from "modules/active-measures/redux/slice";
import submission from "modules/submission/redux/slice";
import { reducer as skipLogicManager } from "modules/skip-logic-manager/redux/reducers";

const caseReviewPersistConfig = {
  key: "caseReview",
  storage
};

const casesPersistConfig = {
  key: "cases",
  storage,
  whitelist: ["activeCaseListIds", "currentCaseIndex"]
};

export const reducer = combineReducers({
  ...reducers,
  accountSettings,
  facility,
  caseReview: persistReducer(caseReviewPersistConfig, caseReview),
  skipLogicManager,
  populationData,
  psychiatricPatientCensus,
  cases: persistReducer(casesPersistConfig, cases),
  upload,
  facilityGroups,
  sampleFinalization,
  activeMeasures,
  submission
});

export default reducer;
