import { createSlice } from "@reduxjs/toolkit";
import { always, evolve, mergeRight, pipe } from "ramda";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null
};

export const accountSettingsSlice = createSlice({
  name: "accountSettings",
  initialState,
  reducers: {
    requestAccountSettings: state =>
      evolve({ isFetching: always(true), isLoaded: always(false) }, state),
    successAccountSettings: (state, { payload }) => {
      const {
        data: { data }
      } = payload;
      const accountSettings = {
        ...data.attributes,
        id: data.id
      };

      return pipe(
        evolve({
          isFetching: always(false),
          isLoaded: always(true),
          error: always(null)
        }),
        mergeRight(accountSettings)
      )(state);
    },
    failureAccountSettings: (state, { payload }) => {
      const { error } = payload;

      return evolve(
        {
          isFetching: always(false),
          isLoaded: always(false),
          error: always(error)
        },
        state
      );
    }
  }
});

// Action creators are generated for each case reducer function
export const {
  requestAccountSettings,
  successAccountSettings,
  failureAccountSettings
} = accountSettingsSlice.actions;

export default accountSettingsSlice.reducer;
