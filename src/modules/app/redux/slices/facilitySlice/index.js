import { createSlice } from "@reduxjs/toolkit";
import { always, evolve, mergeLeft, pipe } from "ramda";
import client from "base/apolloClient";
import { GET_AVAILABLE_APPS_DATA } from "modules/app/graphql/query";

const initialState = {
  isFetching: false,
  isLoaded: false,
  error: null
};

export const facilitySlice = createSlice({
  name: "facility",
  initialState,
  reducers: {
    requestFacility: state =>
      evolve({ isFetching: always(true), isLoaded: always(false) }, state),
    successFacility: (state, { payload }) => {
      const {
        data: { data }
      } = payload;
      const facility = {
        ...data.attributes,
        id: data.id
      };

      return pipe(
        evolve({
          isFetching: always(false),
          isLoaded: always(true),
          error: always(null)
        }),
        mergeLeft(facility)
      )(state);
    },
    failureFacility: (state, { payload }) => {
      const { error } = payload;

      return evolve(
        {
          isFetching: always(false),
          isLoaded: always(false),
          error: always(error)
        },
        state
      );
    },
    setSelectedFacility: state => {
      client.refetchQueries({
        include: [GET_AVAILABLE_APPS_DATA]
      });

      return evolve(
        {
          isFetching: always(false),
          isLoaded: always(true),
          error: always(null)
        },
        state
      );
    }
  }
});

// Action creators are generated for each case reducer function
export const {
  requestFacility,
  successFacility,
  failureFacility,
  setSelectedFacility
} = facilitySlice.actions;

export default facilitySlice.reducer;
