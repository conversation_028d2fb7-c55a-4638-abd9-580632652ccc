import { useCallback, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { userFeatureToggles } from "modules/facility-groups/utils/userFeatureToggles";
import { not } from "ramda";

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const navigate = useNavigate();
  const { type } = useParams();

  const { isFeatureEnabled, featureTogglesLoaded } = userFeatureToggles();

  const handleBackButton = useCallback(
    e => {
      if (e) e.preventDefault();

      navigate("/cases");
    },
    [navigate]
  );

  const isHoursEnabled = isFeatureEnabled("Hours Redesign");
  const isPhase4Enabled = isFeatureEnabled("QFlow Phase 4");

  const [createChecked, setCreateChecked] = useState(false);
  const [duplicateChecked, setDuplicateChecked] = useState(false);
  const [disableStartTimer, setDisableStartTimer] = useState(true);
  const [saveAndStartTimer, setSaveAndStartTimer] = useState(false);

  const handleCreateChecked = () => {
    setCreateChecked(not);

    if (createChecked) {
      setDuplicateChecked(false);
    }
  };

  const handleResetCheckboxes = () => {
    setCreateChecked(false);
    setDuplicateChecked(false);
  };

  const handleDuplicateChecked = () => setDuplicateChecked(not);

  const handleSaveAndStartTimer = useCallback(
    value => setSaveAndStartTimer(value),
    []
  );
  const handleDisableStartTimer = useCallback(
    value => setDisableStartTimer(value),
    []
  );

  return {
    handleBackButton,
    isHoursEnabled,
    featureTogglesLoaded,
    type,
    createChecked,
    duplicateChecked,
    saveAndStartTimer,
    disableStartTimer,
    handleCreateChecked,
    handleDuplicateChecked,
    handleResetCheckboxes,
    handleSaveAndStartTimer,
    handleDisableStartTimer,
    isPhase4Enabled
  };
};
