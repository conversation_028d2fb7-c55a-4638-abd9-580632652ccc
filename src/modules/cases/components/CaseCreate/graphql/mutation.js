import { gql } from "@apollo/client";

export const CREATE_CASE = gql`
  mutation createCase($_case: CaseInput!) {
    createCase(_case: $_case) {
      response
      case {
        id
        facility {
          id
          name
        }
        caseType {
          id
          name
          isOncology
        }
        patient {
          id
          mrn
          firstName
          middleName
          lastName
          fullName
          bornOn
        }
        visit {
          id
          hospitalDischargedAt
          surgeryDate
          arrivedAt
          admittedAt
        }
        assignee {
          id
          firstName
          lastName
          fullName
          email
        }
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;
