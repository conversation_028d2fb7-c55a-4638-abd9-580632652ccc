import {
  FACILITIES,
  CASE_TYPES_BY_FACILITY,
  CANCER_TYPES,
  VISIT_DATA,
  PATIENT_DATA,
  GET_HOUR_TYPES,
  GET_HOUR_PRODUCT_CATEGORIES,
  GET_HOUR_APPROVERS,
  GET_ACTIVITY_PERMISSIONS
} from "modules/cases/components/CaseCreate/graphql/query";
import { CREATE_CASE } from "./mutation";
import { CREATE_OR_UPDATE_HOURS_GROUP } from "modules/hours/graphql/mutation";
import { map, pipe, range } from "ramda";

export const facilitiesSuccess = {
  data: {
    currentUserAvailableFacilities: {
      __typename: "UserFacilities",
      userAvailableFacilities: [
        { id: "1", name: "Large Medical Center", __typename: "Facility" },
        { id: "2", name: "Small Medical Center", __typename: "Facility" }
      ]
    }
  }
};

export const caseTypesByFacilitySuccess = {
  data: {
    userCaseTypesByFacility: {
      userFacilityCaseTypes: [
        {
          __typename: "Case",
          id: "1",
          name: "Case Type 1 (Regulatory)",
          isOncology: false,
          isRegulatory: true,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: null,
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null
          }
        },
        {
          __typename: "Case",
          id: "2",
          name: "Case Type 2, (Service Line is GWTG and Business Offering is registry)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "registry",
          serviceLine: "GWTG",
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: 1,
            end: 10,
            allowOverride: true
          }
        },
        {
          __typename: "Case",
          id: "3",
          name: "Case Type 3 (Regulatory and Business Offering is core_measures)",
          isOncology: false,
          isRegulatory: true,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "core_measures",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        },
        {
          __typename: "Case",
          id: "4",
          name: "Case Type 4 (Business Offering is other)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "other",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        },
        {
          __typename: "Case",
          id: "5",
          name: "Case Type 5 (Oncology and Business Offering is other)",
          isOncology: true,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "other",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        },
        {
          __typename: "Case",
          id: "6",
          name: "Case Type 6, 3-10 days LOS (Business Offering is registry)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "registry",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: 3,
            end: 10,
            allowOverride: true
          }
        },
        {
          __typename: "Case",
          id: "7",
          name: "Case Type 7, 1-3 days LOS (Business Offering is registry)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "registry",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: 1,
            end: 3,
            allowOverride: true
          }
        },
        {
          __typename: "Case",
          id: "8",
          name: "Case Type 8, 0 days LOS (Business Offering is core_measures)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "core_measures",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: 0,
            end: 0,
            allowOverride: true
          }
        },
        {
          __typename: "Case",
          id: "9",
          name: "Case Type 9, 1-2 days LOS (Business Offering is core_measures)",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "core_measures",
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: 1,
            end: 2,
            allowOverride: true
          }
        },
        {
          __typename: "Case",
          id: "10",
          name: "CaseType 10 (Regulatory and AdminInpatient)",
          isOncology: false,
          isRegulatory: true,
          isAdminInpatient: true,
          isAdminOutpatient: false,
          businessOffering: null,
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        },
        {
          __typename: "Case",
          id: "11",
          name: "CaseType 11 (Regulatory and AdminOutpatient)",
          isOncology: false,
          isRegulatory: true,
          isAdminInpatient: false,
          isAdminOutpatient: true,
          businessOffering: null,
          serviceLine: null,
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        },
        {
          __typename: "Case",
          id: "12",
          name: "CaseType 12 (Regulatory and AdminOutpatient and allows empty discharge)",
          isOncology: false,
          isRegulatory: true,
          isAdminInpatient: false,
          isAdminOutpatient: true,
          businessOffering: null,
          serviceLine: null,
          allowsEmptyDischargeDate: true,
          lengthOfStay: {
            start: null,
            end: null,
            allowOverride: false
          }
        }
      ]
    }
  }
};

export const cancerTypesSuccess = {
  data: {
    cancerSites: [
      { id: "1", name: "Cancer Type 1", __typename: "CancerSite" },
      { id: "2", name: "Cancer Type 2", __typename: "CancerSite" },
      { id: "3", name: "Cancer Type 3", __typename: "CancerSite" }
    ]
  }
};

export const hourTypesSuccess = {
  data: {
    hourTypes: [
      { id: "1", name: "Hour Type 1" },
      { id: "2", name: "Hour Type 2" },
      { id: "3", name: "Hour Type 3" }
    ]
  }
};

const createProductCategories = (num1, num2) =>
  pipe(
    range(num1),
    map(val => ({ name: `Hour Product Category ${val}` }))
  )(num2);

export const hourApproversSuccess = {
  data: {
    hourApprovers: [
      { id: "1", name: "John Smith" },
      { id: "2", name: "Kate Black" }
    ]
  }
};

const visitDataSuccess = {
  data: {
    visit: {
      id: "1",
      firstContact: null,
      arrivedAt: null,
      admittedAt: "2023-03-26T23:32:02-04:00",
      surgeryDate: null,
      hospitalDischargedAt: null,
      __typename: "Visit",
      patient: {
        id: "1",
        mrn: "1",
        firstName: "Cherelle",
        lastName: "Anderson",
        __typename: "Patient"
      }
    }
  }
};

const patientDataSuccess = {
  data: {
    patient: {
      id: "1",
      firstName: "Cherelle",
      middleName: null,
      lastName: "Anderson",
      bornOn: null,
      __typename: "Patient"
    }
  }
};

const createCaseSuccess = {
  data: {
    createCase: {
      response: "true",
      case: {
        id: "92",
        facility: {
          id: "1",
          name: "Large Medical Center"
        },
        caseType: {
          id: "1",
          name: "ACO",
          isOncology: false
        },
        patient: {
          id: "69",
          mrn: "1",
          firstName: "Cherelle",
          middleName: null,
          lastName: "Anderson",
          fullName: "Cherelle Anderson",
          bornOn: null
        },
        visit: {
          id: "76",
          hospitalDischargedAt: "2023-07-03T19:00:00-04:00",
          surgeryDate: null,
          arrivedAt: "2023-07-01T19:00:00-04:00",
          admittedAt: null
        },
        assignee: {
          id: "1",
          firstName: "Russell",
          lastName: "Reas",
          fullName: "Russell Reas",
          email: "<EMAIL>"
        }
      },
      errors: null
    }
  }
};

const createHourGroupSuccess = {
  data: {
    createOrUpdateHourGroup: {
      hourGroup: {
        approver: {
          id: "1"
        },
        facilities: [{ id: "1", name: "Test" }],
        facilityHourGroups: {
          facilityId: "2"
        },

        hourEntries: {
          id: "3"
        },

        hourType: {
          id: "3"
        },
        id: "1",
        notes: "",
        owner: {
          id: "45"
        },
        productCategory: {},
        qty: 5,
        totalTime: 5
      },
      errors: null
    }
  }
};

const createActivitiesPermissionsSuccess = {
  data: {
    activityPermissions: {
      authorized: true
    }
  }
};

const createActivitiesPermissionsFailure = {
  data: {
    activityPermissions: {
      authorized: false
    }
  }
};

export default [
  {
    request: {
      query: FACILITIES,
      variables: { perPage: 25, page: 1, search: undefined }
    },
    result: facilitiesSuccess
  },
  {
    request: {
      query: CASE_TYPES_BY_FACILITY,
      variables: {
        facilityId: 1,
        perPage: 25,
        page: 1,
        search: undefined
      }
    },
    result: caseTypesByFacilitySuccess
  },
  {
    request: { query: CANCER_TYPES, variables: {} },
    result: cancerTypesSuccess
  },
  {
    request: {
      query: VISIT_DATA,
      variables: {
        facilityId: 1,
        visitNumber: "1"
      }
    },
    result: visitDataSuccess
  },
  {
    request: {
      query: PATIENT_DATA,
      variables: {
        facilityId: 1,
        mrn: "1"
      }
    },
    result: patientDataSuccess
  },
  {
    request: {
      query: CREATE_CASE,
      variables: {
        _case: {
          facilityId: "1",
          caseTypeId: "1",
          losOverride: false,
          visitNumber: "1",
          mrn: "1",
          arrivedAt: "2023-07-01",
          admittedAt: "2023-03-27",
          hospitalDischargedAt: "2023-07-03",
          firstName: "Cherelle",
          lastName: "Anderson"
        }
      }
    },
    result: createCaseSuccess
  },
  {
    request: {
      query: FACILITIES,
      variables: { perPage: 25, page: 1, search: undefined }
    },
    result: facilitiesSuccess
  },
  {
    request: { query: GET_HOUR_TYPES, variables: {} },
    result: hourTypesSuccess
  },
  {
    request: {
      query: GET_HOUR_PRODUCT_CATEGORIES,
      variables: { perPage: 25, page: 1 }
    },
    result: {
      data: {
        hourProductCategories: {
          count: 50,
          categories: createProductCategories(1, 25)
        }
      }
    }
  },
  {
    request: {
      query: GET_HOUR_PRODUCT_CATEGORIES,
      variables: { perPage: 25, page: 2 }
    },
    result: {
      data: {
        hourProductCategories: {
          count: 50,
          categories: createProductCategories(26, 50)
        }
      }
    }
  },
  {
    request: { query: GET_HOUR_APPROVERS, variables: {} },
    result: hourApproversSuccess
  },
  {
    request: {
      query: CREATE_OR_UPDATE_HOURS_GROUP,
      variables: {
        HourGroupInput: {
          facilityId: ["1"],
          hourTypes: "1",
          hourProductCategories: "Hour Product Category 1",
          hourApprovers: "1",
          notes: "test"
        }
      }
    },
    result: createHourGroupSuccess
  },
  {
    request: {
      query: GET_ACTIVITY_PERMISSIONS,
      variables: { action: "create" }
    },
    result: createActivitiesPermissionsSuccess
  },
  {
    request: {
      query: GET_ACTIVITY_PERMISSIONS,
      variables: { action: "create" }
    },
    result: createActivitiesPermissionsFailure
  }
];
