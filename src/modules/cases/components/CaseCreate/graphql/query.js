import { gql } from "@apollo/client";

export const FACILITIES = gql`
  query currentUserAvailableFacilities(
    $perPage: Int
    $page: Int
    $search: String
  ) {
    currentUserAvailableFacilities(
      perPage: $perPage
      page: $page
      search: $search
    ) {
      userAvailableFacilities {
        id
        name
      }
      count
    }
  }
`;

export const CASE_TYPES_BY_FACILITY = gql`
  query userCaseTypesByFacility(
    $facilityId: Int
    $perPage: Int
    $page: Int
    $search: String
    $excludeGenericCaseTypes: Boolean
  ) {
    userCaseTypesByFacility(
      facilityId: $facilityId
      perPage: $perPage
      page: $page
      search: $search
      excludeGenericCaseTypes: $excludeGenericCaseTypes
    ) {
      userFacilityCaseTypes {
        id
        name
        generic
        isOncology
        isRegulatory
        isAdminInpatient
        isAdminOutpatient
        businessOffering
        serviceLine
        allowsEmptyDischargeDate
        lengthOfStay {
          start
          end
          allowOverride
        }
      }
      count
    }
  }
`;

export const CANCER_TYPES = gql`
  query cancerTypes {
    cancerSites {
      id
      name
    }
  }
`;

export const VISIT_DATA = gql`
  query visit($facilityId: Int!, $visitNumber: String!) {
    visit(facilityId: $facilityId, visitNumber: $visitNumber) {
      id
      firstContact
      arrivedAt
      admittedAt
      surgeryDate
      hospitalDischargedAt
      patient {
        id
        mrn
        firstName
        lastName
      }
    }
  }
`;

export const PATIENT_DATA = gql`
  query patient($facilityId: Int!, $mrn: String!) {
    patient(facilityId: $facilityId, mrn: $mrn) {
      id
      firstName
      middleName
      lastName
      bornOn
    }
  }
`;

export const GET_HOUR_TYPES = gql`
  query hourTypes($search: String, $page: Int, $perPage: Int) {
    hourTypes(search: $search, page: $page, perPage: $perPage) {
      hourTypes {
        id
        name
        clientRequired
        requireProductCategory
        approvalRequired
      }
      page
      perPage
      count
    }
  }
`;

export const GET_HOUR_PRODUCT_CATEGORIES = gql`
  query hourProductCategories($search: String, $page: Int, $perPage: Int) {
    hourProductCategories(search: $search, page: $page, perPage: $perPage) {
      count
      categories {
        name
      }
    }
  }
`;

export const GET_HOUR_APPROVERS = gql`
  query activeInternalUsersByNameAndFacility(
    $page: Int
    $perPage: Int
    $search: String
  ) {
    activeInternalUsersByNameAndFacility(
      page: $page
      perPage: $perPage
      search: $search
    ) {
      users {
        id
        fullName
      }
      count
      page
      perPage
    }
  }
`;

export const GET_ACTIVITY_PERMISSIONS = gql`
  query activityPermissions($action: String!, $activityId: ID) {
    activityPermissions(action: $action, activityId: $activityId) {
      authorized
    }
  }
`;
