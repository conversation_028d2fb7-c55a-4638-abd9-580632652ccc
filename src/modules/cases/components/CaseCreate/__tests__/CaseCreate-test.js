import { act, create } from "react-test-renderer";
import CaseCreate from "modules/cases/components/CaseCreate";
import { decorated } from "utils/tests/decorated";
import { MemoryRouter } from "react-router-dom";
import wait from "waait";

jest.mock("@q-centrix/q-components-react", () => ({
  Topbar: "Topbar",
  SecondaryToolbar: "SecondaryToolbar",
  SelectorButton: "SelectorButton",
  Button: "Button",
  SideNavMenu: "SideNavMenu",
  Spinner: "Spinner"
}));
jest.mock("../Content", () => "Content");
jest.mock("shared/components/Layout", () => "Layout");
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    type: "cases"
  }),
  useNavigate: () => jest.fn()
}));

const CaseCreateWithRouter = enabledFeatureToggles => (
  <MemoryRouter>
    <CaseCreate enabledFeatureToggles={enabledFeatureToggles} />
  </MemoryRouter>
);

describe("CaseCreate", () => {
  function render(enabledFeatureToggles) {
    return create(
      decorated(
        CaseCreateWithRouter,
        {},
        {},
        {
          enabledFeatureToggles
        }
      )
    );
  }
  test("it renders component correctly", async () => {
    const component = render({
      enabledFeatureToggles: ["Hours Redesign"],
      isLoaded: true
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
  test("it renders Spinner when loading", () => {
    const component = render({
      enabledFeatureToggles: [],
      isLoaded: false
    });

    expect(component).toMatchSnapshot();
  });
});
