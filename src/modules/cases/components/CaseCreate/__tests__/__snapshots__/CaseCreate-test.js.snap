// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseCreate it renders Spinner when loading 1`] = `
<Layout
  onBackArrowClick={[Function]}
  stChildren={
    <div
      className="tw-flex tw-gap-5"
    >
      <Button
        bg="success"
        customStyle="tw-flex tw-gap-2.5 tw-items-center"
        form="caseCreateForm"
        type="submit"
      >
        <i
          className="fa-solid fa-check"
        />
        Save
      </Button>
    </div>
  }
  tbChildren={
    <div
      className="tw-flex tw-w-full tw-items-center tw-justify-between"
    >
      <h1
        className="tw-text-2xl tw-font-semibold"
      >
        Create New
      </h1>
      <div
        className="tw-flex tw-items-center tw-gap-5"
      >
        <Hours />
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  }
  tbLabel="PC"
>
  <div
    className="tw-flex tw-h-full tw-justify-center tw-bg-white"
  >
    <Spinner />
  </div>
</Layout>
`;

exports[`CaseCreate it renders component correctly 1`] = `
<Layout
  onBackArrowClick={[Function]}
  stChildren={
    <div
      className="tw-flex tw-gap-5"
    >
      <Button
        bg="success"
        customStyle="tw-flex tw-gap-2.5 tw-items-center"
        form="caseCreateForm"
        type="submit"
      >
        <i
          className="fa-solid fa-check"
        />
        Save
      </Button>
    </div>
  }
  tbChildren={
    <div
      className="tw-flex tw-w-full tw-items-center tw-justify-between"
    >
      <h1
        className="tw-text-2xl tw-font-semibold"
      >
        Create New
      </h1>
      <div
        className="tw-flex tw-items-center tw-gap-5"
      >
        <Hours />
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  }
  tbLabel="PC"
>
  <Content
    createChecked={false}
    duplicateChecked={false}
    handleDisableStartTimer={[Function]}
    handleResetCheckboxes={[Function]}
    handleSaveAndStartTimer={[Function]}
    isHoursEnabled={true}
    saveAndStartTimer={false}
    type="cases"
  />
</Layout>
`;
