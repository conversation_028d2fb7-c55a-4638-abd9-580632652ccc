import { <PERSON><PERSON>, <PERSON><PERSON> } from "@q-centrix/q-components-react";
import Layout from "shared/components/Layout";
import Content from "./Content";
import { ApolloProvider } from "@apollo/client";
import apolloClient from "base/apolloClient";
import Hours from "shared/widgets/Hours";
import LastLogInTile from "shared/widgets/LastLogInTile";
import QPoints from "shared/widgets/QPoints";
import Earnings from "shared/widgets/Earnings";
import { useComponentLogic } from "./hooks";
import { prop } from "ramda";
import ActivitiesFormSecondaryToolbarContent from "./ActivitiesFormSecondaryToolbarContent";

const client = apolloClient("/qapps/graphql");

const formTypes = {
  cases: "caseCreateForm",
  hours: "hourCreateForm",
  activities: "activityCreateForm"
};

const tbChildren = (
  <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
    <h1 className="tw-text-2xl tw-font-semibold">Create New</h1>
    <div className="tw-flex tw-items-center tw-gap-5">
      <Hours />
      <QPoints />
      <Earnings />
      <LastLogInTile />
    </div>
  </div>
);

const CaseCreate = () => {
  const {
    handleBackButton,
    isHoursEnabled,
    featureTogglesLoaded,
    type,
    createChecked,
    duplicateChecked,
    saveAndStartTimer,
    disableStartTimer,
    handleCreateChecked,
    handleDuplicateChecked,
    handleResetCheckboxes,
    handleSaveAndStartTimer,
    handleDisableStartTimer,
    isPhase4Enabled
  } = useComponentLogic();

  const formValue = prop(type, formTypes);

  const stChildren = (
    <div className="tw-flex tw-gap-5">
      {type === "activities" && (
        <ActivitiesFormSecondaryToolbarContent
          createChecked={createChecked}
          duplicateChecked={duplicateChecked}
          handleCreateChecked={handleCreateChecked}
          handleDuplicateChecked={handleDuplicateChecked}
          handleSaveAndStartTimer={handleSaveAndStartTimer}
          form={formValue}
          disableStartTimer={disableStartTimer}
          isPhase4Enabled={isPhase4Enabled}
        />
      )}
      <Button
        bg="success"
        customStyle="tw-flex tw-gap-2.5 tw-items-center"
        type="submit"
        form={formValue}
      >
        <i className="fa-solid fa-check" />
        Save
      </Button>
    </div>
  );

  return (
    <ApolloProvider client={client}>
      <Layout
        tbLabel="PC"
        tbChildren={tbChildren}
        stChildren={stChildren}
        onBackArrowClick={handleBackButton}
      >
        {featureTogglesLoaded ? (
          <Content
            type={type}
            isHoursEnabled={isHoursEnabled}
            createChecked={createChecked}
            duplicateChecked={duplicateChecked}
            handleResetCheckboxes={handleResetCheckboxes}
            saveAndStartTimer={saveAndStartTimer}
            handleSaveAndStartTimer={handleSaveAndStartTimer}
            handleDisableStartTimer={handleDisableStartTimer}
          />
        ) : (
          <div className="tw-flex tw-h-full tw-justify-center tw-bg-white">
            <Spinner />
          </div>
        )}
      </Layout>
    </ApolloProvider>
  );
};

export default CaseCreate;
