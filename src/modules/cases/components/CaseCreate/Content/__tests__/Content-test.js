import "@testing-library/jest-dom";
import { create, act } from "react-test-renderer";
import Content from "..";
import { decoratedApollo } from "utils/tests/decorated";
import apolloMocks from "../../graphql/mocks";
import wait from "waait";

jest.mock("@q-centrix/q-components-react", () => ({
  SelectorButton: "SelectorButton",
  Spinner: "Spinner"
}));
jest.mock("../CasesForm", () => "CasesForm");
jest.mock("../HoursForm", () => "HoursForm");
jest.mock("../ActivitiesForm", () => "ActivitiesForm");
jest.mock("react-router-dom", () => ({
  Link: "Link"
}));

describe("Content", () => {
  function render(props, mocks = apolloMocks) {
    return create(
      decoratedApollo({
        component: Content,
        props,
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with loading spinner", () => {
    const component = render({
      type: "cases",
      isHoursEnabled: true
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with Activities Entry button disabled", async () => {
    const component = render(
      {
        type: "cases",
        isHoursEnabled: true
      },
      []
    );

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with Hours Entry button disabled", async () => {
    const component = render({
      type: "cases",
      isHoursEnabled: false
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with all buttons enabled", async () => {
    const component = render({
      type: "cases",
      isHoursEnabled: true
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
