// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content it renders component correctly with Activities Entry button disabled 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-1"
>
  <div
    className="tw-grow-0 tw-basis-3/12"
  >
    <h2
      className="tw-m-5 tw-text-xl tw-font-semibold tw-text-gray-900"
    >
      What are you creating?
    </h2>
    <Link
      to="/cases/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={true}
        >
          Case
        </SelectorButton>
      </div>
    </Link>
    <Link
      to="/hours/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={false}
        >
          Hour Entry
        </SelectorButton>
      </div>
    </Link>
  </div>
  <div
    className="tw-flex tw-max-h-full tw-grow-0 tw-basis-9/12 tw-overflow-hidden tw-bg-gray-300 tw-p-5"
  >
    <CasesForm
      isActivitiesEnabled={false}
      isHoursEnabled={true}
    />
  </div>
</div>
`;

exports[`Content it renders component correctly with Hours Entry button disabled 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-1"
>
  <div
    className="tw-grow-0 tw-basis-3/12"
  >
    <h2
      className="tw-m-5 tw-text-xl tw-font-semibold tw-text-gray-900"
    >
      What are you creating?
    </h2>
    <Link
      to="/cases/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={true}
        >
          Case
        </SelectorButton>
      </div>
    </Link>
    <Link
      to="/activities/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={false}
        >
          Activity
        </SelectorButton>
      </div>
    </Link>
  </div>
  <div
    className="tw-flex tw-max-h-full tw-grow-0 tw-basis-9/12 tw-overflow-hidden tw-bg-gray-300 tw-p-5"
  >
    <CasesForm
      isActivitiesEnabled={true}
      isHoursEnabled={false}
    />
  </div>
</div>
`;

exports[`Content it renders component correctly with all buttons enabled 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-1"
>
  <div
    className="tw-grow-0 tw-basis-3/12"
  >
    <h2
      className="tw-m-5 tw-text-xl tw-font-semibold tw-text-gray-900"
    >
      What are you creating?
    </h2>
    <Link
      to="/cases/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={true}
        >
          Case
        </SelectorButton>
      </div>
    </Link>
    <Link
      to="/hours/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={false}
        >
          Hour Entry
        </SelectorButton>
      </div>
    </Link>
    <Link
      to="/activities/new"
    >
      <div
        className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5"
      >
        <SelectorButton
          icon={
            <i
              className="fa-regular fa-memo-pad"
            />
          }
          isSelected={false}
        >
          Activity
        </SelectorButton>
      </div>
    </Link>
  </div>
  <div
    className="tw-flex tw-max-h-full tw-grow-0 tw-basis-9/12 tw-overflow-hidden tw-bg-gray-300 tw-p-5"
  >
    <CasesForm
      isActivitiesEnabled={true}
      isHoursEnabled={true}
    />
  </div>
</div>
`;

exports[`Content it renders component with loading spinner 1`] = `
<div
  className="tw-flex tw-h-full tw-items-center tw-justify-center"
>
  <Spinner />
</div>
`;
