/* eslint-disable complexity */
import {
  Button,
  DateInput,
  Switch,
  Input,
  Label
} from "@q-centrix/q-components-react";
import "styles/cases.scss";
import { useComponentLogic } from "./hooks";
import {
  ADMITTED_AT,
  ARRIVED_AT,
  BOR<PERSON>_ON,
  CANCER_SITE_ID,
  CASE_TYPE_ID,
  FACILITY_ID,
  FIRST_CONTACT,
  FIRST_NAME,
  GWTG_ID,
  HOSPITAL_DISCHARGED_AT,
  LAST_NAME,
  LOS_OVERRIDE,
  MIDDLE_NAME,
  MRN,
  SURGERY_DATE,
  VISIT_NUMBER
} from "./hooks/formReducer/constants";
import Select from "shared/components/Select";
import {
  CANCER_TYPES,
  CASE_TYPES_BY_FACILITY,
  FACILITIES
} from "modules/cases/components/CaseCreate/graphql/query";

const CasesForm = () => {
  const {
    lastUsedFacilityAndCaseType,
    handleFillLastUsedFacilityAndCaseType,
    facilitySelectedOption,
    caseTypeConditionallySelectedOption,
    serviceLineIsGwtg,
    losOverrideIsRequired,
    shouldShowCancerTypes,
    caseTypeIsOncology,
    cancerTypeSelectedOption,
    formState,
    handleInputChange,
    handleSwitchChange,
    handleDateInputChangeFor,
    handleSubmit,
    handleInputDropdownChangeFor,
    today,
    minDate,
    caseTypeIsRegulatory,
    caseTypeIsOutPatient,
    caseTypeAllowsEmptyDischargeDate,
    requireAdmissionDate,
    requireArrivalDate,
    shouldAllowLosOverride,
    isPsychiatricVisitAdminDataCaseType
  } = useComponentLogic();

  const { values, feedback } = formState;

  return (
    <form
      id="caseCreateForm"
      autoComplete="off"
      onSubmit={handleSubmit}
      className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
    >
      {lastUsedFacilityAndCaseType && (
        <Button
          type="button"
          customStyle="tw-gap-2.5 tw-self-start"
          outline
          onClick={handleFillLastUsedFacilityAndCaseType}
        >
          <i className="fa-regular fa-circle-check" />
          Fill Last Used Facility And Case Type
        </Button>
      )}
      <Select
        id={FACILITY_ID}
        name={FACILITY_ID}
        label="Facility"
        placeholder="Select Facility"
        feedback={feedback.facilityId}
        onChange={handleInputDropdownChangeFor(FACILITY_ID)}
        initialValue={facilitySelectedOption}
        query={FACILITIES}
        path={["currentUserAvailableFacilities", "userAvailableFacilities"]}
        variables={{ perPage: 25 }}
        isPageable
        isSearchable
        clearable
      />
      {losOverrideIsRequired && shouldAllowLosOverride && (
        <Label className="tw-flex tw-flex-col tw-gap-y-1">
          <span>Override LOS</span>
          <Switch fieldName={LOS_OVERRIDE} onChange={handleSwitchChange} />
        </Label>
      )}
      <Select
        id={CASE_TYPE_ID}
        name={CASE_TYPE_ID}
        label="Case Type"
        placeholder="Select Case Type"
        feedback={feedback.caseTypeId}
        onChange={handleInputDropdownChangeFor(CASE_TYPE_ID)}
        initialValue={caseTypeConditionallySelectedOption}
        disabled={!facilitySelectedOption}
        fields={[
          "id",
          "name",
          "isOncology",
          "isRegulatory",
          "isAdminInpatient",
          "isAdminOutpatient",
          "businessOffering",
          "serviceLine",
          "allowsEmptyDischargeDate",
          "lengthOfStay"
        ]}
        query={CASE_TYPES_BY_FACILITY}
        path={["userCaseTypesByFacility", "userFacilityCaseTypes"]}
        variables={
          values.facilityId && {
            facilityId: parseInt(values.facilityId, 10),
            perPage: 25
          }
        }
        isPageable
        isSearchable
        clearable
      />
      {shouldShowCancerTypes && (
        <Select
          id={CANCER_SITE_ID}
          name={CANCER_SITE_ID}
          label="Cancer Type"
          placeholder="Select Cancer Type"
          feedback={feedback.cancerSiteId}
          onChange={handleInputDropdownChangeFor(CANCER_SITE_ID)}
          initialValue={cancerTypeSelectedOption}
          query={CANCER_TYPES}
          path={["cancerSites"]}
          disabled={!shouldShowCancerTypes}
          clearable
        />
      )}
      {!caseTypeIsOncology && (
        <Input
          label="Visit Number"
          id={VISIT_NUMBER}
          name={VISIT_NUMBER}
          type="text"
          placeholder="Enter Visit Number"
          value={values.visitNumber}
          onChange={handleInputChange}
          feedbackType={feedback.visitNumber?.type}
          feedbackText={feedback.visitNumber?.message}
        />
      )}
      <Input
        label="Medical Record Number"
        id={MRN}
        name={MRN}
        type="text"
        placeholder="Enter MRN"
        value={values.mrn}
        onChange={handleInputChange}
        feedbackType={feedback.mrn?.type}
        feedbackText={feedback.mrn?.message}
      />
      {serviceLineIsGwtg && (
        <Input
          label="GWTG ID (optional)"
          id={GWTG_ID}
          name={GWTG_ID}
          type="text"
          placeholder="Enter GWTG ID"
          value={values.gwtgId}
          onChange={handleInputChange}
          feedbackType={feedback.gwtgId?.type}
          feedbackText={feedback.gwtgId?.message}
        />
      )}
      {caseTypeIsOncology && (
        <DateInput
          label="Date of First Contact"
          id={FIRST_CONTACT}
          name={FIRST_CONTACT}
          value={values.firstContact}
          onChange={handleDateInputChangeFor(FIRST_CONTACT)}
          minDate={minDate}
          maxDate={today}
          feedbackType={feedback.firstContact?.type}
          feedbackText={feedback.firstContact?.message}
        />
      )}
      {requireArrivalDate && (
        <DateInput
          label="Arrival Date"
          id={ARRIVED_AT}
          name={ARRIVED_AT}
          value={values.arrivedAt}
          onChange={handleDateInputChangeFor(ARRIVED_AT)}
          minDate={minDate}
          maxDate={today}
          feedbackType={feedback.arrivedAt?.type}
          feedbackText={feedback.arrivedAt?.message}
        />
      )}
      {requireAdmissionDate && (
        <DateInput
          label={caseTypeIsOutPatient ? "Encounter Date" : "Admission Date"}
          id={ADMITTED_AT}
          name={ADMITTED_AT}
          value={values.admittedAt}
          onChange={handleDateInputChangeFor(ADMITTED_AT)}
          minDate={minDate}
          maxDate={today}
          feedbackType={feedback.admittedAt?.type}
          feedbackText={feedback.admittedAt?.message}
        />
      )}
      {!caseTypeIsOncology &&
        !caseTypeIsRegulatory &&
        !isPsychiatricVisitAdminDataCaseType && (
          <DateInput
            label="Procedure Date (optional)"
            id={SURGERY_DATE}
            name={SURGERY_DATE}
            value={values.surgeryDate}
            onChange={handleDateInputChangeFor(SURGERY_DATE)}
            minDate={minDate}
            maxDate={today}
            feedbackType={feedback.surgeryDate?.type}
            feedbackText={feedback.surgeryDate?.message}
          />
        )}
      {!(caseTypeIsOutPatient && caseTypeAllowsEmptyDischargeDate) && (
        <DateInput
          label="Discharge Date"
          id={HOSPITAL_DISCHARGED_AT}
          name={HOSPITAL_DISCHARGED_AT}
          value={values.hospitalDischargedAt}
          onChange={handleDateInputChangeFor(HOSPITAL_DISCHARGED_AT)}
          minDate={minDate}
          maxDate={today}
          feedbackType={feedback.hospitalDischargedAt?.type}
          feedbackText={feedback.hospitalDischargedAt?.message}
        />
      )}
      <Input
        label="Patient First Name (optional)"
        id={FIRST_NAME}
        name={FIRST_NAME}
        type="text"
        placeholder="Enter Patient First Name"
        value={values.firstName}
        onChange={handleInputChange}
        feedbackType={feedback.firstName?.type}
        feedbackText={feedback.firstName?.message}
      />
      <Input
        label="Patient Middle Name (optional)"
        id={MIDDLE_NAME}
        name={MIDDLE_NAME}
        type="text"
        placeholder="Enter Patient Middle Name"
        value={values.middleName}
        onChange={handleInputChange}
        feedbackType={feedback.middleName?.type}
        feedbackText={feedback.middleName?.message}
      />
      <Input
        label="Patient Last Name (optional)"
        id={LAST_NAME}
        name={LAST_NAME}
        type="text"
        placeholder="Enter Patient Last Name"
        value={values.lastName}
        onChange={handleInputChange}
        feedbackType={feedback.lastName?.type}
        feedbackText={feedback.lastName?.message}
      />
      <DateInput
        label="Patient DOB (optional)"
        id={BORN_ON}
        name={BORN_ON}
        value={values.bornOn}
        onChange={handleDateInputChangeFor(BORN_ON)}
        minDate={minDate}
        maxDate={today}
        feedbackType={feedback.bornOn?.type}
        feedbackText={feedback.bornOn?.message}
      />
    </form>
  );
};

export default CasesForm;
