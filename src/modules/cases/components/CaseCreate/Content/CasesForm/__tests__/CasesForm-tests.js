import "@testing-library/jest-dom";
import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import CasesForm from "modules/cases/components/CaseCreate/Content/CasesForm";
import mocks from "modules/cases/components/CaseCreate/graphql/mocks";
import { Select } from "@q-centrix/q-components-react";
import { MemoryRouter } from "react-router-dom";

jest.mock("@q-centrix/q-components-react", () => ({
  Select: "Select",
  Button: "Button",
  DateInput: "DateInput",
  Switch: "Switch",
  Input: "Input",
  Label: "Label",
  useToast: () => ({ toast: jest.fn() })
}));

jest.useFakeTimers().setSystemTime(new Date("2022-01-01T00:00:00.000Z"));

const ContentWithRouter = props => (
  <MemoryRouter>
    <CasesForm {...props} />
  </MemoryRouter>
);

describe("CasesForm", () => {
  function render() {
    return create(
      decoratedApollo({
        component: ContentWithRouter,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with Cancer Type InputDropdown and Date of First Contact DateInput when Case Type isOncology is true", () => {
    const component = render();
    const instance = component.root;
    const Selects = instance.findAllByType(Select);
    const [FacilityInputDropdown, CaseTypeInputDropdown] = Selects;

    act(() =>
      FacilityInputDropdown.props.onChange({
        value: "1",
        label: "Large Medical Center"
      })
    );

    act(() =>
      CaseTypeInputDropdown.props.onChange({
        isOncology: true,
        label: "Case Type 3",
        value: "3"
      })
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component with out Cancer Type InputDropdown and Date of First Contact DateInput when Case Type isOncology is false", () => {
    const component = render();
    const instance = component.root;
    const Selects = instance.findAllByType(Select);
    const [FacilityInputDropdown, CaseTypeInputDropdown] = Selects;

    act(() =>
      FacilityInputDropdown.props.onChange({
        value: "1",
        label: "Large Medical Center"
      })
    );

    act(() =>
      CaseTypeInputDropdown.props.onChange({
        isOncology: false,
        label: "Case Type 1",
        value: "1"
      })
    );

    expect(component).toMatchSnapshot();
  });
});
