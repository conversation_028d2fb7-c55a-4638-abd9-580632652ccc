// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CasesForm it renders component correctly 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
  id="caseCreateForm"
  onSubmit={[Function]}
>
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="facilityId"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Facility"
    name="facilityId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Facility"
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Select
    cacheKey="default"
    clearable={true}
    disabled={true}
    iconClass="fa-solid fa-chevron-down"
    id="caseTypeId"
    isLoading={false}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Case Type"
    name="caseTypeId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Case Type"
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Input
    id="visitNumber"
    label="Visit Number"
    name="visitNumber"
    onChange={[Function]}
    placeholder="Enter Visit Number"
    type="text"
    value=""
  />
  <Input
    id="mrn"
    label="Medical Record Number"
    name="mrn"
    onChange={[Function]}
    placeholder="Enter MRN"
    type="text"
    value=""
  />
  <DateInput
    id="admittedAt"
    label="Admission Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="admittedAt"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="surgeryDate"
    label="Procedure Date (optional)"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="surgeryDate"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="hospitalDischargedAt"
    label="Discharge Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="hospitalDischargedAt"
    onChange={[Function]}
    value={null}
  />
  <Input
    id="firstName"
    label="Patient First Name (optional)"
    name="firstName"
    onChange={[Function]}
    placeholder="Enter Patient First Name"
    type="text"
    value=""
  />
  <Input
    id="middleName"
    label="Patient Middle Name (optional)"
    name="middleName"
    onChange={[Function]}
    placeholder="Enter Patient Middle Name"
    type="text"
    value=""
  />
  <Input
    id="lastName"
    label="Patient Last Name (optional)"
    name="lastName"
    onChange={[Function]}
    placeholder="Enter Patient Last Name"
    type="text"
    value=""
  />
  <DateInput
    id="bornOn"
    label="Patient DOB (optional)"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="bornOn"
    onChange={[Function]}
    value={null}
  />
</form>
`;

exports[`CasesForm it renders component with Cancer Type InputDropdown and Date of First Contact DateInput when Case Type isOncology is true 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
  id="caseCreateForm"
  onSubmit={[Function]}
>
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="facilityId"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Facility"
    name="facilityId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Facility"
    smallPagination={false}
    totalCount={0}
    value={
      Object {
        "label": "Large Medical Center",
        "value": "1",
      }
    }
  />
  <Select
    cacheKey="default"
    clearable={true}
    disabled={false}
    iconClass="fa-solid fa-chevron-down"
    id="caseTypeId"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Case Type"
    name="caseTypeId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Case Type"
    smallPagination={false}
    totalCount={0}
    value={
      Object {
        "isOncology": true,
        "label": "Case Type 3",
        "value": "3",
      }
    }
  />
  <Select
    cacheKey="default"
    clearable={true}
    disabled={false}
    iconClass="fa-solid fa-chevron-down"
    id="cancerSiteId"
    isLoading={true}
    isMulti={false}
    isPageable={false}
    isSearchable={false}
    label="Cancer Type"
    name="cancerSiteId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Cancer Type"
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Input
    id="mrn"
    label="Medical Record Number"
    name="mrn"
    onChange={[Function]}
    placeholder="Enter MRN"
    type="text"
    value=""
  />
  <DateInput
    id="firstContact"
    label="Date of First Contact"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="firstContact"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="admittedAt"
    label="Admission Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="admittedAt"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="hospitalDischargedAt"
    label="Discharge Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="hospitalDischargedAt"
    onChange={[Function]}
    value={null}
  />
  <Input
    id="firstName"
    label="Patient First Name (optional)"
    name="firstName"
    onChange={[Function]}
    placeholder="Enter Patient First Name"
    type="text"
    value=""
  />
  <Input
    id="middleName"
    label="Patient Middle Name (optional)"
    name="middleName"
    onChange={[Function]}
    placeholder="Enter Patient Middle Name"
    type="text"
    value=""
  />
  <Input
    id="lastName"
    label="Patient Last Name (optional)"
    name="lastName"
    onChange={[Function]}
    placeholder="Enter Patient Last Name"
    type="text"
    value=""
  />
  <DateInput
    id="bornOn"
    label="Patient DOB (optional)"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="bornOn"
    onChange={[Function]}
    value={null}
  />
</form>
`;

exports[`CasesForm it renders component with out Cancer Type InputDropdown and Date of First Contact DateInput when Case Type isOncology is false 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
  id="caseCreateForm"
  onSubmit={[Function]}
>
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="facilityId"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Facility"
    name="facilityId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Facility"
    smallPagination={false}
    totalCount={0}
    value={
      Object {
        "label": "Large Medical Center",
        "value": "1",
      }
    }
  />
  <Select
    cacheKey="default"
    clearable={true}
    disabled={false}
    iconClass="fa-solid fa-chevron-down"
    id="caseTypeId"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Case Type"
    name="caseTypeId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Case Type"
    smallPagination={false}
    totalCount={0}
    value={
      Object {
        "isOncology": false,
        "label": "Case Type 1",
        "value": "1",
      }
    }
  />
  <Input
    id="visitNumber"
    label="Visit Number"
    name="visitNumber"
    onChange={[Function]}
    placeholder="Enter Visit Number"
    type="text"
    value=""
  />
  <Input
    id="mrn"
    label="Medical Record Number"
    name="mrn"
    onChange={[Function]}
    placeholder="Enter MRN"
    type="text"
    value=""
  />
  <DateInput
    id="admittedAt"
    label="Admission Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="admittedAt"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="surgeryDate"
    label="Procedure Date (optional)"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="surgeryDate"
    onChange={[Function]}
    value={null}
  />
  <DateInput
    id="hospitalDischargedAt"
    label="Discharge Date"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="hospitalDischargedAt"
    onChange={[Function]}
    value={null}
  />
  <Input
    id="firstName"
    label="Patient First Name (optional)"
    name="firstName"
    onChange={[Function]}
    placeholder="Enter Patient First Name"
    type="text"
    value=""
  />
  <Input
    id="middleName"
    label="Patient Middle Name (optional)"
    name="middleName"
    onChange={[Function]}
    placeholder="Enter Patient Middle Name"
    type="text"
    value=""
  />
  <Input
    id="lastName"
    label="Patient Last Name (optional)"
    name="lastName"
    onChange={[Function]}
    placeholder="Enter Patient Last Name"
    type="text"
    value=""
  />
  <DateInput
    id="bornOn"
    label="Patient DOB (optional)"
    maxDate={2022-01-01T00:00:00.000Z}
    minDate={1900-01-01T00:00:00.000Z}
    name="bornOn"
    onChange={[Function]}
    value={null}
  />
</form>
`;
