/* eslint-disable max-statements */
/* eslint-disable complexity */
import {
  parseVisitData,
  parsePatientData,
  generateSuccessFeedback,
  clearErrorsFromFeedback,
  generateAutoFilledFields,
  generateFieldsToReset,
  patientDataProps
} from "./helpers";
import {
  FACILITY_ID,
  CASE_TYPE_ID,
  LOS_OVERRIDE,
  CANCER_SITE_ID,
  VISIT_NUMBER,
  MRN,
  GWTG_ID,
  FIRST_CONTACT,
  ARRIVED_AT,
  ADMITTED_AT,
  SURGERY_DATE,
  HOSPITAL_DISCHARGED_AT,
  FIRST_NAME,
  MIDDLE_NAME,
  LAST_NAME,
  BORN_ON,
  ACTION_TYPES,
  FEEDBACK_TYPES
} from "./constants";
import { isNullOrEmpty } from "utils/fp";
import { pick, where } from "ramda";

export const INITIAL_FORM_STATE = {
  values: {
    [FACILITY_ID]: null,
    [CASE_TYPE_ID]: null,
    [LOS_OVERRIDE]: false,
    [CANCER_SITE_ID]: null,
    [VISIT_NUMBER]: "",
    [MRN]: "",
    [GWTG_ID]: "",
    [FIRST_CONTACT]: null,
    [ARRIVED_AT]: null,
    [ADMITTED_AT]: null,
    [SURGERY_DATE]: null,
    [HOSPITAL_DISCHARGED_AT]: null,
    [FIRST_NAME]: "",
    [MIDDLE_NAME]: "",
    [LAST_NAME]: "",
    [BORN_ON]: null
  },
  feedback: {
    [FACILITY_ID]: null,
    [CASE_TYPE_ID]: null,
    [LOS_OVERRIDE]: null,
    [CANCER_SITE_ID]: null,
    [VISIT_NUMBER]: null,
    [MRN]: null,
    [GWTG_ID]: null,
    [FIRST_CONTACT]: null,
    [ARRIVED_AT]: null,
    [ADMITTED_AT]: null,
    [SURGERY_DATE]: null,
    [HOSPITAL_DISCHARGED_AT]: null,
    [FIRST_NAME]: null,
    [MIDDLE_NAME]: null,
    [LAST_NAME]: null,
    [BORN_ON]: null
  },
  isAutoFilled: {
    [FACILITY_ID]: false,
    [CASE_TYPE_ID]: false,
    [LOS_OVERRIDE]: false,
    [CANCER_SITE_ID]: false,
    [VISIT_NUMBER]: false,
    [MRN]: false,
    [GWTG_ID]: false,
    [FIRST_CONTACT]: false,
    [ARRIVED_AT]: false,
    [ADMITTED_AT]: false,
    [SURGERY_DATE]: false,
    [HOSPITAL_DISCHARGED_AT]: false,
    [FIRST_NAME]: false,
    [MIDDLE_NAME]: false,
    [LAST_NAME]: false,
    [BORN_ON]: false
  }
};

export const formReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.VALUE_CHANGE: {
      const { name, value } = action.payload;
      const { valuesToReset, feedbackToReset, isAutoFieldToReset } =
        generateFieldsToReset(name, state.isAutoFilled);

      return {
        values: {
          ...state.values,
          ...valuesToReset,
          [name]: value ?? null
        },
        feedback: {
          ...state.feedback,
          ...feedbackToReset,
          [name]: null
        },
        isAutoFilled: {
          ...state.isAutoFilled,
          ...isAutoFieldToReset,
          [name]: false
        }
      };
    }
    case ACTION_TYPES.FEEDBACK_CHANGE: {
      const { name, feedback } = action.payload;

      return {
        ...state,
        feedback: {
          ...state.feedback,
          [name]: feedback ?? null
        }
      };
    }
    case ACTION_TYPES.UPDATE_VALUES_WITH_VISIT_DATA: {
      const { visitData } = action.payload;
      const valuesToPopulate = parseVisitData(visitData);
      const successFeedback = generateSuccessFeedback(valuesToPopulate);
      const autoFilledFields = generateAutoFilledFields(valuesToPopulate);

      if (visitData?.patient?.mrn) {
        valuesToPopulate.mrn = visitData.patient.mrn;
        autoFilledFields.mrn = true;
      }

      const visitNumberFeedbackMessage =
        visitData?.patient?.firstName && visitData?.patient?.lastName
          ? `Record found for <b class="tw-font-semibold">${visitData.patient.firstName} ${visitData.patient.lastName}</b>`
          : "Record found";

      return {
        values: {
          ...state.values,
          ...valuesToPopulate
        },
        feedback: {
          ...state.feedback,
          ...successFeedback,
          visitNumber: {
            type: FEEDBACK_TYPES.SUCCESS,
            message: visitNumberFeedbackMessage
          }
        },
        isAutoFilled: {
          ...state.isAutoFilled,
          ...autoFilledFields
        }
      };
    }
    case ACTION_TYPES.UPDATE_VALUES_WITH_PATIENT_DATA: {
      const valuesToPopulate = parsePatientData(action.payload.patientData);
      const successFeedback = generateSuccessFeedback(valuesToPopulate);
      const autoFilledFields = generateAutoFilledFields(valuesToPopulate);
      const showClearPatient = where({
        [VISIT_NUMBER]: isNullOrEmpty,
        [ARRIVED_AT]: isNullOrEmpty,
        [ADMITTED_AT]: isNullOrEmpty,
        [HOSPITAL_DISCHARGED_AT]: isNullOrEmpty
      })(state.values);
      const clearPatientElement = showClearPatient
        ? ' <button id="clear-patient" type="button">[Clear Patient]</button>'
        : "";

      const patientDataFeedbackMessage =
        valuesToPopulate.firstName && valuesToPopulate.lastName
          ? `Record found for <b class="tw-font-semibold">${valuesToPopulate.firstName} ${valuesToPopulate.lastName}</b>${clearPatientElement}`
          : "Record found";

      return {
        values: {
          ...state.values,
          ...valuesToPopulate
        },
        feedback: {
          ...state.feedback,
          ...successFeedback,
          mrn: {
            type: FEEDBACK_TYPES.SUCCESS,
            message: patientDataFeedbackMessage
          }
        },
        isAutoFilled: {
          ...state.isAutoFilled,
          ...autoFilledFields
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK: {
      const updatedFeedback = clearErrorsFromFeedback(state.feedback);

      return {
        ...state,
        feedback: {
          ...updatedFeedback
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_FORM_STATE: {
      return {
        values: { ...INITIAL_FORM_STATE.values },
        feedback: { ...INITIAL_FORM_STATE.feedback },
        isAutoFilled: { ...INITIAL_FORM_STATE.isAutoFilled }
      };
    }
    case ACTION_TYPES.RESET_PATIENT_DATA: {
      const fieldsToReset = [MRN, ...patientDataProps];

      return {
        values: {
          ...state.values,
          ...pick(fieldsToReset, INITIAL_FORM_STATE.values)
        },
        feedback: {
          ...state.feedback,
          ...pick(fieldsToReset, INITIAL_FORM_STATE.feedback)
        },
        isAutoFilled: {
          ...state.isAutoFilled,
          ...pick(fieldsToReset, INITIAL_FORM_STATE.isAutoFilled)
        }
      };
    }
    default:
      return state;
  }
};

export const changeValue = (fieldName, value) => ({
  type: ACTION_TYPES.VALUE_CHANGE,
  payload: { name: fieldName, value }
});

export const populateWithVisitData = visitData => ({
  type: ACTION_TYPES.UPDATE_VALUES_WITH_VISIT_DATA,
  payload: { visitData }
});

export const populateWithPatientData = patientData => ({
  type: ACTION_TYPES.UPDATE_VALUES_WITH_PATIENT_DATA,
  payload: { patientData }
});

export const changeFeedback = (fieldName, feedback) => ({
  type: ACTION_TYPES.FEEDBACK_CHANGE,
  payload: {
    name: fieldName,
    feedback
  }
});

export const resetValue = fieldName =>
  changeValue(fieldName, INITIAL_FORM_STATE.values[fieldName]);

export const resetFeedback = fieldName => changeFeedback(fieldName, null);

export const changeFeedbackToNeutral = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.NEUTRAL
  });

export const changeFeedbackToError = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.ERROR
  });

export const resetAllErrorFeedback = () => ({
  type: ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK
});

export const resetForm = () => ({
  type: ACTION_TYPES.RESET_ALL_FORM_STATE
});

export const clearPatientData = () => ({
  type: ACTION_TYPES.RESET_PATIENT_DATA
});
