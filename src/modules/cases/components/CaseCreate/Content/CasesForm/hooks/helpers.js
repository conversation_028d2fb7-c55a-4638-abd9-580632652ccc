/* eslint-disable complexity */
/* eslint-disable max-statements */
import {
  pipe,
  is,
  trim,
  identity,
  cond,
  filter,
  reduce,
  T,
  toPairs,
  either,
  equals,
  ifElse,
  both,
  always,
  assoc,
  __,
  and,
  has,
  isNil,
  includes
} from "ramda";
import { isNotNullOrEmpty } from "utils/fp";
import {
  ARRIVED_AT,
  BORN_ON,
  FIRST_NAME,
  HOSPITAL_DISCHARGED_AT,
  LAST_NAME,
  MIDDLE_NAME,
  SURGERY_DATE,
  VISIT_NUMBER
} from "./formReducer/constants";
import { getDayRange } from "utils/getDayRange";

export const checkIfLosOverrideIsRequired = ({
  losStart = null,
  losEnd = null,
  arrivedAt,
  hospitalDischargedAt
}) => {
  if (
    isNil(losStart) ||
    isNil(losEnd) ||
    isNil(arrivedAt) ||
    isNil(hospitalDischargedAt)
  ) {
    return false;
  }

  const arrivalAndDischargeDayRange = getDayRange({
    startDate: arrivedAt,
    endDate: hospitalDischargedAt,
    inclusive: false
  });

  const isValidLOS =
    arrivalAndDischargeDayRange >= losStart &&
    arrivalAndDischargeDayRange <= losEnd;

  return !isValidLOS;
};

export const validateForm = ({
  values,
  shouldShowCancerTypes,
  caseTypeIsOncology,
  caseTypeAllowsEmptyDischargeDate,
  requireAdmissionDate,
  requireArrivalDate
}) => {
  const formIssues = [];

  if (!values.facilityId) {
    formIssues.push({
      facilityId: "Facility required"
    });
  }

  if (!values.caseTypeId) {
    formIssues.push({
      caseTypeId: "Case Type required"
    });
  }

  if (shouldShowCancerTypes && !values.cancerSiteId) {
    formIssues.push({
      cancerSiteId: "Cancer Type required"
    });
  }

  if (caseTypeIsOncology && !values.firstContact) {
    formIssues.push({
      firstContact: "Date Of First Contact required"
    });
  }

  if (!caseTypeIsOncology && !values.visitNumber) {
    formIssues.push({
      visitNumber: "Visit Number required"
    });
  }

  if (!values.mrn) {
    formIssues.push({
      mrn: "Medical Record Number required"
    });
  }

  if (requireAdmissionDate && !values.admittedAt) {
    formIssues.push({
      admittedAt: "Required"
    });
  }

  if (requireArrivalDate && !values.arrivedAt) {
    formIssues.push({
      arrivedAt: "Arrival Date required"
    });
  }

  if (!caseTypeAllowsEmptyDischargeDate && !values.hospitalDischargedAt) {
    formIssues.push({
      hospitalDischargedAt: "Discharge Date required"
    });
  }

  if (
    values.hospitalDischargedAt &&
    values.arrivedAt &&
    values.hospitalDischargedAt < values.arrivedAt
  ) {
    formIssues.push({
      hospitalDischargedAt: "Discharge Date cannot be before the Arrival Date"
    });
  }

  return formIssues;
};

export const generateCaseInput = (
  formValues,
  {
    caseTypeIsOncology,
    caseTypeIsInPatient,
    caseTypeIsOutPatient,
    caseTypeAllowsEmptyDischargeDate
  }
) =>
  pipe(
    toPairs,
    filter(([key, value]) =>
      cond([
        [
          either(equals(ARRIVED_AT), equals(SURGERY_DATE)),
          () =>
            !caseTypeIsInPatient &&
            !caseTypeIsOutPatient &&
            isNotNullOrEmpty(value)
        ],
        [
          equals(HOSPITAL_DISCHARGED_AT),
          () =>
            (!caseTypeIsOutPatient || !caseTypeAllowsEmptyDischargeDate) &&
            isNotNullOrEmpty(value)
        ],
        // we allow visitNumber to potentially be an empty string due to backend not allowing null or undefined
        // we also allow first name, middle name and last name to empty strings because users want to be able to remove these
        [
          includes(__, [
            VISIT_NUMBER,
            FIRST_NAME,
            MIDDLE_NAME,
            LAST_NAME,
            BORN_ON
          ]),
          T
        ],
        [T, () => isNotNullOrEmpty(value)]
      ])(key)
    ),
    reduce(
      (acc, [key, value]) =>
        pipe(
          ifElse(
            // force visitNumber to be an empty string for oncology cases
            both(always(caseTypeIsOncology), equals(VISIT_NUMBER)),
            always(""),
            () =>
              cond([
                [
                  is(Date),
                  date =>
                    date.toLocaleDateString("en-US", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric"
                    })
                ],
                [
                  and(is(Object), has("value")),
                  fieldObject => fieldObject.value
                ],
                // we replace empty strings with nulls so that we can set these fields to null in the backend
                [equals(""), always(null)],
                [is(String), trim],
                [T, identity]
              ])(value)
          ),
          assoc(key, __, acc)
        )(key),
      {}
    )
  )(formValues);
