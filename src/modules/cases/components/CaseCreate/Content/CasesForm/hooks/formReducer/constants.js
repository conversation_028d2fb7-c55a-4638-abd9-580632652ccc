export const FACILITY_ID = "facilityId";
export const CASE_TYPE_ID = "caseTypeId";
export const LOS_OVERRIDE = "losOverride";
export const CANCER_SITE_ID = "cancerSiteId";
export const VISIT_NUMBER = "visitNumber";
export const MRN = "mrn";
export const GWTG_ID = "gwtgId";
export const FIRST_CONTACT = "firstContact";
export const ARRIVED_AT = "arrivedAt";
export const ADMITTED_AT = "admittedAt";
export const SURGERY_DATE = "surgeryDate";
export const HOSPITAL_DISCHARGED_AT = "hospitalDischargedAt";
export const FIRST_NAME = "firstName";
export const MIDDLE_NAME = "middleName";
export const LAST_NAME = "lastName";
export const BORN_ON = "bornOn";

export const SERVICE_LINE_GWTG = "GWTG";

export const BUSINESS_OFFERING_CORE_MEASURES = "core_measures";
export const BUSINESS_OFFERING_REGISTRY = "registry";

export const ACTION_TYPES = {
  VALUE_CHANGE: "VALUE_CHANGE",
  UPDATE_VALUES_WITH_VISIT_DATA: "UPDATE_VALUES_WITH_VISIT_DATA",
  UPDATE_VALUES_WITH_PATIENT_DATA: "UPDATE_VALUES_WITH_PATIENT_DATA",
  FEEDBACK_CHANGE: "FEEDBACK_CHANGE",
  RESET_ALL_FORM_STATE: "RESET_FORM_STATE",
  RESET_ALL_ERROR_FEEDBACK: "RESET_ALL_ERROR_FEEDBACK"
};

export const FEEDBACK_TYPES = {
  ERROR: "error",
  SUCCESS: "success",
  NEUTRAL: "neutral"
};
