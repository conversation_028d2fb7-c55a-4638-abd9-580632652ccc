/* eslint-disable complexity */
import {
  pick,
  reject,
  map,
  when,
  pipe,
  isNil,
  mapObjIndexed,
  always,
  equals
} from "ramda";
import {
  FACILITY_ID,
  CASE_TYPE_ID,
  LOS_OVERRIDE,
  CANCER_SITE_ID,
  VISIT_NUMBER,
  MRN,
  GWTG_ID,
  FIRST_CONTACT,
  ARRIVED_AT,
  ADMITTED_AT,
  SURGERY_DATE,
  HOSPITAL_DISCHARGED_AT,
  FIRST_NAME,
  MIDDLE_NAME,
  LAST_NAME,
  BORN_ON,
  FEEDBACK_TYPES
} from "./constants";
import { isISOString } from "utils/isISOString";
import { INITIAL_FORM_STATE } from ".";

const visitDataProps = [
  FIRST_CONTACT,
  ARRIVED_AT,
  ADMITTED_AT,
  SURGERY_DATE,
  HOSPITAL_DISCHARGED_AT
];

export const patientDataProps = [FIRST_NAME, MIDDLE_NAME, LAST_NAME, BORN_ON];

export const parseData = allowedProps =>
  pipe(
    pick(allowedProps),
    reject(isNil),
    map(when(isISOString, value => new Date(value)))
  );

export const parseVisitData = parseData(visitDataProps);

export const parsePatientData = parseData(patientDataProps);

// to have feedback messages match form label naming
const feedBackPropMap = {
  [FIRST_CONTACT]: "Date Of First Contact",
  [ARRIVED_AT]: "Arrival Date",
  [ADMITTED_AT]: "Admission Date",
  [SURGERY_DATE]: "Procedure Date",
  [HOSPITAL_DISCHARGED_AT]: "Discharge Date",
  [FIRST_NAME]: "Patient First Name",
  [MIDDLE_NAME]: "Patient Middle Name",
  [LAST_NAME]: "Patient Last Name",
  [BORN_ON]: "Patient DOB"
};

export const generateSuccessFeedback = mapObjIndexed((_value, currentKey) => ({
  type: FEEDBACK_TYPES.SUCCESS,
  message: `${feedBackPropMap[currentKey]} found`
}));

export const generateAutoFilledFields = map(() => true);

export const clearErrorsFromFeedback = map(
  when(value => value?.type === FEEDBACK_TYPES.ERROR, always(null))
);

const mrnAutoFillDependents = [...patientDataProps];

const visitNumberAutoFillDependents = [...visitDataProps];

const facilityIdAutoFillDependents = [
  CASE_TYPE_ID,
  VISIT_NUMBER,
  MRN,
  ...mrnAutoFillDependents,
  ...visitNumberAutoFillDependents
];

const resetAllForFacilityChange = {
  [FACILITY_ID]: false,
  [CASE_TYPE_ID]: true,
  [LOS_OVERRIDE]: true,
  [CANCER_SITE_ID]: true,
  [VISIT_NUMBER]: true,
  [MRN]: true,
  [GWTG_ID]: true,
  [FIRST_CONTACT]: true,
  [ARRIVED_AT]: true,
  [ADMITTED_AT]: true,
  [SURGERY_DATE]: true,
  [HOSPITAL_DISCHARGED_AT]: true,
  [FIRST_NAME]: true,
  [MIDDLE_NAME]: true,
  [LAST_NAME]: true,
  [BORN_ON]: true
};

const getValuesToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    reject(equals(false)),
    mapObjIndexed((_val, name) => INITIAL_FORM_STATE.values[name])
  );

const getFeedbackToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    map(() => null)
  );

const getIsAutoFilledToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    reject(equals(false)),
    map(() => false)
  );

const getResetData = (isAutoFilled, dependents) => {
  const valuesToReset = getValuesToReset(dependents)(isAutoFilled);
  const feedbackToReset = getFeedbackToReset(dependents)(isAutoFilled);
  const isAutoFieldToReset = getIsAutoFilledToReset(dependents)(isAutoFilled);

  return { valuesToReset, feedbackToReset, isAutoFieldToReset };
};

export const generateFieldsToReset = (name, isAutoFilled) => {
  switch (name) {
    case FACILITY_ID:
      return getResetData(
        resetAllForFacilityChange,
        facilityIdAutoFillDependents
      );

    case VISIT_NUMBER:
      return getResetData(isAutoFilled, visitNumberAutoFillDependents);

    case MRN:
      return getResetData(isAutoFilled, mrnAutoFillDependents);

    default:
      return {};
  }
};
