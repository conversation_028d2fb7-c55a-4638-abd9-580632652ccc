/* eslint-disable complexity */
import { Spinner, SelectorButton } from "@q-centrix/q-components-react";
import CasesForm from "./CasesForm";
import HoursForm from "./HoursForm";
import ActivitiesForm from "./ActivitiesForm";
import "styles/cases.scss";
import { equals, prop } from "ramda";
import { Link } from "react-router-dom";
import { isNullOrEmpty } from "utils/fp";
import redirectToRoot from "utils/redirectToRoot";
import { useComponentLogic } from "./hooks";

const formTypes = {
  cases: CasesForm,
  hours: HoursForm,
  activities: ActivitiesForm
};

const Content = props => {
  const {
    isHoursEnabled,
    type,
    createChecked,
    duplicateChecked,
    handleResetCheckboxes,
    saveAndStartTimer,
    handleSaveAndStartTimer,
    handleDisableStartTimer
  } = props;
  const { isActivitiesLoading, isActivitiesEnabled } = useComponentLogic();

  const Form = prop(type, formTypes);

  if (isNullOrEmpty(Form)) return redirectToRoot();

  if (isActivitiesLoading)
    return (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center">
        <Spinner />
      </div>
    );

  return (
    <div className="tw-flex tw-h-full tw-flex-1">
      <div className="tw-grow-0 tw-basis-3/12">
        <h2 className="tw-m-5 tw-text-xl tw-font-semibold tw-text-gray-900">
          What are you creating?
        </h2>
        <Link to="/cases/new">
          <div className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5">
            <SelectorButton
              isSelected={equals("cases")(type)}
              icon={<i className="fa-regular fa-memo-pad" />}
            >
              Case
            </SelectorButton>
          </div>
        </Link>
        {isHoursEnabled && (
          <Link to="/hours/new">
            <div className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5">
              <SelectorButton
                isSelected={equals("hours")(type)}
                icon={<i className="fa-regular fa-memo-pad" />}
              >
                Hour Entry
              </SelectorButton>
            </div>
          </Link>
        )}
        {isActivitiesEnabled && (
          <Link to="/activities/new">
            <div className="tw-m-5 tw-flex tw-flex-col tw-gap-2.5">
              <SelectorButton
                isSelected={equals("activities")(type)}
                icon={<i className="fa-regular fa-memo-pad" />}
              >
                Activity
              </SelectorButton>
            </div>
          </Link>
        )}
      </div>
      <div className="tw-flex tw-max-h-full tw-grow-0 tw-basis-9/12 tw-overflow-hidden tw-bg-gray-300 tw-p-5">
        <Form
          isHoursEnabled={isHoursEnabled}
          isActivitiesEnabled={isActivitiesEnabled}
          createChecked={createChecked}
          duplicateChecked={duplicateChecked}
          handleResetCheckboxes={handleResetCheckboxes}
          saveAndStartTimer={saveAndStartTimer}
          handleSaveAndStartTimer={handleSaveAndStartTimer}
          handleDisableStartTimer={handleDisableStartTimer}
        />
      </div>
    </div>
  );
};

export default Content;
