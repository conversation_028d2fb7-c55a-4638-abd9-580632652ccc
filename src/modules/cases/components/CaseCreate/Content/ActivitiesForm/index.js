import Select from "shared/components/Select";
import {
  FACILITIES,
  GET_HOUR_PRODUCT_CATEGORIES
} from "modules/cases/components/CaseCreate/graphql/query";
import { DateInput, TextArea } from "@q-centrix/q-components-react";
import {
  ACTIVITY_TYPE,
  FACILITY_ID,
  ACTIVITY_ASSIGNEE,
  ACTIVITY_DEADLINE,
  ACTIVITY_PRODUCT_CATEGORY,
  ACTIVITY_STEP_TYPE,
  ACTIVITY_COMMENT
} from "./hooks/formReducer/constants";
import redirectToRoot from "utils/redirectToRoot";
import { useComponentLogic } from "./hooks";
import {
  GET_ACTIVITY_TYPES,
  GET_STEP_TYPES
} from "modules/activity/graphql/query";
import { USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT } from "shared/components/CaseReassignment/graphql/queries";
import { isNullOrEmpty } from "utils/fp";

// eslint-disable-next-line complexity
const ActivitiesForm = ({
  isActivitiesEnabled,
  createChecked,
  duplicateChecked,
  handleResetCheckboxes,
  saveAndStartTimer,
  handleSaveAndStartTimer,
  handleDisableStartTimer
}) => {
  if (!isActivitiesEnabled) return redirectToRoot();

  const {
    facilitySelectedOption,
    activityTypeSelectedOption,
    activityProductCategorySelectedOption,
    activityAssigneeSelectedOption,
    activityStepTypeSelectedOption,
    activityDeadline,
    activityComment,
    formState,
    handleDateInputChangeFor,
    handleActivityCommentChange,
    handleInputDropdownChangeFor,
    handleSubmit,
    today,
    hasError,
    isPhase4Enabled
  } = useComponentLogic({
    createChecked,
    duplicateChecked,
    handleResetCheckboxes,
    handleSaveAndStartTimer,
    handleDisableStartTimer
  });

  const { feedback } = formState;

  return (
    <form
      id="activityCreateForm"
      autoComplete="off"
      onSubmit={e => handleSubmit(saveAndStartTimer, e)}
      className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
    >
      <Select
        id={ACTIVITY_TYPE}
        name={ACTIVITY_TYPE}
        query={GET_ACTIVITY_TYPES}
        feedback={feedback.activityType}
        onChange={handleInputDropdownChangeFor(ACTIVITY_TYPE)}
        initialValue={activityTypeSelectedOption}
        path={["activityTypes"]}
        label="Activity Type"
        placeholder="Select Activity Type"
        isSearchable
        clearable
        error={hasError("activityType")(feedback)}
        errorText={feedback.activityType?.message}
      />
      {isPhase4Enabled && (
        <div className="tw-flex tw-flex-row tw-items-center tw-gap-5">
          <i className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 " />
          <div className="tw-w-full">
            <Select
              id={ACTIVITY_STEP_TYPE}
              name={ACTIVITY_STEP_TYPE}
              query={GET_STEP_TYPES}
              feedback={feedback.activityStepType}
              variables={{ activityTypeId: activityTypeSelectedOption?.value }}
              onChange={handleInputDropdownChangeFor(ACTIVITY_STEP_TYPE)}
              initialValue={activityStepTypeSelectedOption}
              path={["stepTypes", "stepTypes"]}
              label="Step Type"
              placeholder="Select Step Type"
              isPageable
              isSearchable
              clearable
              error={hasError("activityStepType")(feedback)}
              errorText={feedback.activityStepType?.message}
              disabled={isNullOrEmpty(activityTypeSelectedOption)}
              optional
            />
          </div>
        </div>
      )}
      <Select
        id={FACILITY_ID}
        name={FACILITY_ID}
        label="Facility"
        placeholder="Select Facility"
        feedback={feedback.facilityId}
        onChange={handleInputDropdownChangeFor(FACILITY_ID)}
        initialValue={facilitySelectedOption}
        query={FACILITIES}
        path={["currentUserAvailableFacilities", "userAvailableFacilities"]}
        variables={{ perPage: 25 }}
        isPageable
        isSearchable
        clearable
        error={hasError("facility")(feedback)}
        errorText={feedback.facility?.message}
      />
      {isPhase4Enabled && (
        <Select
          id={ACTIVITY_PRODUCT_CATEGORY}
          name={ACTIVITY_PRODUCT_CATEGORY}
          label="Product Category"
          fields={["name", "name"]}
          placeholder="Select Product Category"
          feedback={feedback.activityProductCategory}
          onChange={handleInputDropdownChangeFor(ACTIVITY_PRODUCT_CATEGORY)}
          initialValue={activityProductCategorySelectedOption}
          query={GET_HOUR_PRODUCT_CATEGORIES}
          path={["hourProductCategories", "categories"]}
          variables={{ perPage: 25 }}
          rowsPerPage={25}
          isPageable
          isSearchable
          clearable
          errorText={feedback.facility?.message}
        />
      )}
      <Select
        id={ACTIVITY_ASSIGNEE}
        name={ACTIVITY_ASSIGNEE}
        fields={["id", "fullName"]}
        feedback={feedback.activityAssignee}
        initialValue={activityAssigneeSelectedOption}
        onChange={handleInputDropdownChangeFor(ACTIVITY_ASSIGNEE)}
        query={USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT}
        variables={{ perPage: 25 }}
        path={["activityUsers", "users"]}
        label="Assignee"
        placeholder="Select Assignee"
        isPageable
        isSearchable
        clearable
        error={hasError("activityAssignee")(feedback)}
        errorText={feedback.activityAssignee?.message}
      />
      <DateInput
        label="Deadline"
        id={ACTIVITY_DEADLINE}
        name={ACTIVITY_DEADLINE}
        value={activityDeadline}
        onChange={handleDateInputChangeFor(ACTIVITY_DEADLINE)}
        minDate={today}
        feedbackType={feedback.activityDeadline?.type}
        feedbackText={feedback.activityDeadline?.message}
      />
      {isPhase4Enabled && (
        <TextArea
          id={ACTIVITY_COMMENT}
          name={ACTIVITY_COMMENT}
          value={activityComment}
          onChange={handleActivityCommentChange}
          label="Comment"
          placeholder="Enter comments here..."
          textareaClassName="tw-min-h-[128px] tw-resize-none"
          optional
          feedbackType={feedback.activityComment?.type}
          feedbackText={feedback.activityComment?.message}
        />
      )}
    </form>
  );
};

export default ActivitiesForm;
