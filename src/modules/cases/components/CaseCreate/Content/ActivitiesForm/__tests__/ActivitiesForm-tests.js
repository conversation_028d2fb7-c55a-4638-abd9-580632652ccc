import "@testing-library/jest-dom";
import { create } from "react-test-renderer";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import ActivitiesForm from "modules/cases/components/CaseCreate/Content/ActivitiesForm";
import mocks from "modules/cases/components/CaseCreate/graphql/mocks";
import { MemoryRouter } from "react-router-dom";
import wait from "waait";

jest.mock("@q-centrix/q-components-react", () => ({
  Select: "Select",
  DateInput: "DateInput",
  TextArea: "TextArea",
  useToast: () => ({ toast: jest.fn() })
}));

jest.mock("utils/frequentDateValues", () => ({
  today: new Date("2025-05-28T00:00:00Z")
}));

const ContentWithRouter = props => (
  <MemoryRouter>
    <ActivitiesForm {...props} />
  </MemoryRouter>
);

describe("ActivitiesForm", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: ContentWithRouter,
        props,
        initialValues: {},
        initialAppValues: {
          enabledFeatureToggles: {
            enabledFeatureToggles: ["QFlow Phase 4"]
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render({ isActivitiesEnabled: true });

    await wait(100);

    expect(component).toMatchSnapshot();
  });
});
