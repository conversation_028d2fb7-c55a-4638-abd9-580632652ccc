// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActivitiesForm it renders component correctly 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
  id="activityCreateForm"
  onSubmit={[Function]}
>
  <Select
    cacheKey="default"
    clearable={true}
    error={false}
    iconClass="fa-solid fa-chevron-down"
    id="activityType"
    isLoading={false}
    isMulti={false}
    isPageable={false}
    isSearchable={true}
    label="Activity Type"
    name="activityType"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Activity Type"
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <div
    className="tw-flex tw-flex-row tw-items-center tw-gap-5"
  >
    <i
      className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
    />
    <div
      className="tw-w-full"
    >
      <Select
        cacheKey="default"
        clearable={true}
        disabled={true}
        error={false}
        iconClass="fa-solid fa-chevron-down"
        id="activityStepType"
        isLoading={false}
        isMulti={false}
        isPageable={true}
        isSearchable={true}
        label="Step Type"
        name="activityStepType"
        onChange={[Function]}
        onInputChange={[Function]}
        onPageChange={[Function]}
        optional={true}
        options={Array []}
        page={1}
        placeholder="Select Step Type"
        smallPagination={false}
        totalCount={0}
        value={null}
      />
    </div>
  </div>
  <Select
    cacheKey="default"
    clearable={true}
    error={false}
    iconClass="fa-solid fa-chevron-down"
    id="facilityId"
    isLoading={false}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Facility"
    name="facilityId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={
      Array [
        Object {
          "label": "Large Medical Center",
          "value": "1",
        },
        Object {
          "label": "Small Medical Center",
          "value": "2",
        },
      ]
    }
    page={1}
    placeholder="Select Facility"
    smallPagination={false}
    totalCount={2}
    value={null}
  />
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="activityProductCategory"
    isLoading={false}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Product Category"
    name="activityProductCategory"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={
      Array [
        Object {
          "label": "Hour Product Category 1",
          "value": "Hour Product Category 1",
        },
        Object {
          "label": "Hour Product Category 2",
          "value": "Hour Product Category 2",
        },
        Object {
          "label": "Hour Product Category 3",
          "value": "Hour Product Category 3",
        },
        Object {
          "label": "Hour Product Category 4",
          "value": "Hour Product Category 4",
        },
        Object {
          "label": "Hour Product Category 5",
          "value": "Hour Product Category 5",
        },
        Object {
          "label": "Hour Product Category 6",
          "value": "Hour Product Category 6",
        },
        Object {
          "label": "Hour Product Category 7",
          "value": "Hour Product Category 7",
        },
        Object {
          "label": "Hour Product Category 8",
          "value": "Hour Product Category 8",
        },
        Object {
          "label": "Hour Product Category 9",
          "value": "Hour Product Category 9",
        },
        Object {
          "label": "Hour Product Category 10",
          "value": "Hour Product Category 10",
        },
        Object {
          "label": "Hour Product Category 11",
          "value": "Hour Product Category 11",
        },
        Object {
          "label": "Hour Product Category 12",
          "value": "Hour Product Category 12",
        },
        Object {
          "label": "Hour Product Category 13",
          "value": "Hour Product Category 13",
        },
        Object {
          "label": "Hour Product Category 14",
          "value": "Hour Product Category 14",
        },
        Object {
          "label": "Hour Product Category 15",
          "value": "Hour Product Category 15",
        },
        Object {
          "label": "Hour Product Category 16",
          "value": "Hour Product Category 16",
        },
        Object {
          "label": "Hour Product Category 17",
          "value": "Hour Product Category 17",
        },
        Object {
          "label": "Hour Product Category 18",
          "value": "Hour Product Category 18",
        },
        Object {
          "label": "Hour Product Category 19",
          "value": "Hour Product Category 19",
        },
        Object {
          "label": "Hour Product Category 20",
          "value": "Hour Product Category 20",
        },
        Object {
          "label": "Hour Product Category 21",
          "value": "Hour Product Category 21",
        },
        Object {
          "label": "Hour Product Category 22",
          "value": "Hour Product Category 22",
        },
        Object {
          "label": "Hour Product Category 23",
          "value": "Hour Product Category 23",
        },
        Object {
          "label": "Hour Product Category 24",
          "value": "Hour Product Category 24",
        },
      ]
    }
    page={1}
    placeholder="Select Product Category"
    rowsPerPage={25}
    smallPagination={false}
    totalCount={50}
    value={null}
  />
  <Select
    cacheKey="default"
    clearable={true}
    error={false}
    iconClass="fa-solid fa-chevron-down"
    id="activityAssignee"
    isLoading={false}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Assignee"
    name="activityAssignee"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Assignee"
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <DateInput
    id="activityDeadline"
    label="Deadline"
    minDate={2025-05-28T00:00:00.000Z}
    name="activityDeadline"
    onChange={[Function]}
    value={2025-05-28T00:00:00.000Z}
  />
  <TextArea
    id="activityComment"
    label="Comment"
    name="activityComment"
    onChange={[Function]}
    optional={true}
    placeholder="Enter comments here..."
    textareaClassName="tw-min-h-[128px] tw-resize-none"
    value=""
  />
</form>
`;
