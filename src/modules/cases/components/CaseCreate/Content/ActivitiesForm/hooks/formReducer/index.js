import { clearErrorsFromFeedback } from "./helpers";
import {
  FEEDBACK_TYPES,
  FACILITY_ID,
  ACTIVITY_TYPE,
  ACTIVITY_ASSIGNEE,
  ACTIVITY_DEADLINE,
  ACTION_TYPES,
  ACTIVITY_PRODUCT_CATEGORY,
  ACTIVITY_STEP_TYPE,
  ACTIVITY_COMMENT
} from "./constants";
import { today } from "utils/frequentDateValues";

export const INITIAL_FORM_STATE = {
  values: {
    [ACTIVITY_TYPE]: null,
    [FACILITY_ID]: null,
    [ACTIVITY_ASSIGNEE]: null,
    [ACTIVITY_DEADLINE]: today,
    [ACTIVITY_PRODUCT_CATEGORY]: null,
    [ACTIVITY_STEP_TYPE]: null,
    [ACTIVITY_COMMENT]: ""
  },
  feedback: {
    [ACTIVITY_TYPE]: null,
    [FACILITY_ID]: null,
    [ACTIVITY_ASSIGNEE]: null,
    [ACTIVITY_DEADLINE]: null,
    [ACTIVITY_PRODUCT_CATEGORY]: null,
    [ACTIVITY_STEP_TYPE]: null,
    [ACTIVITY_COMMENT]: ""
  }
};

// eslint-disable-next-line complexity
export const formReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.VALUE_CHANGE: {
      const { name, value } = action.payload;

      return {
        values: {
          ...state.values,
          [name]: value ?? null
        },
        feedback: {
          ...state.feedback,
          [name]: null
        }
      };
    }
    case ACTION_TYPES.FEEDBACK_CHANGE: {
      const { name, feedback } = action.payload;

      return {
        ...state,
        feedback: {
          ...state.feedback,
          [name]: feedback ?? null
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK: {
      const updatedFeedback = clearErrorsFromFeedback(state.feedback);

      return {
        ...state,
        feedback: {
          ...updatedFeedback
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_FORM_STATE: {
      return {
        values: { ...INITIAL_FORM_STATE.values },
        feedback: { ...INITIAL_FORM_STATE.feedback }
      };
    }
    default:
      return state;
  }
};

export const changeValue = (fieldName, value) => ({
  type: ACTION_TYPES.VALUE_CHANGE,
  payload: { name: fieldName, value }
});

export const changeFeedback = (fieldName, feedback) => ({
  type: ACTION_TYPES.FEEDBACK_CHANGE,
  payload: {
    name: fieldName,
    feedback
  }
});

export const resetValue = fieldName =>
  changeValue(fieldName, INITIAL_FORM_STATE.values[fieldName]);

export const resetFeedback = fieldName => changeFeedback(fieldName, null);

export const changeFeedbackToNeutral = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.NEUTRAL
  });

export const changeFeedbackToError = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.ERROR
  });

export const resetAllErrorFeedback = () => ({
  type: ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK
});

export const resetForm = () => ({
  type: ACTION_TYPES.RESET_ALL_FORM_STATE
});
