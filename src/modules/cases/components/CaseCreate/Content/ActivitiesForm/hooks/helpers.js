// eslint-disable-next-line complexity
export const validateForm = ({ values }) => {
  const formIssues = [];

  if (!values.activityType) {
    formIssues.push({
      activityType: "Activity Type required"
    });
  }

  if (!values.activityAssignee) {
    formIssues.push({
      activityAssignee: "Assignee required"
    });
  }

  if (!values.activityDeadline) {
    formIssues.push({
      activityDeadline: "Deadline required"
    });
  }

  return formIssues;
};
