import {
  always,
  and,
  applySpec,
  assoc,
  both,
  cond,
  equals,
  ifElse,
  includes,
  not,
  pathOr,
  pipe,
  prop,
  propOr,
  T,
  when
} from "ramda";
import { useMutation } from "@apollo/client";
import { useCallback, useReducer, useState } from "react";
import { useSelector } from "react-redux";
import { CREATE_ACTIVITY } from "modules/activity/graphql/mutation";
import {
  INITIAL_FORM_STATE,
  formReducer,
  changeValue,
  changeFeedbackToError,
  resetForm,
  resetValue
} from "./formReducer";
import { validateForm } from "./helpers";
import {
  FACILITY_ID,
  ACTIVITY_TYPE,
  ACTIVITY_ASSIGNEE,
  ACTIVITY_DEADLINE,
  ACTIVITY_PRODUCT_CATEGORY,
  ACTIVITY_STEP_TYPE,
  ACTIVITY_COMMENT
} from "./formReducer/constants";
import { useToast } from "@q-centrix/q-components-react";
import { useNavigate } from "react-router-dom";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";
import { format } from "date-fns";
import { userFeatureToggles } from "modules/facility-groups/utils/userFeatureToggles";
import accountSettingsSelectors from "modules/app/redux/selectors/accountSettings";
import { today } from "utils/frequentDateValues";

const translateDate = dateProp =>
  pipe(
    prop(dateProp),
    ifElse(isNullOrEmpty, always(null), val =>
      format(new Date(val), "yyyy-MM-dd")
    )
  );

const hasError = fieldName =>
  pipe(pathOr("", [fieldName, "type"]), equals("error"));

const isAssigneeOrStep = value =>
  includes(value, ["activityAssignee", "activityStepType"]);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  createChecked,
  duplicateChecked,
  handleResetCheckboxes,
  handleSaveAndStartTimer,
  handleDisableStartTimer
}) => {
  const { isFeatureEnabled } = userFeatureToggles();
  const isPhase4Enabled = isFeatureEnabled("QFlow Phase 4");
  const [selectedOptions, setSelectedOptions] = useState({});
  const loggedInUser = useSelector(accountSettingsSelectors.accountSettings);

  const [formState, dispatch] = useReducer(
    formReducer,
    duplicateChecked ? selectedOptions : INITIAL_FORM_STATE
  );

  const { values } = formState;

  const {
    [FACILITY_ID]: facilitySelectedOption = null,
    [ACTIVITY_TYPE]: activityTypeSelectedOption = null,
    [ACTIVITY_PRODUCT_CATEGORY]: activityProductCategorySelectedOption = null,
    [ACTIVITY_ASSIGNEE]: activityAssigneeSelectedOption = null,
    [ACTIVITY_DEADLINE]: activityDeadline = today,
    [ACTIVITY_STEP_TYPE]: activityStepTypeSelectedOption = null,
    [ACTIVITY_COMMENT]: activityComment = ""
  } = selectedOptions;

  const handleActivityCommentChange = e => {
    setSelectedOptions(assoc([ACTIVITY_COMMENT], e.target.value));
    dispatch(changeValue([ACTIVITY_COMMENT], e.target.value));
  };

  const updateButtonState = useCallback(
    (dropdownType, selectedValue) => {
      const shouldDisableButton = pipe(
        ifElse(
          equals("activityAssignee"),
          () =>
            and(
              equals(selectedValue, loggedInUser.id),
              isNotNullOrEmpty(values.activityStepType)
            ),
          () =>
            and(
              equals(values.activityAssignee, loggedInUser.id),
              isNotNullOrEmpty(selectedValue)
            )
        ),
        not
      )(dropdownType);

      handleDisableStartTimer(shouldDisableButton);
    },
    [values, handleDisableStartTimer]
  );

  const handleDateInputChangeFor = dateInputName => date => {
    setSelectedOptions(assoc(dateInputName, date));
    dispatch(changeValue(dateInputName, date));
  };

  const handleInputDropdownChangeFor = inputDropdownName => option => {
    setSelectedOptions(assoc(inputDropdownName, option));
    when(equals("activityType"), () =>
      setSelectedOptions(assoc([ACTIVITY_STEP_TYPE], null))
    )(inputDropdownName);

    const selectedValue = propOr(null, "value")(option);

    dispatch(changeValue(inputDropdownName, selectedValue));
    if (isAssigneeOrStep(inputDropdownName)) {
      updateButtonState(inputDropdownName, selectedValue);
    }
  };

  const { toast } = useToast();

  const navigate = useNavigate();

  const [createActivity] = useMutation(CREATE_ACTIVITY);

  const handleSubmit = (startTimer, e) => {
    e.preventDefault();

    const formIssues = validateForm({
      values
    });

    formIssues.forEach(issue => {
      const [[fieldName, feedbackMessage]] = Object.entries(issue);

      dispatch(changeFeedbackToError(fieldName, feedbackMessage));
    });

    if (formIssues.length) return;

    const loadingToast = toast({
      variant: "loading",
      description: "Saving new Activity..."
    });

    createActivity({
      variables: {
        activity: applySpec({
          facilityId: propOr(null, "facilityId"),
          activityTypeId: propOr(null, "activityType"),
          assigneeId: propOr(null, "activityAssignee"),
          productCategory: propOr(null, "activityProductCategory"),
          deadlineDate: translateDate("activityDeadline"),
          stepTypeId: propOr(null, "activityStepType"),
          comment: propOr(null, "activityComment")
        })(values),
        startTimer
      },
      // eslint-disable-next-line no-shadow, complexity, max-statements
      onCompleted: ({ createActivity }) => {
        if (createActivity?.errors) {
          createActivity.errors?.fullMessages.forEach((errorMessage, index) => {
            if (index === 0) {
              loadingToast.update({
                description: errorMessage,
                variant: "error"
              });
            } else {
              toast({
                variant: "error",
                description: errorMessage
              });
            }
          });
        } else {
          loadingToast.update({
            variant: "success",
            description: "Successfully saved new Activity!"
          });
          handleResetCheckboxes();
          handleSaveAndStartTimer(false);

          const resetFormWithInitialState = () => {
            dispatch(resetForm());
            setSelectedOptions(INITIAL_FORM_STATE.values);
          };

          cond([
            [
              equals(true),
              () => {
                dispatch(resetValue([ACTIVITY_ASSIGNEE]));
                setSelectedOptions(assoc([ACTIVITY_ASSIGNEE], null));
                dispatch(resetValue([ACTIVITY_COMMENT]));
                setSelectedOptions(assoc([ACTIVITY_COMMENT], ""));
              }
            ],
            [
              both(equals(false), () => equals(true)(createChecked)),
              resetFormWithInitialState
            ],
            [
              T,
              () => {
                resetFormWithInitialState();
                navigate(`/activities/${createActivity.activity.id}`);
              }
            ]
          ])(duplicateChecked);
        }
      },
      onError: error => {
        loadingToast.update({
          variant: "error",
          description: error.message
        });
      },
      // invalidate list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            activityList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
  };

  return {
    facilitySelectedOption,
    activityTypeSelectedOption,
    activityProductCategorySelectedOption,
    activityAssigneeSelectedOption,
    activityDeadline,
    activityStepTypeSelectedOption,
    activityComment,
    formState,
    handleDateInputChangeFor,
    handleActivityCommentChange,
    handleInputDropdownChangeFor,
    handleSubmit,
    today,
    hasError,
    isPhase4Enabled
  };
};
