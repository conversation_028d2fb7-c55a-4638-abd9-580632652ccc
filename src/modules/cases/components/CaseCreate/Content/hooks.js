import { useQuery } from "@apollo/client";
import { GET_ACTIVITY_PERMISSIONS } from "../graphql/query";

export const useComponentLogic = () => {
  const {
    data: { activityPermissions: { authorized } } = {
      activityPermissions: { authorized: false }
    },
    loading
  } = useQuery(GET_ACTIVITY_PERMISSIONS, {
    variables: {
      action: "create"
    }
  });

  return {
    isActivitiesLoading: loading,
    isActivitiesEnabled: authorized
  };
};
