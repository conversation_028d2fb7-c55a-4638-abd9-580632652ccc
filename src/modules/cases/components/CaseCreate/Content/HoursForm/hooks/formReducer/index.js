/* eslint-disable max-statements */
/* eslint-disable complexity */
import { clearErrorsFromFeedback } from "./helpers";
import {
  FACILITY_ID,
  HOUR_TYPES,
  HOUR_PRODUCT_CATEGORIES,
  HOUR_APPROVERS,
  NOTES,
  ACTION_TYPES,
  FEEDBACK_TYPES
} from "./constants";

export const INITIAL_FORM_STATE = {
  values: {
    [FACILITY_ID]: null,
    [HOUR_TYPES]: null,
    [HOUR_PRODUCT_CATEGORIES]: null,
    [HOUR_APPROVERS]: null,
    [NOTES]: ""
  },
  feedback: {
    [FACILITY_ID]: null,
    [HOUR_TYPES]: null,
    [HOUR_PRODUCT_CATEGORIES]: null,
    [HOUR_APPROVERS]: null,
    [NOTES]: null
  }
};

export const formReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.VALUE_CHANGE: {
      const { name, value } = action.payload;

      return {
        values: {
          ...state.values,
          [name]: value ?? null
        },
        feedback: {
          ...state.feedback,
          [name]: null
        }
      };
    }
    case ACTION_TYPES.FEEDBACK_CHANGE: {
      const { name, feedback } = action.payload;

      return {
        ...state,
        feedback: {
          ...state.feedback,
          [name]: feedback ?? null
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK: {
      const updatedFeedback = clearErrorsFromFeedback(state.feedback);

      return {
        ...state,
        feedback: {
          ...updatedFeedback
        }
      };
    }
    case ACTION_TYPES.RESET_ALL_FORM_STATE: {
      return {
        values: { ...INITIAL_FORM_STATE.values },
        feedback: { ...INITIAL_FORM_STATE.feedback }
      };
    }
    default:
      return state;
  }
};

export const changeValue = (fieldName, value) => ({
  type: ACTION_TYPES.VALUE_CHANGE,
  payload: { name: fieldName, value }
});

export const changeFeedback = (fieldName, feedback) => ({
  type: ACTION_TYPES.FEEDBACK_CHANGE,
  payload: {
    name: fieldName,
    feedback
  }
});

export const resetValue = fieldName =>
  changeValue(fieldName, INITIAL_FORM_STATE.values[fieldName]);

export const resetFeedback = fieldName => changeFeedback(fieldName, null);

export const changeFeedbackToNeutral = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.NEUTRAL
  });

export const changeFeedbackToError = (fieldName, feedbackMessage) =>
  changeFeedback(fieldName, {
    message: feedbackMessage,
    type: FEEDBACK_TYPES.ERROR
  });

export const resetAllErrorFeedback = () => ({
  type: ACTION_TYPES.RESET_ALL_ERROR_FEEDBACK
});

export const resetForm = () => ({
  type: ACTION_TYPES.RESET_ALL_FORM_STATE
});
