/* eslint-disable max-statements */
/* eslint-disable complexity */
export const validateForm = ({ values, conditions }) => {
  const { approvalRequired, facilityRequired, productCategoryRequired } =
    conditions;
  const formIssues = [];

  if (facilityRequired && !values.facilityId) {
    formIssues.push({
      facilityId: "Facility required"
    });
  }

  if (!values.hourTypes) {
    formIssues.push({
      hourTypes: "Hour Type required"
    });
  }

  if (productCategoryRequired && !values.hourProductCategories) {
    formIssues.push({
      hourProductCategories: "Product Category required"
    });
  }

  if (approvalRequired && !values.hourApprovers) {
    formIssues.push({
      hourApprovers: "Hour Approver required"
    });
  }

  return formIssues;
};
