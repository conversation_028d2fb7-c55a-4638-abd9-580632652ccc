/* eslint-disable complexity */
/* eslint-disable max-statements */
import {
  always,
  and,
  applySpec,
  assoc,
  dissoc,
  equals,
  ifElse,
  map,
  pipe,
  pluck,
  prop,
  propEq,
  propOr,
  when
} from "ramda";
import { useMutation } from "@apollo/client";
import { useReducer, useState } from "react";
import { CREATE_OR_UPDATE_HOURS_GROUP } from "modules/hours/graphql/mutation";
import {
  INITIAL_FORM_STATE,
  formReducer,
  changeValue,
  changeFeedbackToError,
  resetForm
} from "./formReducer";
import { validateForm } from "./helpers";
import {
  FACILITY_ID,
  HOUR_TYPES,
  HOUR_PRODUCT_CATEGORIES,
  HOUR_APPROVERS
} from "./formReducer/constants";
import { useToast } from "@q-centrix/q-components-react";
import { useNavigate } from "react-router-dom";
import useLocalStorage from "shared/hooks/useLocalStorage";
import { isNotNullOrEmpty } from "utils/fp";

const shouldRemoveProductCategory = (dropdownName, selectedOption) =>
  and(
    equals("hourTypes", dropdownName),
    propEq("requireProductCategory", false)(selectedOption)
  );

export const useComponentLogic = () => {
  const [selectedOptions, setSelectedOptions] = useState({});

  const [lastUsedFacilityAndHourType, setLastUsedFacilityAndHourType] =
    useLocalStorage("lastUsedFacilityAndHourType", null);

  const [formState, dispatch] = useReducer(formReducer, INITIAL_FORM_STATE);

  const { values } = formState;

  const {
    [FACILITY_ID]: facilitySelectedOption = null,
    [HOUR_TYPES]: hourTypesSelectedOption = null,
    [HOUR_PRODUCT_CATEGORIES]: hourProductCategoriesSelectedOption = null,
    [HOUR_APPROVERS]: hourApproversSelectedOption = null
  } = selectedOptions;

  const handleFillLastUsedFacilityAndHourType = () => {
    dispatch(
      changeValue(
        FACILITY_ID,
        pipe(prop([FACILITY_ID]), pluck("value"))(lastUsedFacilityAndHourType)
      )
    );

    dispatch(
      changeValue(HOUR_TYPES, lastUsedFacilityAndHourType[HOUR_TYPES].value)
    );

    if (
      isNotNullOrEmpty(lastUsedFacilityAndHourType[HOUR_PRODUCT_CATEGORIES])
    ) {
      dispatch(
        changeValue(
          HOUR_PRODUCT_CATEGORIES,
          lastUsedFacilityAndHourType[HOUR_PRODUCT_CATEGORIES].value
        )
      );
    }

    setSelectedOptions(currSelectedOptions => ({
      ...currSelectedOptions,
      ...lastUsedFacilityAndHourType
    }));
  };

  const handleInputChange = e =>
    dispatch(changeValue(e.target.name, e.target.value));

  const handleInputDropdownChangeFor = inputDropdownName => option => {
    setSelectedOptions(assoc(inputDropdownName, option));

    const selectedValue = ifElse(
      equals("facilityId"),
      () => map(prop("value"))(option),
      always(option?.value)
    )(inputDropdownName);

    if (shouldRemoveProductCategory(inputDropdownName, option)) {
      dispatch(changeValue([HOUR_PRODUCT_CATEGORIES], null));
      setSelectedOptions(dissoc([HOUR_PRODUCT_CATEGORIES]));
    }

    dispatch(changeValue(inputDropdownName, selectedValue));
  };

  const [createHoursGroup] = useMutation(CREATE_OR_UPDATE_HOURS_GROUP);

  const { toast } = useToast();

  const navigate = useNavigate();

  const showProductCategory = propOr(
    false,
    "requireProductCategory"
  )(hourTypesSelectedOption);

  const facilityRequired = propOr(
    false,
    "clientRequired"
  )(hourTypesSelectedOption);

  const approvalRequired = propOr(
    false,
    "approvalRequired"
  )(hourTypesSelectedOption);

  const handleSubmit = e => {
    e.preventDefault();

    const formIssues = validateForm({
      values,
      conditions: {
        productCategoryRequired: showProductCategory,
        approvalRequired,
        facilityRequired
      }
    });

    formIssues.forEach(issue => {
      const [[fieldName, feedbackMessage]] = Object.entries(issue);

      dispatch(changeFeedbackToError(fieldName, feedbackMessage));
    });

    if (formIssues.length) return;

    const loadingToast = toast({
      variant: "loading",
      description: "Saving new Hour Group..."
    });

    createHoursGroup({
      variables: {
        hourGroupInput: applySpec({
          facilityIds: propOr([], "facilityId"),
          approverId: propOr(null, "hourApprovers"),
          productCategory: propOr(null, "hourProductCategories"),
          hourTypeId: propOr(null, "hourTypes"),
          notes: propOr(null, "notes"),
          qty: pipe(
            propOr(null, "qty"),
            when(
              qty => !equals(null)(qty),
              input => Number(input)
            )
          )
        })(values)
      },
      // eslint-disable-next-line no-shadow
      onCompleted: ({ createOrUpdateHourGroup }) => {
        if (createOrUpdateHourGroup?.errors) {
          createOrUpdateHourGroup.errors?.fullMessages.forEach(
            (errorMessage, index) => {
              if (index === 0) {
                loadingToast.update({
                  description: errorMessage,
                  variant: "error"
                });
              } else {
                toast({
                  variant: "error",
                  description: errorMessage
                });
              }
            }
          );
        } else {
          setLastUsedFacilityAndHourType({
            [FACILITY_ID]: facilitySelectedOption,
            [HOUR_TYPES]: hourTypesSelectedOption,
            [HOUR_PRODUCT_CATEGORIES]: hourProductCategoriesSelectedOption
          });
          dispatch(resetForm());
          loadingToast.update({
            variant: "success",
            description: "Successfully saved new Hour Group!"
          });
          navigate(`/hours/${createOrUpdateHourGroup.hourGroup.id}`);
        }
      },
      onError: error => {
        loadingToast.update({
          variant: "error",
          description: error.message
        });
      },
      // invalidate list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            hourGroupList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
  };

  return {
    facilitySelectedOption,
    hourTypesSelectedOption,
    hourProductCategoriesSelectedOption,
    hourApproversSelectedOption,
    showProductCategory,
    formState,
    handleInputChange,
    handleSubmit,
    handleInputDropdownChangeFor,
    lastUsedFacilityAndHourType,
    handleFillLastUsedFacilityAndHourType
  };
};
