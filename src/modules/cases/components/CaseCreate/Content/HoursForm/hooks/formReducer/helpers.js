import { map, when, mapObjIndexed, always } from "ramda";
import {
  FEEDBACK_TYPES,
  FACILITY_ID,
  HOUR_TYPES,
  HOUR_PRODUCT_CATEGORIES,
  HOUR_APPROVERS,
  NOTES
} from "./constants";

const feedBackPropMap = {
  [FACILITY_ID]: "Facilities",
  [HOUR_TYPES]: "Hour Type",
  [HOUR_PRODUCT_CATEGORIES]: "Product Category",
  [HOUR_APPROVERS]: "Approver",
  [NOTES]: "Notes"
};

export const generateSuccessFeedback = mapObjIndexed((_value, currentKey) => ({
  type: FEEDBACK_TYPES.SUCCESS,
  message: `${feedBackPropMap[currentKey]} found`
}));

export const generateAutoFilledFields = map(() => true);

export const clearErrorsFromFeedback = map(
  when(value => value?.type === FEEDBACK_TYPES.ERROR, always(null))
);
