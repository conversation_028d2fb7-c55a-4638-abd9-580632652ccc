import Select from "shared/components/Select";
import {
  FACILITIES,
  GET_HOUR_TYPES,
  GET_HOUR_PRODUCT_CATEGORIES,
  GET_HOUR_APPROVERS
} from "modules/cases/components/CaseCreate/graphql/query";
import { TextArea, Input, Button } from "@q-centrix/q-components-react";
import {
  FACILITY_ID,
  HOUR_TYPES,
  HOUR_PRODUCT_CATEGORIES,
  HOUR_APPROVERS,
  NOTES,
  QUANTITY
} from "./hooks/formReducer/constants";
import { useComponentLogic } from "./hooks";
import redirectToRoot from "utils/redirectToRoot";

// eslint-disable-next-line complexity
const HoursForm = ({ isHoursEnabled }) => {
  const {
    formState,
    handleInputDropdownChangeFor,
    handleInputChange,
    handleSubmit,
    showProductCategory,
    facilitySelectedOption,
    hourTypesSelectedOption,
    hourProductCategoriesSelectedOption,
    hourApproversSelectedOption,
    lastUsedFacilityAndHourType,
    handleFillLastUsedFacilityAndHourType
  } = useComponentLogic();
  const { values, feedback } = formState;

  if (!isHoursEnabled) return redirectToRoot();

  return (
    <form
      id="hourCreateForm"
      autoComplete="off"
      onSubmit={handleSubmit}
      className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
    >
      {lastUsedFacilityAndHourType && (
        <Button
          type="button"
          customStyle="tw-gap-2.5 tw-self-start"
          outline
          onClick={handleFillLastUsedFacilityAndHourType}
        >
          <i className="fa-regular fa-circle-check" />
          Fill Last Used Facility And Hour Type
        </Button>
      )}
      <Select
        id={FACILITY_ID}
        name={FACILITY_ID}
        label="Facilities"
        placeholder="Select Facilities"
        feedback={feedback.facilityId}
        onChange={handleInputDropdownChangeFor(FACILITY_ID)}
        initialValue={facilitySelectedOption}
        query={FACILITIES}
        path={["currentUserAvailableFacilities", "userAvailableFacilities"]}
        variables={{ perPage: 25 }}
        rowsPerPage={25}
        isPageable
        isSearchable
        clearable
        isMulti
      />

      <Select
        id={HOUR_TYPES}
        name={HOUR_TYPES}
        label="Hour Type"
        fields={[
          "id",
          "name",
          "requireProductCategory",
          "approvalRequired",
          "clientRequired"
        ]}
        placeholder="Select Hour Type"
        feedback={feedback.hourTypes}
        onChange={handleInputDropdownChangeFor(HOUR_TYPES)}
        initialValue={hourTypesSelectedOption}
        query={GET_HOUR_TYPES}
        variables={{ perPage: 25 }}
        rowsPerPage={25}
        path={["hourTypes", "hourTypes"]}
        isPageable
        isSearchable
        clearable
      />
      {showProductCategory && (
        <Select
          id={HOUR_PRODUCT_CATEGORIES}
          name={HOUR_PRODUCT_CATEGORIES}
          label="Product Category"
          fields={["name", "name"]}
          placeholder="Select Product Category"
          feedback={feedback.hourProductCategories}
          onChange={handleInputDropdownChangeFor(HOUR_PRODUCT_CATEGORIES)}
          initialValue={hourProductCategoriesSelectedOption}
          query={GET_HOUR_PRODUCT_CATEGORIES}
          path={["hourProductCategories", "categories"]}
          variables={{ perPage: 25 }}
          rowsPerPage={25}
          isPageable
          isSearchable
          clearable
        />
      )}
      <Select
        id={HOUR_APPROVERS}
        name={HOUR_APPROVERS}
        fields={["id", "fullName"]}
        label="Approver"
        placeholder="Select Approver"
        feedback={feedback.hourApprovers}
        onChange={handleInputDropdownChangeFor(HOUR_APPROVERS)}
        initialValue={hourApproversSelectedOption}
        query={GET_HOUR_APPROVERS}
        variables={{ perPage: 25 }}
        rowsPerPage={25}
        path={["activeInternalUsersByNameAndFacility", "users"]}
        isPageable
        isSearchable
        clearable
      />
      <Input
        label="Quantity"
        id={QUANTITY}
        name={QUANTITY}
        type="number"
        min={0}
        placeholder="Enter Quantity"
        value={values.qty}
        onChange={handleInputChange}
        feedbackType={feedback.qty?.type}
        feedbackText={feedback.qty?.message}
        optional
      />
      <TextArea
        label="Notes"
        id={NOTES}
        name={NOTES}
        type="text"
        placeholder="Enter Notes"
        inputClassName="tw-h-[128px] tw-flex flex-col tw-justify-start "
        value={values.notes}
        onChange={handleInputChange}
        feedbackType={feedback.notes?.type}
        feedbackText={feedback.notes?.message}
      />
    </form>
  );
};

export default HoursForm;
