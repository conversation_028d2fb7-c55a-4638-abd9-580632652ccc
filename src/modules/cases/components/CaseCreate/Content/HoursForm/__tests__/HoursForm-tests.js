import "@testing-library/jest-dom";
import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import HoursForm from "modules/cases/components/CaseCreate/Content/HoursForm";
import mocks from "modules/cases/components/CaseCreate/graphql/mocks";
import { MemoryRouter } from "react-router-dom";

jest.mock("@q-centrix/q-components-react", () => ({
  Select: "Select",
  Input: "Input",
  TextArea: "TextArea",
  useToast: () => ({ toast: jest.fn() })
}));

const ContentWithRouter = props => (
  <MemoryRouter>
    <HoursForm {...props} />
  </MemoryRouter>
);

describe("HoursForm", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: ContentWithRouter,
        props,
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render({ isHoursEnabled: true });

    expect(component).toMatchSnapshot();
  });
});
