// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HoursForm it renders component correctly 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-h-full tw-w-3/5 tw-flex-col tw-gap-y-4 tw-rounded-md tw-border tw-border-solid tw-border-gray-200 tw-bg-white tw-p-5 tw-text-sm tw-shadow-md"
  id="hourCreateForm"
  onSubmit={[Function]}
>
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="facilityId"
    isLoading={true}
    isMulti={true}
    isPageable={true}
    isSearchable={true}
    label="Facilities"
    name="facilityId"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Facilities"
    rowsPerPage={25}
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="hourTypes"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Hour Type"
    name="hourTypes"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Hour Type"
    rowsPerPage={25}
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Select
    cacheKey="default"
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    id="hourApprovers"
    isLoading={true}
    isMulti={false}
    isPageable={true}
    isSearchable={true}
    label="Approver"
    name="hourApprovers"
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optional={false}
    options={Array []}
    page={1}
    placeholder="Select Approver"
    rowsPerPage={25}
    smallPagination={false}
    totalCount={0}
    value={null}
  />
  <Input
    id="qty"
    label="Quantity"
    min={0}
    name="qty"
    onChange={[Function]}
    optional={true}
    placeholder="Enter Quantity"
    type="number"
  />
  <TextArea
    id="notes"
    inputClassName="tw-h-[128px] tw-flex flex-col tw-justify-start "
    label="Notes"
    name="notes"
    onChange={[Function]}
    placeholder="Enter Notes"
    type="text"
    value=""
  />
</form>
`;
