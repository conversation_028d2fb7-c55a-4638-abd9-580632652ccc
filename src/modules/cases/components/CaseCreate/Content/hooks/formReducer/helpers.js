/* eslint-disable complexity */
import {
  pick,
  reject,
  map,
  when,
  pipe,
  isNil,
  mapObjIndexed,
  always,
  equals
} from "ramda";
import {
  FEEDBACK_TYPES,
  FACILITY_ID,
  VISIT_NUMBER,
  MRN,
  FIRST_CONTACT,
  ARRIVED_AT,
  ADMITTED_AT,
  SURGERY_DATE,
  HOSPITAL_DISCHARGED_AT,
  FIRST_NAME,
  MIDDLE_NAME,
  LAST_NAME,
  BORN_ON,
  CASE_TYPE_ID
} from "./constants";
import { isISOString } from "utils/isISOString";
import { INITIAL_FORM_STATE } from ".";

const visitDataProps = [
  FIRST_CONTACT,
  ARRIVED_AT,
  ADMITTED_AT,
  SURGERY_DATE,
  HOSPITAL_DISCHARGED_AT
];

const patientDataProps = [FIRST_NAME, MIDDLE_NAME, LAST_NAME, BORN_ON];

export const parseData = allowedProps =>
  pipe(
    pick(allowedProps),
    reject(isNil),
    map(when(isISOString, value => new Date(value)))
  );

export const parseVisitData = parseData(visitDataProps);

export const parsePatientData = parseData(patientDataProps);

// to have feedback messages match form label naming
const feedBackPropMap = {
  [FIRST_CONTACT]: "Date Of First Contact",
  [ARRIVED_AT]: "Arrival Date",
  [ADMITTED_AT]: "Admission Date",
  [SURGERY_DATE]: "Procedure Date",
  [HOSPITAL_DISCHARGED_AT]: "Discharge Date",
  [FIRST_NAME]: "Patient First Name",
  [MIDDLE_NAME]: "Patient Middle Name",
  [LAST_NAME]: "Patient Last Name",
  [BORN_ON]: "Patient DOB"
};

export const generateSuccessFeedback = mapObjIndexed((_value, currentKey) => ({
  type: FEEDBACK_TYPES.SUCCESS,
  message: `${feedBackPropMap[currentKey]} found`
}));

export const generateAutoFilledFields = map(() => true);

export const clearErrorsFromFeedback = map(
  when(value => value?.type === FEEDBACK_TYPES.ERROR, always(null))
);

const mrnAutoFillDependents = [...patientDataProps];

const visitNumberAutoFillDependents = [...visitDataProps];

const facilityIdAutoFillDependents = [
  CASE_TYPE_ID,
  VISIT_NUMBER,
  MRN,
  ...mrnAutoFillDependents,
  ...visitNumberAutoFillDependents
];

const getValuesToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    reject(equals(false)),
    mapObjIndexed((_val, name) => INITIAL_FORM_STATE.values[name])
  );

const getFeedbackToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    map(() => null)
  );

const getIsAutoFilledToReset = dependentFields =>
  pipe(
    pick(dependentFields),
    reject(equals(false)),
    map(() => false)
  );

const getResetData = (isAutoFilled, dependents) => {
  const valuesToReset = getValuesToReset(dependents)(isAutoFilled);
  const feedbackToReset = getFeedbackToReset(dependents)(isAutoFilled);
  const isAutoFieldToReset = getIsAutoFilledToReset(dependents)(isAutoFilled);

  return { valuesToReset, feedbackToReset, isAutoFieldToReset };
};

export const generateFieldsToReset = (name, isAutoFilled) => {
  switch (name) {
    case FACILITY_ID:
      return getResetData(isAutoFilled, facilityIdAutoFillDependents);

    case VISIT_NUMBER:
      return getResetData(isAutoFilled, visitNumberAutoFillDependents);

    case MRN:
      return getResetData(isAutoFilled, mrnAutoFillDependents);

    default:
      return {};
  }
};
