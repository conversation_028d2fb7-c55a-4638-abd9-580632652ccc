/* eslint-disable complexity */
/* eslint-disable max-statements */
import {
  always,
  when,
  complement,
  isNil,
  assoc,
  propEq,
  none,
  equals
} from "ramda";
import { useLazyQuery, useMutation } from "@apollo/client";
import { useEffect, useReducer, useState, useRef } from "react";
import useDebounce from "shared/hooks/useDebounce";
import {
  VISIT_DATA,
  PATIENT_DATA
} from "modules/cases/components/CaseCreate/graphql/query";
import { CREATE_CASE } from "../../graphql/mutation";
import {
  INITIAL_FORM_STATE,
  formReducer,
  changeValue,
  resetValue,
  populateWithVisitData,
  populateWithPatientData,
  changeFeedbackToNeutral,
  changeFeedbackToError,
  resetForm,
  resetFeedback
} from "./formReducer";
import {
  checkIfLosOverrideIsRequired,
  generateCaseInput,
  validateForm
} from "./helpers";
import useLocalStorage from "shared/hooks/useLocalStorage";
import {
  CANCER_SITE_ID,
  CASE_TYPE_ID,
  FACILITY_ID,
  FEEDBACK_TYPES,
  LOS_OVERRIDE,
  MRN,
  SERVICE_LINE_GWTG,
  VISIT_NUMBER,
  BUSINESS_OFFERING_CORE_MEASURES,
  BUSINESS_OFFERING_REGISTRY
} from "./formReducer/constants";
import { useToast } from "@q-centrix/q-components-react";
import { differenceInSeconds } from "date-fns";
import { MIN_DATE } from "modules/cases/constants";
import { useNavigate } from "react-router-dom";

export const useComponentLogic = () => {
  const timer = useRef(Date.now());

  const [lastUsedFacilityAndCaseType, setLastUsedFacilityAndCaseType] =
    useLocalStorage("lastUsedFacilityAndCaseType", null);

  const [selectedOptions, setSelectedOptions] = useState({});

  const [formState, dispatch] = useReducer(formReducer, INITIAL_FORM_STATE);

  const { values, feedback } = formState;

  const handleFillLastUsedFacilityAndCaseType = () => {
    dispatch(
      changeValue(FACILITY_ID, lastUsedFacilityAndCaseType[FACILITY_ID].value)
    );

    dispatch(
      changeValue(CASE_TYPE_ID, lastUsedFacilityAndCaseType[CASE_TYPE_ID].value)
    );

    setSelectedOptions(currSelectedOptions => ({
      ...currSelectedOptions,
      ...lastUsedFacilityAndCaseType
    }));
  };

  const handleInputChange = e =>
    dispatch(changeValue(e.target.name, e.target.value));

  const handleSwitchChange = (fieldName, value) =>
    dispatch(changeValue(fieldName, value));

  const handleInputDropdownChangeFor = inputDropdownName => option => {
    setSelectedOptions(assoc(inputDropdownName, option));
    dispatch(changeValue(inputDropdownName, option?.value ?? null));
  };

  const handleDateInputChangeFor = dateInputName => date =>
    dispatch(changeValue(dateInputName, date));

  const {
    [FACILITY_ID]: facilitySelectedOption = null,
    [CASE_TYPE_ID]: caseTypeSelectedOption = null,
    [CANCER_SITE_ID]: cancerTypeSelectedOption = null
  } = selectedOptions;

  const { serviceLine = null, businessOffering = null } =
    caseTypeSelectedOption ?? {};

  const serviceLineIsGwtg = serviceLine === SERVICE_LINE_GWTG;

  const caseTypeIsPsychiatricVisitAdminData = propEq(
    "label",
    "Psychiatric Visit Admin Data (UR)"
  )(caseTypeSelectedOption);

  const businessOfferingIsCoreMeasure =
    businessOffering === BUSINESS_OFFERING_CORE_MEASURES;

  const businessOfferingIsRegistry =
    businessOffering === BUSINESS_OFFERING_REGISTRY;

  const { start: caseTypeLOSStart, end: caseTypeLOSEnd } =
    caseTypeSelectedOption?.lengthOfStay ?? {};
  const { allowOverride: shouldAllowLosOverride } =
    caseTypeSelectedOption?.lengthOfStay ?? {};
  const caseTypeIsRegulatory = Boolean(caseTypeSelectedOption?.isRegulatory);

  const caseTypeAllowsEmptyDischargeDate = Boolean(
    caseTypeSelectedOption?.allowsEmptyDischargeDate
  );

  const caseTypeIsInPatient = Boolean(caseTypeSelectedOption?.isAdminInpatient);

  const caseTypeIsOutPatient = Boolean(
    caseTypeSelectedOption?.isAdminOutpatient
  );

  const requireAdmissionDate =
    caseTypeIsRegulatory ||
    !(businessOfferingIsCoreMeasure || businessOfferingIsRegistry);

  const requireArrivalDate = !requireAdmissionDate;

  const losOverrideIsRequired = checkIfLosOverrideIsRequired({
    losStart: caseTypeLOSStart,
    losEnd: caseTypeLOSEnd,
    arrivedAt: values.arrivedAt,
    hospitalDischargedAt: values.hospitalDischargedAt
  });

  useEffect(() => {
    if (!losOverrideIsRequired) {
      dispatch(changeValue(LOS_OVERRIDE, false));
      dispatch(resetFeedback(CASE_TYPE_ID));
    } else if (!shouldAllowLosOverride) {
      dispatch(changeValue(LOS_OVERRIDE, false));
      dispatch(
        changeFeedbackToError(
          CASE_TYPE_ID,
          "The case type must match the LOS. Correct the case type or LOS and try again."
        )
      );
    } else if (values.losOverride) {
      dispatch(resetFeedback(CASE_TYPE_ID));
    } else {
      dispatch(
        changeFeedbackToError(
          CASE_TYPE_ID,
          "The case type must match the LOS. Correct the case type, LOS, or override and try again."
        )
      );
    }
  }, [losOverrideIsRequired, shouldAllowLosOverride, values.losOverride]);

  const caseTypeConditionallySelectedOption = when(
    complement(isNil),
    always(caseTypeSelectedOption)
  )(facilitySelectedOption);

  const caseTypeIsOncology = Boolean(caseTypeSelectedOption?.isOncology);

  useEffect(() => {
    if (!caseTypeIsOncology) {
      dispatch(resetValue(CANCER_SITE_ID));
      setSelectedOptions(assoc(CANCER_SITE_ID, null));
    }
  }, [caseTypeIsOncology]);

  const shouldShowCancerTypes =
    Boolean(facilitySelectedOption) && caseTypeIsOncology;

  const debouncedVisitNumber = useDebounce(values.visitNumber, 200);

  const [getVisitData] = useLazyQuery(VISIT_DATA);

  useEffect(() => {
    if (debouncedVisitNumber && values.facilityId) {
      getVisitData({
        variables: {
          facilityId: parseInt(values.facilityId, 10),
          visitNumber: debouncedVisitNumber
        },
        onCompleted: data => {
          if (data?.visit) {
            const { visit } = data;

            dispatch(populateWithVisitData(visit));
          } else {
            dispatch(
              changeFeedbackToNeutral(
                VISIT_NUMBER,
                "Visit number not found at facility - a new visit will be created"
              )
            );
          }
        }
      });
    }
  }, [getVisitData, debouncedVisitNumber, values.facilityId]);

  const debouncedMedicalRecordNumber = useDebounce(values.mrn, 200);

  const [getPatientData] = useLazyQuery(PATIENT_DATA);

  useEffect(() => {
    if (debouncedMedicalRecordNumber && values.facilityId) {
      getPatientData({
        variables: {
          facilityId: parseInt(values.facilityId, 10),
          mrn: debouncedMedicalRecordNumber
        },
        onCompleted: data => {
          if (data?.patient) {
            const { patient } = data;

            dispatch(populateWithPatientData(patient));
          } else {
            dispatch(
              changeFeedbackToNeutral(
                MRN,
                "MRN not found at facility - a new patient will be created"
              )
            );
          }
        }
      });
    }
  }, [getPatientData, debouncedMedicalRecordNumber, values.facilityId]);

  const [createCase] = useMutation(CREATE_CASE);

  const { toast } = useToast();

  const navigate = useNavigate();

  const handleSubmit = e => {
    e.preventDefault();

    const formHasPreValidationErrors = Object.values(feedback).some(
      value => value?.type === FEEDBACK_TYPES.ERROR
    );

    const formIssues = validateForm({
      values,
      shouldShowCancerTypes,
      caseTypeIsOncology,
      caseTypeAllowsEmptyDischargeDate,
      requireAdmissionDate,
      requireArrivalDate
    });

    formIssues.forEach(issue => {
      const [[fieldName, feedbackMessage]] = Object.entries(issue);

      dispatch(changeFeedbackToError(fieldName, feedbackMessage));
    });

    if (formHasPreValidationErrors || formIssues.length) return;

    const valuesWithTimer = assoc(
      "elapsedTime",
      differenceInSeconds(Date.now(), timer.current, {
        roundingMethod: "ceil"
      })
    )(values);

    const caseInput = generateCaseInput(valuesWithTimer, {
      caseTypeIsOncology,
      caseTypeIsInPatient,
      caseTypeIsOutPatient,
      caseTypeAllowsEmptyDischargeDate
    });

    const loadingToast = toast({
      variant: "loading",
      description: "Saving new Case..."
    });

    createCase({
      variables: {
        _case: caseInput
      },
      // eslint-disable-next-line no-shadow
      onCompleted: ({ createCase }) => {
        if (createCase?.errors) {
          createCase.errors?.fullMessages.forEach((errorMessage, index) => {
            if (index === 0) {
              loadingToast.update({
                description: errorMessage,
                variant: "error"
              });
            } else {
              toast({
                variant: "error",
                description: errorMessage
              });
            }
          });
        } else {
          setLastUsedFacilityAndCaseType({
            [FACILITY_ID]: facilitySelectedOption,
            [CASE_TYPE_ID]: caseTypeSelectedOption
          });
          dispatch(resetForm());
          loadingToast.update({
            variant: "success",
            description: "Successfully saved new Case!"
          });
          navigate(`/cases/${createCase.case.id}`);
        }
      },
      onError: error => {
        loadingToast.update({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const today = new Date();

  const minDate = new Date(MIN_DATE);

  const showProcedureDate = none(equals(true), [
    caseTypeIsOncology,
    caseTypeIsRegulatory,
    caseTypeIsPsychiatricVisitAdminData
  ]);

  return {
    lastUsedFacilityAndCaseType,
    handleFillLastUsedFacilityAndCaseType,
    facilitySelectedOption,
    caseTypeConditionallySelectedOption,
    serviceLineIsGwtg,
    losOverrideIsRequired,
    shouldShowCancerTypes,
    caseTypeIsOncology,
    cancerTypeSelectedOption,
    formState,
    handleInputChange,
    handleSwitchChange,
    handleDateInputChangeFor,
    handleSubmit,
    handleInputDropdownChangeFor,
    today,
    minDate,
    caseTypeIsOutPatient,
    caseTypeAllowsEmptyDischargeDate,
    requireAdmissionDate,
    requireArrivalDate,
    shouldAllowLosOverride,
    showProcedureDate
  };
};
