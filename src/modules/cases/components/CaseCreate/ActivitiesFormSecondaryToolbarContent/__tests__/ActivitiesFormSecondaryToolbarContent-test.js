import { create } from "react-test-renderer";
import ActivitiesFormSecondaryToolbarContent from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Checkbox: "Checkbox",
  Button: "Button"
}));

const mockedProps = {
  createChecked: false,
  duplicateChecked: false,
  handleCreateChecked: () => jest.fn(),
  handleDuplicateChecked: () => jest.fn(),
  handleSaveAndStartTimer: () => jest.fn(),
  form: "testForm",
  disableStartTimer: true,
  isPhase4Enabled: true
};

describe("ActivitiesFormSecondaryToolbarContent", () => {
  function render(props = mockedProps) {
    return create(<ActivitiesFormSecondaryToolbarContent {...props} />);
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with second checkbox enabled", () => {
    const component = render({ ...mockedProps, createChecked: true });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with Save and Start Timer button enabled", () => {
    const component = render({ ...mockedProps, disableStartTimer: false });

    expect(component).toMatchSnapshot();
  });
});
