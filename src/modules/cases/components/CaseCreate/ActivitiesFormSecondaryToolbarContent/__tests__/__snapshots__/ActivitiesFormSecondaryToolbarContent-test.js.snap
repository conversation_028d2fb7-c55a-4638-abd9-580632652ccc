// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActivitiesFormSecondaryToolbarContent it renders component correctly 1`] = `
<div
  className="tw-flex tw-gap-5"
>
  <Checkbox
    checked={false}
    id="1"
    label="Create Another"
    name="First"
    onChange={[Function]}
  />
  <Checkbox
    checked={false}
    disabled={true}
    id="2"
    label="Duplicate Input Data"
    name="Second"
    onChange={[Function]}
  />
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-items-center tw-bg-gray-800 hover:tw-bg-gray-800 hover:!tw-shadow-none"
    disabled={true}
    form="testForm"
    onClick={[Function]}
    type="submit"
  >
    <i
      className="fa-light fa-clock"
    />
    Save and Start Timer
  </Button>
</div>
`;

exports[`ActivitiesFormSecondaryToolbarContent it renders component correctly with Save and Start Timer button enabled 1`] = `
<div
  className="tw-flex tw-gap-5"
>
  <Checkbox
    checked={false}
    id="1"
    label="Create Another"
    name="First"
    onChange={[Function]}
  />
  <Checkbox
    checked={false}
    disabled={true}
    id="2"
    label="Duplicate Input Data"
    name="Second"
    onChange={[Function]}
  />
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-items-center"
    disabled={false}
    form="testForm"
    onClick={[Function]}
    type="submit"
  >
    <i
      className="fa-light fa-clock"
    />
    Save and Start Timer
  </Button>
</div>
`;

exports[`ActivitiesFormSecondaryToolbarContent it renders component correctly with second checkbox enabled 1`] = `
<div
  className="tw-flex tw-gap-5"
>
  <Checkbox
    checked={true}
    id="1"
    label="Create Another"
    name="First"
    onChange={[Function]}
  />
  <Checkbox
    checked={false}
    disabled={false}
    id="2"
    label="Duplicate Input Data"
    name="Second"
    onChange={[Function]}
  />
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-items-center tw-bg-gray-800 hover:tw-bg-gray-800 hover:!tw-shadow-none"
    disabled={true}
    form="testForm"
    onClick={[Function]}
    type="submit"
  >
    <i
      className="fa-light fa-clock"
    />
    Save and Start Timer
  </Button>
</div>
`;
