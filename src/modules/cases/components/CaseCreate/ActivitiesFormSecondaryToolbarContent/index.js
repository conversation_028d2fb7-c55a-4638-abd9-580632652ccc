import { Button, Checkbox } from "@q-centrix/q-components-react";
import classNames from "classnames";

const ActivitiesFormSecondaryToolbarContent = ({
  createChecked,
  duplicateChecked,
  handleCreateChecked,
  handleDuplicateChecked,
  handleSaveAndStartTimer,
  form,
  disableStartTimer,
  isPhase4Enabled
}) => {
  const startTimerClassnames = classNames(
    "tw-flex tw-gap-2.5 tw-items-center",
    {
      "tw-bg-gray-800 hover:tw-bg-gray-800 hover:!tw-shadow-none":
        disableStartTimer
    }
  );

  return (
    <div className="tw-flex tw-gap-5">
      <Checkbox
        id="1"
        name="First"
        label="Create Another"
        checked={createChecked}
        onChange={handleCreateChecked}
      />
      <Checkbox
        id="2"
        name="Second"
        label="Duplicate Input Data"
        disabled={!createChecked}
        checked={duplicateChecked}
        onChange={handleDuplicateChecked}
      />
      {isPhase4Enabled && (
        <Button
          bg="main"
          customStyle={startTimerClassnames}
          onClick={() => handleSaveAndStartTimer(true)}
          type="submit"
          form={form}
          disabled={disableStartTimer}
        >
          <i className="fa-light fa-clock" />
          Save and Start Timer
        </Button>
      )}
    </div>
  );
};

export default ActivitiesFormSecondaryToolbarContent;
