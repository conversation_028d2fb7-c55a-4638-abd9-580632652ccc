import { gql } from "@apollo/client";

export const GET_CASE_QUESTIONNAIRES = gql`
  query caseQuestionnaries($caseId: Int!) {
    caseQuestionnaries(caseId: $caseId) {
      name
      path
      lastUpdated
      completionStatus
      samplingStatus
      locked
      user {
        id
      }
    }
  }
`;

export const GET_CASE_DETAILS = gql`
  query caseDetails($id: Int!) {
    caseDetails(caseId: $id) {
      startEditingOnLoad
      case {
        id
        isEditable
        batch {
          id
          link
        }
        losOverride
        status
        clientDueDate
        facility {
          id
          name
        }
        owner {
          id
          fullName
        }
        visit {
          id
          number
          hospitalDischargedAt
          firstContact
          arrivedAt
          admittedAt
          surgeryDate
        }
        caseType {
          id
          name
          generic
          isOncology
          isRegulatory
          isAdminInpatient
          isAdminOutpatient
          businessOffering
          serviceLine
          allowsEmptyDischargeDate
          lengthOfStay {
            start
            end
            allowOverride
          }
        }
        cancerSite {
          id
          name
        }
        gwtgId
        patient {
          id
          mrn
          firstName
          middleName
          lastName
          bornOn
        }
        primarySite
        caseStatusOptions {
          value
          displayValue
        }
        reportable
        analytic
        assignee {
          id
          fullName
        }
        abstractionMismatch
        permissions {
          canEditVisitNumberAndMrn
          canChangeCompleteCaseStatus
          canEditFacilityAndCaseType
          canEditAbstractionMismatch
        }
      }
      userHasTimeTrackingDisabled
    }
  }
`;

export const GET_CASE_USERS = gql`
  query caseUsers($perPage: Int) {
    caseUsers(perPage: $perPage) {
      caseUsers {
        id
        fullName
      }
    }
  }
`;

export const GET_TASKS_DATA = gql`
  query caseTasks($caseId: Int!) {
    caseTasks(caseId: $caseId) {
      id
      qPointsComp
      canShowQPoints
      owner {
        id
        fullName
      }
      billable
      completed
      taskType
      questionnaire {
        id
        name
        path
      }
      permissions {
        userCanAddTaskEntries
        userCanDeleteTaskEntries
      }
      taskEntries {
        id
        createdAt
        startedAt
        finishedAt
        totalTime {
          hours
          minutes
          seconds
        }
        permissions {
          userCanEdit
          userCanDelete
        }
      }
    }
  }
`;

export const GET_CASE_TASK_TYPES = gql`
  query caseTaskTypes($caseId: Int!) {
    caseTaskTypes(caseId: $caseId) {
      name
      isDisabled
    }
  }
`;

export const GET_CASE_RESPONSES = gql`
  query caseResponses($patientId: ID!) {
    caseResponses(patientId: $patientId) {
      caseStatus
      definitiveFacilityName
      qrAnswers {
        prompt
        fieldName
        order
        value
      }
      qrId
    }
  }
`;
