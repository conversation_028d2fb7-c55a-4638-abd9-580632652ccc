import { gql } from "@apollo/client";

export const UPDATE_CASE = gql`
  mutation updateCase($caseId: ID!, $case: CaseUpdateInput!) {
    updateCase(caseId: $caseId, _case: $case) {
      response
      errors {
        messages {
          errors
          attribute
        }
        fullMessages
      }
    }
  }
`;

export const UPDATE_CASE_CASE_TYPE = gql`
  mutation UpdateCaseCaseType($caseId: ID!, $caseTypeId: ID!) {
    updateCaseCaseType(caseId: $caseId, caseTypeId: $caseTypeId) {
      response
      case {
        id
        caseType {
          id
          name
          generic
          isOncology
          isRegulatory
          isAdminInpatient
          isAdminOutpatient
          businessOffering
          serviceLine
          allowsEmptyDischargeDate
          lengthOfStay {
            start
            end
          }
        }
      }
    }
  }
`;

export const DELETE_CASES = gql`
  mutation deleteCases($caseIds: [ID!]!, $reason: String!) {
    deleteCases(caseIds: $caseIds, reason: $reason) {
      response
    }
  }
`;

export const UPDATE_CASE_REPORTABLE_STATUS = gql`
  mutation updateCaseReportableStatus($caseId: ID!, $reportable: Int!) {
    updateCaseReportableStatus(caseId: $caseId, reportable: $reportable) {
      response
      case {
        id
        reportable
      }
      errors {
        fullMessages
      }
    }
  }
`;

export const UPDATE_CASE_ANALYTIC_STATUS = gql`
  mutation updateCaseAnalyticStatus($caseId: ID!, $analytic: Boolean!) {
    updateCaseAnalyticStatus(caseId: $caseId, analytic: $analytic) {
      response
      case {
        id
        analytic
      }
      errors {
        fullMessages
      }
    }
  }
`;

export const UPDATE_CASE_STATUS = gql`
  mutation updateCaseStatus($caseId: ID!, $status: String!) {
    updateCaseStatus(caseId: $caseId, status: $status) {
      case {
        id
        status
      }
      errors {
        messages {
          attribute
          errors
        }
        fullMessages
      }
    }
  }
`;

export const CREATE_UPDATE_TASK_ENTRY = gql`
  mutation createOrUpdateTaskEntry($taskEntryId: ID, $input: TaskEntryInput!) {
    createOrUpdateTaskEntry(taskEntryId: $taskEntryId, input: $input) {
      taskEntry {
        id
        task {
          id
        }
        totalTime {
          hours
          minutes
        }
        startedAt
        finishedAt
        startedAtDatascience
        finishedAtDatascience
      }
      errors {
        fullMessages
        messages {
          errors
        }
      }
    }
  }
`;

export const CREATE_TASK_WITH_TASK_ENTRY = gql`
  mutation createTaskWithTaskEntry($caseId: ID!, $taskType: String!) {
    createTaskWithTaskEntry(caseId: $caseId, taskType: $taskType) {
      task {
        id
        qPointsComp
        canShowQPoints
        taskEntries {
          id
          startedAt
          finishedAt
        }
      }
    }
  }
`;

export const COMPLETE_TASK = gql`
  mutation completeTask($taskId: ID!, $billable: Boolean) {
    completeTask(taskId: $taskId, billable: $billable) {
      response
      errors {
        fullMessages
        messages {
          errors
        }
      }
    }
  }
`;

export const UPDATE_TASK_BILLING_STATUS = gql`
  mutation updateTaskBillingStatus($taskId: ID!, $billable: Boolean!) {
    updateTaskBillingStatus(taskId: $taskId, billable: $billable) {
      response
    }
  }
`;

export const DELETE_TASK = gql`
  mutation deleteTask($taskId: ID) {
    deleteTask(taskId: $taskId) {
      response
      errors {
        fullMessages
        messages {
          errors
        }
      }
    }
  }
`;

export const DELETE_TASK_ENTRY = gql`
  mutation deleteTaskEntry($taskEntryId: ID!) {
    deleteTaskEntry(taskEntryId: $taskEntryId) {
      response
      errors {
        fullMessages
        messages {
          errors
        }
      }
    }
  }
`;

export const UPDATE_QUESTIONNAIRE_RESPONSE_LOCK_STATUS = gql`
  mutation updateQuestionnaireResponseLockStatus(
    $questionnaireResponseId: Int!
    $locked: Boolean!
  ) {
    updateQuestionnaireResponseLockStatus(
      questionnaireResponseId: $questionnaireResponseId
      locked: $locked
    ) {
      successful
      errors
    }
  }
`;

export const UPDATE_ABSTRACTION_MISMATCH = gql`
  mutation updateCaseAbstractionMismatchAttribute(
    $caseId: ID!
    $abstractionMismatchValue: Boolean!
  ) {
    updateCaseAbstractionMismatchAttribute(
      caseId: $caseId
      abstractionMismatchValue: $abstractionMismatchValue
    ) {
      case {
        id
        abstractionMismatch
      }
    }
  }
`;
