import { gql } from "@apollo/client";

export const UPDATED_USER_Q_POINTS_SUBSCRIPTION = gql`
  subscription updatedUserQpoints($taskId: ID!) {
    updatedUserQpoints(taskId: $taskId) {
      id
      qPointsComp
      canShowQPoints
    }
  }
`;

export const UPDATE_HOURS_FOR_CURRENT_WEEK_SUBSCRIPTION = gql`
  subscription updatedHoursForCurrentWeek($userId: ID!) {
    updatedHoursForCurrentWeek(userId: $userId) {
      abstractionHours
      nonAbstractionHours
      ptoHours
      totalHours
    }
  }
`;
