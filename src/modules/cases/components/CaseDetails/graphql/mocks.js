import { GET_CURRENT_USER_DATA } from "shared/graphql/query";
import {
  cancerTypesSuccess,
  caseTypesByFacilitySuccess,
  facilitiesSuccess
} from "../../CaseCreate/graphql/mocks";
import {
  CANCER_TYPES,
  CASE_TYPES_BY_FACILITY,
  FACILITIES
} from "../../CaseCreate/graphql/query";
import {
  DELETE_CASES,
  UPDATE_CASE,
  UPDATE_CASE_ANALYTIC_STATUS,
  UPDATE_CASE_REPORTABLE_STATUS,
  CREATE_TASK_WITH_TASK_ENTRY,
  CREATE_UPDATE_TASK_ENTRY
} from "./mutation";
import {
  GET_CASE_DETAILS,
  GET_TASKS_DATA,
  GET_CASE_QUESTIONNAIRES,
  GET_CASE_TASK_TYPES,
  GET_CASE_RESPONSES
} from "./query";

const caseDetailsQuestionnaire = {
  data: {
    caseQuestionnaries: [
      {
        name: "Abstraction Questionnaire",
        path: "/",
        lastUpdated: "2023-10-27T15:53:00-05:00",
        samplingStatus: true,
        locked: false,
        user: {
          fullName: "Russell Reas"
        }
      }
    ]
  }
};

const caseResponsesMockResponse = {
  data: {
    caseResponses: [
      {
        caseStatus: "pending_initial_reabstraction",
        qrId: "1",
        qrAnswers: [
          { sequenceNumberHospital: "Test Hospital 1" },
          { primarySite: "Eye" },
          { morphTypebehavIcdO3: "09-04" },
          { nameLast: "Smith" },
          { nameFirst: "Kate" },
          { dateOfBirth: "01/01/1987" },
          { dateOfDiagnosis: "01/01/2024" },
          { laterality: "Laterality1" },
          { medicalRecordNumber: "12234" },
          { dateOfLastContact: "09/01/2023" },
          { accessionNumberHosp: "4545" },
          { classOfCase: "Class of Case1" }
        ],
        definitiveFacilityName: "Inova Large Medical Center"
      },
      {
        caseStatus: "billable",
        qrId: "2",
        qrAnswers: [
          { sequenceNumberHospital: "Test Hospital 2" },
          { primarySite: "Nose" },
          { morphTypebehavIcdO3: "01-34" },
          { nameLast: "Black" },
          { nameFirst: "John" },
          { dateOfBirth: "03/03/1986" },
          { dateOfDiagnosis: "02/01/2024" },
          { laterality: "Laterality2" },
          { medicalRecordNumber: "45678" },
          { dateOfLastContact: "01/01/2023" },
          { accessionNumberHosp: "456" },
          { classOfCase: "Class of Case2" }
        ],
        definitiveFacilityName: "Inova Large Medical Center"
      }
    ]
  }
};

export const caseDetailsMockSuccessResponse = {
  data: {
    caseDetails: {
      startEditingOnLoad: false,
      case: {
        id: "1",
        generic: false,
        isEditable: true,
        batch: {
          id: "1",
          link: "/qapps/upload/case_uploads/1"
        },
        losOverride: null,
        status: "Billable",
        facility: {
          id: "1",
          name: "Large Medical Center"
        },
        owner: {
          id: "1",
          fullName: "Russell Reas"
        },
        visit: {
          id: "1",
          number: "1",
          hospitalDischargedAt: "2023-06-19T19:00:00-04:00",
          surgeryDate: null,
          arrivedAt: "2023-06-18T19:00:00-04:00",
          admittedAt: null,
          firstContact: null
        },
        caseType: {
          id: "1",
          name: "Mock Case Type",
          isOncology: false,
          isRegulatory: false,
          isAdminInpatient: false,
          isAdminOutpatient: false,
          businessOffering: "registry",
          serviceLine: "GWTG",
          allowsEmptyDischargeDate: false,
          lengthOfStay: {
            start: null,
            end: null
          }
        },
        cancerSite: null,
        gwtgId: "333111",
        patient: {
          id: "1",
          mrn: "1",
          firstName: "John",
          middleName: "",
          lastName: "Doe",
          bornOn: "1995-12-12T19:00:00-04:00"
        },
        primarySite: null,
        caseStatusOptions: [
          {
            value: "Pending-Initial Reabstraction",
            displayValue: "Pending-Initial Reabstraction"
          },
          {
            value: "Pending-Incomplete Abstraction",
            displayValue: "Pending-Incomplete Abstraction"
          },
          {
            value: "Pending-Missing Documentation",
            displayValue: "Pending-Missing Documentation"
          },
          {
            value: "Pending-Second Look",
            displayValue: "Pending-Second Look"
          },
          {
            value: "Pending-Fallout Review",
            displayValue: "Pending-Fallout Review"
          },
          {
            value: "Pending-Other",
            displayValue: "Pending-Other"
          },
          {
            value: "Billable",
            displayValue: "Billable"
          },
          {
            value: "Billable-Pending Client Response",
            displayValue: "Billable-Pending Client Response"
          },
          {
            value: "Ineligible",
            displayValue: "Ineligible"
          }
        ],
        reportable: "non_reportable",
        analytic: false,
        assignee: {
          id: "1",
          fullName: "Russell Reas"
        },
        permissions: {
          canEditVisitNumberAndMrn: true,
          canChangeCompleteCaseStatus: true,
          canEditFacilityAndCaseType: true
        }
      }
    }
  }
};

const caseDetailsTasks = {
  data: {
    caseTasks: [
      {
        __typename: "Task",
        id: "2",
        qPointsComp: 10,
        canShowQPoints: true,
        owner: {
          __typename: "User",
          id: "1",
          fullName: "Russell Reas"
        },
        billable: false,
        completed: "2023-01-19 19:00:00 -0500",
        taskType: "Targeted Review",
        permissions: {
          __typename: "TaskPermissions",
          userCanAddTaskEntries: true,
          userCanDeleteTaskEntries: true
        },
        taskEntries: [
          {
            __typename: "TaskEntry",
            id: "1",
            createdAt: "2023-11-16 14:42:36 -0500",
            startedAt: "2023-11-16 14:42:36 -0500",
            finishedAt: null,
            totalTime: null,
            task: {
              __typename: "Task",
              id: "73"
            },
            permissions: {
              __typename: "TaskEntryPermissions",
              userCanEdit: true,
              userCanDelete: true
            }
          }
        ]
      },
      {
        __typename: "Task",
        id: "66",
        qPointsComp: null,
        canShowQPoints: false,
        owner: {
          __typename: "User",
          id: "1",
          fullName: "Russell Reas"
        },
        billable: false,
        completed: null,
        taskType: "Reabstract-Self",
        permissions: {
          __typename: "TaskPermissions",
          userCanAddTaskEntries: true,
          userCanDeleteTaskEntries: true
        },
        taskEntries: [
          {
            __typename: "TaskEntry",
            id: "1",
            createdAt: "2023-11-15 16:03:59 -0500",
            startedAt: "2023-11-15 16:03:59 -0500",
            finishedAt: null,
            totalTime: null,
            task: {
              __typename: "Task",
              id: "66"
            },
            permissions: {
              __typename: "TaskEntryPermissions",
              userCanEdit: true,
              userCanDelete: true
            }
          }
        ]
      },
      {
        __typename: "Task",
        id: "67",
        qPointsComp: null,
        canShowQPoints: false,
        owner: {
          __typename: "User",
          id: "1",
          fullName: "Russell Reas"
        },
        billable: false,
        completed: null,
        taskType: "Reabstract-IRR",
        permissions: {
          __typename: "TaskPermissions",
          userCanAddTaskEntries: true,
          userCanDeleteTaskEntries: true
        },
        taskEntries: [
          {
            __typename: "TaskEntry",
            id: "1",
            createdAt: "2023-11-16 08:55:20 -0500",
            startedAt: "2023-11-16 08:55:20 -0500",
            finishedAt: null,
            totalTime: null,
            task: {
              __typename: "Task",
              id: "67"
            },
            permissions: {
              __typename: "TaskEntryPermissions",
              userCanEdit: true,
              userCanDelete: true
            }
          }
        ]
      }
    ]
  }
};

const caseTask = {
  data: {
    createTaskWithTaskEntry: {
      task: {
        id: "75",
        taskEntries: [
          {
            id: "75",
            startedAt: "2023-11-16 16:30:29 -0500",
            finishedAt: null,
            __typename: "TaskEntry"
          }
        ],
        __typename: "Task"
      },
      __typename: "CreateTaskWithTaskEntryPayload"
    }
  }
};

const caseTaskEntry = {
  createOrUpdateTaskEntry: {
    taskEntry: {
      id: "108",
      task: {
        id: "2",
        __typename: "Task",
        completedAt: "2023-11-16 16:30:29"
      },
      totalTime: null,
      startedAt: new Date(),
      finishedAt: null,
      startedAtDatascience: 1700490288,
      finishedAtDatascience: null,
      __typename: "TaskEntry"
    },
    errors: null,
    __typename: "CreateOrUpdateTaskEntryPayload"
  }
};

const deleteCaseResult = {
  data: {
    deleteCases: {
      response: true
    }
  }
};

const caseTaskTypes = {
  data: {
    caseTaskTypes: [
      {
        name: "IRR-Billable",
        isDisabled: true
      },
      {
        name: "Reabstract-IRR",
        isDisabled: true
      },
      {
        name: "Abstraction",
        isDisabled: true
      },
      {
        name: "Fallout Review",
        isDisabled: false
      },
      {
        name: "Reabstract-Self",
        isDisabled: false
      },
      {
        name: "Targeted Review",
        isDisabled: false
      }
    ]
  }
};

const caseUpdateSuccessResponse = {
  data: {
    updateCase: {
      response: "true",
      errors: null
    }
  }
};

export const MOCK_CASE_ID = 1;

const caseDetailsMocks = [
  {
    request: {
      query: FACILITIES,
      variables: { perPage: 2000 }
    },
    result: facilitiesSuccess
  },
  {
    request: {
      query: CASE_TYPES_BY_FACILITY,
      variables: {
        facilityId: 1,
        perPage: 2000
      }
    },
    result: caseTypesByFacilitySuccess
  },
  {
    request: { query: CANCER_TYPES, variables: {} },
    result: cancerTypesSuccess
  },
  {
    request: {
      query: GET_CASE_QUESTIONNAIRES,
      variables: { caseId: MOCK_CASE_ID }
    },
    result: caseDetailsQuestionnaire
  },
  {
    request: {
      query: GET_CASE_DETAILS,
      variables: {
        id: MOCK_CASE_ID
      }
    },
    result: caseDetailsMockSuccessResponse
  },
  {
    request: {
      query: GET_TASKS_DATA,
      variables: { caseId: MOCK_CASE_ID }
    },
    result: caseDetailsTasks
  },
  {
    request: {
      query: UPDATE_CASE_REPORTABLE_STATUS,
      variables: {
        caseId: MOCK_CASE_ID,
        reportable: 1
      },
      result: {
        data: {
          updateCaseReportableStatus: {
            response: "true"
          }
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_CASE_ANALYTIC_STATUS,
      variables: {
        caseId: MOCK_CASE_ID,
        analytic: true
      },
      result: {
        data: {
          updateCaseAnalyticStatus: {
            response: "true"
          }
        }
      }
    }
  },
  {
    request: {
      query: DELETE_CASES,
      variables: {
        caseIds: [MOCK_CASE_ID]
      }
    },
    result: deleteCaseResult
  },
  {
    request: {
      query: GET_CASE_TASK_TYPES,
      variables: { caseId: MOCK_CASE_ID },
      result: caseTaskTypes
    }
  },
  {
    request: {
      query: UPDATE_CASE,
      variables: {
        caseId: MOCK_CASE_ID,
        case: {
          hospitalDischargedAt: "2023-08-21",
          surgeryDate: "2023-09-20",
          admittedAt: "2023-08-20",
          arrivedAt: "2023-08-20",
          firstContact: "2023-08-19",
          middleName: "George",
          firstName: "John",
          lastName: "Doe",
          mrn: "12345",
          visitNumber: "54321",
          caseTypeId: "94",
          facilityId: "1"
        }
      },
      result: caseUpdateSuccessResponse
    }
  },
  {
    request: {
      query: CREATE_TASK_WITH_TASK_ENTRY,
      variables: { caseId: MOCK_CASE_ID, taskType: "Reabstract-Self" },
      result: caseTask
    }
  },
  {
    request: {
      query: CREATE_UPDATE_TASK_ENTRY,
      variables: {
        taskEntryId: null,
        input: {
          taskId: "2",
          startedAt: new Date(),
          finishedAt: null
        }
      },
      result: caseTaskEntry
    }
  },
  {
    request: {
      query: GET_CURRENT_USER_DATA
    },
    result: {
      data: {
        currentUser: {
          isInternalUser: true,
          qappsRole: "case_administrator"
        }
      }
    }
  },
  {
    request: {
      query: GET_CASE_RESPONSES,
      variables: {
        patientId: "1"
      }
    },
    result: caseResponsesMockResponse
  }
];

export default caseDetailsMocks;
