import { Apollo<PERSON><PERSON>ider } from "@apollo/client";
import apolloClient from "base/apolloClient";
import Layout from "shared/components/Layout";
import Content from "./Content";
import TopBarContent from "./TopBarContent";
import SecondaryToolbarContent from "./SecondaryToolbarContent";
import { useComponentLogic } from "./hooks";

export const client = apolloClient("/qapps/graphql");

const CaseDetails = () => {
  const {
    shouldShowCaseReassignment,
    handleBackArrowClick,
    toggleCaseReassignment,
    deleteModalIsOpen,
    toggleDeleteModal,
    handleCreateNew
  } = useComponentLogic();

  const tbChildren = <TopBarContent title="Case Details" />;

  const stChildren = (
    <SecondaryToolbarContent
      caseReassignmentDisabled={shouldShowCaseReassignment}
      onReassignCase={toggleCaseReassignment}
      onDeleteCase={toggleDeleteModal}
      onCreateNew={handleCreateNew}
    />
  );

  return (
    <ApolloProvider client={client}>
      <Layout
        tbLabel="PC"
        tbChildren={tbChildren}
        stChildren={stChildren}
        stChildrenClassName="tw-gap-5"
        onBackArrowClick={handleBackArrowClick}
      >
        <Content
          toggleDeleteModal={toggleDeleteModal}
          deleteModalIsOpen={deleteModalIsOpen}
          toggleCaseReassignment={toggleCaseReassignment}
          shouldShowCaseReassignment={shouldShowCaseReassignment}
        />
      </Layout>
    </ApolloProvider>
  );
};

export default CaseDetails;
