import { useToast } from "@q-centrix/q-components-react";
import { useState } from "react";
import { useMutation } from "@apollo/client";
import { UPDATE_CASE_CASE_TYPE } from "modules/cases/components/CaseDetails/graphql/mutation";
import {
  GET_CASE_DETAILS,
  GET_CASE_QUESTIONNAIRES
} from "modules/cases/components/CaseDetails/graphql/query";

export const useComponentLogic = ({ onChangeLater, onSaveSuccess, caseId }) => {
  const { toast } = useToast();
  const [nonGenericCaseTypeOption, setNonGenericCaseTypeOption] =
    useState(null);

  const [updateCaseCaseType, { loading }] = useMutation(UPDATE_CASE_CASE_TYPE);

  const handleSubmit = e => {
    e.preventDefault();

    updateCaseCaseType({
      variables: {
        caseId,
        caseTypeId: nonGenericCaseTypeOption.value
      },
      onCompleted: ({ updateCaseCaseType: updateCaseCaseTypeResponse }) => {
        if (updateCaseCaseTypeResponse?.errors) {
          updateCaseCaseTypeResponse.errors?.messages.forEach(errorMessage => {
            toast({
              variant: "error",
              description: errorMessage
            });
          });
        }
        if (updateCaseCaseTypeResponse?.response) {
          onSaveSuccess();
          setNonGenericCaseTypeOption(null);
        }
      },
      onError: error => {
        toast({
          description: error.message,
          variant: "error"
        });
      },
      refetchQueries: [
        { query: GET_CASE_DETAILS, variables: { id: Number(caseId) } },
        {
          query: GET_CASE_QUESTIONNAIRES,
          variables: { caseId: Number(caseId) }
        }
      ]
    });
  };

  const handleChangeLater = () => {
    onChangeLater();
    setNonGenericCaseTypeOption(null);
  };

  return {
    nonGenericCaseTypeOption,
    setNonGenericCaseTypeOption,
    handleChangeLater,
    handleSubmit,
    loading
  };
};
