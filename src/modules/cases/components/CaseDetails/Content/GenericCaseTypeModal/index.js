import { Button, ConfirmationModal } from "@q-centrix/q-components-react";
import Select from "shared/components/Select";
import { CASE_TYPES_BY_FACILITY } from "modules/cases/components/CaseCreate/graphql/query";
import { useComponentLogic } from "./hooks";

const GenericCaseTypeModal = ({
  title,
  body,
  isOpen,
  onChangeLater,
  onSaveSuccess,
  facilityId,
  caseId
}) => {
  const {
    nonGenericCaseTypeOption,
    setNonGenericCaseTypeOption,
    handleChangeLater,
    handleSubmit,
    loading
  } = useComponentLogic({ onChangeLater, onSaveSuccess, caseId });

  return (
    <ConfirmationModal
      title={title}
      isOpen={isOpen}
      appElement=".main"
      size="sm"
    >
      <form
        className="tw-flex tw-flex-col tw-gap-y-5"
        onSubmit={handleSubmit}
        autoComplete="off"
      >
        <p className="tw-text-sm">{body}</p>
        <Select
          id="non-generic-case-type-modal-cases-type"
          name="caseTypeId"
          label="Case Type"
          placeholder="Select a Case Type"
          initialValue={nonGenericCaseTypeOption}
          onChange={setNonGenericCaseTypeOption}
          query={CASE_TYPES_BY_FACILITY}
          variables={{
            facilityId: parseInt(facilityId, 10),
            excludeGenericCaseTypes: true,
            perPage: 25
          }}
          fields={["id", "name"]}
          path={["userCaseTypesByFacility", "userFacilityCaseTypes"]}
          isSearchable
          isPageable
          smallPagination
          clearable
        />
        <div className="tw-flex tw-gap-x-5">
          <Button
            type="button"
            outline
            onClick={handleChangeLater}
            disabled={loading}
            customStyle="tw-grow"
          >
            Change It Later
          </Button>
          <Button
            type="submit"
            bg="success"
            customStyle="tw-flex tw-gap-2.5 tw-items-center tw-grow"
            disabled={!nonGenericCaseTypeOption || loading}
          >
            <i className="fa-light fa-check" />
            Save Changes
          </Button>
        </div>
      </form>
    </ConfirmationModal>
  );
};

export default GenericCaseTypeModal;
