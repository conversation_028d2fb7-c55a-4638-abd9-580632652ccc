import { create } from "react-test-renderer";
import GenericCaseTypeModal from "..";
import { decoratedApollo } from "utils/tests/decorated";
import caseDetailsMocks from "../../../graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal",
  Select: "Select",
  // eslint-disable-next-line no-empty-function
  useToast: () => ({ toast: () => {} })
}));

const mockProps = {
  title: "12345",
  body: "This case is currently using a generic case type. Please select a detailed case type to get paid for the case.",
  isOpen: true,
  onChangeLater: jest.fn(),
  onSaveSuccess: jest.fn(),
  facilityId: 1,
  caseId: 1
};

describe("GenericCaseTypeModal", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: GenericCaseTypeModal,
        props,
        initialAppValues: {},
        apolloMocks: caseDetailsMocks
      })
    );
  }

  test("components renders correctly", () => {
    const component = render(mockProps);

    expect(component).toMatchSnapshot();
  });
});
