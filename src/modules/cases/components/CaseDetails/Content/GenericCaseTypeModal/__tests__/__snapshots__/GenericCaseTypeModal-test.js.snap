// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GenericCaseTypeModal components renders correctly 1`] = `
<ConfirmationModal
  appElement=".main"
  isOpen={true}
  size="sm"
  title="12345"
>
  <form
    autoComplete="off"
    className="tw-flex tw-flex-col tw-gap-y-5"
    onSubmit={[Function]}
  >
    <p
      className="tw-text-sm"
    >
      This case is currently using a generic case type. Please select a detailed case type to get paid for the case.
    </p>
    <Select
      cacheKey="default"
      clearable={true}
      iconClass="fa-solid fa-chevron-down"
      id="non-generic-case-type-modal-cases-type"
      isLoading={true}
      isMulti={false}
      isPageable={true}
      isSearchable={true}
      label="Case Type"
      name="caseTypeId"
      onChange={[Function]}
      onInputChange={[Function]}
      onPageChange={[Function]}
      optional={false}
      options={Array []}
      page={1}
      placeholder="Select a Case Type"
      smallPagination={true}
      totalCount={0}
      value={null}
    />
    <div
      className="tw-flex tw-gap-x-5"
    >
      <Button
        customStyle="tw-grow"
        disabled={false}
        onClick={[Function]}
        outline={true}
        type="button"
      >
        Change It Later
      </Button>
      <Button
        bg="success"
        customStyle="tw-flex tw-gap-2.5 tw-items-center tw-grow"
        disabled={true}
        type="submit"
      >
        <i
          className="fa-light fa-check"
        />
        Save Changes
      </Button>
    </div>
  </form>
</ConfirmationModal>
`;
