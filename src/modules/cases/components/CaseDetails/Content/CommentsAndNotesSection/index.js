import { Button } from "@q-centrix/q-components-react";
import Comments from "shared/components/Comments";
import Notes from "shared/components/Notes";
import { CREATE_OR_UPDATE_CASE_NOTE } from "./graphql/mutations";
import { CASE_NOTE } from "./graphql/queries";
import { useComponentLogic } from "./hooks";

const CommentsAndNotesSection = ({ caseId, loggedInUser }) => {
  const {
    shouldShowComments,
    showComments,
    showNotes,
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    createOrUpdateCaseCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    caseCommentsLoading,
    caseCommentsError
  } = useComponentLogic({ caseId });

  return (
    <>
      <div className="tw-flex tw-justify-center tw-bg-gray-300 tw-p-5">
        <Button
          customStyle="!tw-rounded-r-none tw-basis-1/2"
          onClick={showComments}
          outline={!shouldShowComments}
        >
          Comments
        </Button>
        <Button
          outline={shouldShowComments}
          customStyle="!tw-rounded-l-none tw-basis-1/2"
          onClick={showNotes}
        >
          Notes
        </Button>
      </div>
      {shouldShowComments ? (
        <Comments
          loggedInUser={loggedInUser}
          newCommentValue={newCommentValue}
          onNewCommentChange={handleNewCommentChange}
          onSubmitNewComment={handleSubmitNewComment}
          createLoading={createOrUpdateCaseCommentLoading}
          onUpdateExistingComment={handleUpdateExistingComment}
          updateLoading={createOrUpdateCaseCommentLoading}
          onDeleteExistingComment={handleDeleteExistingComment}
          deleteLoading={deleteCommentLoading}
          comments={sortedComments}
          loading={caseCommentsLoading}
          error={caseCommentsError}
        />
      ) : (
        <Notes
          caseId={caseId}
          query={CASE_NOTE}
          mutation={CREATE_OR_UPDATE_CASE_NOTE}
        />
      )}
    </>
  );
};

export default CommentsAndNotesSection;
