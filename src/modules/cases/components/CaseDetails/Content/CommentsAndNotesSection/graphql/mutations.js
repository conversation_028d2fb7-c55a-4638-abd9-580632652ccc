import { gql } from "@apollo/client";

export const CREATE_OR_UPDATE_CASE_COMMENT = gql`
  mutation createOrUpdateCaseComment(
    $caseId: ID!
    $commentId: ID
    $body: String!
  ) {
    createOrUpdateCaseComment(
      caseId: $caseId
      commentId: $commentId
      body: $body
    ) {
      comment {
        id
        createdAt
        updatedAt
        editable
        body
      }
      errors {
        messages {
          errors
          attribute
        }
        fullMessages
      }
    }
  }
`;

export const CREATE_OR_UPDATE_CASE_NOTE = gql`
  mutation createOrUpdateCaseNote($caseId: ID!, $text: String!) {
    createOrUpdateCaseNote(caseId: $caseId, text: $text) {
      note {
        id
        text
      }
    }
  }
`;
