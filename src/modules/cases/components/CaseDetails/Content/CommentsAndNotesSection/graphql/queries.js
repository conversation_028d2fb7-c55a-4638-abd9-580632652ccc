import { gql } from "@apollo/client";

export const CASE_COMMENTS = gql`
  query caseComments($caseId: ID!) {
    caseComments(caseId: $caseId) {
      id
      user {
        id
        fullName
        email
      }
      editable
      body
      createdAt
      updatedAt
    }
  }
`;

export const CASE_NOTE = gql`
  query caseNote($caseId: Int!) {
    caseNote(caseId: $caseId) {
      id
      text
    }
  }
`;
