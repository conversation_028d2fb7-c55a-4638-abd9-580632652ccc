import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedApollo } from "utils/tests/decorated";
import {
  caseCommentsSuccessMock,
  caseCommentsErrorMock
} from "../graphql/mocks";
import CommentsAndNotesSection from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Spinner: "Spinner",
  TextArea: "TextArea",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("shared/components/Notes", () => "Notes");
jest.mock("shared/components/Comments", () => "Comments");

function render(apolloMocks = [caseCommentsSuccessMock]) {
  return create(
    decoratedApollo({
      component: CommentsAndNotesSection,
      props: {
        caseId: "1",
        loggedInUser: {
          menuLinks: [],
          email: "<EMAIL>",
          id: "1",
          isFetching: false,
          isLoaded: true,
          error: null
        }
      },
      initialValues: {},
      initialAppValues: {},
      apolloMocks
    })
  );
}

describe("CommentsAndNotesSection", () => {
  test("it renders component with loading state", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const component = render([caseCommentsErrorMock]);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
