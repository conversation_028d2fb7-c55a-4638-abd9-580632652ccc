// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommentsAndNotesSection it renders component with data 1`] = `
Array [
  <div
    className="tw-flex tw-justify-center tw-bg-gray-300 tw-p-5"
  >
    <Button
      customStyle="!tw-rounded-r-none tw-basis-1/2"
      onClick={[Function]}
      outline={false}
    >
      Comments
    </Button>
    <Button
      customStyle="!tw-rounded-l-none tw-basis-1/2"
      onClick={[Function]}
      outline={true}
    >
      Notes
    </Button>
  </div>,
  <Comments
    comments={
      Array [
        Object {
          "__typename": "Comment",
          "body": "Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.

Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.",
          "createdAt": "2023-10-24T11:57:24-04:00",
          "editable": true,
          "id": "142",
          "updatedAt": "2023-12-19T11:45:51-05:00",
          "user": Object {
            "__typename": "User",
            "email": "<EMAIL>",
            "fullName": "Russell Reas",
            "id": "1",
          },
        },
        Object {
          "__typename": "Comment",
          "body": "Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.",
          "createdAt": "2023-10-24T11:57:21-04:00",
          "editable": true,
          "id": "141",
          "updatedAt": "2023-10-24T11:57:21-04:00",
          "user": Object {
            "__typename": "User",
            "email": "<EMAIL>",
            "fullName": "Russell Reas",
            "id": "1",
          },
        },
        Object {
          "__typename": "Comment",
          "body": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. 

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?",
          "createdAt": "2023-10-18T13:04:51-04:00",
          "editable": true,
          "id": "52",
          "updatedAt": "2023-10-24T11:57:39-04:00",
          "user": Object {
            "__typename": "User",
            "email": "<EMAIL>",
            "fullName": "Russell Reas",
            "id": "1",
          },
        },
        Object {
          "__typename": "Comment",
          "body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
          "createdAt": "2023-10-18T13:04:11-04:00",
          "editable": true,
          "id": "51",
          "updatedAt": "2023-10-24T11:57:55-04:00",
          "user": Object {
            "__typename": "User",
            "email": "<EMAIL>",
            "fullName": "Russell Reas",
            "id": "1",
          },
        },
      ]
    }
    createLoading={false}
    deleteLoading={false}
    loading={false}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "error": null,
        "id": "1",
        "isFetching": false,
        "isLoaded": true,
        "menuLinks": Array [],
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />,
]
`;

exports[`CommentsAndNotesSection it renders component with error 1`] = `
Array [
  <div
    className="tw-flex tw-justify-center tw-bg-gray-300 tw-p-5"
  >
    <Button
      customStyle="!tw-rounded-r-none tw-basis-1/2"
      onClick={[Function]}
      outline={false}
    >
      Comments
    </Button>
    <Button
      customStyle="!tw-rounded-l-none tw-basis-1/2"
      onClick={[Function]}
      outline={true}
    >
      Notes
    </Button>
  </div>,
  <Comments
    comments={Array []}
    createLoading={false}
    deleteLoading={false}
    error={[ApolloError: an error occurred]}
    loading={false}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "error": null,
        "id": "1",
        "isFetching": false,
        "isLoaded": true,
        "menuLinks": Array [],
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />,
]
`;

exports[`CommentsAndNotesSection it renders component with loading state 1`] = `
Array [
  <div
    className="tw-flex tw-justify-center tw-bg-gray-300 tw-p-5"
  >
    <Button
      customStyle="!tw-rounded-r-none tw-basis-1/2"
      onClick={[Function]}
      outline={false}
    >
      Comments
    </Button>
    <Button
      customStyle="!tw-rounded-l-none tw-basis-1/2"
      onClick={[Function]}
      outline={true}
    >
      Notes
    </Button>
  </div>,
  <Comments
    comments={Array []}
    createLoading={false}
    deleteLoading={false}
    loading={true}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "error": null,
        "id": "1",
        "isFetching": false,
        "isLoaded": true,
        "menuLinks": Array [],
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />,
]
`;
