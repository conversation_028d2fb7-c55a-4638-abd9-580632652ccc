/* eslint-disable max-statements */
import { useState, useMemo } from "react";
import { useQuery, useMutation } from "@apollo/client";
import { isEmpty } from "ramda";
import { CASE_COMMENTS } from "./graphql/queries";
import { CREATE_OR_UPDATE_CASE_COMMENT } from "./graphql/mutations";
import { DELETE_COMMENT } from "shared/graphql/mutation";
import { sortByCreatedAtDescending } from "utils/fp/sortByCreatedAtDescending";
import { useToast } from "@q-centrix/q-components-react";

export const useComponentLogic = ({ caseId }) => {
  const [newCommentValue, setNewCommentValue] = useState("");
  const [shouldShowComments, setShouldShowComments] = useState(true);
  const showComments = () => setShouldShowComments(true);
  const showNotes = () => setShouldShowComments(false);
  const { toast } = useToast();

  const {
    data: caseCommentsData = {},
    loading: caseCommentsLoading,
    error: caseCommentsError
  } = useQuery(CASE_COMMENTS, {
    variables: { caseId }
  });

  const { caseComments = [] } = caseCommentsData;

  const sortedComments = useMemo(
    () => sortByCreatedAtDescending(caseComments),
    [caseComments]
  );

  const [
    createOrUpdateCaseComment,
    { loading: createOrUpdateCaseCommentLoading }
  ] = useMutation(CREATE_OR_UPDATE_CASE_COMMENT, {
    refetchQueries: [CASE_COMMENTS]
  });

  const handleNewCommentChange = e => setNewCommentValue(e.target.value);

  const handleSubmitNewComment = e => {
    e.preventDefault();

    if (isEmpty(newCommentValue)) return;

    createOrUpdateCaseComment({
      variables: {
        caseId,
        body: newCommentValue.trim()
      },
      onCompleted: ({
        createOrUpdateCaseComment: createOrUpdateCaseCommentResult
      }) => {
        if (createOrUpdateCaseCommentResult?.errors) {
          createOrUpdateCaseCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          setNewCommentValue("");
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleUpdateExistingComment = (
    commentId,
    updatedComment,
    { onCompleted }
  ) => {
    createOrUpdateCaseComment({
      variables: {
        caseId,
        commentId,
        body: updatedComment.trim()
      },
      onCompleted: ({
        createOrUpdateCaseComment: createOrUpdateCaseCommentResult
      }) => {
        if (createOrUpdateCaseCommentResult?.errors) {
          createOrUpdateCaseCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          onCompleted();
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const [deleteComment, { loading: deleteCommentLoading }] = useMutation(
    DELETE_COMMENT,
    { refetchQueries: [CASE_COMMENTS] }
  );

  const handleDeleteExistingComment = commentId => {
    deleteComment({
      variables: { commentId },
      onCompleted: ({ deleteComment: deleteCommentResult }) => {
        if (deleteCommentResult?.errors) {
          deleteCommentResult.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  return {
    shouldShowComments,
    showComments,
    showNotes,
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    createOrUpdateCaseCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    caseCommentsLoading,
    caseCommentsError
  };
};
