import { <PERSON><PERSON><PERSON><PERSON>ead<PERSON>, Spinner } from "@q-centrix/q-components-react";
import QuestionnaireRow from "./QuestionnaireRow";
import QuestionnaireHeader from "./QuestionnaireHeader";
import { useComponentLogic } from "./hooks";
import { apolloClient } from "../../../../../../base";
import { ApolloProvider } from "@apollo/client";

const client = apolloClient("/api/registries/graphql");

// eslint-disable-next-line complexity, max-statements
const QuestionnairesSection = props => {
  const {
    toggleQuestionnaireStatus,
    isRegulatoryCase,
    patientId,
    isOncologyCase
  } = props;
  const {
    data,
    isLoading,
    called,
    error,
    refetchCaseQuestionnaires,
    caseResponses
  } = useComponentLogic(props);

  let cardContent = null;

  if (isLoading) {
    cardContent = (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10">
        <Spinner />
      </div>
    );
  }

  if (error) {
    cardContent = (
      <p className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500">
        Error: {error.message}
      </p>
    );
  }

  if (!error && !isLoading) {
    const questionnaires = data?.caseQuestionnaries?.map(
      ({
        id,
        lastEdited,
        locked,
        samplingStatus,
        name,
        lastUpdated,
        user,
        completionStatus,
        path
      }) => (
        <QuestionnaireRow
          key={id}
          questionnaire={name}
          lastEdited={lastEdited}
          locked={locked}
          samplingStatus={samplingStatus}
          lastUpdated={lastUpdated}
          user={user}
          completionStatus={completionStatus}
          path={path}
          refetchCaseQuestionnaires={refetchCaseQuestionnaires}
          toggleQuestionnaireStatus={toggleQuestionnaireStatus}
          isRegulatoryCase={isRegulatoryCase}
          patientId={patientId}
          caseResponses={caseResponses}
          isOncologyCase={isOncologyCase}
        />
      )
    );

    cardContent = questionnaires.length ? (
      questionnaires
    ) : (
      <p className="tw-text-md tw-flex tw-items-center tw-justify-center tw-p-4 tw-font-semibold tw-text-gray-700">
        No Questionnaires
      </p>
    );
  }

  return (
    <ApolloProvider client={client}>
      <CardWithHeader
        headerContent={
          <QuestionnaireHeader isRegulatoryCase={isRegulatoryCase} />
        }
        headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
        cardClasses="!tw-overflow-visible"
        bodyClasses="tw-rounded-b-[5px] tw-overflow-hidden"
        loading={isLoading}
        loaded={!isLoading && called}
      >
        {cardContent}
      </CardWithHeader>
    </ApolloProvider>
  );
};

export default QuestionnairesSection;
