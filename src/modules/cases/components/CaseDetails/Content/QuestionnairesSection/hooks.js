import {
  GET_CASE_RESPONSES,
  GET_CASE_QUESTIONNAIRES
} from "modules/cases/components/CaseDetails/graphql/query";
import { useQuery } from "@apollo/client";
import apolloClient from "base/apolloClient";
import { isNullOrEmpty } from "utils/fp/isNullOrEmpty";
import { useState } from "react";
import { and } from "ramda";

const client = apolloClient("/qapps/graphql");

export const useComponentLogic = ({ caseId, patientId, isOncologyCase }) => {
  const [isCaseQuestionnairesCompleted, setCaseQuestionnairesCompleted] =
    useState(false);
  const [isCaseResponsesQueryCompleted, setCaseResponsesQueryCompleted] =
    useState(false);

  const {
    data,
    called,
    loading,
    error,
    refetch: refetchCaseQuestionnaires
  } = useQuery(GET_CASE_QUESTIONNAIRES, {
    variables: {
      caseId: Number(caseId)
    },
    onCompleted: () => {
      setCaseQuestionnairesCompleted(true);
    }
  });

  const { data: { caseResponses } = {} } = useQuery(GET_CASE_RESPONSES, {
    variables: { patientId },
    skip: isNullOrEmpty(patientId) || !isOncologyCase,
    client,
    onCompleted: () => {
      setCaseResponsesQueryCompleted(true);
    }
  });

  return {
    data,
    isLoading: isOncologyCase
      ? !and(isCaseQuestionnairesCompleted, isCaseResponsesQueryCompleted)
      : loading,
    called,
    error,
    refetchCaseQuestionnaires,
    caseResponses
  };
};
