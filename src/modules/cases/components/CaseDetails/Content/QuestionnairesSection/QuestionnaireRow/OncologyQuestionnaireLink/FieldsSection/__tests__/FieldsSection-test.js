import { create } from "react-test-renderer";
import FieldsSection from "..";

describe("FieldsSection", () => {
  const caseResponses = [
    {
      caseStatus: "pending_initial_reabstraction",
      qrId: "1",
      qrAnswers: [
        {
          prompt: "Medical Record Number",
          value: null,
          fieldName: "medicalRecordNumber"
        },
        { prompt: "Name--Last", value: "Lamp", fieldName: "nameLast" },
        { prompt: "Name--First", value: "Lila", fieldName: "nameFirst" },
        {
          prompt: "Date of Birth",
          value: "1998-03-03",
          fieldName: "dateOfBirth"
        },
        {
          prompt: "Primary Site",
          value: "C000|External lip upper",
          fieldName: "primarySite"
        },
        {
          prompt: "Histologic Type/Behavior Code ICD-O-3",
          value: "8000/1|Neoplasm",
          fieldName: "morphTypebehavIcdO3"
        },
        {
          prompt: "Date of Diagnosis",
          value: "2024-10-03",
          fieldName: "dateOfDiagnosis"
        }
      ],
      definitiveFacilityName: "Inova Large Medical Center"
    },
    {
      caseStatus: "billable",
      qrId: "2",
      qrAnswers: [
        {
          prompt: "Medical Record Number",
          value: null,
          fieldName: "medicalRecordNumber"
        },
        { prompt: "Name--Last", value: "Lamp", fieldName: "nameLast" },
        { prompt: "Name--First", value: "Lila", fieldName: "nameFirst" },
        {
          prompt: "Date of Birth",
          value: "1998-03-03",
          fieldName: "dateOfBirth"
        },
        {
          prompt: "Primary Site",
          value: "C000|External lip upper",
          fieldName: "primarySite"
        },
        {
          prompt: "Histologic Type/Behavior Code ICD-O-3",
          value: "8000/1|Neoplasm",
          fieldName: "morphTypebehavIcdO3"
        },
        {
          prompt: "Date of Diagnosis",
          value: "2024-10-03",
          fieldName: "dateOfDiagnosis"
        }
      ],
      definitiveFacilityName: "Inova Large Medical Center"
    }
  ];

  const qrId = "1";

  function render() {
    return create(<FieldsSection caseResponses={caseResponses} qrId={qrId} />);
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
