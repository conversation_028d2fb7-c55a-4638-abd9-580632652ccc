import { cleanURL, isNullOrEmpty } from "utils/fp";
import { serverURI } from "base/constants";
import {
  Button,
  SummaryPopUp,
  ConfirmationModal,
  InputFeedbackMessage
} from "@q-centrix/q-components-react";
import FieldsSection from "./FieldsSection";
import FacilitiesHeader from "./FacilitiesHeader";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const OncologyQuestionnaireLink = props => {
  const { content, path, caseResponses } = props;
  const {
    toggleModal,
    isModalOpen,
    radioSelection,
    handleRadioSelection,
    handleCloseConfirmationModal,
    handleOpenConfirmationModal,
    isConfirmationModalOpen,
    isInitialPath,
    handleNavigateButtonClick,
    isNavigating
  } = useComponentLogic(props);

  return isInitialPath ? (
    <>
      <p
        onClick={toggleModal}
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
      >
        {content}
      </p>

      <>
        <SummaryPopUp
          isOpen={isModalOpen}
          title=" Copy an existing abstraction?"
        >
          {isNullOrEmpty(caseResponses) ? (
            <p className="tw-py-5 tw-font-inter tw-text-[11px] tw-font-semibold">
              No matches found. Create an independent abstraction.
            </p>
          ) : (
            <>
              <p className="tw-py-5 tw-font-inter tw-text-[11px] tw-font-semibold">
                Select an abstract to view details.
              </p>
              <div className="tw-flex tw-flex-col tw-gap-2">
                {caseResponses?.map(caseResponse => (
                  <FacilitiesHeader
                    key={caseResponse.qrId}
                    caseResponse={caseResponse}
                    handleRadioSelection={handleRadioSelection}
                    qrId={radioSelection}
                  />
                ))}
              </div>
            </>
          )}
          {radioSelection ? (
            <>
              <div className="tw-pt-2">
                <InputFeedbackMessage
                  type="neutral"
                  text="The following data fields will be used:"
                />
              </div>
              <FieldsSection
                caseResponses={caseResponses}
                qrId={radioSelection}
              />
            </>
          ) : null}

          <div className="tw-flex tw-flex-row tw-gap-5 tw-pt-2.5">
            <Button
              onClick={handleOpenConfirmationModal}
              bg="qcBlue"
              disabled={!radioSelection}
            >
              Copy this abstract
            </Button>
            <Button bg="qcLightBlue">
              <a
                href={cleanURL(`${serverURI}${path}`)}
                className="tw-cursor-pointer"
              >
                Create independent abstraction
              </a>
            </Button>
            <Button bg="qcNeutral" onClick={toggleModal}>
              Cancel
            </Button>
          </div>
        </SummaryPopUp>

        <ConfirmationModal
          title="Create a copy of the selected abstract?"
          isOpen={isConfirmationModalOpen}
        >
          <p
            className={`tw-pt-5 tw-font-inter  ${
              isNavigating ? "tw-opacity-40" : ""
            }`}
          >
            If any existing data for this response conflicts with data you are
            copying, the copied data will be preserved.
          </p>
          <div className="tw-flex tw-flex-row tw-justify-between tw-gap-4 tw-align-middle">
            <Button
              onClick={handleCloseConfirmationModal}
              bg="qcNeutral"
              disabled={isNavigating}
              customStyle="tw-w-full disabled:hover:tw-shadow-none"
            >
              <i className="fa fa-xmark tw-pr-[12px]" />
              Cancel
            </Button>
            <Button
              bg="qcBlueGradient"
              onClick={handleNavigateButtonClick}
              customStyle="tw-w-full"
            >
              <div className="tw-flex tw-items-center">
                {isNavigating ? (
                  <>
                    <div className="tw-flex tw-flex-row">
                      <i className="fas fa-spinner fa-spin " />
                    </div>
                    <p className="tw-pl-[12px]">Copying ...</p>
                  </>
                ) : (
                  <>
                    <i className="fa fa-arrow-right tw-pr-[12px]" />
                    I&apos;m sure
                  </>
                )}
              </div>
            </Button>
          </div>
        </ConfirmationModal>
      </>
    </>
  ) : (
    <>
      <a
        href={cleanURL(`${serverURI}${path}`)}
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
      >
        {content}
      </a>
    </>
  );
};

export default OncologyQuestionnaireLink;
