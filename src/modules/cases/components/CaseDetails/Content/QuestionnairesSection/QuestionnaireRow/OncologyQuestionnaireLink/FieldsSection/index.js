import { useComponentLogic } from "./hooks";
import Field from "./Field";

const FieldsSection = props => {
  const { patientDisplayFields, facilityDisplayFields } =
    useComponentLogic(props);

  return (
    <div className="tw-mb-5 tw-flex tw-flex-col">
      <div className="tw-my-2 tw-w-full tw-border tw-border-qcNeutrals-400 tw-bg-qcNeutrals-200 tw-p-3">
        {patientDisplayFields.map(patientDisplayField => (
          <Field key={patientDisplayField} displayField={patientDisplayField} />
        ))}
      </div>
      <div className="tw-w-full tw-border tw-border-qcNeutrals-400 tw-bg-qcNeutrals-200 tw-p-3 ">
        {facilityDisplayFields.map(facilityDisplayField => (
          <Field
            key={facilityDisplayField}
            displayField={facilityDisplayField}
          />
        ))}
      </div>
    </div>
  );
};

export default FieldsSection;
