import { RadioButton, Tag } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import classnames from "classnames";

const FacilitiesHeader = props => {
  const { caseResponse, handleRadioSelection, qrId } = props;
  const { tagStatus, formattedCaseStatus } = useComponentLogic(props);

  const caseStatusClases = classnames({
    success: tagStatus === "billable",
    warning: tagStatus === "pending",
    danger: tagStatus === "ineligible"
  });

  return (
    <div className="tw-flex tw-flex-col">
      <div className="tw-flex tw-w-full tw-flex-row tw-justify-between">
        <RadioButton
          name="facility"
          label={caseResponse.definitiveFacilityName}
          value={caseResponse.qrId}
          checked={caseResponse.qrId === qrId}
          onChange={handleRadioSelection}
        />
        <Tag
          text={formattedCaseStatus}
          status={caseStatusClases}
          className="tw-text-[11px] tw-font-semibold"
        />
      </div>
    </div>
  );
};

export default FacilitiesHeader;
