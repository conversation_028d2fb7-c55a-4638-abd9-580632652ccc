import { useMutation } from "@apollo/client";
import { format } from "date-fns-tz";
import { UPDATE_QUESTIONNAIRE_RESPONSE_LOCK_STATUS } from "../../../graphql/mutation";
import { last, pipe, split, isNil, equals, ifElse, prop, and } from "ramda";
import { useEffect } from "react";
import { useToast } from "@q-centrix/q-components-react";
import { userFeatureToggles } from "modules/facility-groups/utils/userFeatureToggles";

const getQuestionnaireId = pipe(split("%2F"), last);

// eslint-disable-next-line max-statements, complexity
export const useComponentLogic = ({
  samplingStatus,
  lastUpdated,
  locked,
  path,
  toggleQuestionnaireStatus,
  refetchCaseQuestionnaires,
  isRegulatoryCase,
  isOncologyCase,
  caseResponses
}) => {
  useEffect(() => toggleQuestionnaireStatus(locked), [locked]);

  const [handleChangeQuestionnaireLockStatus] = useMutation(
    UPDATE_QUESTIONNAIRE_RESPONSE_LOCK_STATUS
  );

  const { toast } = useToast();

  const questionnaireId = getQuestionnaireId(path);

  const handleChangeLockStatusMutation = () => {
    const loadingToast = toast({
      variant: "loading",
      description: "Changing Lock Status..."
    });

    handleChangeQuestionnaireLockStatus({
      variables: {
        questionnaireResponseId: Number(questionnaireId),
        locked: !locked
      },
      onCompleted: ({ updateQuestionnaireResponseLockStatus }) => {
        ifElse(
          pipe(prop("successful"), equals(true)),
          () => {
            refetchCaseQuestionnaires();
            loadingToast.update({
              variant: "success",
              description: "Lock Status Changed Successfully!"
            });
          },
          questionnaireData => {
            loadingToast.update({
              variant: "error",
              description: prop("errors")(questionnaireData)
            });
          }
        )(updateQuestionnaireResponseLockStatus);
      },
      onError: error => {
        loadingToast.update({
          description: error.message,
          variant: "error"
        });
      }
    });
  };

  const lockStatusPill = {
    text: locked ? "Locked" : "Unlocked",
    color: locked ? "danger" : "success",
    shouldRender: !isNil(locked) && isRegulatoryCase
  };

  const samplingStatusPill = {
    text: samplingStatus ? "Sampled" : "Not Sampled",
    color: samplingStatus ? "success" : "danger",
    shouldRender: !isNil(samplingStatus)
  };

  const editInfoText = lastUpdated
    ? `Last Edited on ${format(new Date(lastUpdated), "MM/dd/yyyy h:mma z")}`
    : "";

  const { isFeatureEnabled } = userFeatureToggles();
  const isSharedAbstractionMatching = isFeatureEnabled(
    "Shared Abstraction Matching"
  );

  const shouldShowCopyModal = and(isOncologyCase, isSharedAbstractionMatching);

  const renderOncologyLink = and(shouldShowCopyModal, caseResponses);

  return {
    editInfoText,
    lockStatusPill,
    samplingStatusPill,
    handleChangeLockStatusMutation,
    renderOncologyLink
  };
};
