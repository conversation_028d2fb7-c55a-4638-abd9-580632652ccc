// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FieldsSection it renders component correctly 1`] = `
<div
  className="tw-mb-5 tw-flex tw-flex-col"
>
  <div
    className="tw-my-2 tw-w-full tw-border tw-border-qcNeutrals-400 tw-bg-qcNeutrals-200 tw-p-3"
  >
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        Last Name
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        Lamp
      </p>
    </div>
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        First Name
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        Lila
      </p>
    </div>
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        DOB
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        1998-03-03
      </p>
    </div>
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        Cancer Type
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        C000|External lip upper
      </p>
    </div>
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        Histology/Behavior
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        8000/1|Neoplasm
      </p>
    </div>
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        Date of Diagnosis
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      >
        2024-10-03
      </p>
    </div>
  </div>
  <div
    className="tw-w-full tw-border tw-border-qcNeutrals-400 tw-bg-qcNeutrals-200 tw-p-3 "
  >
    <div
      className="tw-flex tw-flex-row tw-gap-3 [&:not(:last-child)]:tw-pb-3"
    >
      <p
        className="tw-text-sm tw-font-semibold"
      >
        MRN
        :
      </p>
      <p
        className="tw-text-neutrals-700 tw-text-sm tw-font-normal"
      />
    </div>
  </div>
</div>
`;
