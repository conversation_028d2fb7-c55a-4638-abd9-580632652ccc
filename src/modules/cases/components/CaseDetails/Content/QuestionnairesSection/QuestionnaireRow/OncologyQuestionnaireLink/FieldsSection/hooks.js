import {
  find,
  propEq,
  map,
  pipe,
  filter,
  includes,
  prop,
  reduce,
  assoc
} from "ramda";
import { useMemo } from "react";
import { mfabDisplayFields } from "./mfabDisplayFieldsConfig";

const modalFields = reduce(
  (acc, section) =>
    reduce(
      (innerAcc, field) => assoc(field.field, field.label, innerAcc),
      acc,
      section.fields
    ),
  {},
  mfabDisplayFields.sections
);

const patientFieldsToPick = map(
  prop("field"),
  mfabDisplayFields.sections[0].fields
);

const facilityFieldsToPick = map(
  prop("field"),
  mfabDisplayFields.sections[1].fields
);

const extractFields = (fieldsToPick, qrAnswers) =>
  pipe(
    filter(answer => includes(answer.fieldName, fieldsToPick)),
    map(answer => ({
      label: modalFields[answer.fieldName],
      value: answer.value
    }))
  )(qrAnswers);

export const useComponentLogic = props => {
  const { caseResponses, qrId } = props;

  const findFieldsByQrId = (id, data) => {
    const caseResponse = find(propEq("qrId", id))(data);

    if (!caseResponse) return { patientFields: [], facilityFields: [] };

    const { qrAnswers } = caseResponse;

    return {
      patientFields: extractFields(patientFieldsToPick, qrAnswers),
      facilityFields: extractFields(facilityFieldsToPick, qrAnswers)
    };
  };

  const result = useMemo(
    () => findFieldsByQrId(qrId, caseResponses),
    [qrId, caseResponses]
  );

  const patientDisplayFields = result.patientFields;
  const facilityDisplayFields = result.facilityFields;

  return { patientDisplayFields, facilityDisplayFields };
};
