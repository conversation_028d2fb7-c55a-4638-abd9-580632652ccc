import { useComponentLogic } from "./hooks";
import { cleanURL } from "utils/fp";
import { serverURI } from "base/constants";

const QuestionnaireLink = ({ content, path }) => {
  const { clicked, handleClick } = useComponentLogic();

  return clicked ? (
    <p className="tw-text-sm tw-font-semibold tw-text-gray-700 tw-underline">
      {content}
    </p>
  ) : (
    <a
      href={cleanURL(`${serverURI}${path}`)}
      onClick={handleClick}
      className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
    >
      {content}
    </a>
  );
};

export default QuestionnaireLink;
