import { create } from "react-test-renderer";
import QuestionnaireLink from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Tag: "Tag"
}));

jest.mock("date-fns", () => ({
  format: () => "10/27/2023 3:53PM GMT-5"
}));

describe("QuestionnaireLink", () => {
  function render(props = {}) {
    return create(<QuestionnaireLink {...props} />);
  }

  test("it renders component correctly", () => {
    const component = render({
      questionnaire: "Abstraction Questionnaire",
      locked: false,
      samplingStatus: null,
      lastUpdated: "2023-10-27T15:53:00-05:00",
      user: {
        fullName: "Russell Re<PERSON>"
      },
      completionStatus: null,
      path: "/"
    });

    expect(component).toMatchSnapshot();
  });
});
