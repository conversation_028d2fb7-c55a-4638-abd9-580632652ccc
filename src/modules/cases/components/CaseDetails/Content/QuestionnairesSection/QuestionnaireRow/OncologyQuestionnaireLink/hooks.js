import { not, pipe, split, includes, and } from "ramda";
import { useCallback, useState } from "react";
import { cleanURL } from "utils/fp";
import { serverURI } from "base/constants";

const checkIfInitialPath = pipe(
  split("/"),
  includes("admin"),
  and(includes("visits"))
);

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { path } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  const [radioSelection, setRadioSelection] = useState();

  const handleRadioSelection = useCallback(e => {
    setRadioSelection(e.target.value);
  });

  const handleNavigateButtonClick = () => {
    if (isNavigating) return;

    setIsNavigating(true);
    window.location.href = `${cleanURL(
      `${serverURI}${path}`
    )}&donor_qr_id=${radioSelection}`;
  };

  const toggleModal = useCallback(() => setIsModalOpen(not), []);

  const isInitialPath = checkIfInitialPath(path);

  const handleCloseConfirmationModal = useCallback(() => {
    setIsConfirmationModalOpen(not);
    setIsModalOpen(true);
  }, []);

  const handleOpenConfirmationModal = useCallback(() => {
    setIsConfirmationModalOpen(true);
    setIsModalOpen(false);
  }, []);

  return {
    toggleModal,
    isModalOpen,
    radioSelection,
    handleRadioSelection,
    isConfirmationModalOpen,
    handleCloseConfirmationModal,
    handleOpenConfirmationModal,
    isInitialPath,
    handleNavigateButtonClick,
    isNavigating
  };
};
