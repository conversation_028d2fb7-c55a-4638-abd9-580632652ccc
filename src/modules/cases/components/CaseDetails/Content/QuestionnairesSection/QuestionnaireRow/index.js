import { Tag } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import QuestionnaireLink from "./QuestionnaireLink";
import OncologyQuestionnaireLink from "./OncologyQuestionnaireLink";

// eslint-disable-next-line complexity
const QuestionnaireRow = props => {
  const { questionnaire, path, patientId, caseResponses } = props;

  const {
    editInfoText,
    lockStatusPill,
    samplingStatusPill,
    handleChangeLockStatusMutation,
    renderOncologyLink
  } = useComponentLogic(props);

  return (
    <div className="tw-flex tw-items-center tw-justify-between tw-self-stretch tw-border-b tw-px-5 tw-py-2.5 tw-text-sm last:tw-border-0">
      <div className="tw-flex tw-basis-[660px] tw-items-center tw-gap-5">
        {renderOncologyLink ? (
          <OncologyQuestionnaireLink
            content={questionnaire}
            path={path}
            patientId={patientId}
            caseResponses={caseResponses}
          />
        ) : (
          <QuestionnaireLink content={questionnaire} path={path} />
        )}

        <p className="tw-text-xs tw-text-gray-800">{editInfoText}</p>
      </div>
      <div className="tw-min-w-[100px]">
        {lockStatusPill.shouldRender && (
          <Tag
            text={lockStatusPill.text}
            status={lockStatusPill.color}
            className="tw-text-xs"
            onClick={handleChangeLockStatusMutation}
          />
        )}
      </div>
      <div className="tw-min-w-[100px]">
        {samplingStatusPill.shouldRender && (
          <Tag
            text={samplingStatusPill.text}
            status={samplingStatusPill.color}
            className="tw-text-xs"
          />
        )}
      </div>
    </div>
  );
};

export default QuestionnaireRow;
