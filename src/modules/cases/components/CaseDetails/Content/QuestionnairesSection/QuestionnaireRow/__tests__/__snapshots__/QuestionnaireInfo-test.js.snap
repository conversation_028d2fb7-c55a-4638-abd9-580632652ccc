// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuestionnaireInfo it renders component correctly 1`] = `
<div
  className="tw-flex tw-items-center tw-justify-between tw-self-stretch tw-border-b tw-px-5 tw-py-2.5 tw-text-sm last:tw-border-0"
>
  <div
    className="tw-flex tw-basis-[660px] tw-items-center tw-gap-5"
  >
    <a
      className="tw-border-b tw-border-b-qc-blue-700 tw-font-semibold tw-text-qc-blue-700 hover:tw-cursor-pointer"
      href="/"
    >
      Abstraction Questionnaire
    </a>
    <p
      className="tw-text-xs tw-text-gray-800"
    >
      Last Edited on 10/27/2023 3:53PM GMT-5
    </p>
  </div>
  <div
    className="tw-min-w-[100px]"
  >
    <Tag
      className="tw-text-xs"
      status="success"
      text="Unlocked"
    />
  </div>
  <div
    className="tw-min-w-[100px]"
  />
</div>
`;
