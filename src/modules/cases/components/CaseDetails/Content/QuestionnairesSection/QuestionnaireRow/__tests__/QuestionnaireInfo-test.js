import { create } from "react-test-renderer";
import QuestionnaireInfo from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Tag: "Tag"
}));

jest.mock("date-fns", () => ({
  format: () => "10/27/2023 3:53PM GMT-5"
}));

describe("QuestionnaireInfo", () => {
  function render(props = {}) {
    return create(<QuestionnaireInfo {...props} />);
  }

  test.skip("it renders component correctly", () => {
    const component = render({
      questionnaire: "Abstraction Questionnaire",
      locked: false,
      samplingStatus: null,
      lastUpdated: "2023-10-27T15:53:00-05:00",
      user: {
        fullName: "<PERSON>"
      },
      completionStatus: null,
      path: "/"
    });

    expect(component).toMatchSnapshot();
  });
});
