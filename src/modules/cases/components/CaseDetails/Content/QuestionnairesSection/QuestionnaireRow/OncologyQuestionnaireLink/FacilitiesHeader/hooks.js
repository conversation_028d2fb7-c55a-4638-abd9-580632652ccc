import { useMemo } from "react";
import {
  includes,
  always,
  T,
  cond,
  pipe,
  split,
  adjust,
  toUpper,
  join,
  replace,
  map,
  splitAt
} from "ramda";

const capitalize = pipe(split(""), adjust(0, toUpper), join(""));

const moreThanTwoWords = pipe(
  splitAt(2),
  ([firstTwoWords, rest]) => `${join("-", firstTwoWords)} ${join(" ", rest)}`
);

const formatString = pipe(
  replace(/[_-]/g, " "),
  split(" "),
  map(capitalize),
  words => (words.length === 1 ? words[0] : moreThanTwoWords(words))
);

const checkStatus = cond([
  [includes("billable"), always("billable")],
  [includes("pending"), always("pending")],
  [includes("ineligible"), always("ineligible")],
  [T, always("unknown")]
]);

export const useComponentLogic = props => {
  const { caseResponse } = props;
  const caseStatus = caseResponse?.caseStatus;
  const tagStatus = useMemo(
    () => checkStatus(caseStatus.toLowerCase()),
    [caseStatus]
  );
  const formattedCaseStatus = useMemo(
    () => formatString(caseStatus),
    [caseStatus]
  );

  return {
    caseStatus,
    checkStatus,
    tagStatus,
    formattedCaseStatus
  };
};
