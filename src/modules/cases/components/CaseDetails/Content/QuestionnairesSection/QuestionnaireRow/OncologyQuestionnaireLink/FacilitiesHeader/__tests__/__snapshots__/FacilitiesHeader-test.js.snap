// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FacilitiesHeader it renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-col"
>
  <div
    className="tw-flex tw-w-full tw-flex-row tw-justify-between"
  >
    <div
      className="tw-inline-flex tw-items-center tw-gap-2.5"
    >
      <input
        checked={false}
        className="tw-appearance-none before:tw-block before:tw-h-[16px] before:tw-w-[16px] before:tw-rounded-full before:tw-transition-shadow before:tw-ease-in-out before:tw-duration-200 before:tw-shadow-[inset_0px_0px_0px_2px_#005f86]"
        id="facility-1"
        name="facility"
        type="radio"
        value="1"
      />
      <label
        className="tw-text-black-70 tw-text-sm"
        htmlFor="facility-1"
      >
        Inova Large Medical Center
      </label>
    </div>
    <button
      className="tw-flex tw-justify-center tw-items-center !tw-h-6 tw-min-w-[65px] !tw-w-fit tw-py-[5px] tw-px-[15px] tw-border-2 tw-rounded-xl tw-font-sans tw-text-sm tw-font-extrabold hover:tw-cursor-pointer tw-text-[11px] tw-font-semibold !tw-border-warning-700 tw-text-warning-700 !tw-bg-warning-50"
    >
      Pending-Initial Reabstraction
    </button>
  </div>
</div>
`;
