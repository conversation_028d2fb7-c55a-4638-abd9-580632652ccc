import { create } from "react-test-renderer";
import FacilitiesHeader from "..";

describe("FacilitiesHeader", () => {
  const caseResponse = {
    caseStatus: "pending_initial_reabstraction",
    qrId: "1",
    qrAnswers: [
      { sequenceNumberHospital: "Test Hospital 1" },
      { primarySite: "Eye" },
      { morphTypebehavIcdO3: "09-04" },
      { nameLast: "Smith" },
      { nameFirst: "Kate" },
      { dateOfBirth: "01/01/1987" },
      { dateOfDiagnosis: "01/01/2024" }
    ],
    definitiveFacilityName: "Inova Large Medical Center"
  };

  const radioSelection = "1";

  function render() {
    return create(
      <FacilitiesHeader
        caseResponse={caseResponse}
        radioSelection={radioSelection}
      />
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
