import { create } from "react-test-renderer";
import Header from "..";

describe("Header", () => {
  function render(isRegulatoryCase = true) {
    return create(<Header isRegulatoryCase={isRegulatoryCase} />);
  }

  test("it renders component correctly for regulatory case", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly for non-regulatory case", () => {
    const component = render(false);

    expect(component).toMatchSnapshot();
  });
});
