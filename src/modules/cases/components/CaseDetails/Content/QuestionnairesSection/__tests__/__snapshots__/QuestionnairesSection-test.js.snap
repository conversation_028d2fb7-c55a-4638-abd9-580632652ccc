// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QuestionnairesSection it renders component correctly 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px] tw-overflow-hidden"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={<QuestionnaireHeader />}
  loaded={true}
  loading={false}
>
  <QuestionnaireRow
    lastUpdated="2023-10-27T15:53:00-05:00"
    locked={false}
    path="/"
    questionnaire="Abstraction Questionnaire"
    refetchCaseQuestionnaires={[Function]}
    samplingStatus={true}
    user={
      Object {
        "fullName": "Russell Reas",
      }
    }
  />
</CardWithHeader>
`;

exports[`QuestionnairesSection it renders component correctly with no questionnaires 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px] tw-overflow-hidden"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={<QuestionnaireHeader />}
  loaded={true}
  loading={false}
>
  <p
    className="tw-text-md tw-flex tw-items-center tw-justify-center tw-p-4 tw-font-semibold tw-text-gray-700"
  >
    No Questionnaires
  </p>
</CardWithHeader>
`;

exports[`QuestionnairesSection it renders component loading 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px] tw-overflow-hidden"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={<QuestionnaireHeader />}
  loaded={false}
  loading={true}
>
  <div
    className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10"
  >
    <Spinner />
  </div>
</CardWithHeader>
`;
