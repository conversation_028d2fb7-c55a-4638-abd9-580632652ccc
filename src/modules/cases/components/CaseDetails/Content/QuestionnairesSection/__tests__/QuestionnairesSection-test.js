import { create, act } from "react-test-renderer";
import wait from "waait";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import mocks from "../../../graphql/mocks";
import QuestionnairesSection from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  Spinner: "Spinner"
}));

jest.mock("../QuestionnaireRow", () => "QuestionnaireRow");

describe("QuestionnairesSection", () => {
  function render(apolloMocks = mocks) {
    return create(
      decoratedApollo({
        component: QuestionnairesSection,
        props: {
          caseId: 1
        },
        initialValues: {},
        initialAppValues: {},
        apolloMocks
      })
    );
  }

  test("it renders component loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(200));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with no questionnaires", async () => {
    const mocksWithNoQuestionnaires = mocks.map(mock => {
      if (
        mock.request.query.definitions[0].name.value === "caseQuestionnaries"
      ) {
        return {
          ...mock,
          result: {
            data: {
              caseQuestionnaries: []
            }
          }
        };
      }

      return mock;
    });

    const component = render(mocksWithNoQuestionnaires);

    await act(() => wait(200));

    expect(component).toMatchSnapshot();
  });
});
