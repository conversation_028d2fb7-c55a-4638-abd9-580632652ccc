import { useQuery, useMutation } from "@apollo/client";
import { useNavigate, useParams } from "react-router-dom";
import {
  GET_CASE_DETAILS,
  GET_CASE_QUESTIONNAIRES,
  GET_TASKS_DATA
} from "modules/cases/components/CaseDetails/graphql/query";
import {
  DELETE_CASES,
  UPDATE_CASE
} from "modules/cases/components/CaseDetails/graphql/mutation";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import formSchema from "./validationSchema";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import accountSettingsSelectors from "modules/app/redux/selectors/accountSettings";
import facilitySelectors from "modules/app/redux/selectors/facility";
import { useToast } from "@q-centrix/q-components-react";
import {
  always,
  and,
  any,
  applySpec,
  head,
  ifElse,
  isEmpty,
  map,
  path,
  pathOr,
  pipe,
  propEq,
  propOr,
  reduce,
  split,
  toPairs
} from "ramda";
import { isNullOrEmpty } from "utils/fp";
import { CREATE_OR_UPDATE_CASE_COMMENT } from "./CommentsAndNotesSection/graphql/mutations";
import { CASE_COMMENTS } from "./CommentsAndNotesSection/graphql/queries";
import { GET_CURRENT_USER_DATA } from "shared/graphql/query";
import { parse } from "date-fns";

const translateOption = (labelPath, valuePath) =>
  ifElse(
    and(path(labelPath), path(valuePath)),
    applySpec({
      label: path(labelPath),
      value: path(valuePath)
    }),
    always(null)
  );

const translateOptionKeepingExtraData = objectPath =>
  pipe(
    path(objectPath),
    toPairs,
    reduce((acc, [key, value]) => {
      if (key === "name") acc.label = value;
      else if (key === "id") acc.value = value;
      else acc[key] = value;
      return acc;
    }, {})
  );

const translateDate = datePath =>
  pipe(
    path(datePath),
    ifElse(
      isNullOrEmpty,
      always(null),
      pipe(split("T"), head, val => parse(val, "yyyy-MM-dd", new Date()))
    )
  );

// eslint-disable-next-line max-statements, complexity
export const useComponentLogic = ({
  shouldShowCaseReassignment,
  toggleDeleteModal,
  toggleCaseReassignment
}) => {
  const loggedInUser = useSelector(accountSettingsSelectors.accountSettings);
  const facility = useSelector(facilitySelectors.facility);
  const [editMode, setEditMode] = useState(false);
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true);
  const [questionnaireLocked, setQuestionnaireLocked] = useState(false);
  const { id: caseId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [caseReassignmentCommentValue, setCaseReassignmentCommentValue] =
    useState("");
  const [genericCaseTypeModalIsOpen, setGenericCaseTypeModalIsOpen] =
    useState(false);
  const [reason, setReason] = useState("");
  const handleReasonChange = useCallback(e => {
    setReason(e.target.value);
  }, []);

  const openGenericCaseTypeModal = () => setGenericCaseTypeModalIsOpen(true);

  const closeGenericCaseTypeModal = () => setGenericCaseTypeModalIsOpen(false);

  const handleCaseReassignmentCommentChange = e =>
    setCaseReassignmentCommentValue(e.target.value);

  const [
    createOrUpdateCaseComment,
    { loading: caseReassignmentCommentIsSubmitting }
  ] = useMutation(CREATE_OR_UPDATE_CASE_COMMENT, {
    refetchQueries: [CASE_COMMENTS]
  });

  const handleSubmitCaseReassignmentComment = () => {
    if (isEmpty(caseReassignmentCommentValue)) return;

    createOrUpdateCaseComment({
      variables: {
        caseId,
        body: caseReassignmentCommentValue.trim()
      },
      onCompleted: ({
        createOrUpdateCaseComment: createOrUpdateCaseCommentResult
      }) => {
        if (createOrUpdateCaseCommentResult?.errors) {
          createOrUpdateCaseCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          setCaseReassignmentCommentValue("");
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleCaseReassignmentSuccess = () => {
    handleSubmitCaseReassignmentComment();
    toggleCaseReassignment();
  };
  const {
    data: {
      caseDetails: {
        case: caseDetails,
        startEditingOnLoad,
        userHasTimeTrackingDisabled
      } = {}
    } = {},
    loading: caseDetailsLoading,
    called: caseDetailsCalled,
    error: caseDetailsError
  } = useQuery(GET_CASE_DETAILS, {
    variables: { id: Number(caseId) },
    onCompleted: () => {
      if (startEditingOnLoad) {
        setEditMode(true);
      }
    }
  });

  const { data: { currentUser } = {} } = useQuery(GET_CURRENT_USER_DATA);

  const { data: { caseTasks: tasks = [] } = {} } = useQuery(GET_TASKS_DATA, {
    variables: {
      caseId: Number(caseId)
    }
  });

  const formattedCaseStatus = useMemo(
    () =>
      caseDetails?.status === "Billable" && !currentUser?.isInternalUser
        ? "Complete"
        : caseDetails?.status,
    [caseDetails?.status, currentUser?.isInternalUser]
  );

  const formattedCaseStatusOptions = useMemo(
    () =>
      map(({ displayValue, value }) => ({
        label:
          value === "Billable" && !currentUser?.isInternalUser
            ? "Complete"
            : displayValue,
        value
      }))(caseDetails?.caseStatusOptions ?? []),
    [caseDetails?.caseStatusOptions, currentUser?.isInternalUser]
  );

  const hasAbstractionTask = useMemo(
    () => any(propEq("taskType", "Abstraction"))(tasks),
    [tasks]
  );

  const form = useForm({
    resolver: yupResolver(formSchema),
    values: applySpec({
      losOverride: propOr(false, "losOverride"),
      facilityId: translateOption(["facility", "name"], ["facility", "id"]),
      caseTypeId: translateOptionKeepingExtraData(["caseType"]),
      cancerSiteId: translateOption(
        ["cancerSite", "name"],
        ["cancerSite", "id"]
      ),
      visitNumber: pathOr("", ["visit", "number"]),
      lastName: pathOr("", ["patient", "lastName"]),
      bornOn: translateDate(["patient", "bornOn"]),
      mrn: pathOr("", ["patient", "mrn"]),
      firstName: pathOr("", ["patient", "firstName"]),
      gwtgId: pathOr("", ["gwtgId"]),
      middleName: pathOr("", ["patient", "middleName"]),
      firstContact: translateDate(["visit", "firstContact"]),
      arrivedAt: translateDate(["visit", "arrivedAt"]),
      admittedAt: translateDate(["visit", "admittedAt"]),
      surgeryDate: translateDate(["visit", "surgeryDate"]),
      hospitalDischargedAt: translateDate(["visit", "hospitalDischargedAt"])
    })(caseDetails)
  });

  const toggleEditMode = () => {
    setEditMode(!editMode);
  };

  const toggleQuestionnaireStatus = locked => {
    setQuestionnaireLocked(locked);
  };

  const [updateCase] = useMutation(UPDATE_CASE);

  const handleUpdateCase = caseData => {
    updateCase({
      variables: {
        caseId: Number(caseId),
        case: caseData
      },
      refetchQueries: [
        GET_CASE_DETAILS,
        GET_TASKS_DATA,
        GET_CASE_QUESTIONNAIRES
      ],
      onCompleted: ({ updateCase: updateCaseResult }) => {
        if (updateCaseResult?.errors) {
          updateCaseResult.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
        // backend returns boolean as string
        if (updateCaseResult.response === "true") {
          toggleEditMode();
          form.reset();
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleCancelEdit = () => {
    toggleEditMode();
    form.reset();
  };

  const [deleteCases] = useMutation(DELETE_CASES);

  const handleDeleteCase = () => {
    deleteCases({
      variables: {
        caseIds: [Number(caseId)],
        reason
      },
      onCompleted: () => {
        navigate("/cases");
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      // invalidate activitiy list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            caseList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
    toggleDeleteModal();
    setReason("");
  };

  const checkIfGenericCaseTypeModalShouldOpen = () => {
    if (caseDetails?.caseType?.generic && currentUser?.isInternalUser) {
      openGenericCaseTypeModal();
    }
  };

  const showTasksSection = !(
    currentUser?.isInternalUser && facility.standalone
  );

  const isRegulatoryCase = useMemo(
    () => path(["caseType", "isRegulatory"])(caseDetails),
    [caseDetails]
  );

  const patientId = path(["patient", "id"])(caseDetails);
  const isOncologyCase = path(["caseType", "isOncology"])(caseDetails);

  useEffect(() => {
    if (shouldShowCaseReassignment && !isRightPanelOpen) {
      setIsRightPanelOpen(true);
    }
  }, [shouldShowCaseReassignment, isRightPanelOpen]);

  return {
    isRightPanelOpen,
    setIsRightPanelOpen,
    genericCaseTypeModalIsOpen,
    checkIfGenericCaseTypeModalShouldOpen,
    closeGenericCaseTypeModal,
    caseReassignmentCommentValue,
    handleCaseReassignmentCommentChange,
    handleCaseReassignmentSuccess,
    caseReassignmentCommentIsSubmitting,
    loggedInUser,
    caseId,
    form,
    caseDetails,
    caseDetailsLoading,
    caseDetailsCalled,
    caseDetailsError,
    editMode,
    questionnaireLocked,
    toggleEditMode,
    handleUpdateCase,
    handleCancelEdit,
    handleDeleteCase,
    toggleQuestionnaireStatus,
    hasAbstractionTask,
    formattedCaseStatus,
    formattedCaseStatusOptions,
    showTasksSection,
    isRegulatoryCase,
    userHasTimeTrackingDisabled,
    patientId,
    isOncologyCase,
    reason,
    handleReasonChange
  };
};
