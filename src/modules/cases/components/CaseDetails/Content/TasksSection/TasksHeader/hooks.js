import { useToast } from "@q-centrix/q-components-react";
import { useQuery, useMutation } from "@apollo/client";
import {
  GET_CASE_QUESTIONNAIRES,
  GET_CASE_TASK_TYPES,
  GET_TASKS_DATA
} from "modules/cases/components/CaseDetails/graphql/query";
import { CREATE_TASK_WITH_TASK_ENTRY } from "modules/cases/components/CaseDetails/graphql/mutation";
import { useMemo } from "react";

import { filter, map, pipe } from "ramda";

export const useComponentLogic = ({ caseId }) => {
  const { toast } = useToast();
  const {
    data: { caseTaskTypes = [] } = {},
    caseTaskTypesLoading,
    caseTaskTypesError
  } = useQuery(GET_CASE_TASK_TYPES, {
    variables: { caseId: Number(caseId) }
  });

  const [createTaskWithTaskEntry] = useMutation(CREATE_TASK_WITH_TASK_ENTRY);

  const handleCreateTaskWithTaskEntry = selection => {
    const taskType = selection.value;

    createTaskWithTaskEntry({
      variables: {
        taskType,
        caseId
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      refetchQueries: [
        { query: GET_TASKS_DATA, variables: { caseId: Number(caseId) } },
        { query: GET_CASE_TASK_TYPES, variables: { caseId: Number(caseId) } },
        {
          query: GET_CASE_QUESTIONNAIRES,
          variables: { caseId: Number(caseId) }
        }
      ]
    });
  };

  const caseTaskOptions = useMemo(
    () =>
      pipe(
        filter(taskType => !taskType.isDisabled),
        map(taskType => ({ label: taskType.name, value: taskType.name }))
      )(caseTaskTypes),
    [caseTaskTypes]
  );

  return {
    caseTaskOptions,
    caseTaskTypesLoading,
    caseTaskTypesError,
    handleCreateTaskWithTaskEntry
  };
};
