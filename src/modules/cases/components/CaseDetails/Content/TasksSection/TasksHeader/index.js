import { Dropdown } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const TasksHeader = ({ caseId }) => {
  const {
    caseTaskOptions,
    caseTaskTypesLoading,
    caseTaskTypesError,
    handleCreateTaskWithTaskEntry
  } = useComponentLogic({
    caseId
  });

  return (
    <div className="tw-flex tw-justify-between">
      <h3 className="tw-flex tw-items-center">Tasks</h3>
      {caseTaskTypesError ? (
        <p className="tw-text-sm tw-text-error-500">
          {`Error: ${caseTaskTypesError.message}`}
        </p>
      ) : (
        <Dropdown
          loading={caseTaskTypesLoading}
          options={caseTaskOptions}
          onChange={handleCreateTaskWithTaskEntry}
          iconClass="fa-solid fa-chevron-down"
          placeholder="Select New Task to Add"
          menuPlacement="auto"
        />
      )}
    </div>
  );
};

export default TasksHeader;
