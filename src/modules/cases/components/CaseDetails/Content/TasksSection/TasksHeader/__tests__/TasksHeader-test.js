import { create } from "react-test-renderer";
import TasksHeader from "..";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import mocks from "../../../../graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Dropdown: "Dropdown",
  useToast: () => ({ toast: jest.fn() })
}));

describe("TasksHeader", () => {
  function render() {
    return create(
      decoratedApollo({
        component: TasksHeader,
        props: { caseId: 1 },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
