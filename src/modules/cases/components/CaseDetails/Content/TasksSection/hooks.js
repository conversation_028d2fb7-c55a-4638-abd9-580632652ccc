import { GET_TASKS_DATA } from "../../graphql/query";
import { useQuery } from "@apollo/client";
import { UPDATED_USER_Q_POINTS_SUBSCRIPTION } from "../../graphql/subscription";

export const useComponentLogic = ({ caseId }) => {
  const {
    loading,
    called,
    error,
    data: { caseTasks: tasks = [] } = {},
    subscribeToMore
  } = useQuery(GET_TASKS_DATA, {
    variables: {
      caseId: Number(caseId)
    }
  });

  const subscribeToQpointsUpdates = taskId =>
    subscribeToMore({
      document: UPDATED_USER_Q_POINTS_SUBSCRIPTION,
      variables: {
        taskId
      },
      updateQuery: (prev, { subscriptionData }) => {
        if (!subscriptionData.data) return prev;
        const { updatedUserQpoints } = subscriptionData.data;

        return {
          ...prev,
          caseTasks: prev.caseTasks.map(task => {
            if (task.id === updatedUserQpoints.id) {
              return {
                ...task,
                qPointsComp: updatedUserQpoints.qPointsComp,
                canShowQPoints: updatedUserQpoints.canShowQPoints
              };
            }
            return task;
          })
        };
      }
    });

  return { tasks, loading, called, error, subscribeToQpointsUpdates };
};
