import {
  Button,
  ConfirmationModal,
  RadioButton
} from "@q-centrix/q-components-react";
import "styles/confirmation-modal.scss";
import { useComponentLogic } from "./hooks";

const MarkBillableConfirmationModal = props => {
  const { onCancel, isOpen } = props;
  const {
    billableValue,
    handleBillableRadioSelection,
    handleTaskAction,
    title,
    buttonText
  } = useComponentLogic(props);

  return (
    <ConfirmationModal title={title} isOpen={isOpen} appElement=".main">
      <div className="modal-content">
        <p className="tw-text-xs tw-font-semibold">
          Mark as the following status:
        </p>
        <div>
          <RadioButton
            name="billable"
            label="Completed - Billable"
            value="true"
            checked={billableValue === true}
            onChange={handleBillableRadioSelection}
          />
        </div>
        <div>
          <RadioButton
            name="billable"
            label="Completed - Non-Billable"
            value="false"
            checked={billableValue === false}
            onChange={handleBillableRadioSelection}
          />
        </div>
      </div>
      <div className="tw-flex tw-flex-row tw-justify-between">
        <Button
          onClick={onCancel}
          customStyle="tw-w-[120px] tw-h-10 tw-text-sm"
          outline
          bg="neutral"
        >
          Cancel
        </Button>
        <Button
          onClick={handleTaskAction}
          customStyle="tw-w-[130px] tw-h-10 tw-text-sm"
          bg="success"
        >
          {buttonText}
        </Button>
      </div>
    </ConfirmationModal>
  );
};

export default MarkBillableConfirmationModal;
