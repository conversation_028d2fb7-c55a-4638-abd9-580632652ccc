import {
  COMPLETE_TASK,
  UPDATE_TASK_BILLING_STATUS
} from "modules/cases/components/CaseDetails/graphql/mutation";
import { GET_TASKS_DATA } from "modules/cases/components/CaseDetails/graphql/query";
import { useMutation } from "@apollo/client";
import { useToast } from "@q-centrix/q-components-react";
import { useCallback, useMemo, useState } from "react";

const convertToBoolean = val => val === "true";

const updateData = {
  title: "Change Fallout Review Status?",
  button: "Change Status",
  successMessage: "Status updated successfully",
  mutation: UPDATE_TASK_BILLING_STATUS
};

const completedData = {
  title: "Mark Fallout Review Complete?",
  button: "Mark Complete",
  successMessage: "Task completed successfully",
  mutation: COMPLETE_TASK
};

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { taskId, onCancel, billable, completed } = props;
  const { toast } = useToast();
  const [billableValue, setBillableValue] = useState(billable);

  const handleBillableRadioSelection = useCallback(e => {
    setBillableValue(convertToBoolean(e.target.value));
  }, []);

  const getActionData = useMemo(
    () => (completed ? updateData : completedData),
    [completed]
  );

  const [taskMutation] = useMutation(getActionData.mutation, {
    refetchQueries: [GET_TASKS_DATA]
  });

  const handleTaskAction = () => {
    taskMutation({
      variables: { taskId, billable: billableValue },
      onCompleted: ({ taskMutation: taskMutationResults }) => {
        if (taskMutationResults?.errors) {
          taskMutationResults.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        } else {
          toast({
            variant: "success",
            description: getActionData.successMessage
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
    onCancel();
  };

  return {
    billableValue,
    handleBillableRadioSelection,
    handleTaskAction,
    title: getActionData.title,
    buttonText: getActionData.button
  };
};
