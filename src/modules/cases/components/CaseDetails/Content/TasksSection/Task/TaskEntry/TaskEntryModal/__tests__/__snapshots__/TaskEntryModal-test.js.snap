// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Save Modal renders component 1`] = `
<ConfirmationModal
  appElement="#psychiatric-patient-census-main"
  isDanger={false}
  isOpen={true}
  title="Your time is greater than 5 hours"
>
  <div
    className="modal-content"
  >
    <p
      className="modal-paragraph"
    >
      You've added a time entry longer than 5 hours. Confirm your time is accurate or edit your end time.
    </p>
    <div
      className="button-container "
    >
      <Button
        bg="neutral"
        customStyle="modal-buttons"
        onClick={[MockFunction]}
        outline={true}
      >
        <i
          className="fa-regular fa-x"
        />
        Edit
      </Button>
      <Button
        bg="success"
        customStyle="modal-buttons"
        onClick={[MockFunction]}
      >
        <i
          className="fa-regular fa-check"
        />
        Confirm
      </Button>
    </div>
  </div>
</ConfirmationModal>
`;
