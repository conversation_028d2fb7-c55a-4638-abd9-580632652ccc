// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TaskEntry it renders component correctly 1`] = `
<div
  className="tw-flex tw-items-center tw-border-t tw-border-gray-200 tw-bg-white tw-px-5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0"
>
  <i
    className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
  />
  <div
    className="tw-flex tw-grow tw-items-center tw-justify-between tw-gap-2 xl:!tw-gap-5"
  >
    <Field
      label="Date of Entry"
    >
      <p
        className="tw-font-normal"
      >
        Tue Sep 26, 2023
      </p>
    </Field>
    <div
      className="tw-flex tw-flex-col tw-gap-2 xl:tw-grow xl:!tw-flex-row xl:!tw-justify-evenly"
    >
      <Field
        label="Start Time"
      >
        <p
          className="tw-font-normal"
        >
          01:00:00 pm
        </p>
      </Field>
      <Field
        label="End Time"
      >
        <p
          className="tw-font-normal"
        >
          02:30:00 pm
        </p>
      </Field>
    </div>
    <Field
      label="Total Time"
    >
      <p
        className="tw-font-normal"
      >
        1 hrs 0 mins 12 secs
      </p>
    </Field>
    <div
      className="tw-flex tw-justify-between tw-gap-2 xl:!tw-gap-5"
    >
      <TimerButtonGroup
        disableSave={false}
        handleCancelButton={[Function]}
        handleDeleteButton={[Function]}
        handleEditButton={[Function]}
        handleSaveButton={[Function]}
        isEditing={false}
        showTimer={false}
        userCanDelete={true}
        userCanEdit={true}
      />
    </div>
  </div>
</div>
`;

exports[`TaskEntry it renders component correctly when parent Task is completed 1`] = `
<div
  className="tw-flex tw-items-center tw-border-t tw-border-gray-200 tw-bg-white tw-px-5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0"
>
  <i
    className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
  />
  <div
    className="tw-flex tw-grow tw-items-center tw-justify-between tw-gap-2 xl:!tw-gap-5"
  >
    <Field
      label="Date of Entry"
    >
      <p
        className="tw-font-normal"
      >
        Tue Sep 26, 2023
      </p>
    </Field>
    <div
      className="tw-flex tw-flex-col tw-gap-2 xl:tw-grow xl:!tw-flex-row xl:!tw-justify-evenly"
    >
      <Field
        label="Start Time"
      >
        <p
          className="tw-font-normal"
        >
          01:00:00 pm
        </p>
      </Field>
      <Field
        label="End Time"
      >
        <p
          className="tw-font-normal"
        >
          02:30:00 pm
        </p>
      </Field>
    </div>
    <Field
      label="Total Time"
    >
      <p
        className="tw-font-normal"
      >
        1 hrs 0 mins 12 secs
      </p>
    </Field>
    <div
      className="tw-flex tw-justify-between tw-gap-2 xl:!tw-gap-5"
    >
      <TimerButtonGroup
        disableSave={false}
        handleCancelButton={[Function]}
        handleDeleteButton={[Function]}
        handleEditButton={[Function]}
        handleSaveButton={[Function]}
        isEditing={false}
        showTimer={false}
        userCanDelete={true}
        userCanEdit={true}
      />
    </div>
  </div>
</div>
`;

exports[`TaskEntry it renders component correctly with permissions set to false 1`] = `
<div
  className="tw-flex tw-items-center tw-border-t tw-border-gray-200 tw-bg-white tw-px-5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0"
>
  <i
    className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
  />
  <div
    className="tw-flex tw-grow tw-items-center tw-justify-between tw-gap-2 xl:!tw-gap-5"
  >
    <Field
      label="Date of Entry"
    >
      <p
        className="tw-font-normal"
      >
        Tue Sep 26, 2023
      </p>
    </Field>
    <div
      className="tw-flex tw-flex-col tw-gap-2 xl:tw-grow xl:!tw-flex-row xl:!tw-justify-evenly"
    >
      <Field
        label="Start Time"
      >
        <p
          className="tw-font-normal"
        >
          01:00:00 pm
        </p>
      </Field>
      <Field
        label="End Time"
      >
        <p
          className="tw-font-normal"
        >
          02:30:00 pm
        </p>
      </Field>
    </div>
    <Field
      label="Total Time"
    >
      <p
        className="tw-font-normal"
      >
        1 hrs 0 mins 12 secs
      </p>
    </Field>
    <div
      className="tw-flex tw-justify-between tw-gap-2 xl:!tw-gap-5"
    >
      <TimerButtonGroup
        disableSave={false}
        handleCancelButton={[Function]}
        handleDeleteButton={[Function]}
        handleEditButton={[Function]}
        handleSaveButton={[Function]}
        isEditing={false}
        showTimer={false}
        userCanDelete={false}
        userCanEdit={false}
      />
    </div>
  </div>
</div>
`;
