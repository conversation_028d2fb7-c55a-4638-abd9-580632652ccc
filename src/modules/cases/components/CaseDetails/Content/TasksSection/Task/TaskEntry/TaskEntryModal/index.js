import { Button, ConfirmationModal } from "@q-centrix/q-components-react";
import "styles/confirmation-modal.scss";

const TaskEntryModal = props => {
  const { title, body, isOpen, onEdit, onConfirm, showConfirmButton } = props;

  return (
    <ConfirmationModal
      title={title}
      isDanger={false}
      isOpen={isOpen}
      appElement="#psychiatric-patient-census-main"
    >
      <div className="modal-content">
        <p className="modal-paragraph">{body}</p>
        <div
          className={`button-container ${
            showConfirmButton ? "" : "center-container"
          }`}
        >
          <Button
            outline
            customStyle="modal-buttons"
            onClick={onEdit}
            bg="neutral"
          >
            <i className="fa-regular fa-x" />
            Edit
          </Button>
          {showConfirmButton && (
            <Button
              customStyle="modal-buttons"
              onClick={onConfirm}
              bg="success"
            >
              <i className="fa-regular fa-check" />
              Confirm
            </Button>
          )}
        </div>
      </div>
    </ConfirmationModal>
  );
};

export default TaskEntryModal;
