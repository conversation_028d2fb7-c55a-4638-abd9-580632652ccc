import { Tag, Button } from "@q-centrix/q-components-react";
import DeleteModal from "shared/components/DeleteModal";
import { useComponentLogic } from "./hooks";
import TaskEntry from "./TaskEntry";
import { cleanURL } from "utils/fp";
import { serverURI } from "base/constants";
import MarkBillableConfirmationModal from "./MarkBillableConfirmationModal";

// eslint-disable-next-line complexity
const Task = ({
  id,
  type,
  billable,
  ownerFullName,
  questionnaire,
  entries,
  completed,
  permissionToDelete,
  permissionToAddEntries,
  onSaveTaskEntrySuccess,
  qPointsComp,
  canShowQPoints,
  subscribeToQpointsUpdates,
  userHasTimeTrackingDisabled
}) => {
  const {
    deleteModalIsOpen,
    completeTaskIsDisabled,
    toggleDeleteModal,
    handleCreateTaskEntry,
    handleCompleteTask,
    handleDeleteTask,
    createTaskEntryLoading,
    completeTaskLoading,
    deleteTaskLoading,
    markBillableModalIsOpen,
    closeConfirmationModal,
    tagText,
    handleStatusTagClick
  } = useComponentLogic({
    taskId: id,
    entries,
    type,
    billable,
    completed,
    subscribeToQpointsUpdates,
    userHasTimeTrackingDisabled
  });

  return (
    <div className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b">
      <div className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5">
        <div className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70">
          <div className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5">
            <h3 className="tw-font-semibold">{type}</h3>
            {canShowQPoints && (
              <Tag
                text={`${qPointsComp} Q points earned`}
                status="success"
                outline
                className="tw-text-xs"
              />
            )}
          </div>
          {ownerFullName && <p className="tw-font-normal">{ownerFullName}</p>}
        </div>
        <div className="tw-flex tw-gap-5 xl:!tw-gap-10">
          <div className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2">
            <Tag
              text={tagText}
              status={completed ? "success" : "warning"}
              className="tw-text-xs"
              onClick={handleStatusTagClick}
            />
            {questionnaire && (
              <a
                href={cleanURL(`${serverURI}${questionnaire.path}`)}
                className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
              >
                {questionnaire.name}
              </a>
            )}
          </div>
          <div className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5">
            {!completed && (
              <Button
                outline
                onClick={handleCompleteTask}
                customStyle="tw-flex tw-gap-2.5"
                disabled={completeTaskIsDisabled || completeTaskLoading}
              >
                <i title="Mark Complete" className="fa-regular fa-check" />
                <span className="tw-hidden lg:!tw-inline">Mark Complete</span>
              </Button>
            )}
            <MarkBillableConfirmationModal
              isOpen={markBillableModalIsOpen}
              onCancel={closeConfirmationModal}
              taskId={id}
              billable={billable}
              completed={completed}
            />
            {permissionToDelete && (
              <Button
                outline
                onClick={toggleDeleteModal}
                bg="danger"
                customStyle="tw-flex tw-gap-2.5"
                disabled={deleteTaskLoading}
              >
                <i title="Delete" className="fa-regular fa-trash" />
                <span className="tw-hidden lg:!tw-inline">Delete</span>
              </Button>
            )}
            {permissionToAddEntries && (
              <Button
                outline
                bg="success"
                onClick={handleCreateTaskEntry}
                customStyle="tw-flex tw-gap-2.5"
                disabled={createTaskEntryLoading}
                title="Create Task Entry"
              >
                <i className="fa-regular fa-plus" />
                <i className="fa-regular fa-timer" />
              </Button>
            )}
          </div>
        </div>
      </div>
      {entries?.map(entry => (
        <TaskEntry
          entry={entry}
          key={`${id}-${entry.id}`}
          taskId={id}
          onSaveTaskEntrySuccess={onSaveTaskEntrySuccess}
        />
      ))}
      <DeleteModal
        title="Delete Task"
        body="Are you sure you want to permanently delete this Task?"
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteTask}
        disabledConfirm={deleteTaskLoading}
        disabledCancel={deleteTaskLoading}
      />
    </div>
  );
};

export default Task;
