import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import MarkBillableConfirmationModal from "..";
import mocks from "modules/cases/components/CaseDetails/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal",
  RadioButton: "RadioButton",
  useToast: () => ({ toast: jest.fn() })
}));

describe("MarkBillableConfirmationModal", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: MarkBillableConfirmationModal,
        props,
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly for Mark Complete", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly for Update Billing Status", () => {
    const component = render({ completed: true });

    expect(component).toMatchSnapshot();
  });
});
