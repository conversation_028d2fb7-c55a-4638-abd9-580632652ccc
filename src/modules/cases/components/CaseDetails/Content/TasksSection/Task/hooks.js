import {
  CREATE_UPDATE_TASK_ENTRY,
  COMPLETE_TASK,
  DELETE_TASK
} from "modules/cases/components/CaseDetails/graphql/mutation";
import {
  GET_CASE_TASK_TYPES,
  GET_TASKS_DATA
} from "modules/cases/components/CaseDetails/graphql/query";
import { useMutation } from "@apollo/client";
import { useToast } from "@q-centrix/q-components-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  always,
  and,
  any,
  cond,
  either,
  equals,
  F,
  ifElse,
  isNil,
  not,
  propSatisfies,
  T
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  taskId,
  entries,
  type,
  billable,
  completed,
  subscribeToQpointsUpdates,
  userHasTimeTrackingDisabled
}) => {
  useEffect(() => {
    const unsubscribe = subscribeToQpointsUpdates(taskId);

    return unsubscribe;
  }, []);
  const [deleteModalIsOpen, setDeleteModalIsOpen] = useState(false);
  const [markBillableModalIsOpen, setMarkBillableModalIsOpen] = useState(false);
  const toggleDeleteModal = () => setDeleteModalIsOpen(not);
  const { toast } = useToast();

  const falloutReviewTask = equals("Fallout Review", type);

  const falloutReviewTaskCompletedBillable = and(
    falloutReviewTask,
    and(completed, billable)
  );

  const falloutReviewTaskCompletedNonBillable = and(
    falloutReviewTask,
    and(completed, not(billable))
  );

  const tagText = cond([
    [
      always(falloutReviewTaskCompletedBillable),
      always("Completed - Billable")
    ],
    [
      always(falloutReviewTaskCompletedNonBillable),
      always("Completed - Non-Billable")
    ],
    [always(completed), always("Completed")],
    [T, always("Incomplete")]
  ])(completed);

  const completeTaskIsDisabled = useMemo(
    () =>
      ifElse(
        () => and(userHasTimeTrackingDisabled, equals(type, "Reabstract-IRR")),
        F,
        either(isNullOrEmpty, any(propSatisfies(isNil, "finishedAt")))
      )(entries),
    [entries, userHasTimeTrackingDisabled, type]
  );

  const [createOrUpdateTaskEntry, { loading: createTaskEntryLoading }] =
    useMutation(CREATE_UPDATE_TASK_ENTRY, {
      refetchQueries: [GET_TASKS_DATA]
    });

  const handleCreateTaskEntry = () => {
    createOrUpdateTaskEntry({
      variables: {
        taskEntryId: null,
        input: {
          taskId,
          startedAt: new Date(),
          finishedAt: null
        }
      },
      onCompleted: ({
        createOrUpdateTaskEntry: createOrUpdateTaskEntryResult
      }) => {
        if (createOrUpdateTaskEntryResult?.errors) {
          createOrUpdateTaskEntryResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const [completeTask, { loading: completeTaskLoading }] = useMutation(
    COMPLETE_TASK,
    {
      refetchQueries: [GET_TASKS_DATA]
    }
  );

  const handleCompleteTask = () => {
    if (equals("Fallout Review", type)) {
      setMarkBillableModalIsOpen(true);
    } else {
      completeTask({
        variables: { taskId },
        onCompleted: ({ completeTask: completeTaskResult }) => {
          if (completeTaskResult?.errors) {
            completeTaskResult.errors?.fullMessages?.forEach(message => {
              toast({
                variant: "error",
                description: message
              });
            });
          }
        },
        onError: error => {
          toast({
            variant: "error",
            description: error.message
          });
        }
      });
    }
  };

  const closeConfirmationModal = useCallback(
    () => setMarkBillableModalIsOpen(not),
    []
  );

  const [deleteTask, { loading: deleteTaskLoading }] = useMutation(
    DELETE_TASK,
    {
      refetchQueries: [GET_TASKS_DATA, GET_CASE_TASK_TYPES]
    }
  );

  const handleDeleteTask = () => {
    deleteTask({
      variables: { taskId },
      onCompleted: ({ deleteTask: deleteTaskResult }) => {
        if (deleteTaskResult?.errors) {
          deleteTaskResult.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
    toggleDeleteModal();
  };

  const handleStatusTagClick = () => {
    if (and(equals("Fallout Review", type), completed)) {
      setMarkBillableModalIsOpen(true);
    }
  };

  return {
    deleteModalIsOpen,
    completeTaskIsDisabled,
    toggleDeleteModal,
    handleCreateTaskEntry,
    handleCompleteTask,
    handleDeleteTask,
    createTaskEntryLoading,
    completeTaskLoading,
    deleteTaskLoading,
    markBillableModalIsOpen,
    closeConfirmationModal,
    tagText,
    handleStatusTagClick
  };
};
