import Field from "shared/components/Field";
import TimeInput from "shared/components/TimeInput";
import { DateInput } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import Timer from "shared/components/Timer";
import TimerButtonGroup from "shared/components/Timer/TimerButtonGroup";
import DeleteModal from "shared/components/DeleteModal";
import TaskEntryModal from "./TaskEntryModal";

// eslint-disable-next-line complexity
const TaskEntry = props => {
  const { entry } = props;
  const { userCanEdit, userCanDelete } = entry.permissions;
  const {
    formattedStartTime,
    formattedEndTime,
    dateOfEntry,
    date,
    handleDate,
    handleEditButton,
    handleSaveButton,
    handleCancelButton,
    handleDeleteButton,
    deleteModalIsOpen,
    toggleDeleteModal,
    taskEntryModalContent,
    taskEntryWarningModalIsOpen,
    showConfirmButton,
    handleEditTaskEntry,
    handleConfirmTaskEntry,
    handleDeleteTaskEntry,
    isEditing,
    formattedTotalTime,
    times,
    handleTime,
    handleTimePeriod,
    isRunning,
    setIsRunning,
    timerStartTime,
    elapsedTime,
    setElapsedTime,
    showTimer,
    setShowTimer,
    handleTimerClick,
    handleStartTimer,
    saveIsDisabled
  } = useComponentLogic(props);

  return (
    <div className="tw-flex tw-items-center tw-border-t tw-border-gray-200 tw-bg-white tw-px-5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0">
      <i className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 " />
      <div className="tw-flex tw-grow tw-items-center tw-justify-between tw-gap-2 xl:!tw-gap-5">
        <Field label="Date of Entry">
          {isEditing ? (
            <DateInput
              value={date}
              onChange={handleDate}
              inputContainerClassName="tw-min-w-[110px]"
            />
          ) : (
            <p className="tw-font-normal">{dateOfEntry}</p>
          )}
        </Field>
        <div className="tw-flex tw-flex-col tw-gap-2 xl:tw-grow xl:!tw-flex-row xl:!tw-justify-evenly">
          <Field label="Start Time">
            {isEditing ? (
              <TimeInput
                name="start"
                value={times.start}
                handleTime={handleTime}
                handleTimePeriod={handleTimePeriod}
              />
            ) : (
              <p className="tw-font-normal">{formattedStartTime}</p>
            )}
          </Field>
          <Field label="End Time">
            {isEditing ? (
              <TimeInput
                name="end"
                value={times.end}
                handleTime={handleTime}
                handleTimePeriod={handleTimePeriod}
              />
            ) : (
              <p className="tw-font-normal">{formattedEndTime}</p>
            )}
          </Field>
        </div>
        {!isEditing && (
          <Field label="Total Time">
            <p className="tw-font-normal">{formattedTotalTime}</p>
          </Field>
        )}
        <div className="tw-flex tw-justify-between tw-gap-2 xl:!tw-gap-5">
          {showTimer && (
            <Timer
              isRunning={isRunning}
              setIsRunning={setIsRunning}
              startTime={timerStartTime}
              elapsedTime={elapsedTime}
              setElapsedTime={setElapsedTime}
              setShowTimer={setShowTimer}
              handleTimerClick={handleTimerClick}
              handleStartTimer={handleStartTimer}
            />
          )}
          <TimerButtonGroup
            showTimer={showTimer}
            userCanEdit={userCanEdit}
            userCanDelete={userCanDelete}
            isEditing={isEditing}
            handleEditButton={handleEditButton}
            handleSaveButton={handleSaveButton}
            disableSave={saveIsDisabled}
            handleDeleteButton={handleDeleteButton}
            handleCancelButton={handleCancelButton}
          />
        </div>
      </div>
      <DeleteModal
        title="Delete task entry?"
        body="Are you sure you want to permanently delete this task entry?"
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteTaskEntry}
      />
      <TaskEntryModal
        title={taskEntryModalContent.title}
        body={taskEntryModalContent.body}
        isOpen={taskEntryWarningModalIsOpen}
        showConfirmButton={showConfirmButton}
        onEdit={handleEditTaskEntry}
        onConfirm={handleConfirmTaskEntry}
      />
    </div>
  );
};

export default TaskEntry;
