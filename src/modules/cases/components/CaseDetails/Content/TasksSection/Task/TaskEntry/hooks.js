import { useMutation } from "@apollo/client";
import {
  format,
  isValid,
  parse,
  differenceInHours,
  secondsToHours,
  secondsToMinutes
} from "date-fns";
import {
  CREATE_UPDATE_TASK_ENTRY,
  DELETE_TASK_ENTRY
} from "modules/cases/components/CaseDetails/graphql/mutation";
import { GET_TASKS_DATA } from "modules/cases/components/CaseDetails/graphql/query";
import {
  __,
  all,
  always,
  any,
  anyPass,
  assocPath,
  complement,
  compose,
  equals,
  ifElse,
  isEmpty,
  gt,
  gte,
  length,
  lt,
  map,
  none,
  not,
  path,
  cond,
  T,
  pipe,
  replace,
  when,
  isNil,
  splitEvery,
  applySpec,
  modulo,
  identity,
  concat,
  append,
  split,
  head
} from "ramda";
import { useCallback, useState } from "react";
import { useToast } from "@q-centrix/q-components-react";
import { isNullOrEmpty } from "utils/fp";

const TASK_ENTRY_MODAL_TEXTS = {
  greaterThan8hours: {
    title: "Your time is greater than 8 hours",
    body: "Time entries longer than 8 hours are not allowed, please edit your end time."
  },
  greaterThan5hours: {
    title: "Your time is greater than 5 hours",
    body: "You've added a time entry longer than 5 hours. Confirm your time is accurate or edit your end time."
  }
};

const formatTime = time =>
  ifElse(isNullOrEmpty, always("-"), () =>
    format(new Date(time), "hh:mm:ss aaa")
  )(time);

const formatDateEntry = date =>
  ifElse(isNullOrEmpty, always("-"), () =>
    format(new Date(date), "E MMM dd, yyyy")
  )(date);

const convertToHoursMinutesAndSeconds = seconds =>
  ifElse(
    isNil,
    always(null),
    applySpec({
      hours: secondsToHours,
      minutes: pipe(secondsToMinutes, modulo(__, 60)),
      seconds: modulo(__, 60)
    })
  )(seconds);

const formatTotalTime = totalTime =>
  ifElse(
    () =>
      all(isNullOrEmpty, [
        totalTime?.hours,
        totalTime?.minutes,
        totalTime?.seconds
      ]),
    always("-"),
    ({ hours, minutes, seconds }) =>
      `${hours} hrs ${minutes} mins ${seconds} secs`
  )(totalTime);

const splitTime = pipe(
  ifElse(pipe(split(":"), head, length, equals(1)), concat("0"), identity),
  replace(/:/g, ""),
  ifElse(
    anyPass([compose(lt(__, 2), length), compose(gt(__, 6), length)]),
    always("00:00:00"),
    pipe(
      ifElse(
        compose(equals(4), length),
        pipe(splitEvery(2), append("")),
        splitEvery(2)
      ),
      map(when(isEmpty, always("00")))
    )
  )
);

// eslint-disable-next-line max-params
const formatDateTime = (date, hours, minutes, seconds, period) =>
  parse(
    `${format(
      new Date(date),
      "yyyy-MM-dd"
    )} ${hours}:${minutes}:${seconds} ${period}`,
    "yyyy-MM-dd hh:mm:ss aaa",
    new Date()
  );

const pad0s = val => val.padStart(2, "0");

const convertToDateTime = (date, time, period) =>
  ifElse(
    none(isNullOrEmpty),
    pipe(
      () => splitTime(time),
      map(pad0s),
      ([hours, minutes, seconds]) =>
        formatDateTime(date, hours, minutes, seconds, period),
      when(complement(isValid), always(null))
    ),
    always(null)
  )([date, time, period]);

const timeObject = dateTime =>
  ifElse(
    isNil,
    always({ time: null, period: null, dateTime: null }),
    pipe(
      val => new Date(val),
      applySpec({
        time: val => format(val, "hh:mm:ss"),
        period: val => format(val, "aaa"),
        dateTime: identity
      })
    )
  )(dateTime);

// eslint-disable-next-line max-statements, complexity
export const useComponentLogic = ({
  entry,
  taskId,
  onSaveTaskEntrySuccess
}) => {
  const { id, startedAt, finishedAt, totalTime } = entry;
  const [showTimer, setShowTimer] = useState(!isNullOrEmpty(id) && !finishedAt);
  const [isRunning, setIsRunning] = useState(true);
  const timerStartTime = new Date(startedAt).getTime();
  const [elapsedTime, setElapsedTime] = useState(0);
  const [deleteModalIsOpen, setDeleteModalIsOpen] = useState(false);
  const [showConfirmButton, setShowConfirmButton] = useState(true);
  const [taskEntryModalContent, setTaskEntryModalContent] = useState({
    title: "",
    body: ""
  });

  const [taskEntryWarningModalIsOpen, setTaskEntryWarningModalIsOpen] =
    useState(false);
  const toggleDeleteModal = () => setDeleteModalIsOpen(not);
  const toggleTaskEntryWarningModal = (type = null) => {
    if (type) {
      setTaskEntryModalContent({
        title: TASK_ENTRY_MODAL_TEXTS[type].title,
        body: TASK_ENTRY_MODAL_TEXTS[type].body
      });
    }

    if (type === "greaterThan8hours") {
      setShowConfirmButton(false);
    } else {
      setShowConfirmButton(true);
    }

    setTaskEntryWarningModalIsOpen(not);
  };

  const [times, setTimes] = useState({
    start: timeObject(startedAt),
    end: timeObject(finishedAt)
  });
  const [date, setDate] = useState(startedAt ? new Date(startedAt) : null);
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();

  const [createOrUpdateTaskEntry] = useMutation(CREATE_UPDATE_TASK_ENTRY, {
    refetchQueries: [GET_TASKS_DATA]
  });

  const [deleteTaskEntry] = useMutation(DELETE_TASK_ENTRY, {
    refetchQueries: [GET_TASKS_DATA]
  });

  const saveIsDisabled = any(isNullOrEmpty, [
    times.start.dateTime,
    times.end.dateTime
  ]);

  const handleTime = e => {
    const dateTime = convertToDateTime(
      date,
      e.target.value,
      path([e.target.name, "period"])(times)
    );

    setTimes(
      pipe(
        assocPath([e.target.name, "time"], e.target.value),
        assocPath([e.target.name, "dateTime"], dateTime)
      )
    );
  };

  const handleTimePeriod = (value, name) => {
    const dateTime = convertToDateTime(
      date,
      path([name, "time"])(times),
      value.label
    );

    setTimes(
      pipe(
        assocPath([name, "period"], value.label),
        assocPath([name, "dateTime"], dateTime)
      )
    );
  };

  const handleDate = value => {
    const startDateTime = convertToDateTime(
      value,
      times.start.time,
      times.start.period
    );
    const endDateTime = convertToDateTime(
      value,
      times.end.time,
      times.end.period
    );

    setDate(value);

    setTimes(
      pipe(
        assocPath(["start", "dateTime"], startDateTime),
        assocPath(["end", "dateTime"], endDateTime)
      )
    );
  };

  const handleEditButton = useCallback(() => {
    setIsEditing(not);
  }, []);

  const saveTaskEntry = endDateTime => {
    createOrUpdateTaskEntry({
      variables: {
        taskEntryId: id,
        input: {
          taskId,
          startedAt: times.start.dateTime,
          finishedAt: endDateTime
        }
      },
      onCompleted: data => {
        if (data.createOrUpdateTaskEntry.errors) {
          data.createOrUpdateTaskEntry.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          setTimes(prev => ({
            start: timeObject(prev.start.dateTime),
            end: timeObject(endDateTime)
          }));
          setIsEditing(false);
          onSaveTaskEntrySuccess();
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const diffInHours = (endTime, startTime) =>
    differenceInHours(
      new Date(endTime).getTime(),
      new Date(startTime).getTime()
    );

  const checkTimeBeforeSave = endDateTime =>
    pipe(
      () => diffInHours(endDateTime, times.start.dateTime),
      cond([
        [gte(__, 8), () => toggleTaskEntryWarningModal("greaterThan8hours")],
        [gte(__, 5), () => toggleTaskEntryWarningModal("greaterThan5hours")],
        [T, () => saveTaskEntry(endDateTime)]
      ])
    )(endDateTime);

  const handleSaveButton = () => {
    const endDateTime = times.end.dateTime;

    checkTimeBeforeSave(endDateTime);
  };

  const handleCancelButton = () => {
    setIsEditing(false);
    setDate(startedAt ? new Date(startedAt) : null);
    setTimes({
      start: timeObject(startedAt),
      end: timeObject(finishedAt)
    });
  };

  const handleDeleteButton = () => {
    setDeleteModalIsOpen(true);
  };

  const handleDeleteTaskEntry = () => {
    deleteTaskEntry({
      variables: {
        taskEntryId: id
      },
      // eslint-disable-next-line no-shadow
      onCompleted: ({ deleteTaskEntry }) => {
        if (deleteTaskEntry.errors) {
          deleteTaskEntry.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleConfirmTaskEntry = () => {
    const endDateTime = new Date().toISOString();

    setTaskEntryWarningModalIsOpen(not);
    saveTaskEntry(endDateTime);
  };

  const handleEditTaskEntry = () => {
    setTaskEntryWarningModalIsOpen(not);
    setIsEditing(true);
  };

  const handleTimerClick = () => {
    setIsRunning(false);
    setShowTimer(false);

    const endDateTime = new Date().toISOString();

    checkTimeBeforeSave(endDateTime);
  };

  const handleStartTimer = () => {
    setIsRunning(true);
  };

  const formattedStartTime = formatTime(times.start.dateTime);
  const formattedEndTime = formatTime(times.end.dateTime);
  const formattedTotalTime = formatTotalTime(
    convertToHoursMinutesAndSeconds(totalTime?.seconds)
  );
  const dateOfEntry = formatDateEntry(date);

  return {
    formattedStartTime,
    formattedEndTime,
    dateOfEntry,
    handleDate,
    date,
    handleEditButton,
    handleSaveButton,
    handleCancelButton,
    handleDeleteButton,
    deleteModalIsOpen,
    taskEntryWarningModalIsOpen,
    taskEntryModalContent,
    showConfirmButton,
    handleEditTaskEntry,
    handleConfirmTaskEntry,
    toggleDeleteModal,
    handleDeleteTaskEntry,
    isEditing,
    formattedTotalTime,
    times,
    handleTime,
    handleTimePeriod,
    isRunning,
    setIsRunning,
    timerStartTime,
    elapsedTime,
    setElapsedTime,
    showTimer,
    setShowTimer,
    handleTimerClick,
    handleStartTimer,
    saveIsDisabled
  };
};
