import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import TaskEntryModal from "..";
import mocks from "modules/psychiatric-patient-census/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

describe("Save Modal", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: TaskEntryModal,
        props,
        initialValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("renders component", () => {
    const component = render({
      title: "Your time is greater than 5 hours",
      body: "You've added a time entry longer than 5 hours. Confirm your time is accurate or edit your end time.",
      isOpen: true,
      onEdit: jest.fn(),
      onConfirm: jest.fn(),
      showConfirmButton: true
    });

    expect(component).toMatchSnapshot();
  });
});
