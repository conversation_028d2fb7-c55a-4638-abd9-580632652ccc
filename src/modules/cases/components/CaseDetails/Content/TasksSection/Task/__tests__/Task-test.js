import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import Task from "..";
import mocks from "modules/cases/components/CaseDetails/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Tag: "Tag",
  Button: "Button",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("../TaskEntry", () => "TaskEntry");
jest.mock("shared/components/DeleteModal", () => "DeleteModal");
jest.mock(
  "../MarkBillableConfirmationModal",
  () => "MarkBillableConfirmationModal"
);

const mockTask = {
  id: "1",
  type: "Mock Type",
  ownerFullName: "Russell Reas",
  questionnaire: null,
  completed: null,
  permissionToDelete: true,
  permissionToAddEntries: true,
  onSaveTaskEntrySuccess: jest.fn(),
  qPointsComp: null,
  canShowQPoints: false,
  subscribeToQpointsUpdates: jest.fn(),
  entries: [
    {
      id: "1",
      createdAt: "2023-12-14 17:41:48 -0500",
      startedAt: "2023-12-14 17:41:48 -0500",
      finishedAt: null,
      totalTime: null,
      task: { id: "1" },
      permissions: {
        userCanEdit: true,
        userCanDelete: true
      }
    }
  ]
};

function render(task) {
  return create(
    decoratedApollo({
      component: Task,
      props: { ...task },
      initialValues: {},
      initialAppValues: {},
      apolloMocks: mocks
    })
  );
}

describe("Task", () => {
  test("it renders a pending Task correctly with 1 entry", () => {
    const component = render(mockTask);

    expect(component).toMatchSnapshot();
  });

  test("it renders a completed Task correctly with 1 entry", () => {
    const component = render({
      ...mockTask,
      completed: "2023-12-14 17:46:55 -0500"
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders a Task correctly with 1 entry when user is not allowed to delete the Task", () => {
    const component = render({
      ...mockTask,
      permissionToDelete: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders a Task correctly with 1 entry when user is not allowed to add an entry to the Task", () => {
    const component = render({
      ...mockTask,
      permissionToAddEntries: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders a Task correctly with 1 entry when there is a questionnaire", () => {
    const component = render({
      ...mockTask,
      questionnaire: {
        id: null,
        name: "Targeted Review Questionnaire",
        path: "/qapps/cases/1/tasks/107/case_reviews/targeted"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it a renders Task correctly with no entries", () => {
    const component = render({
      ...mockTask,
      entries: []
    });

    expect(component).toMatchSnapshot();
  });

  test("it a renders Task correctly with Q Points", () => {
    const component = render({
      ...mockTask,
      completed: "2023-01-19 19:00:00 -0500",
      qPointsComp: 10,
      canShowQPoints: true
    });

    expect(component).toMatchSnapshot();
  });
});
