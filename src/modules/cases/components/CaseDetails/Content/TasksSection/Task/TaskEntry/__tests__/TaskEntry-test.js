import { create } from "react-test-renderer";
import { decoratedApollo } from "utils/tests/decorated";
import TaskEntry from "..";

jest.mock("shared/components/Field", () => "Field");
jest.mock("shared/components/TimeInput", () => "TimeInput");
jest.mock("shared/components/Timer", () => "Timer");
jest.mock("shared/components/Timer/TimerButtonGroup", () => "TimerButtonGroup");

const mockedProps = {
  taskId: "1",
  taskCompleted: false,
  entry: {
    id: "1",
    createdAt: "2023-09-26 13:00:00.000000",
    startedAt: "2023-09-26 13:00:00.000000",
    finishedAt: "2023-09-26 14:30:00.000000",
    totalTime: {
      hours: 1,
      minutes: 60,
      seconds: 3612
    },
    permissions: {
      userCanEdit: true,
      userCanDelete: true
    }
  }
};

function render(props) {
  return create(
    decoratedApollo({
      component: TaskEntry,
      props,
      initialValues: {},
      initialAppValues: {}
    })
  );
}

describe("TaskEntry", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with permissions set to false", () => {
    const component = render({
      ...mockedProps,
      entry: {
        ...mockedProps.entry,
        permissions: { userCanEdit: false, userCanDelete: false }
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly when parent Task is completed", () => {
    const component = render({
      ...mockedProps,
      taskCompleted: true
    });

    expect(component).toMatchSnapshot();
  });
});
