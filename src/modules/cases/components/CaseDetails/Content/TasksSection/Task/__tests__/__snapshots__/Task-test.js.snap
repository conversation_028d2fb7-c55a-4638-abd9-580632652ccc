// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Task it a renders Task correctly with Q Points 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
        <Tag
          className="tw-text-xs"
          outline={true}
          status="success"
          text="10 Q points earned"
        />
      </div>
      <p
        className="tw-font-normal"
      >
        <PERSON>
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="success"
          text="Completed"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <MarkBillableConfirmationModal
          completed="2023-01-19 19:00:00 -0500"
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it a renders Task correctly with no entries 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="warning"
          text="Incomplete"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <Button
          customStyle="tw-flex tw-gap-2.5"
          disabled={true}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-check"
            title="Mark Complete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Mark Complete
          </span>
        </Button>
        <MarkBillableConfirmationModal
          completed={null}
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it renders a Task correctly with 1 entry when there is a questionnaire 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="warning"
          text="Incomplete"
        />
        <a
          className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
          href="/qapps/cases/1/tasks/107/case_reviews/targeted"
        >
          Targeted Review Questionnaire
        </a>
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <Button
          customStyle="tw-flex tw-gap-2.5"
          disabled={true}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-check"
            title="Mark Complete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Mark Complete
          </span>
        </Button>
        <MarkBillableConfirmationModal
          completed={null}
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it renders a Task correctly with 1 entry when user is not allowed to add an entry to the Task 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="warning"
          text="Incomplete"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <Button
          customStyle="tw-flex tw-gap-2.5"
          disabled={true}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-check"
            title="Mark Complete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Mark Complete
          </span>
        </Button>
        <MarkBillableConfirmationModal
          completed={null}
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it renders a Task correctly with 1 entry when user is not allowed to delete the Task 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="warning"
          text="Incomplete"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <Button
          customStyle="tw-flex tw-gap-2.5"
          disabled={true}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-check"
            title="Mark Complete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Mark Complete
          </span>
        </Button>
        <MarkBillableConfirmationModal
          completed={null}
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it renders a completed Task correctly with 1 entry 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="success"
          text="Completed"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <MarkBillableConfirmationModal
          completed="2023-12-14 17:46:55 -0500"
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;

exports[`Task it renders a pending Task correctly with 1 entry 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-border-gray-200 [&:not(:last-child)]:tw-border-b"
>
  <div
    className="tw-flex tw-items-center tw-justify-between tw-gap-2 tw-bg-gray-25 tw-px-5 tw-py-2.5"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-y-2 tw-text-sm tw-text-black-70"
    >
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <h3
          className="tw-font-semibold"
        >
          Mock Type
        </h3>
      </div>
      <p
        className="tw-font-normal"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="tw-flex tw-gap-5 xl:!tw-gap-10"
    >
      <div
        className="tw-flex tw-flex-col tw-items-end tw-justify-center tw-gap-y-2"
      >
        <Tag
          className="tw-text-xs"
          onClick={[Function]}
          status="warning"
          text="Incomplete"
        />
      </div>
      <div
        className="tw-flex tw-items-center tw-gap-2 xl:!tw-gap-5"
      >
        <Button
          customStyle="tw-flex tw-gap-2.5"
          disabled={true}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-check"
            title="Mark Complete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Mark Complete
          </span>
        </Button>
        <MarkBillableConfirmationModal
          completed={null}
          isOpen={false}
          onCancel={[Function]}
          taskId="1"
        />
        <Button
          bg="danger"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
        >
          <i
            className="fa-regular fa-trash"
            title="Delete"
          />
          <span
            className="tw-hidden lg:!tw-inline"
          >
            Delete
          </span>
        </Button>
        <Button
          bg="success"
          customStyle="tw-flex tw-gap-2.5"
          disabled={false}
          onClick={[Function]}
          outline={true}
          title="Create Task Entry"
        >
          <i
            className="fa-regular fa-plus"
          />
          <i
            className="fa-regular fa-timer"
          />
        </Button>
      </div>
    </div>
  </div>
  <TaskEntry
    entry={
      Object {
        "createdAt": "2023-12-14 17:41:48 -0500",
        "finishedAt": null,
        "id": "1",
        "permissions": Object {
          "userCanDelete": true,
          "userCanEdit": true,
        },
        "startedAt": "2023-12-14 17:41:48 -0500",
        "task": Object {
          "id": "1",
        },
        "totalTime": null,
      }
    }
    onSaveTaskEntrySuccess={[MockFunction]}
    taskId="1"
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this Task?"
    disabledCancel={false}
    disabledConfirm={false}
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete Task"
  />
</div>
`;
