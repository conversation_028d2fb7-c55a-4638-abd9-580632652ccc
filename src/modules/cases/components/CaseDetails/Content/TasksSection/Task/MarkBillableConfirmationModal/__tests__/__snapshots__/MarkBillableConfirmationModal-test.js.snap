// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MarkBillableConfirmationModal it renders component correctly for Mark Complete 1`] = `
<ConfirmationModal
  appElement=".main"
  title="Mark Fallout Review Complete?"
>
  <div
    className="modal-content"
  >
    <p
      className="tw-text-xs tw-font-semibold"
    >
      Mark as the following status:
    </p>
    <div>
      <RadioButton
        checked={false}
        label="Completed - Billable"
        name="billable"
        onChange={[Function]}
        value="true"
      />
    </div>
    <div>
      <RadioButton
        checked={false}
        label="Completed - Non-Billable"
        name="billable"
        onChange={[Function]}
        value="false"
      />
    </div>
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-between"
  >
    <Button
      bg="neutral"
      customStyle="tw-w-[120px] tw-h-10 tw-text-sm"
      outline={true}
    >
      Cancel
    </Button>
    <Button
      bg="success"
      customStyle="tw-w-[130px] tw-h-10 tw-text-sm"
      onClick={[Function]}
    >
      Mark Complete
    </Button>
  </div>
</ConfirmationModal>
`;

exports[`MarkBillableConfirmationModal it renders component correctly for Update Billing Status 1`] = `
<ConfirmationModal
  appElement=".main"
  title="Change Fallout Review Status?"
>
  <div
    className="modal-content"
  >
    <p
      className="tw-text-xs tw-font-semibold"
    >
      Mark as the following status:
    </p>
    <div>
      <RadioButton
        checked={false}
        label="Completed - Billable"
        name="billable"
        onChange={[Function]}
        value="true"
      />
    </div>
    <div>
      <RadioButton
        checked={false}
        label="Completed - Non-Billable"
        name="billable"
        onChange={[Function]}
        value="false"
      />
    </div>
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-between"
  >
    <Button
      bg="neutral"
      customStyle="tw-w-[120px] tw-h-10 tw-text-sm"
      outline={true}
    >
      Cancel
    </Button>
    <Button
      bg="success"
      customStyle="tw-w-[130px] tw-h-10 tw-text-sm"
      onClick={[Function]}
    >
      Change Status
    </Button>
  </div>
</ConfirmationModal>
`;
