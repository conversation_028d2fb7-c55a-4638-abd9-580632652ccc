import { <PERSON><PERSON><PERSON><PERSON>ead<PERSON>, Spinner } from "@q-centrix/q-components-react";
import TasksHeader from "./TasksHeader";
import Task from "./Task";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const TasksSection = ({
  caseId,
  onSaveTaskEntrySuccess,
  userHasTimeTrackingDisabled
}) => {
  const { tasks, loading, called, error, subscribeToQpointsUpdates } =
    useComponentLogic({ caseId });

  let cardContent = null;

  if (loading) {
    cardContent = (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10">
        <Spinner />
      </div>
    );
  }

  if (error) {
    cardContent = (
      <p className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500">
        Error: {error.message}
      </p>
    );
  }

  if (!error && !loading) {
    const taskList = tasks.map(task => (
      <Task
        key={task.id}
        id={task.id}
        type={task.taskType}
        billable={task.billable}
        ownerFullName={task.owner?.fullName}
        questionnaire={task.questionnaire}
        entries={task.taskEntries}
        completed={task.completed}
        permissionToDelete={task.permissions.userCanDeleteTaskEntries}
        permissionToAddEntries={task.permissions.userCanAddTaskEntries}
        onSaveTaskEntrySuccess={onSaveTaskEntrySuccess}
        qPointsComp={task.qPointsComp}
        canShowQPoints={task.canShowQPoints}
        subscribeToQpointsUpdates={subscribeToQpointsUpdates}
        userHasTimeTrackingDisabled={userHasTimeTrackingDisabled}
      />
    ));

    cardContent = taskList.length ? (
      taskList
    ) : (
      <p className="tw-text-md tw-flex tw-items-center tw-justify-center tw-p-4 tw-font-semibold tw-text-gray-700">
        No Tasks
      </p>
    );
  }

  return (
    <CardWithHeader
      headerContent={<TasksHeader caseId={caseId} />}
      headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
      cardClasses="!tw-overflow-visible"
      bodyClasses="tw-rounded-b-[5px]"
      loading={loading}
      loaded={!loading && called}
    >
      {cardContent}
    </CardWithHeader>
  );
};

export default TasksSection;
