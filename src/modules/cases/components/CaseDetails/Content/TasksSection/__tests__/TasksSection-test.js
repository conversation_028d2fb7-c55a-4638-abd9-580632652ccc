import { create, act } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import TaskSection from "..";
import { GET_TASKS_DATA } from "../../../graphql/query";
import mocks, { MOCK_CASE_ID } from "../../../graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  Spinner: "Spinner"
}));
jest.mock("../Task", () => "Task");
jest.mock("../TasksHeader", () => "TasksHeader");

describe("TaskSection", () => {
  function render(caseId, mock = mocks) {
    return create(
      decoratedApollo({
        component: TaskSection,
        props: { caseId },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mock
      })
    );
  }

  test("it renders component loading", () => {
    const component = render(MOCK_CASE_ID);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", async () => {
    const component = render(MOCK_CASE_ID);

    await act(() => wait(200));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with no tasks", async () => {
    const mocksWithNoTasks = mocks.map(mock => {
      if (mock.request.query.definitions[0].name.value === "caseTasks") {
        return {
          ...mock,
          result: {
            data: {
              caseTasks: []
            }
          }
        };
      }

      return mock;
    });

    const component = render(MOCK_CASE_ID, mocksWithNoTasks);

    await act(() => wait(200));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const errorMockCaseId = 2;

    const errorMock = [
      {
        request: {
          query: GET_TASKS_DATA,
          variables: { caseId: errorMockCaseId }
        },
        error: new Error("an error occurred")
      }
    ];

    const component = render(errorMockCaseId, errorMock);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
