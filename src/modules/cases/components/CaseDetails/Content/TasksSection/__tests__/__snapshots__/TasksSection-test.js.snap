// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TaskSection it renders component correctly 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <TasksHeader
      caseId={1}
    />
  }
  loaded={true}
  loading={false}
>
  <Task
    billable={false}
    canShowQPoints={true}
    completed="2023-01-19 19:00:00 -0500"
    entries={
      Array [
        Object {
          "__typename": "TaskEntry",
          "createdAt": "2023-11-16 14:42:36 -0500",
          "finishedAt": null,
          "id": "1",
          "permissions": Object {
            "__typename": "TaskEntryPermissions",
            "userCanDelete": true,
            "userCanEdit": true,
          },
          "startedAt": "2023-11-16 14:42:36 -0500",
          "task": Object {
            "__typename": "Task",
            "id": "73",
          },
          "totalTime": null,
        },
      ]
    }
    id="2"
    ownerFullName="Russell Reas"
    permissionToAddEntries={true}
    permissionToDelete={true}
    qPointsComp={10}
    subscribeToQpointsUpdates={[Function]}
    type="Targeted Review"
  />
  <Task
    billable={false}
    canShowQPoints={false}
    completed={null}
    entries={
      Array [
        Object {
          "__typename": "TaskEntry",
          "createdAt": "2023-11-15 16:03:59 -0500",
          "finishedAt": null,
          "id": "1",
          "permissions": Object {
            "__typename": "TaskEntryPermissions",
            "userCanDelete": true,
            "userCanEdit": true,
          },
          "startedAt": "2023-11-15 16:03:59 -0500",
          "task": Object {
            "__typename": "Task",
            "id": "66",
          },
          "totalTime": null,
        },
      ]
    }
    id="66"
    ownerFullName="Russell Reas"
    permissionToAddEntries={true}
    permissionToDelete={true}
    qPointsComp={null}
    subscribeToQpointsUpdates={[Function]}
    type="Reabstract-Self"
  />
  <Task
    billable={false}
    canShowQPoints={false}
    completed={null}
    entries={
      Array [
        Object {
          "__typename": "TaskEntry",
          "createdAt": "2023-11-16 08:55:20 -0500",
          "finishedAt": null,
          "id": "1",
          "permissions": Object {
            "__typename": "TaskEntryPermissions",
            "userCanDelete": true,
            "userCanEdit": true,
          },
          "startedAt": "2023-11-16 08:55:20 -0500",
          "task": Object {
            "__typename": "Task",
            "id": "67",
          },
          "totalTime": null,
        },
      ]
    }
    id="67"
    ownerFullName="Russell Reas"
    permissionToAddEntries={true}
    permissionToDelete={true}
    qPointsComp={null}
    subscribeToQpointsUpdates={[Function]}
    type="Reabstract-IRR"
  />
</CardWithHeader>
`;

exports[`TaskSection it renders component correctly with no tasks 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <TasksHeader
      caseId={1}
    />
  }
  loaded={true}
  loading={false}
>
  <p
    className="tw-text-md tw-flex tw-items-center tw-justify-center tw-p-4 tw-font-semibold tw-text-gray-700"
  >
    No Tasks
  </p>
</CardWithHeader>
`;

exports[`TaskSection it renders component loading 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <TasksHeader
      caseId={1}
    />
  }
  loaded={false}
  loading={true}
>
  <div
    className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10"
  >
    <Spinner />
  </div>
</CardWithHeader>
`;

exports[`TaskSection it renders component with error 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <TasksHeader
      caseId={2}
    />
  }
  loaded={true}
  loading={false}
>
  <p
    className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500"
  >
    Error: 
    an error occurred
  </p>
</CardWithHeader>
`;
