import { <PERSON><PERSON><PERSON>Header } from "@q-centrix/q-components-react";
import CaseDetailsForm from "./CaseDetailsForm";
import CaseDetailsHeaderContent from "./CaseDetailsHeaderContent";

const CaseDetailsSection = ({
  caseId,
  editMode,
  form,
  caseDetails,
  loading,
  loaded,
  error,
  caseStatus,
  caseStatusOptions,
  onEditClick,
  onCancelEdit,
  onUpdateCase,
  questionnaireLocked
}) => (
  <CardWithHeader
    headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
    cardClasses="!tw-overflow-visible"
    bodyClasses="tw-rounded-b-[5px]"
    loading={loading}
    loaded={loaded}
    headerContent={
      <CaseDetailsHeaderContent
        caseDetails={caseDetails}
        questionnaireLocked={questionnaireLocked}
        editMode={editMode}
        onEditClick={onEditClick}
        onCancelEdit={onCancelEdit}
        form={form}
        loading={loading}
      />
    }
  >
    <CaseDetailsForm
      onUpdateCase={onUpdateCase}
      caseId={caseId}
      editMode={editMode}
      form={form}
      caseDetails={caseDetails}
      error={error}
      loading={loading}
      caseStatus={caseStatus}
      caseStatusOptions={caseStatusOptions}
    />
  </CardWithHeader>
);

export default CaseDetailsSection;
