import { Button } from "@q-centrix/q-components-react";
import classnames from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { useComponentLogic } from "./hooks";

const variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  transition: { duration: 0.3, ease: "easeInOut" }
};

const iconVariants = {
  initial: { pathLength: 0 },
  animate: { pathLength: 1 },
  transition: { type: "tween", duration: 0.3, ease: "easeOut" }
};

// eslint-disable-next-line complexity
const CaseDetailsHeaderContent = props => {
  const {
    caseDetails,
    questionnaireLocked,
    editMode,
    onCancelEdit,
    onEditClick,
    form,
    loading
  } = props;
  const {
    currentState,
    handleStatusChange,
    isBillable,
    billableLoading,
    showBillageSuccessAnimation
  } = useComponentLogic(props);

  const indicatorClassIcon = classnames({
    "fad fa-spinner-third fa-spin tw-animate-spin-fast":
      currentState === "isSubmitting",
    "fa-light fa-check": currentState === "default"
  });

  return (
    <div className="tw-flex tw-justify-between">
      <h3 className="tw-flex tw-items-center">Case Details</h3>
      {caseDetails?.isEditable && !questionnaireLocked && (
        <AnimatePresence mode="wait">
          {editMode ? (
            <motion.div
              key="edit-buttons"
              variants={variants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="tw-flex tw-gap-5"
            >
              <Button
                bg="neutral"
                outline
                customStyle="tw-flex tw-gap-2.5 tw-items-center"
                onClick={onCancelEdit}
              >
                <i className="fa-light fa-xmark" />
                Cancel
              </Button>
              <Button
                bg="success"
                outline
                customStyle="tw-flex tw-gap-2.5 tw-items-center tw-w-24"
                type="submit"
                form="case-details-form"
                disabled={form.formState.isSubmitting}
              >
                {currentState === "isSubmitting" ||
                  (currentState === "default" && (
                    <motion.div
                      key="loading"
                      variants={variants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                    >
                      <div>
                        <i className={indicatorClassIcon} />
                        <span className="tw-pl-2">Save</span>
                      </div>
                    </motion.div>
                  ))}
                {currentState === "isFinished" && (
                  <motion.svg
                    key="check"
                    variants={variants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="tw-w-6 tw-h-6 tw-text-blue-500"
                  >
                    <motion.path
                      variants={iconVariants}
                      initial="initial"
                      animate="animate"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M4.5 12.75l6 6 9-13.5"
                    />
                  </motion.svg>
                )}
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="edit-case-details"
              variants={variants}
              animate="visible"
              exit="hidden"
              className="tw-flex tw-flex-row tw-gap-2"
            >
              <Button
                outline
                customStyle="tw-flex tw-gap-2.5 tw-items-center tw-self-end"
                onClick={onEditClick}
                disabled={loading}
              >
                <i className="fa-light fa-pen" />
                Edit Case Details
              </Button>
              <Button
                outline
                bg="success"
                customStyle="tw-flex tw-gap-2.5 tw-items-center tw-self-end"
                onClick={handleStatusChange}
                disabled={isBillable}
              >
                {(() => {
                  if (billableLoading) {
                    return (
                      <i className="fa-solid fa-circle-notch fa-spin fa-spin-fast" />
                    );
                  }

                  if (showBillageSuccessAnimation) {
                    return (
                      <motion.svg
                        key="billable-check"
                        variants={variants}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="tw-w-6 tw-h-6 tw-text-green-500"
                      >
                        <motion.path
                          variants={iconVariants}
                          initial="initial"
                          animate="animate"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M4.5 12.75l6 6 9-13.5"
                        />
                      </motion.svg>
                    );
                  }

                  return <i className="fa-solid fa-circle-check" />;
                })()}
                Mark As Billable
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

export default CaseDetailsHeaderContent;
