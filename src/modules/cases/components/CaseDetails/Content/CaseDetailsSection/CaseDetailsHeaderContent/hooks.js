import { useMutation } from "@apollo/client";
import { useToast } from "@q-centrix/q-components-react";
import { UPDATE_CASE_STATUS } from "modules/cases/components/CaseDetails/graphql/mutation";
import { GET_CASE_DETAILS } from "modules/cases/components/CaseDetails/graphql/query";
import {
  always,
  cond,
  forEach,
  ifElse,
  pipe,
  prop,
  propEq,
  T,
  when
} from "ramda";
import { useEffect, useState } from "react";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = props => {
  const [
    showBillageSuccessAnimation,
    setShowBillageSuccessAnimation
  ] = useState(false);
  const { caseDetails } = props;

  useEffect(() => {
    if (!showBillageSuccessAnimation) {
      return;
    }

    const timeoutId = setTimeout(
      () => setShowBillageSuccessAnimation(false),
      2000
    );

    // eslint-disable-next-line consistent-return
    return () => {
      clearTimeout(timeoutId);
    };
  }, [showBillageSuccessAnimation]);

  const {
    form: { formState }
  } = props;
  const currentState = cond([
    [prop("isSubmitting"), always("isSubmitting")],
    [prop("isSubmitSuccessful"), always("isFinished")],
    [T, always("default")]
  ])(formState);

  const { toast } = useToast();
  const [updateCaseStatusMutation, { loading }] = useMutation(
    UPDATE_CASE_STATUS
  );
  const isBillable = propEq("status", "Billable")(caseDetails);

  const handleStatusChange = () => {
    setShowBillageSuccessAnimation(false);
    updateCaseStatusMutation({
      variables: {
        caseId: caseDetails.id,
        status: "Billable"
      },
      refetchQueries: [GET_CASE_DETAILS],
      onCompleted: ({ updateCaseStatus }) => {
        ifElse(
          isNullOrEmpty,
          () => {
            setShowBillageSuccessAnimation(true);
            toast({
              variant: "success",
              description: "Case Status updated successfully"
            });
          },
          when(
            prop("fullMessages"),
            pipe(
              prop("fullMessages"),
              forEach(message => {
                toast({
                  variant: "error",
                  description: message
                });
              })
            )
          )
        )(updateCaseStatus.errors);
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  return {
    currentState,
    handleStatusChange,
    isBillable,
    billableLoading: loading,
    showBillageSuccessAnimation
  };
};
