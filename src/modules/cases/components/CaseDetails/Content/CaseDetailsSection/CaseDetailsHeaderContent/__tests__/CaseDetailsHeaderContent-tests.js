import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import CaseDetailsHeaderContent from "..";

import mocks from "modules/cases/components/CaseDetails/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  useToast: () => ({ toast: jest.fn() })
}));

jest.mock("framer-motion", () => ({
  AnimatePresence: () => "AnimatePresence",
  motion: {
    div: () => "motion.div"
  }
}));

const mockProps = {
  caseId: 1,
  editMode: false,
  form: {
    formState: {
      isSubmitting: false,
      isSubmitSuccessful: true,
      errors: {}
    }
  },
  loading: false,
  error: undefined,
  caseStatus: "Not Started"
};

describe("CaseDetailsHeaderContent", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: CaseDetailsHeaderContent,
        props,
        initialValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("renders component with data", () => {
    const component = render(mockProps);

    expect(component).toMatchSnapshot();
  });
});
