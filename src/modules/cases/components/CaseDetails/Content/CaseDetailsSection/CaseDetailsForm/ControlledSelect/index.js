import { Controller } from "react-hook-form";
import Select from "shared/components/Select";

const ControlledSelect = ({
  id,
  name,
  label,
  control,
  query,
  variables = {},
  fields,
  queryObjectNamePath,
  clearable = false,
  isSearchable = false,
  isPageable = false,
  isMulti = false,
  smallPagination = false,
  error,
  errorText,
  placeholder,
  optionsModifier
}) => (
  <Controller
    name={name}
    control={control}
    render={({ field }) => (
      <Select
        disabled={field.disabled}
        name={field.name}
        // eslint-disable-next-line react/jsx-handler-names
        onChange={field.onChange}
        initialValue={field.value}
        label={label}
        variables={variables}
        path={queryObjectNamePath}
        id={id}
        query={query}
        fields={fields}
        placeholder={placeholder || "Select An Option"}
        clearable={clearable}
        isMulti={isMulti}
        isPageable={isPageable}
        isSearchable={isSearchable}
        smallPagination={smallPagination}
        error={error}
        errorText={errorText}
        optionsModifier={optionsModifier}
      />
    )}
  />
);

export default ControlledSelect;
