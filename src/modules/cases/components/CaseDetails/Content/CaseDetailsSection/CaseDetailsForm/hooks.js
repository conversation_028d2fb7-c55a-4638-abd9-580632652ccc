import { useMutation } from "@apollo/client";
import {
  UPDATE_ABSTRACTION_MISMATCH,
  UPDATE_CASE_ANALYTIC_STATUS,
  UPDATE_CASE_REPORTABLE_STATUS,
  UPDATE_CASE_STATUS
} from "../../../graphql/mutation";
import { useToast } from "@q-centrix/q-components-react";
import { useEffect } from "react";
import { forEach, ifElse, pipe, prop, when } from "ramda";
import { isNullOrEmpty } from "utils/fp";
import { GET_CASE_DETAILS } from "../../../graphql/query";
import { MIN_DATE } from "modules/cases/constants";
import {
  BUSINESS_OFFERING_CORE_MEASURES,
  BUSINESS_OFFERING_REGISTRY
} from "modules/cases/components/CaseCreate/Content/hooks/formReducer/constants";
import {
  checkIfLosOverrideIsRequired,
  generateCaseInput
} from "modules/cases/components/CaseCreate/Content/hooks/helpers";

// eslint-disable-next-line max-statements, complexity
export const useComponentLogic = ({
  caseId,
  caseDetails,
  watch,
  setValue,
  setError,
  clearErrors,
  onUpdateCase
}) => {
  const today = new Date();

  const minDate = new Date(MIN_DATE);

  const facilityIdSelectedOption = watch("facilityId");

  const caseTypeSelectedOption = watch("caseTypeId");

  const arrivedAt = watch("arrivedAt");

  const hospitalDischargedAt = watch("hospitalDischargedAt");

  const losOverride = watch("losOverride");

  const losOverrideIsRequired = checkIfLosOverrideIsRequired({
    losStart: caseTypeSelectedOption?.lengthOfStay?.start,
    losEnd: caseTypeSelectedOption?.lengthOfStay?.end,
    arrivedAt,
    hospitalDischargedAt
  });

  const shouldAllowLosOverride =
    caseTypeSelectedOption?.lengthOfStay?.allowOverride;

  useEffect(() => {
    if (facilityIdSelectedOption?.value !== caseDetails?.facility?.id) {
      setValue("caseTypeId", null);
    }
  }, [facilityIdSelectedOption?.value, caseDetails?.facility?.id]);

  useEffect(() => {
    if (!caseTypeSelectedOption?.isOncology) {
      /* we use setValue and not resetField because the latter
       affects cancerSiteId's initial values if the user cancels
       edit and then clicks edit again */
      setValue("cancerSiteId", null);
    }
  }, [caseTypeSelectedOption?.isOncology]);

  // eslint-disable-next-line complexity
  useEffect(() => {
    if (!losOverrideIsRequired) {
      setValue("losOverride", false);
      clearErrors("caseTypeId");
    } else if (!shouldAllowLosOverride) {
      setValue("losOverride", false);
      setError("caseTypeId", {
        type: "custom",
        message:
          "The case type must match the LOS. Correct the case type or LOS and try again."
      });
    } else if (losOverride) {
      clearErrors("caseTypeId");
    } else {
      setError("caseTypeId", {
        type: "custom",
        message:
          "The case type must match the LOS. Correct the case type, LOS, or override and try again."
      });
    }
  }, [losOverrideIsRequired, losOverride, shouldAllowLosOverride]);

  const showAdmissionDateInput =
    caseTypeSelectedOption?.isRegulatory ||
    !(
      caseTypeSelectedOption?.businessOffering ===
        BUSINESS_OFFERING_CORE_MEASURES ||
      caseTypeSelectedOption?.businessOffering === BUSINESS_OFFERING_REGISTRY
    );

  const showArrivalDateInput = !showAdmissionDateInput;

  const showAdmissionDateReadOnlyField =
    caseDetails?.caseType?.isRegulatory ||
    !(
      caseDetails?.caseType?.businessOffering ===
        BUSINESS_OFFERING_CORE_MEASURES ||
      caseDetails?.caseType?.businessOffering === BUSINESS_OFFERING_REGISTRY
    );

  const showArrivalDateReadOnlyField = !showAdmissionDateReadOnlyField;

  const showSurgeryDateInput =
    !caseTypeSelectedOption?.isOncology &&
    !caseTypeSelectedOption?.isRegulatory;

  const showSurgeryDateReadOnlyField =
    !caseDetails?.caseType?.isOncology && !caseDetails?.caseType?.isRegulatory;

  const showHospitalDischargeDateInput = !(
    caseTypeSelectedOption?.isAdminOutpatient &&
    caseTypeSelectedOption?.allowsEmptyDischargeDate
  );

  const showHospitalDischargeReadOnlyField = !(
    caseDetails?.caseType?.isAdminOutpatient &&
    caseDetails?.caseType?.allowsEmptyDischargeDate
  );

  // will run only if the form has no yup schema validation errors
  const onSubmit = (values, event) => {
    event.preventDefault();

    const updateCaseInput = generateCaseInput(values, {
      caseTypeIsOncology: caseTypeSelectedOption?.isOncology,
      caseTypeIsInPatient: caseTypeSelectedOption?.isAdminInpatient,
      caseTypeIsOutPatient: caseTypeSelectedOption?.isAdminOutpatient,
      caseTypeAllowsEmptyDischargeDate:
        caseTypeSelectedOption?.allowsEmptyDischargeDate
    });

    onUpdateCase(updateCaseInput);
  };

  const [updateCaseReportableStatus] = useMutation(
    UPDATE_CASE_REPORTABLE_STATUS
  );
  const [updateCaseAnalyticStatus] = useMutation(UPDATE_CASE_ANALYTIC_STATUS);
  const [updateCaseStatus] = useMutation(UPDATE_CASE_STATUS);
  const [updateAbstractionMismatch] = useMutation(UPDATE_ABSTRACTION_MISMATCH);

  const { toast } = useToast();

  const handleReportableStatusChange = (newStatus, onError) => {
    updateCaseReportableStatus({
      variables: {
        caseId,
        reportable: newStatus ? 1 : 0
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
        onError();
      }
    });
  };

  const handleAnalyticStatusChange = (newStatus, onError) => {
    updateCaseAnalyticStatus({
      variables: {
        caseId,
        analytic: newStatus
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
        onError();
      }
    });
  };

  const handleStatusChange = ({ value }) => {
    updateCaseStatus({
      variables: {
        caseId,
        status: value
      },
      refetchQueries: [GET_CASE_DETAILS],
      // eslint-disable-next-line no-shadow
      onCompleted: ({ updateCaseStatus }) => {
        ifElse(
          isNullOrEmpty,
          () => {
            toast({
              variant: "success",
              description: "Case Status updated successfully"
            });
          },
          when(
            prop("fullMessages"),
            pipe(
              prop("fullMessages"),
              forEach(message => {
                toast({
                  variant: "error",
                  description: message
                });
              })
            )
          )
        )(updateCaseStatus.errors);
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleAbstractionMismatchChange = (newStatus, onError) => {
    updateAbstractionMismatch({
      variables: {
        caseId,
        abstractionMismatchValue: newStatus
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
        onError();
      }
    });
  };

  return {
    today,
    minDate,
    onSubmit,
    handleReportableStatusChange,
    handleAnalyticStatusChange,
    handleStatusChange,
    handleAbstractionMismatchChange,
    caseTypeSelectedOption,
    showAdmissionDateInput,
    showArrivalDateInput,
    showAdmissionDateReadOnlyField,
    showArrivalDateReadOnlyField,
    showSurgeryDateInput,
    showSurgeryDateReadOnlyField,
    showHospitalDischargeDateInput,
    showHospitalDischargeReadOnlyField,
    losOverrideIsRequired,
    facilityIdSelectedOption,
    shouldAllowLosOverride
  };
};
