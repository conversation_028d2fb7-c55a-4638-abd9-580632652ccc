import { create } from "react-test-renderer";
import CaseDetailsForm from "..";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks, {
  caseDetailsMockSuccessResponse
} from "../../../../graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input",
  Dropdown: "Dropdown",
  Label: "Label",
  Spinner: "Spinner",
  useToast: () => ({ toast: jest.fn() })
}));

jest.mock("../ReadOnlyField", () => "ReadOnlyField");

jest.mock("../ControlledSelect", () => "ControlledSelect");

jest.mock("shared/components/ControlledDateInput", () => "ControlledDateInput");

jest.mock("../ControlledSwitch", () => "ControlledSwitch");

jest.mock("../ControlledSwitch", () => "ControlledSwitch");

jest.mock("../SwitchWrapper", () => "SwitchWrapper");

jest.mock("../TimeLineItem", () => "TimeLineItem");

jest.useFakeTimers().setSystemTime(new Date("2022-01-01T00:00:00.000Z"));

const mockCaseDetails = caseDetailsMockSuccessResponse.data.caseDetails.case;

const mockProps = {
  onUpdateCase: jest.fn(),
  caseId: 1,
  editMode: false,
  form: {
    watch: () => ({
      value: mockCaseDetails.caseType.id,
      label: mockCaseDetails.caseType.name,
      isOncology: mockCaseDetails.caseType.isOncology,
      isRegulatory: mockCaseDetails.caseType.isRegulatory,
      isAdminInpatient: mockCaseDetails.caseType.isAdminInpatient,
      isAdminOutpatient: mockCaseDetails.caseType.isAdminOutpatient,
      businessOffering: mockCaseDetails.caseType.businessOffering,
      serviceLine: mockCaseDetails.caseType.serviceLine,
      allowsEmptyDischargeDate:
        mockCaseDetails.caseType.allowsEmptyDischargeDate,
      lengthOfStay: mockCaseDetails.caseType.lengthOfStay
    }),
    setValue: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    handleSubmit: jest.fn(),
    register: jest.fn(),
    control: jest.fn(),
    formState: {
      errors: {}
    }
  },
  caseDetails: mockCaseDetails,
  loading: false,
  error: undefined,
  caseStatus: "Not Started",
  caseStatusOptions: [
    {
      label: "Not Started",
      value: "Not Started"
    },
    {
      label: "Pending-Initial Reabstraction",
      value: "Pending-Initial Reabstraction"
    },
    {
      label: "Pending-Incomplete Abstraction",
      value: "Pending-Incomplete Abstraction"
    },
    {
      label: "Pending-Missing Documentation",
      value: "Pending-Missing Documentation"
    },
    {
      label: "Pending-Second Look",
      value: "Pending-Second Look"
    },
    {
      label: "Pending-Fallout Review",
      value: "Pending-Fallout Review"
    },
    {
      label: "Pending-Other",
      value: "Pending-Other"
    },
    {
      label: "Billable",
      value: "Billable"
    },
    {
      label: "Ineligible",
      value: "Ineligible"
    }
  ]
};

describe("CaseDetailsForm", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: CaseDetailsForm,
        props,
        initialValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with loading state", () => {
    const component = render({
      ...mockProps,
      loading: true,
      caseDetails: undefined
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", () => {
    const component = render({
      ...mockProps,
      error: new Error("Mock error"),
      caseDetails: null
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data", () => {
    const component = render(mockProps);

    expect(component).toMatchSnapshot();
  });

  test("it renders component with denied permissions", () => {
    const component = render({
      ...mockProps,
      editMode: true,
      caseDetails: {
        ...mockCaseDetails,
        permissions: {
          canEditVisitNumberAndMrn: false,
          canChangeCompleteCaseStatus: false,
          canEditFacilityAndCaseType: false
        }
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with permissions", () => {
    const component = render({
      ...mockProps,
      editMode: true
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with case type is oncology", () => {
    const component = render({
      ...mockProps,
      caseDetails: {
        ...mockCaseDetails,
        caseType: {
          ...mockCaseDetails.caseType,
          isOncology: true
        },
        cancerSite: {
          id: "2",
          name: "Adrenal Gland"
        }
      }
    });

    expect(component).toMatchSnapshot();
  });
});
