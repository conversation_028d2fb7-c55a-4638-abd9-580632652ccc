import { create } from "react-test-renderer";
import TimeLineItem from "..";

const mockProps = {
  children: <p>Mock child</p>
};

describe("TimeLineItem", () => {
  test("it renders component correctly with default props", () => {
    const component = create(<TimeLineItem {...mockProps} />);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with top line not visible", () => {
    const component = create(
      <TimeLineItem {...mockProps} topLineIsVisible={false} />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with bottom line not visible", () => {
    const component = create(
      <TimeLineItem {...mockProps} bottomLineIsVisible={false} />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with circle not filled", () => {
    const component = create(
      <TimeLineItem {...mockProps} circleIsFilled={false} />
    );

    expect(component).toMatchSnapshot();
  });
});
