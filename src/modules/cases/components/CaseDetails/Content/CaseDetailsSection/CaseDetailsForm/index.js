/* eslint-disable complexity */
import { Input, Dropdown, Label, Spinner } from "@q-centrix/q-components-react";
import ControlledSelect from "./ControlledSelect";
import ControlledDateInput from "shared/components/ControlledDateInput";
import ReadOnly<PERSON>ield from "./ReadOnlyField";
import TimeLineItem from "./TimeLineItem";
import SwitchWrapper from "./SwitchWrapper";
import { useComponentLogic } from "modules/cases/components/CaseDetails/Content/CaseDetailsSection/CaseDetailsForm/hooks";
import { SERVICE_LINE_GWTG } from "modules/cases/components/CaseCreate/Content/hooks/formReducer/constants";
import {
  FACILITIES,
  CASE_TYPES_BY_FACILITY,
  CANCER_TYPES
} from "modules/cases/components/CaseCreate/graphql/query";
import ControlledSwitch from "./ControlledSwitch";
import { cleanURL } from "utils/fp";
import { serverURI } from "base/constants";
import { formatISOToDate } from "utils/formatISOToDate";

const CaseDetailsForm = ({
  onUpdateCase,
  caseId,
  editMode,
  form: {
    watch,
    setValue,
    setError,
    clearErrors,
    handleSubmit,
    register,
    control,
    formState: { errors }
  },
  caseDetails,
  loading,
  error,
  caseStatus,
  caseStatusOptions
}) => {
  const {
    today,
    minDate,
    onSubmit,
    handleReportableStatusChange,
    handleAnalyticStatusChange,
    handleStatusChange,
    handleAbstractionMismatchChange,
    caseTypeSelectedOption,
    showAdmissionDateInput,
    showArrivalDateInput,
    showAdmissionDateReadOnlyField,
    showArrivalDateReadOnlyField,
    showSurgeryDateInput,
    showSurgeryDateReadOnlyField,
    showHospitalDischargeDateInput,
    showHospitalDischargeReadOnlyField,
    losOverrideIsRequired,
    facilityIdSelectedOption,
    shouldAllowLosOverride
  } = useComponentLogic({
    caseId,
    caseDetails,
    watch,
    setValue,
    setError,
    clearErrors,
    onUpdateCase
  });

  if (loading) {
    return (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <p className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500">
        Error: {error.message}
      </p>
    );
  }
  return (
    <form
      id="case-details-form"
      autoComplete="off"
      onSubmit={handleSubmit(onSubmit)}
      className="tw-flex tw-rounded-b-[5px] tw-bg-white"
    >
      <div className="tw-grid tw-basis-7/12 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-7 tw-border-r tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-basis-1/2 2xl:tw-grid-cols-3">
        {editMode && caseDetails?.permissions?.canEditFacilityAndCaseType ? (
          <ControlledSelect
            id="facilityId"
            name="facilityId"
            label="Facility Name"
            control={control}
            query={FACILITIES}
            variables={{ perPage: 25 }}
            fields={["id", "name"]}
            queryObjectNamePath={[
              "currentUserAvailableFacilities",
              "userAvailableFacilities"
            ]}
            error={Boolean(errors.facilityId)}
            errorText={errors.facilityId?.message}
            isSearchable
            isPageable
            smallPagination
          />
        ) : (
          <ReadOnlyField
            title="Facility Name"
            body={caseDetails?.facility?.name}
          />
        )}
        {editMode && caseDetails?.permissions?.canEditFacilityAndCaseType ? (
          <ControlledSelect
            id="caseTypeId"
            name="caseTypeId"
            label="Case Type"
            control={control}
            query={CASE_TYPES_BY_FACILITY}
            variables={{
              facilityId: parseInt(facilityIdSelectedOption?.value || "0", 10),
              perPage: 25
            }}
            fields={[
              "id",
              "name",
              "isOncology",
              "isRegulatory",
              "isAdminInpatient",
              "isAdminOutpatient",
              "businessOffering",
              "serviceLine",
              "allowsEmptyDischargeDate",
              "lengthOfStay"
            ]}
            queryObjectNamePath={[
              "userCaseTypesByFacility",
              "userFacilityCaseTypes"
            ]}
            error={Boolean(errors.caseTypeId)}
            errorText={errors.caseTypeId?.message}
            isSearchable
            isPageable
            smallPagination
          />
        ) : (
          <ReadOnlyField title="Case Type" body={caseDetails?.caseType?.name} />
        )}
        {editMode && caseTypeSelectedOption?.isOncology && (
          <ControlledSelect
            id="cancerSiteId"
            name="cancerSiteId"
            label="Cancer Type"
            control={control}
            query={CANCER_TYPES}
            fields={["id", "name"]}
            queryObjectNamePath={["cancerSites"]}
            error={Boolean(errors.cancerSiteId)}
            errorText={errors.cancerSiteId?.message}
            isSearchable
          />
        )}
        {!editMode && caseDetails?.caseType?.isOncology && (
          <ReadOnlyField
            title="Cancer Type"
            body={caseDetails.cancerSite?.name}
          />
        )}
        {editMode && losOverrideIsRequired && shouldAllowLosOverride && (
          <Label className="tw-flex tw-flex-col tw-gap-y-1">
            <span>LOS Override</span>
            <ControlledSwitch name="losOverride" control={control} />
          </Label>
        )}
        {!editMode && caseDetails?.losOverride && (
          <ReadOnlyField title="LOS Override" body="Yes" />
        )}
        {editMode &&
          !caseTypeSelectedOption?.isOncology &&
          caseDetails?.permissions?.canEditVisitNumberAndMrn && (
            <Input
              id="visitNumber"
              label="Visit #"
              {...register("visitNumber")}
              error={Boolean(errors.visitNumber)}
              errorText={errors.visitNumber?.message}
            />
          )}
        {!editMode && !caseDetails?.caseType?.isOncology && (
          <ReadOnlyField title="Visit #" body={caseDetails.visit.number} />
        )}
        {editMode && caseDetails?.permissions?.canEditVisitNumberAndMrn ? (
          <Input
            id="mrn"
            label="MRN"
            {...register("mrn")}
            error={Boolean(errors?.mrn)}
            errorText={errors.mrn?.message}
          />
        ) : (
          <ReadOnlyField title="MRN" body={caseDetails?.patient?.mrn} />
        )}
        {editMode &&
          caseTypeSelectedOption?.serviceLine === SERVICE_LINE_GWTG && (
            <Input
              id="gwtgId"
              label="GWTG ID (Optional)"
              {...register("gwtgId")}
              error={Boolean(errors.gwtgId)}
              errorText={errors.gwtgId?.message}
            />
          )}
        {!editMode &&
          caseDetails?.caseType?.serviceLine === SERVICE_LINE_GWTG && (
            <ReadOnlyField
              title="GWTG ID (Optional)"
              body={caseDetails.gwtgId}
            />
          )}
        <ReadOnlyField title="Case Owner" body={caseDetails?.owner?.fullName} />
        <ReadOnlyField
          title="Case Assignee"
          body={caseDetails?.assignee?.fullName}
        />
        <ReadOnlyField title="Case ID" body={caseDetails?.id} />
        {editMode ? (
          <Input
            id="lastName"
            label="Patient Last Name (Optional)"
            {...register("lastName")}
            error={Boolean(errors.lastName)}
            errorText={errors.lastName?.message}
          />
        ) : (
          <ReadOnlyField
            title="Patient Last Name (Optional)"
            body={caseDetails?.patient?.lastName}
          />
        )}
        {editMode ? (
          <Input
            id="firstName"
            label="Patient First Name (Optional)"
            {...register("firstName")}
            error={Boolean(errors.firstName)}
            errorText={errors.firstName?.message}
          />
        ) : (
          <ReadOnlyField
            title="Patient First Name (Optional)"
            body={caseDetails?.patient?.firstName}
          />
        )}
        {editMode ? (
          <Input
            id="middleName"
            label="Patient Middle Name (Optional)"
            {...register("middleName")}
            error={Boolean(errors.middleName)}
            errorText={errors.middleName?.message}
          />
        ) : (
          <ReadOnlyField
            title="Patient Middle Name (Optional)"
            body={caseDetails?.patient?.middleName}
          />
        )}
        {editMode ? (
          <ControlledDateInput
            id="bornOn"
            name="bornOn"
            label="Patient DOB (Optional)"
            control={control}
            error={Boolean(errors.bornOn)}
            errorText={errors.bornOn?.message}
            minDate={minDate}
            maxDate={today}
          />
        ) : (
          <ReadOnlyField
            title="Patient DOB (Optional)"
            body={
              caseDetails?.patient?.bornOn &&
              formatISOToDate(caseDetails.patient.bornOn, "MM/dd/yyyy")
            }
          />
        )}
        <ReadOnlyField
          title="Deadline (Optional)"
          body={
            caseDetails?.clientDueDate &&
            formatISOToDate(caseDetails.clientDueDate, "MM/dd/yyyy")
          }
        />
        {caseDetails?.batch && (
          <ReadOnlyField title="Batch ID">
            <a
              className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
              href={cleanURL(`${serverURI}${caseDetails.batch.link}`)}
            >
              {caseDetails.batch.id}
            </a>
          </ReadOnlyField>
        )}
      </div>
      <div className="tw-flex tw-basis-5/12 tw-flex-col 2xl:tw-basis-1/2 2xl:!tw-flex-row">
        <div className="tw-flex tw-grow tw-flex-col tw-justify-center tw-border-b tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-grow-0 2xl:tw-basis-1/2 2xl:!tw-justify-start 2xl:tw-border-r">
          {editMode && caseTypeSelectedOption?.isOncology && (
            <TimeLineItem
              topLineIsVisible={false}
              circleColor="purple"
              circleIsFilled={Boolean(caseDetails?.visit?.firstContact)}
              bottomLineIsVisible={
                showHospitalDischargeDateInput ||
                showSurgeryDateInput ||
                showAdmissionDateInput ||
                showArrivalDateInput
              }
            >
              <ControlledDateInput
                id="firstContact"
                name="firstContact"
                label="Date of First Contact"
                control={control}
                error={Boolean(errors.firstContact)}
                errorText={errors.firstContact?.message}
                minDate={minDate}
                maxDate={today}
              />
            </TimeLineItem>
          )}
          {!editMode && caseDetails?.caseType?.isOncology && (
            <TimeLineItem
              topLineIsVisible={false}
              circleColor="purple"
              circleIsFilled={Boolean(caseDetails?.visit?.firstContact)}
              bottomLineIsVisible={
                showHospitalDischargeReadOnlyField ||
                showSurgeryDateReadOnlyField ||
                showAdmissionDateReadOnlyField ||
                showArrivalDateReadOnlyField
              }
            >
              <ReadOnlyField
                title="Date of First Contact"
                body={
                  caseDetails?.visit?.firstContact &&
                  formatISOToDate(caseDetails.visit.firstContact, "MM/dd/yyyy")
                }
              />
            </TimeLineItem>
          )}
          {editMode && showArrivalDateInput && (
            <TimeLineItem
              topLineIsVisible={caseTypeSelectedOption?.isOncology}
              circleColor="orange"
              circleIsFilled={Boolean(caseDetails?.visit?.arrivedAt)}
              bottomLineIsVisible={
                showHospitalDischargeDateInput ||
                showSurgeryDateInput ||
                showAdmissionDateInput
              }
            >
              <ControlledDateInput
                id="arrivedAt"
                name="arrivedAt"
                label="Arrival Date"
                control={control}
                error={Boolean(errors.arrivedAt)}
                errorText={errors.arrivedAt?.message}
                minDate={minDate}
                maxDate={today}
              />
            </TimeLineItem>
          )}
          {!editMode && showArrivalDateReadOnlyField && (
            <TimeLineItem
              topLineIsVisible={caseDetails?.caseType?.isOncology}
              circleColor="orange"
              circleIsFilled={Boolean(caseDetails?.visit?.arrivedAt)}
              bottomLineIsVisible={
                showHospitalDischargeReadOnlyField ||
                showSurgeryDateReadOnlyField ||
                showAdmissionDateReadOnlyField
              }
            >
              <ReadOnlyField
                title="Arrival Date"
                body={
                  caseDetails?.visit?.arrivedAt &&
                  formatISOToDate(caseDetails.visit.arrivedAt, "MM/dd/yyyy")
                }
              />
            </TimeLineItem>
          )}
          {editMode && showAdmissionDateInput && (
            <TimeLineItem
              topLineIsVisible={
                caseTypeSelectedOption?.isOncology || showArrivalDateInput
              }
              circleColor="green"
              circleIsFilled={Boolean(caseDetails?.visit?.admittedAt)}
              bottomLineIsVisible={
                showHospitalDischargeDateInput || showSurgeryDateInput
              }
            >
              <ControlledDateInput
                id="admittedAt"
                name="admittedAt"
                label={
                  caseTypeSelectedOption?.isAdminOutpatient
                    ? "Encounter Date"
                    : "Admission Date"
                }
                control={control}
                error={Boolean(errors.admittedAt)}
                errorText={errors.admittedAt?.message}
                minDate={minDate}
                maxDate={today}
              />
            </TimeLineItem>
          )}
          {!editMode && showAdmissionDateReadOnlyField && (
            <TimeLineItem
              topLineIsVisible={
                caseDetails?.caseType?.isOncology ||
                showArrivalDateReadOnlyField
              }
              circleColor="green"
              circleIsFilled={Boolean(caseDetails?.visit?.admittedAt)}
              bottomLineIsVisible={
                showHospitalDischargeReadOnlyField ||
                showSurgeryDateReadOnlyField
              }
            >
              <ReadOnlyField
                title={
                  caseDetails?.caseType?.isAdminOutpatient
                    ? "Encounter Date"
                    : "Admission Date"
                }
                body={
                  caseDetails?.visit?.admittedAt &&
                  formatISOToDate(caseDetails.visit.admittedAt, "MM/dd/yyyy")
                }
              />
            </TimeLineItem>
          )}
          {editMode && showSurgeryDateInput && (
            <TimeLineItem
              topLineIsVisible={
                caseTypeSelectedOption?.isOncology ||
                showArrivalDateInput ||
                showAdmissionDateInput
              }
              circleColor="red"
              circleIsFilled={Boolean(caseDetails?.visit?.surgeryDate)}
              bottomLineIsVisible={showHospitalDischargeDateInput}
            >
              <ControlledDateInput
                id="surgeryDate"
                name="surgeryDate"
                label="Procedure Date (optional)"
                control={control}
                error={Boolean(errors.surgeryDate)}
                errorText={errors.surgeryDate?.message}
                minDate={minDate}
                maxDate={today}
              />
            </TimeLineItem>
          )}
          {!editMode && showSurgeryDateReadOnlyField && (
            <TimeLineItem
              topLineIsVisible={
                caseDetails?.caseType?.isOncology ||
                showArrivalDateReadOnlyField ||
                showAdmissionDateReadOnlyField
              }
              circleColor="red"
              circleIsFilled={Boolean(caseDetails?.visit?.surgeryDate)}
              bottomLineIsVisible={showHospitalDischargeReadOnlyField}
            >
              <ReadOnlyField
                title="Procedure Date (optional)"
                body={
                  caseDetails?.visit?.surgeryDate &&
                  formatISOToDate(caseDetails.visit.surgeryDate, "MM/dd/yyyy")
                }
              />
            </TimeLineItem>
          )}
          {editMode && showHospitalDischargeDateInput && (
            <TimeLineItem
              topLineIsVisible={
                caseTypeSelectedOption?.isOncology ||
                showArrivalDateInput ||
                showAdmissionDateInput ||
                showSurgeryDateInput
              }
              bottomLineIsVisible={false}
              circleColor="blue"
              circleIsFilled={Boolean(caseDetails?.visit?.hospitalDischargedAt)}
            >
              <ControlledDateInput
                id="hospitalDischargedAt"
                name="hospitalDischargedAt"
                label="Discharge Date"
                control={control}
                error={Boolean(errors.hospitalDischargedAt)}
                errorText={errors.hospitalDischargedAt?.message}
                minDate={minDate}
                maxDate={today}
              />
            </TimeLineItem>
          )}
          {!editMode && showHospitalDischargeReadOnlyField && (
            <TimeLineItem
              topLineIsVisible={
                caseDetails?.caseType?.isOncology ||
                showArrivalDateReadOnlyField ||
                showAdmissionDateReadOnlyField ||
                showSurgeryDateReadOnlyField
              }
              bottomLineIsVisible={false}
              circleColor="blue"
              circleIsFilled={Boolean(caseDetails?.visit?.hospitalDischargedAt)}
            >
              <ReadOnlyField
                title="Discharge Date"
                body={
                  caseDetails?.visit?.hospitalDischargedAt &&
                  formatISOToDate(
                    caseDetails.visit.hospitalDischargedAt,
                    "MM/dd/yyyy"
                  )
                }
              />
            </TimeLineItem>
          )}
        </div>
        <div className="tw-flex tw-flex-col tw-gap-5 tw-px-5 tw-py-10 2xl:tw-basis-1/2">
          <div className="tw-w-full tw-text-sm">
            <Dropdown
              placeholder={caseStatus}
              options={caseStatusOptions}
              onChange={handleStatusChange}
            />
          </div>
          {caseDetails?.caseType?.isOncology && (
            <div className="tw-flex tw-justify-evenly tw-gap-5 2xl:tw-flex-col">
              <div>
                <span className="tw-text-xs tw-text-black-54">Reportable</span>
                <SwitchWrapper
                  callback={handleReportableStatusChange}
                  defaultState={caseDetails?.reportable === "reportable"}
                />
              </div>
              <div>
                <span className="tw-text-xs tw-text-black-54">Analytic</span>
                <SwitchWrapper
                  callback={handleAnalyticStatusChange}
                  defaultState={Boolean(caseDetails?.analytic)}
                />
              </div>
            </div>
          )}
          {caseDetails?.permissions?.canEditAbstractionMismatch && (
            <div className="tw-flex tw-justify-evenly tw-gap-5 2xl:tw-flex-col">
              <div>
                <span className="tw-text-xs tw-text-black-54">
                  Abstraction Mismatch
                </span>
                <SwitchWrapper
                  callback={handleAbstractionMismatchChange}
                  defaultState={Boolean(caseDetails?.abstractionMismatch)}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </form>
  );
};

export default CaseDetailsForm;
