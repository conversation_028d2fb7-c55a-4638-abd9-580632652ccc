// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TimeLineItem it renders component correctly with bottom line not visible 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-6"
>
  <div
    className="tw-flex tw-flex-col tw-items-center tw-self-stretch"
  >
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
    <div
      className="tw-h-[25px] tw-w-[25px] tw-shrink-0 tw-rounded-full tw-border-8 tw-border-qc-blue-700 tw-bg-qc-blue-700"
    />
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-transparent"
    />
  </div>
  <div
    className="tw-grow"
  >
    <p>
      Mock child
    </p>
  </div>
</div>
`;

exports[`TimeLineItem it renders component correctly with circle not filled 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-6"
>
  <div
    className="tw-flex tw-flex-col tw-items-center tw-self-stretch"
  >
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
    <div
      className="tw-h-[25px] tw-w-[25px] tw-shrink-0 tw-rounded-full tw-border-8 tw-border-qc-blue-700 tw-bg-transparent"
    />
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
  </div>
  <div
    className="tw-grow"
  >
    <p>
      Mock child
    </p>
  </div>
</div>
`;

exports[`TimeLineItem it renders component correctly with default props 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-6"
>
  <div
    className="tw-flex tw-flex-col tw-items-center tw-self-stretch"
  >
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
    <div
      className="tw-h-[25px] tw-w-[25px] tw-shrink-0 tw-rounded-full tw-border-8 tw-border-qc-blue-700 tw-bg-qc-blue-700"
    />
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
  </div>
  <div
    className="tw-grow"
  >
    <p>
      Mock child
    </p>
  </div>
</div>
`;

exports[`TimeLineItem it renders component correctly with top line not visible 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-6"
>
  <div
    className="tw-flex tw-flex-col tw-items-center tw-self-stretch"
  >
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-transparent"
    />
    <div
      className="tw-h-[25px] tw-w-[25px] tw-shrink-0 tw-rounded-full tw-border-8 tw-border-qc-blue-700 tw-bg-qc-blue-700"
    />
    <div
      className="tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow tw-bg-gray-600"
    />
  </div>
  <div
    className="tw-grow"
  >
    <p>
      Mock child
    </p>
  </div>
</div>
`;
