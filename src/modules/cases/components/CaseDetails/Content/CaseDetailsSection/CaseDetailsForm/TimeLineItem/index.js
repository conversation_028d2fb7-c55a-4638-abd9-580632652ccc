import PropTypes from "prop-types";
import { cva } from "class-variance-authority";

const lineVariants = cva("tw-h-[25px] tw-w-[2px] tw-shrink-0 tw-grow", {
  variants: {
    visible: {
      true: "tw-bg-gray-600",
      false: "tw-bg-transparent"
    }
  },
  defaultVariants: {
    visible: true
  }
});

const circleVariants = cva(
  "tw-h-[25px] tw-w-[25px] tw-shrink-0 tw-rounded-full tw-border-8",
  {
    variants: {
      color: {
        blue: "tw-border-qc-blue-700",
        red: "tw-border-error-700",
        green: "tw-border-green-700",
        orange: "tw-border-qc-orange-700",
        purple: "tw-border-purple-700"
      },
      filled: {
        true: "",
        false: "tw-bg-transparent"
      }
    },
    compoundVariants: [
      {
        color: "blue",
        filled: true,
        class: "tw-bg-qc-blue-700"
      },
      {
        color: "red",
        filled: true,
        class: "tw-bg-error-700"
      },
      {
        color: "green",
        filled: true,
        class: "tw-bg-green-700"
      },
      {
        color: "orange",
        filled: true,
        class: "tw-bg-qc-orange-700"
      },
      {
        color: "purple",
        filled: true,
        class: "tw-bg-purple-700"
      }
    ],
    defaultVariants: {
      color: "blue",
      filled: true
    }
  }
);

export const TimeLineItem = ({
  children,
  circleColor = "blue",
  circleIsFilled = true,
  topLineIsVisible = true,
  bottomLineIsVisible = true
}) => (
  <div className="tw-flex tw-items-center tw-gap-x-6">
    <div className="tw-flex tw-flex-col tw-items-center tw-self-stretch">
      <div className={lineVariants({ visible: topLineIsVisible })} />
      <div
        className={circleVariants({
          color: circleColor,
          filled: circleIsFilled
        })}
      />
      <div className={lineVariants({ visible: bottomLineIsVisible })} />
    </div>
    <div className="tw-grow">{children}</div>
  </div>
);

TimeLineItem.propTypes = {
  bottomLineIsVisible: PropTypes.bool,
  children: PropTypes.node,
  circleColor: PropTypes.oneOf(["blue", "red", "green", "orange", "purple"]),
  circleIsFilled: PropTypes.bool,
  topLineIsVisible: PropTypes.bool
};

export default TimeLineItem;
