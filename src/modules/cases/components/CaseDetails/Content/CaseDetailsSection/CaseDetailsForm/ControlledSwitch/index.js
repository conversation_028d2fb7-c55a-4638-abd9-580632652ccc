import { Switch } from "@q-centrix/q-components-react";
import { Controller } from "react-hook-form";

const ControlledSwitch = ({ id, name, control, disabled }) => (
  <Controller
    name={name}
    control={control}
    disabled={disabled}
    defaultValue={false}
    render={({ field }) => (
      <Switch
        id={id}
        defaultToggle={false}
        fieldName={field.name}
        checked={field.value}
        disabled={field.disabled}
        // eslint-disable-next-line react/jsx-handler-names
        onChange={field.onChange}
      />
    )}
  />
);

export default ControlledSwitch;
