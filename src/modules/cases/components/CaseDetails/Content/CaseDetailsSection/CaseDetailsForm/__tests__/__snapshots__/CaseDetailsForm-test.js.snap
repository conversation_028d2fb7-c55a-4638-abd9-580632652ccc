// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseDetailsForm it renders component with case type is oncology 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-rounded-b-[5px] tw-bg-white"
  id="case-details-form"
>
  <div
    className="tw-grid tw-basis-7/12 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-7 tw-border-r tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-basis-1/2 2xl:tw-grid-cols-3"
  >
    <ReadOnlyField
      body="Large Medical Center"
      title="Facility Name"
    />
    <ReadOnlyField
      body="Mock Case Type"
      title="Case Type"
    />
    <ReadOnlyField
      body="Adrenal Gland"
      title="Cancer Type"
    />
    <ReadOnlyField
      body="1"
      title="MRN"
    />
    <ReadOnlyField
      body="333111"
      title="GWTG ID (Optional)"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Owner"
    />
    <ReadOnlyField
      body="<PERSON> Reas"
      title="Case Assignee"
    />
    <ReadOnlyField
      body="1"
      title="Case ID"
    />
    <ReadOnlyField
      body="Doe"
      title="Patient Last Name (Optional)"
    />
    <ReadOnlyField
      body="John"
      title="Patient First Name (Optional)"
    />
    <ReadOnlyField
      body=""
      title="Patient Middle Name (Optional)"
    />
    <ReadOnlyField
      body="12/12/1995"
      title="Patient DOB (Optional)"
    />
    <ReadOnlyField
      title="Deadline (Optional)"
    />
    <ReadOnlyField
      title="Batch ID"
    >
      <a
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
        href="/qapps/upload/case_uploads/1"
      >
        1
      </a>
    </ReadOnlyField>
  </div>
  <div
    className="tw-flex tw-basis-5/12 tw-flex-col 2xl:tw-basis-1/2 2xl:!tw-flex-row"
  >
    <div
      className="tw-flex tw-grow tw-flex-col tw-justify-center tw-border-b tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-grow-0 2xl:tw-basis-1/2 2xl:!tw-justify-start 2xl:tw-border-r"
    >
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="purple"
        circleIsFilled={false}
        topLineIsVisible={false}
      >
        <ReadOnlyField
          body={null}
          title="Date of First Contact"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="orange"
        circleIsFilled={true}
        topLineIsVisible={true}
      >
        <ReadOnlyField
          body="06/18/2023"
          title="Arrival Date"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={false}
        circleColor="blue"
        circleIsFilled={true}
        topLineIsVisible={true}
      >
        <ReadOnlyField
          body="06/19/2023"
          title="Discharge Date"
        />
      </TimeLineItem>
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-5 tw-px-5 tw-py-10 2xl:tw-basis-1/2"
    >
      <div
        className="tw-w-full tw-text-sm"
      >
        <Dropdown
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Not Started",
                "value": "Not Started",
              },
              Object {
                "label": "Pending-Initial Reabstraction",
                "value": "Pending-Initial Reabstraction",
              },
              Object {
                "label": "Pending-Incomplete Abstraction",
                "value": "Pending-Incomplete Abstraction",
              },
              Object {
                "label": "Pending-Missing Documentation",
                "value": "Pending-Missing Documentation",
              },
              Object {
                "label": "Pending-Second Look",
                "value": "Pending-Second Look",
              },
              Object {
                "label": "Pending-Fallout Review",
                "value": "Pending-Fallout Review",
              },
              Object {
                "label": "Pending-Other",
                "value": "Pending-Other",
              },
              Object {
                "label": "Billable",
                "value": "Billable",
              },
              Object {
                "label": "Ineligible",
                "value": "Ineligible",
              },
            ]
          }
          placeholder="Not Started"
        />
      </div>
      <div
        className="tw-flex tw-justify-evenly tw-gap-5 2xl:tw-flex-col"
      >
        <div>
          <span
            className="tw-text-xs tw-text-black-54"
          >
            Reportable
          </span>
          <SwitchWrapper
            callback={[Function]}
            defaultState={false}
          />
        </div>
        <div>
          <span
            className="tw-text-xs tw-text-black-54"
          >
            Analytic
          </span>
          <SwitchWrapper
            callback={[Function]}
            defaultState={false}
          />
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`CaseDetailsForm it renders component with data 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-rounded-b-[5px] tw-bg-white"
  id="case-details-form"
>
  <div
    className="tw-grid tw-basis-7/12 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-7 tw-border-r tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-basis-1/2 2xl:tw-grid-cols-3"
  >
    <ReadOnlyField
      body="Large Medical Center"
      title="Facility Name"
    />
    <ReadOnlyField
      body="Mock Case Type"
      title="Case Type"
    />
    <ReadOnlyField
      body="1"
      title="Visit #"
    />
    <ReadOnlyField
      body="1"
      title="MRN"
    />
    <ReadOnlyField
      body="333111"
      title="GWTG ID (Optional)"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Owner"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Assignee"
    />
    <ReadOnlyField
      body="1"
      title="Case ID"
    />
    <ReadOnlyField
      body="Doe"
      title="Patient Last Name (Optional)"
    />
    <ReadOnlyField
      body="John"
      title="Patient First Name (Optional)"
    />
    <ReadOnlyField
      body=""
      title="Patient Middle Name (Optional)"
    />
    <ReadOnlyField
      body="12/12/1995"
      title="Patient DOB (Optional)"
    />
    <ReadOnlyField
      title="Deadline (Optional)"
    />
    <ReadOnlyField
      title="Batch ID"
    >
      <a
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
        href="/qapps/upload/case_uploads/1"
      >
        1
      </a>
    </ReadOnlyField>
  </div>
  <div
    className="tw-flex tw-basis-5/12 tw-flex-col 2xl:tw-basis-1/2 2xl:!tw-flex-row"
  >
    <div
      className="tw-flex tw-grow tw-flex-col tw-justify-center tw-border-b tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-grow-0 2xl:tw-basis-1/2 2xl:!tw-justify-start 2xl:tw-border-r"
    >
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="orange"
        circleIsFilled={true}
        topLineIsVisible={false}
      >
        <ReadOnlyField
          body="06/18/2023"
          title="Arrival Date"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="red"
        circleIsFilled={false}
        topLineIsVisible={true}
      >
        <ReadOnlyField
          body={null}
          title="Procedure Date (optional)"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={false}
        circleColor="blue"
        circleIsFilled={true}
        topLineIsVisible={true}
      >
        <ReadOnlyField
          body="06/19/2023"
          title="Discharge Date"
        />
      </TimeLineItem>
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-5 tw-px-5 tw-py-10 2xl:tw-basis-1/2"
    >
      <div
        className="tw-w-full tw-text-sm"
      >
        <Dropdown
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Not Started",
                "value": "Not Started",
              },
              Object {
                "label": "Pending-Initial Reabstraction",
                "value": "Pending-Initial Reabstraction",
              },
              Object {
                "label": "Pending-Incomplete Abstraction",
                "value": "Pending-Incomplete Abstraction",
              },
              Object {
                "label": "Pending-Missing Documentation",
                "value": "Pending-Missing Documentation",
              },
              Object {
                "label": "Pending-Second Look",
                "value": "Pending-Second Look",
              },
              Object {
                "label": "Pending-Fallout Review",
                "value": "Pending-Fallout Review",
              },
              Object {
                "label": "Pending-Other",
                "value": "Pending-Other",
              },
              Object {
                "label": "Billable",
                "value": "Billable",
              },
              Object {
                "label": "Ineligible",
                "value": "Ineligible",
              },
            ]
          }
          placeholder="Not Started"
        />
      </div>
    </div>
  </div>
</form>
`;

exports[`CaseDetailsForm it renders component with denied permissions 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-rounded-b-[5px] tw-bg-white"
  id="case-details-form"
>
  <div
    className="tw-grid tw-basis-7/12 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-7 tw-border-r tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-basis-1/2 2xl:tw-grid-cols-3"
  >
    <ReadOnlyField
      body="Large Medical Center"
      title="Facility Name"
    />
    <ReadOnlyField
      body="Mock Case Type"
      title="Case Type"
    />
    <ReadOnlyField
      body="1"
      title="MRN"
    />
    <Input
      error={false}
      id="gwtgId"
      label="GWTG ID (Optional)"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Owner"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Assignee"
    />
    <ReadOnlyField
      body="1"
      title="Case ID"
    />
    <Input
      error={false}
      id="lastName"
      label="Patient Last Name (Optional)"
    />
    <Input
      error={false}
      id="firstName"
      label="Patient First Name (Optional)"
    />
    <Input
      error={false}
      id="middleName"
      label="Patient Middle Name (Optional)"
    />
    <ControlledDateInput
      control={[MockFunction]}
      error={false}
      id="bornOn"
      label="Patient DOB (Optional)"
      maxDate={2022-01-01T00:00:00.000Z}
      minDate={1900-01-01T00:00:00.000Z}
      name="bornOn"
    />
    <ReadOnlyField
      title="Deadline (Optional)"
    />
    <ReadOnlyField
      title="Batch ID"
    >
      <a
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
        href="/qapps/upload/case_uploads/1"
      >
        1
      </a>
    </ReadOnlyField>
  </div>
  <div
    className="tw-flex tw-basis-5/12 tw-flex-col 2xl:tw-basis-1/2 2xl:!tw-flex-row"
  >
    <div
      className="tw-flex tw-grow tw-flex-col tw-justify-center tw-border-b tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-grow-0 2xl:tw-basis-1/2 2xl:!tw-justify-start 2xl:tw-border-r"
    >
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="orange"
        circleIsFilled={true}
        topLineIsVisible={false}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="arrivedAt"
          label="Arrival Date"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="arrivedAt"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="red"
        circleIsFilled={false}
        topLineIsVisible={true}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="surgeryDate"
          label="Procedure Date (optional)"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="surgeryDate"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={false}
        circleColor="blue"
        circleIsFilled={true}
        topLineIsVisible={true}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="hospitalDischargedAt"
          label="Discharge Date"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="hospitalDischargedAt"
        />
      </TimeLineItem>
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-5 tw-px-5 tw-py-10 2xl:tw-basis-1/2"
    >
      <div
        className="tw-w-full tw-text-sm"
      >
        <Dropdown
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Not Started",
                "value": "Not Started",
              },
              Object {
                "label": "Pending-Initial Reabstraction",
                "value": "Pending-Initial Reabstraction",
              },
              Object {
                "label": "Pending-Incomplete Abstraction",
                "value": "Pending-Incomplete Abstraction",
              },
              Object {
                "label": "Pending-Missing Documentation",
                "value": "Pending-Missing Documentation",
              },
              Object {
                "label": "Pending-Second Look",
                "value": "Pending-Second Look",
              },
              Object {
                "label": "Pending-Fallout Review",
                "value": "Pending-Fallout Review",
              },
              Object {
                "label": "Pending-Other",
                "value": "Pending-Other",
              },
              Object {
                "label": "Billable",
                "value": "Billable",
              },
              Object {
                "label": "Ineligible",
                "value": "Ineligible",
              },
            ]
          }
          placeholder="Not Started"
        />
      </div>
    </div>
  </div>
</form>
`;

exports[`CaseDetailsForm it renders component with error 1`] = `
<p
  className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500"
>
  Error: 
  Mock error
</p>
`;

exports[`CaseDetailsForm it renders component with loading state 1`] = `
<div
  className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10"
>
  <Spinner />
</div>
`;

exports[`CaseDetailsForm it renders component with permissions 1`] = `
<form
  autoComplete="off"
  className="tw-flex tw-rounded-b-[5px] tw-bg-white"
  id="case-details-form"
>
  <div
    className="tw-grid tw-basis-7/12 tw-grid-cols-2 tw-gap-x-5 tw-gap-y-7 tw-border-r tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-basis-1/2 2xl:tw-grid-cols-3"
  >
    <ControlledSelect
      control={[MockFunction]}
      error={false}
      fields={
        Array [
          "id",
          "name",
        ]
      }
      id="facilityId"
      isPageable={true}
      isSearchable={true}
      label="Facility Name"
      name="facilityId"
      query={
        Object {
          "definitions": Array [
            Object {
              "directives": Array [],
              "kind": "OperationDefinition",
              "name": Object {
                "kind": "Name",
                "value": "currentUserAvailableFacilities",
              },
              "operation": "query",
              "selectionSet": Object {
                "kind": "SelectionSet",
                "selections": Array [
                  Object {
                    "alias": undefined,
                    "arguments": Array [
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "perPage",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "perPage",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "page",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "page",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "search",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "search",
                          },
                        },
                      },
                    ],
                    "directives": Array [],
                    "kind": "Field",
                    "name": Object {
                      "kind": "Name",
                      "value": "currentUserAvailableFacilities",
                    },
                    "selectionSet": Object {
                      "kind": "SelectionSet",
                      "selections": Array [
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "userAvailableFacilities",
                          },
                          "selectionSet": Object {
                            "kind": "SelectionSet",
                            "selections": Array [
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "id",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "name",
                                },
                                "selectionSet": undefined,
                              },
                            ],
                          },
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "count",
                          },
                          "selectionSet": undefined,
                        },
                      ],
                    },
                  },
                ],
              },
              "variableDefinitions": Array [
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "perPage",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "page",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "String",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "search",
                    },
                  },
                },
              ],
            },
          ],
          "kind": "Document",
          "loc": Object {
            "end": 294,
            "start": 0,
          },
        }
      }
      queryObjectNamePath={
        Array [
          "currentUserAvailableFacilities",
          "userAvailableFacilities",
        ]
      }
      smallPagination={true}
      variables={
        Object {
          "perPage": 25,
        }
      }
    />
    <ControlledSelect
      control={[MockFunction]}
      error={false}
      fields={
        Array [
          "id",
          "name",
          "isOncology",
          "isRegulatory",
          "isAdminInpatient",
          "isAdminOutpatient",
          "businessOffering",
          "serviceLine",
          "allowsEmptyDischargeDate",
          "lengthOfStay",
        ]
      }
      id="caseTypeId"
      isPageable={true}
      isSearchable={true}
      label="Case Type"
      name="caseTypeId"
      query={
        Object {
          "definitions": Array [
            Object {
              "directives": Array [],
              "kind": "OperationDefinition",
              "name": Object {
                "kind": "Name",
                "value": "userCaseTypesByFacility",
              },
              "operation": "query",
              "selectionSet": Object {
                "kind": "SelectionSet",
                "selections": Array [
                  Object {
                    "alias": undefined,
                    "arguments": Array [
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "facilityId",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "facilityId",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "perPage",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "perPage",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "page",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "page",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "search",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "search",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "excludeGenericCaseTypes",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "excludeGenericCaseTypes",
                          },
                        },
                      },
                    ],
                    "directives": Array [],
                    "kind": "Field",
                    "name": Object {
                      "kind": "Name",
                      "value": "userCaseTypesByFacility",
                    },
                    "selectionSet": Object {
                      "kind": "SelectionSet",
                      "selections": Array [
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "userFacilityCaseTypes",
                          },
                          "selectionSet": Object {
                            "kind": "SelectionSet",
                            "selections": Array [
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "id",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "name",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "generic",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "isOncology",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "isRegulatory",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "isAdminInpatient",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "isAdminOutpatient",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "businessOffering",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "serviceLine",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "allowsEmptyDischargeDate",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "lengthOfStay",
                                },
                                "selectionSet": Object {
                                  "kind": "SelectionSet",
                                  "selections": Array [
                                    Object {
                                      "alias": undefined,
                                      "arguments": Array [],
                                      "directives": Array [],
                                      "kind": "Field",
                                      "name": Object {
                                        "kind": "Name",
                                        "value": "start",
                                      },
                                      "selectionSet": undefined,
                                    },
                                    Object {
                                      "alias": undefined,
                                      "arguments": Array [],
                                      "directives": Array [],
                                      "kind": "Field",
                                      "name": Object {
                                        "kind": "Name",
                                        "value": "end",
                                      },
                                      "selectionSet": undefined,
                                    },
                                    Object {
                                      "alias": undefined,
                                      "arguments": Array [],
                                      "directives": Array [],
                                      "kind": "Field",
                                      "name": Object {
                                        "kind": "Name",
                                        "value": "allowOverride",
                                      },
                                      "selectionSet": undefined,
                                    },
                                  ],
                                },
                              },
                            ],
                          },
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "count",
                          },
                          "selectionSet": undefined,
                        },
                      ],
                    },
                  },
                ],
              },
              "variableDefinitions": Array [
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "facilityId",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "perPage",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "page",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "String",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "search",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Boolean",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "excludeGenericCaseTypes",
                    },
                  },
                },
              ],
            },
          ],
          "kind": "Document",
          "loc": Object {
            "end": 695,
            "start": 0,
          },
        }
      }
      queryObjectNamePath={
        Array [
          "userCaseTypesByFacility",
          "userFacilityCaseTypes",
        ]
      }
      smallPagination={true}
      variables={
        Object {
          "facilityId": 1,
          "perPage": 25,
        }
      }
    />
    <Input
      error={false}
      id="visitNumber"
      label="Visit #"
    />
    <Input
      error={false}
      id="mrn"
      label="MRN"
    />
    <Input
      error={false}
      id="gwtgId"
      label="GWTG ID (Optional)"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Owner"
    />
    <ReadOnlyField
      body="Russell Reas"
      title="Case Assignee"
    />
    <ReadOnlyField
      body="1"
      title="Case ID"
    />
    <Input
      error={false}
      id="lastName"
      label="Patient Last Name (Optional)"
    />
    <Input
      error={false}
      id="firstName"
      label="Patient First Name (Optional)"
    />
    <Input
      error={false}
      id="middleName"
      label="Patient Middle Name (Optional)"
    />
    <ControlledDateInput
      control={[MockFunction]}
      error={false}
      id="bornOn"
      label="Patient DOB (Optional)"
      maxDate={2022-01-01T00:00:00.000Z}
      minDate={1900-01-01T00:00:00.000Z}
      name="bornOn"
    />
    <ReadOnlyField
      title="Deadline (Optional)"
    />
    <ReadOnlyField
      title="Batch ID"
    >
      <a
        className="tw-cursor-pointer tw-text-sm tw-font-semibold tw-text-qc-blue-700 tw-underline"
        href="/qapps/upload/case_uploads/1"
      >
        1
      </a>
    </ReadOnlyField>
  </div>
  <div
    className="tw-flex tw-basis-5/12 tw-flex-col 2xl:tw-basis-1/2 2xl:!tw-flex-row"
  >
    <div
      className="tw-flex tw-grow tw-flex-col tw-justify-center tw-border-b tw-border-black/[0.12] tw-px-5 tw-py-10 2xl:tw-grow-0 2xl:tw-basis-1/2 2xl:!tw-justify-start 2xl:tw-border-r"
    >
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="orange"
        circleIsFilled={true}
        topLineIsVisible={false}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="arrivedAt"
          label="Arrival Date"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="arrivedAt"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={true}
        circleColor="red"
        circleIsFilled={false}
        topLineIsVisible={true}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="surgeryDate"
          label="Procedure Date (optional)"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="surgeryDate"
        />
      </TimeLineItem>
      <TimeLineItem
        bottomLineIsVisible={false}
        circleColor="blue"
        circleIsFilled={true}
        topLineIsVisible={true}
      >
        <ControlledDateInput
          control={[MockFunction]}
          error={false}
          id="hospitalDischargedAt"
          label="Discharge Date"
          maxDate={2022-01-01T00:00:00.000Z}
          minDate={1900-01-01T00:00:00.000Z}
          name="hospitalDischargedAt"
        />
      </TimeLineItem>
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-5 tw-px-5 tw-py-10 2xl:tw-basis-1/2"
    >
      <div
        className="tw-w-full tw-text-sm"
      >
        <Dropdown
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Not Started",
                "value": "Not Started",
              },
              Object {
                "label": "Pending-Initial Reabstraction",
                "value": "Pending-Initial Reabstraction",
              },
              Object {
                "label": "Pending-Incomplete Abstraction",
                "value": "Pending-Incomplete Abstraction",
              },
              Object {
                "label": "Pending-Missing Documentation",
                "value": "Pending-Missing Documentation",
              },
              Object {
                "label": "Pending-Second Look",
                "value": "Pending-Second Look",
              },
              Object {
                "label": "Pending-Fallout Review",
                "value": "Pending-Fallout Review",
              },
              Object {
                "label": "Pending-Other",
                "value": "Pending-Other",
              },
              Object {
                "label": "Billable",
                "value": "Billable",
              },
              Object {
                "label": "Ineligible",
                "value": "Ineligible",
              },
            ]
          }
          placeholder="Not Started"
        />
      </div>
    </div>
  </div>
</form>
`;
