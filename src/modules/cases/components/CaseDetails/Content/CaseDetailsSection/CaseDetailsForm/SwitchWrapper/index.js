import { useState } from "react";
import { Switch } from "@q-centrix/q-components-react";

const SwitchWrapper = ({ callback, defaultState }) => {
  const [checked, setChecked] = useState(defaultState);

  const handleReportableStatusChange = newStatus => {
    setChecked(newStatus);
    callback(newStatus, () => setChecked(!newStatus));
  };

  return (
    <Switch
      checked={checked}
      onChange={handleReportableStatusChange}
      label={checked ? "Yes" : "No"}
    />
  );
};

export default SwitchWrapper;
