// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseDetailsSection it renders component with editMode to false 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <CaseDetailsHeaderContent
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      editMode={false}
      form={
        Object {
          "formState": Object {
            "isSubmitting": false,
          },
        }
      }
      loading={false}
      onCancelEdit={[MockFunction]}
      onEditClick={[MockFunction]}
    />
  }
  loading={false}
>
  <CaseDetailsForm
    caseDetails={
      Object {
        "analytic": false,
        "assignee": Object {
          "fullName": "Russell Reas",
          "id": "1",
        },
        "batch": Object {
          "id": "1",
          "link": "/qapps/upload/case_uploads/1",
        },
        "cancerSite": null,
        "caseStatusOptions": Array [
          Object {
            "displayValue": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "displayValue": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "displayValue": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "displayValue": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "displayValue": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "displayValue": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "displayValue": "Billable",
            "value": "Billable",
          },
          Object {
            "displayValue": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "displayValue": "Ineligible",
            "value": "Ineligible",
          },
        ],
        "caseType": Object {
          "allowsEmptyDischargeDate": false,
          "businessOffering": "registry",
          "id": "1",
          "isAdminInpatient": false,
          "isAdminOutpatient": false,
          "isOncology": false,
          "isRegulatory": false,
          "lengthOfStay": Object {
            "end": null,
            "start": null,
          },
          "name": "Mock Case Type",
          "serviceLine": "GWTG",
        },
        "facility": Object {
          "id": "1",
          "name": "Large Medical Center",
        },
        "generic": false,
        "gwtgId": "333111",
        "id": "1",
        "isEditable": true,
        "losOverride": null,
        "owner": Object {
          "fullName": "Russell Reas",
          "id": "1",
        },
        "patient": Object {
          "bornOn": "1995-12-12T19:00:00-04:00",
          "firstName": "John",
          "id": "1",
          "lastName": "Doe",
          "middleName": "",
          "mrn": "1",
        },
        "permissions": Object {
          "canChangeCompleteCaseStatus": true,
          "canEditFacilityAndCaseType": true,
          "canEditVisitNumberAndMrn": true,
        },
        "primarySite": null,
        "reportable": "non_reportable",
        "status": "Billable",
        "visit": Object {
          "admittedAt": null,
          "arrivedAt": "2023-06-18T19:00:00-04:00",
          "firstContact": null,
          "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
          "id": "1",
          "number": "1",
          "surgeryDate": null,
        },
      }
    }
    caseId={1}
    caseStatus="Not Started"
    caseStatusOptions={
      Array [
        Object {
          "label": "Not Started",
          "value": "Not Started",
        },
        Object {
          "label": "Pending-Initial Reabstraction",
          "value": "Pending-Initial Reabstraction",
        },
        Object {
          "label": "Pending-Incomplete Abstraction",
          "value": "Pending-Incomplete Abstraction",
        },
        Object {
          "label": "Pending-Missing Documentation",
          "value": "Pending-Missing Documentation",
        },
        Object {
          "label": "Pending-Second Look",
          "value": "Pending-Second Look",
        },
        Object {
          "label": "Pending-Fallout Review",
          "value": "Pending-Fallout Review",
        },
        Object {
          "label": "Pending-Other",
          "value": "Pending-Other",
        },
        Object {
          "label": "Billable",
          "value": "Billable",
        },
        Object {
          "label": "Ineligible",
          "value": "Ineligible",
        },
      ]
    }
    editMode={false}
    form={
      Object {
        "formState": Object {
          "isSubmitting": false,
        },
      }
    }
    loading={false}
    onUpdateCase={[MockFunction]}
  />
</CardWithHeader>
`;

exports[`CaseDetailsSection it renders component with editMode to true 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <CaseDetailsHeaderContent
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      editMode={true}
      form={
        Object {
          "formState": Object {
            "isSubmitting": false,
          },
        }
      }
      loading={false}
      onCancelEdit={[MockFunction]}
      onEditClick={[MockFunction]}
    />
  }
  loading={false}
>
  <CaseDetailsForm
    caseDetails={
      Object {
        "analytic": false,
        "assignee": Object {
          "fullName": "Russell Reas",
          "id": "1",
        },
        "batch": Object {
          "id": "1",
          "link": "/qapps/upload/case_uploads/1",
        },
        "cancerSite": null,
        "caseStatusOptions": Array [
          Object {
            "displayValue": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "displayValue": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "displayValue": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "displayValue": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "displayValue": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "displayValue": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "displayValue": "Billable",
            "value": "Billable",
          },
          Object {
            "displayValue": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "displayValue": "Ineligible",
            "value": "Ineligible",
          },
        ],
        "caseType": Object {
          "allowsEmptyDischargeDate": false,
          "businessOffering": "registry",
          "id": "1",
          "isAdminInpatient": false,
          "isAdminOutpatient": false,
          "isOncology": false,
          "isRegulatory": false,
          "lengthOfStay": Object {
            "end": null,
            "start": null,
          },
          "name": "Mock Case Type",
          "serviceLine": "GWTG",
        },
        "facility": Object {
          "id": "1",
          "name": "Large Medical Center",
        },
        "generic": false,
        "gwtgId": "333111",
        "id": "1",
        "isEditable": true,
        "losOverride": null,
        "owner": Object {
          "fullName": "Russell Reas",
          "id": "1",
        },
        "patient": Object {
          "bornOn": "1995-12-12T19:00:00-04:00",
          "firstName": "John",
          "id": "1",
          "lastName": "Doe",
          "middleName": "",
          "mrn": "1",
        },
        "permissions": Object {
          "canChangeCompleteCaseStatus": true,
          "canEditFacilityAndCaseType": true,
          "canEditVisitNumberAndMrn": true,
        },
        "primarySite": null,
        "reportable": "non_reportable",
        "status": "Billable",
        "visit": Object {
          "admittedAt": null,
          "arrivedAt": "2023-06-18T19:00:00-04:00",
          "firstContact": null,
          "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
          "id": "1",
          "number": "1",
          "surgeryDate": null,
        },
      }
    }
    caseId={1}
    caseStatus="Not Started"
    caseStatusOptions={
      Array [
        Object {
          "label": "Not Started",
          "value": "Not Started",
        },
        Object {
          "label": "Pending-Initial Reabstraction",
          "value": "Pending-Initial Reabstraction",
        },
        Object {
          "label": "Pending-Incomplete Abstraction",
          "value": "Pending-Incomplete Abstraction",
        },
        Object {
          "label": "Pending-Missing Documentation",
          "value": "Pending-Missing Documentation",
        },
        Object {
          "label": "Pending-Second Look",
          "value": "Pending-Second Look",
        },
        Object {
          "label": "Pending-Fallout Review",
          "value": "Pending-Fallout Review",
        },
        Object {
          "label": "Pending-Other",
          "value": "Pending-Other",
        },
        Object {
          "label": "Billable",
          "value": "Billable",
        },
        Object {
          "label": "Ineligible",
          "value": "Ineligible",
        },
      ]
    }
    editMode={true}
    form={
      Object {
        "formState": Object {
          "isSubmitting": false,
        },
      }
    }
    loading={false}
    onUpdateCase={[MockFunction]}
  />
</CardWithHeader>
`;

exports[`CaseDetailsSection it renders component with error 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <CaseDetailsHeaderContent
      caseDetails={null}
      editMode={false}
      form={
        Object {
          "formState": Object {
            "isSubmitting": false,
          },
        }
      }
      loading={false}
      onCancelEdit={[MockFunction]}
      onEditClick={[MockFunction]}
    />
  }
  loading={false}
>
  <CaseDetailsForm
    caseDetails={null}
    caseId={1}
    caseStatus="Not Started"
    caseStatusOptions={
      Array [
        Object {
          "label": "Not Started",
          "value": "Not Started",
        },
        Object {
          "label": "Pending-Initial Reabstraction",
          "value": "Pending-Initial Reabstraction",
        },
        Object {
          "label": "Pending-Incomplete Abstraction",
          "value": "Pending-Incomplete Abstraction",
        },
        Object {
          "label": "Pending-Missing Documentation",
          "value": "Pending-Missing Documentation",
        },
        Object {
          "label": "Pending-Second Look",
          "value": "Pending-Second Look",
        },
        Object {
          "label": "Pending-Fallout Review",
          "value": "Pending-Fallout Review",
        },
        Object {
          "label": "Pending-Other",
          "value": "Pending-Other",
        },
        Object {
          "label": "Billable",
          "value": "Billable",
        },
        Object {
          "label": "Ineligible",
          "value": "Ineligible",
        },
      ]
    }
    editMode={false}
    error={[Error: Mock error]}
    form={
      Object {
        "formState": Object {
          "isSubmitting": false,
        },
      }
    }
    loading={false}
    onUpdateCase={[MockFunction]}
  />
</CardWithHeader>
`;

exports[`CaseDetailsSection it renders component with loading state 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="!tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-p-5 !tw-rounded-t-[5px]"
  headerContent={
    <CaseDetailsHeaderContent
      editMode={false}
      form={
        Object {
          "formState": Object {
            "isSubmitting": false,
          },
        }
      }
      loading={true}
      onCancelEdit={[MockFunction]}
      onEditClick={[MockFunction]}
    />
  }
  loading={true}
>
  <CaseDetailsForm
    caseId={1}
    caseStatus="Not Started"
    caseStatusOptions={
      Array [
        Object {
          "label": "Not Started",
          "value": "Not Started",
        },
        Object {
          "label": "Pending-Initial Reabstraction",
          "value": "Pending-Initial Reabstraction",
        },
        Object {
          "label": "Pending-Incomplete Abstraction",
          "value": "Pending-Incomplete Abstraction",
        },
        Object {
          "label": "Pending-Missing Documentation",
          "value": "Pending-Missing Documentation",
        },
        Object {
          "label": "Pending-Second Look",
          "value": "Pending-Second Look",
        },
        Object {
          "label": "Pending-Fallout Review",
          "value": "Pending-Fallout Review",
        },
        Object {
          "label": "Pending-Other",
          "value": "Pending-Other",
        },
        Object {
          "label": "Billable",
          "value": "Billable",
        },
        Object {
          "label": "Ineligible",
          "value": "Ineligible",
        },
      ]
    }
    editMode={false}
    form={
      Object {
        "formState": Object {
          "isSubmitting": false,
        },
      }
    }
    loading={true}
    onUpdateCase={[MockFunction]}
  />
</CardWithHeader>
`;
