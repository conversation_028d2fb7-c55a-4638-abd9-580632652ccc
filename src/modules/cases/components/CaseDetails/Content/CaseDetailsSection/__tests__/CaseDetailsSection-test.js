import { create } from "react-test-renderer";
import CaseDetailsSection from "..";
import { caseDetailsMockSuccessResponse } from "../../../graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  Button: "Button"
}));

jest.mock("../CaseDetailsForm", () => "CaseDetailsForm");
jest.mock("../CaseDetailsHeaderContent", () => "CaseDetailsHeaderContent");

const mockCaseDetails = caseDetailsMockSuccessResponse.data.caseDetails.case;

const mockProps = {
  onUpdateCase: jest.fn(),
  caseId: 1,
  form: {
    formState: {
      isSubmitting: false
    }
  },
  caseDetails: mockCaseDetails,
  caseStatus: "Not Started",
  caseStatusOptions: [
    {
      label: "Not Started",
      value: "Not Started"
    },
    {
      label: "Pending-Initial Reabstraction",
      value: "Pending-Initial Reabstraction"
    },
    {
      label: "Pending-Incomplete Abstraction",
      value: "Pending-Incomplete Abstraction"
    },
    {
      label: "Pending-Missing Documentation",
      value: "Pending-Missing Documentation"
    },
    {
      label: "Pending-Second Look",
      value: "Pending-Second Look"
    },
    {
      label: "Pending-Fallout Review",
      value: "Pending-Fallout Review"
    },
    {
      label: "Pending-Other",
      value: "Pending-Other"
    },
    {
      label: "Billable",
      value: "Billable"
    },
    {
      label: "Ineligible",
      value: "Ineligible"
    }
  ],
  loading: false,
  error: undefined,
  onEditClick: jest.fn(),
  onCancelEdit: jest.fn()
};

describe("CaseDetailsSection", () => {
  test("it renders component with loading state", () => {
    const component = create(
      <CaseDetailsSection
        {...mockProps}
        caseDetails={undefined}
        loading
        editMode={false}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", () => {
    const component = create(
      <CaseDetailsSection
        {...mockProps}
        caseDetails={null}
        error={new Error("Mock error")}
        editMode={false}
      />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component with editMode to false", () => {
    const component = create(
      <CaseDetailsSection {...mockProps} editMode={false} />
    );

    expect(component).toMatchSnapshot();
  });

  test("it renders component with editMode to true", () => {
    const component = create(<CaseDetailsSection {...mockProps} editMode />);

    expect(component).toMatchSnapshot();
  });
});
