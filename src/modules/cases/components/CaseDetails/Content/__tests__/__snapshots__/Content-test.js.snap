// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content it renders component correctly for external users 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto tw-w-[calc(100%-429px)]"
  >
    <CaseDetailsSection
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      caseId="1"
      caseStatus="Complete"
      caseStatusOptions={
        Array [
          Object {
            "label": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "label": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "label": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "label": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "label": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "label": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "label": "Complete",
            "value": "Billable",
          },
          Object {
            "label": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "label": "Ineligible",
            "value": "Ineligible",
          },
        ]
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "admittedAt": null,
                "arrivedAt": 2023-06-18T00:00:00.000Z,
                "bornOn": 1995-12-12T00:00:00.000Z,
                "cancerSiteId": null,
                "caseTypeId": Object {
                  "allowsEmptyDischargeDate": false,
                  "businessOffering": "registry",
                  "isAdminInpatient": false,
                  "isAdminOutpatient": false,
                  "isOncology": false,
                  "isRegulatory": false,
                  "label": "Mock Case Type",
                  "lengthOfStay": Object {
                    "end": null,
                    "start": null,
                  },
                  "serviceLine": "GWTG",
                  "value": "1",
                },
                "facilityId": Object {
                  "label": "Large Medical Center",
                  "value": "1",
                },
                "firstContact": null,
                "firstName": "John",
                "gwtgId": "333111",
                "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
                "lastName": "Doe",
                "losOverride": false,
                "middleName": "",
                "mrn": "1",
                "surgeryDate": null,
                "visitNumber": "1",
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      loaded={true}
      loading={false}
      onCancelEdit={[Function]}
      onEditClick={[Function]}
      onUpdateCase={[Function]}
      questionnaireLocked={false}
    />
    <QuestionnairesSection
      caseId="1"
      isOncologyCase={false}
      isRegulatoryCase={false}
      patientId="1"
      toggleQuestionnaireStatus={[Function]}
    />
    <TasksSection
      caseId="1"
      onSaveTaskEntrySuccess={[Function]}
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CommentsAndNotesSection
        caseId="1"
      />
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this case?"
    disabledConfirm={true}
    isOpen={false}
    onCancel={[MockFunction]}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete Case"
  />
  <GenericCaseTypeModal
    body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
    caseId="1"
    facilityId="1"
    isOpen={false}
    onChangeLater={[Function]}
    onSaveSuccess={[Function]}
    title="1"
  />
</div>
`;

exports[`Content it renders component correctly for internal users and standalone facility 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto tw-w-[calc(100%-429px)]"
  >
    <CaseDetailsSection
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      caseId="1"
      caseStatus="Billable"
      caseStatusOptions={
        Array [
          Object {
            "label": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "label": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "label": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "label": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "label": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "label": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "label": "Billable",
            "value": "Billable",
          },
          Object {
            "label": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "label": "Ineligible",
            "value": "Ineligible",
          },
        ]
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "admittedAt": null,
                "arrivedAt": 2023-06-18T00:00:00.000Z,
                "bornOn": 1995-12-12T00:00:00.000Z,
                "cancerSiteId": null,
                "caseTypeId": Object {
                  "allowsEmptyDischargeDate": false,
                  "businessOffering": "registry",
                  "isAdminInpatient": false,
                  "isAdminOutpatient": false,
                  "isOncology": false,
                  "isRegulatory": false,
                  "label": "Mock Case Type",
                  "lengthOfStay": Object {
                    "end": null,
                    "start": null,
                  },
                  "serviceLine": "GWTG",
                  "value": "1",
                },
                "facilityId": Object {
                  "label": "Large Medical Center",
                  "value": "1",
                },
                "firstContact": null,
                "firstName": "John",
                "gwtgId": "333111",
                "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
                "lastName": "Doe",
                "losOverride": false,
                "middleName": "",
                "mrn": "1",
                "surgeryDate": null,
                "visitNumber": "1",
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      loaded={true}
      loading={false}
      onCancelEdit={[Function]}
      onEditClick={[Function]}
      onUpdateCase={[Function]}
      questionnaireLocked={false}
    />
    <QuestionnairesSection
      caseId="1"
      isOncologyCase={false}
      isRegulatoryCase={false}
      patientId="1"
      toggleQuestionnaireStatus={[Function]}
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CommentsAndNotesSection
        caseId="1"
      />
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this case?"
    disabledConfirm={true}
    isOpen={false}
    onCancel={[MockFunction]}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete Case"
  />
  <GenericCaseTypeModal
    body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
    caseId="1"
    facilityId="1"
    isOpen={false}
    onChangeLater={[Function]}
    onSaveSuccess={[Function]}
    title="1"
  />
</div>
`;

exports[`Content it renders component correctly with Case Reassignment panel 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto tw-w-[calc(100%-429px)]"
  >
    <CaseDetailsSection
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      caseId="1"
      caseStatus="Billable"
      caseStatusOptions={
        Array [
          Object {
            "label": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "label": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "label": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "label": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "label": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "label": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "label": "Billable",
            "value": "Billable",
          },
          Object {
            "label": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "label": "Ineligible",
            "value": "Ineligible",
          },
        ]
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "admittedAt": null,
                "arrivedAt": 2023-06-18T00:00:00.000Z,
                "bornOn": 1995-12-12T00:00:00.000Z,
                "cancerSiteId": null,
                "caseTypeId": Object {
                  "allowsEmptyDischargeDate": false,
                  "businessOffering": "registry",
                  "isAdminInpatient": false,
                  "isAdminOutpatient": false,
                  "isOncology": false,
                  "isRegulatory": false,
                  "label": "Mock Case Type",
                  "lengthOfStay": Object {
                    "end": null,
                    "start": null,
                  },
                  "serviceLine": "GWTG",
                  "value": "1",
                },
                "facilityId": Object {
                  "label": "Large Medical Center",
                  "value": "1",
                },
                "firstContact": null,
                "firstName": "John",
                "gwtgId": "333111",
                "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
                "lastName": "Doe",
                "losOverride": false,
                "middleName": "",
                "mrn": "1",
                "surgeryDate": null,
                "visitNumber": "1",
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      loaded={true}
      loading={false}
      onCancelEdit={[Function]}
      onEditClick={[Function]}
      onUpdateCase={[Function]}
      questionnaireLocked={false}
    />
    <QuestionnairesSection
      caseId="1"
      isOncologyCase={false}
      isRegulatoryCase={false}
      patientId="1"
      toggleQuestionnaireStatus={[Function]}
    />
    <TasksSection
      caseId="1"
      onSaveTaskEntrySuccess={[Function]}
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CaseReassignment
        caseIds={
          Array [
            "1",
          ]
        }
        hideAssignNewOwner={false}
        onCancel={[MockFunction]}
        onSuccess={[Function]}
        reassignmentType="cases"
        refetchQueries={
          Array [
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "caseDetails",
                  },
                  "operation": "query",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "caseId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "id",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "caseDetails",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "startEditingOnLoad",
                              },
                              "selectionSet": undefined,
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "case",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "id",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "isEditable",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "batch",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "link",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "losOverride",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "status",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "clientDueDate",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "facility",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "name",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "owner",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "fullName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "visit",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "number",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "hospitalDischargedAt",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "firstContact",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "arrivedAt",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "admittedAt",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "surgeryDate",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "caseType",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "name",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "generic",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "isOncology",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "isRegulatory",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "isAdminInpatient",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "isAdminOutpatient",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "businessOffering",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "serviceLine",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "allowsEmptyDischargeDate",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "lengthOfStay",
                                          },
                                          "selectionSet": Object {
                                            "kind": "SelectionSet",
                                            "selections": Array [
                                              Object {
                                                "alias": undefined,
                                                "arguments": Array [],
                                                "directives": Array [],
                                                "kind": "Field",
                                                "name": Object {
                                                  "kind": "Name",
                                                  "value": "start",
                                                },
                                                "selectionSet": undefined,
                                              },
                                              Object {
                                                "alias": undefined,
                                                "arguments": Array [],
                                                "directives": Array [],
                                                "kind": "Field",
                                                "name": Object {
                                                  "kind": "Name",
                                                  "value": "end",
                                                },
                                                "selectionSet": undefined,
                                              },
                                              Object {
                                                "alias": undefined,
                                                "arguments": Array [],
                                                "directives": Array [],
                                                "kind": "Field",
                                                "name": Object {
                                                  "kind": "Name",
                                                  "value": "allowOverride",
                                                },
                                                "selectionSet": undefined,
                                              },
                                            ],
                                          },
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "cancerSite",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "name",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "gwtgId",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "patient",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "mrn",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "firstName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "middleName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "lastName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "bornOn",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "primarySite",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "caseStatusOptions",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "value",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "displayValue",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "reportable",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "analytic",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "assignee",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "id",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "fullName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "abstractionMismatch",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "permissions",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "canEditVisitNumberAndMrn",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "canChangeCompleteCaseStatus",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "canEditFacilityAndCaseType",
                                          },
                                          "selectionSet": undefined,
                                        },
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "canEditAbstractionMismatch",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "userHasTimeTrackingDisabled",
                              },
                              "selectionSet": undefined,
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "Int",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "id",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 1546,
                "start": 0,
              },
            },
          ]
        }
      >
        <TextArea
          disabled={false}
          id="case-reassignment-comment"
          label="Comment"
          onChange={[Function]}
          placeholder="Enter comments here..."
          textareaClassName="tw-min-h-[120px]"
          value=""
        />
      </CaseReassignment>
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this case?"
    disabledConfirm={true}
    isOpen={false}
    onCancel={[MockFunction]}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete Case"
  />
  <GenericCaseTypeModal
    body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
    caseId="1"
    facilityId="1"
    isOpen={false}
    onChangeLater={[Function]}
    onSaveSuccess={[Function]}
    title="1"
  />
</div>
`;

exports[`Content it renders component correctly with default props 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto tw-w-[calc(100%-429px)]"
  >
    <CaseDetailsSection
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      caseId="1"
      caseStatus="Billable"
      caseStatusOptions={
        Array [
          Object {
            "label": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "label": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "label": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "label": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "label": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "label": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "label": "Billable",
            "value": "Billable",
          },
          Object {
            "label": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "label": "Ineligible",
            "value": "Ineligible",
          },
        ]
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "admittedAt": null,
                "arrivedAt": 2023-06-18T00:00:00.000Z,
                "bornOn": 1995-12-12T00:00:00.000Z,
                "cancerSiteId": null,
                "caseTypeId": Object {
                  "allowsEmptyDischargeDate": false,
                  "businessOffering": "registry",
                  "isAdminInpatient": false,
                  "isAdminOutpatient": false,
                  "isOncology": false,
                  "isRegulatory": false,
                  "label": "Mock Case Type",
                  "lengthOfStay": Object {
                    "end": null,
                    "start": null,
                  },
                  "serviceLine": "GWTG",
                  "value": "1",
                },
                "facilityId": Object {
                  "label": "Large Medical Center",
                  "value": "1",
                },
                "firstContact": null,
                "firstName": "John",
                "gwtgId": "333111",
                "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
                "lastName": "Doe",
                "losOverride": false,
                "middleName": "",
                "mrn": "1",
                "surgeryDate": null,
                "visitNumber": "1",
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      loaded={true}
      loading={false}
      onCancelEdit={[Function]}
      onEditClick={[Function]}
      onUpdateCase={[Function]}
      questionnaireLocked={false}
    />
    <QuestionnairesSection
      caseId="1"
      isOncologyCase={false}
      isRegulatoryCase={false}
      patientId="1"
      toggleQuestionnaireStatus={[Function]}
    />
    <TasksSection
      caseId="1"
      onSaveTaskEntrySuccess={[Function]}
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CommentsAndNotesSection
        caseId="1"
      />
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this case?"
    disabledConfirm={true}
    isOpen={false}
    onCancel={[MockFunction]}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete Case"
  />
  <GenericCaseTypeModal
    body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
    caseId="1"
    facilityId="1"
    isOpen={false}
    onChangeLater={[Function]}
    onSaveSuccess={[Function]}
    title="1"
  />
</div>
`;

exports[`Content it renders component correctly with delete modal open 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto tw-w-[calc(100%-429px)]"
  >
    <CaseDetailsSection
      caseDetails={
        Object {
          "analytic": false,
          "assignee": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "batch": Object {
            "id": "1",
            "link": "/qapps/upload/case_uploads/1",
          },
          "cancerSite": null,
          "caseStatusOptions": Array [
            Object {
              "displayValue": "Pending-Initial Reabstraction",
              "value": "Pending-Initial Reabstraction",
            },
            Object {
              "displayValue": "Pending-Incomplete Abstraction",
              "value": "Pending-Incomplete Abstraction",
            },
            Object {
              "displayValue": "Pending-Missing Documentation",
              "value": "Pending-Missing Documentation",
            },
            Object {
              "displayValue": "Pending-Second Look",
              "value": "Pending-Second Look",
            },
            Object {
              "displayValue": "Pending-Fallout Review",
              "value": "Pending-Fallout Review",
            },
            Object {
              "displayValue": "Pending-Other",
              "value": "Pending-Other",
            },
            Object {
              "displayValue": "Billable",
              "value": "Billable",
            },
            Object {
              "displayValue": "Billable-Pending Client Response",
              "value": "Billable-Pending Client Response",
            },
            Object {
              "displayValue": "Ineligible",
              "value": "Ineligible",
            },
          ],
          "caseType": Object {
            "allowsEmptyDischargeDate": false,
            "businessOffering": "registry",
            "id": "1",
            "isAdminInpatient": false,
            "isAdminOutpatient": false,
            "isOncology": false,
            "isRegulatory": false,
            "lengthOfStay": Object {
              "end": null,
              "start": null,
            },
            "name": "Mock Case Type",
            "serviceLine": "GWTG",
          },
          "facility": Object {
            "id": "1",
            "name": "Large Medical Center",
          },
          "generic": false,
          "gwtgId": "333111",
          "id": "1",
          "isEditable": true,
          "losOverride": null,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": "1",
          },
          "patient": Object {
            "bornOn": "1995-12-12T19:00:00-04:00",
            "firstName": "John",
            "id": "1",
            "lastName": "Doe",
            "middleName": "",
            "mrn": "1",
          },
          "permissions": Object {
            "canChangeCompleteCaseStatus": true,
            "canEditFacilityAndCaseType": true,
            "canEditVisitNumberAndMrn": true,
          },
          "primarySite": null,
          "reportable": "non_reportable",
          "status": "Billable",
          "visit": Object {
            "admittedAt": null,
            "arrivedAt": "2023-06-18T19:00:00-04:00",
            "firstContact": null,
            "hospitalDischargedAt": "2023-06-19T19:00:00-04:00",
            "id": "1",
            "number": "1",
            "surgeryDate": null,
          },
        }
      }
      caseId="1"
      caseStatus="Billable"
      caseStatusOptions={
        Array [
          Object {
            "label": "Pending-Initial Reabstraction",
            "value": "Pending-Initial Reabstraction",
          },
          Object {
            "label": "Pending-Incomplete Abstraction",
            "value": "Pending-Incomplete Abstraction",
          },
          Object {
            "label": "Pending-Missing Documentation",
            "value": "Pending-Missing Documentation",
          },
          Object {
            "label": "Pending-Second Look",
            "value": "Pending-Second Look",
          },
          Object {
            "label": "Pending-Fallout Review",
            "value": "Pending-Fallout Review",
          },
          Object {
            "label": "Pending-Other",
            "value": "Pending-Other",
          },
          Object {
            "label": "Billable",
            "value": "Billable",
          },
          Object {
            "label": "Billable-Pending Client Response",
            "value": "Billable-Pending Client Response",
          },
          Object {
            "label": "Ineligible",
            "value": "Ineligible",
          },
        ]
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "admittedAt": null,
                "arrivedAt": 2023-06-18T00:00:00.000Z,
                "bornOn": 1995-12-12T00:00:00.000Z,
                "cancerSiteId": null,
                "caseTypeId": Object {
                  "allowsEmptyDischargeDate": false,
                  "businessOffering": "registry",
                  "isAdminInpatient": false,
                  "isAdminOutpatient": false,
                  "isOncology": false,
                  "isRegulatory": false,
                  "label": "Mock Case Type",
                  "lengthOfStay": Object {
                    "end": null,
                    "start": null,
                  },
                  "serviceLine": "GWTG",
                  "value": "1",
                },
                "facilityId": Object {
                  "label": "Large Medical Center",
                  "value": "1",
                },
                "firstContact": null,
                "firstName": "John",
                "gwtgId": "333111",
                "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
                "lastName": "Doe",
                "losOverride": false,
                "middleName": "",
                "mrn": "1",
                "surgeryDate": null,
                "visitNumber": "1",
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "admittedAt": null,
              "arrivedAt": 2023-06-18T00:00:00.000Z,
              "bornOn": 1995-12-12T00:00:00.000Z,
              "cancerSiteId": null,
              "caseTypeId": Object {
                "allowsEmptyDischargeDate": false,
                "businessOffering": "registry",
                "isAdminInpatient": false,
                "isAdminOutpatient": false,
                "isOncology": false,
                "isRegulatory": false,
                "label": "Mock Case Type",
                "lengthOfStay": Object {
                  "end": null,
                  "start": null,
                },
                "serviceLine": "GWTG",
                "value": "1",
              },
              "facilityId": Object {
                "label": "Large Medical Center",
                "value": "1",
              },
              "firstContact": null,
              "firstName": "John",
              "gwtgId": "333111",
              "hospitalDischargedAt": 2023-06-19T00:00:00.000Z,
              "lastName": "Doe",
              "losOverride": false,
              "middleName": "",
              "mrn": "1",
              "surgeryDate": null,
              "visitNumber": "1",
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      loaded={true}
      loading={false}
      onCancelEdit={[Function]}
      onEditClick={[Function]}
      onUpdateCase={[Function]}
      questionnaireLocked={false}
    />
    <QuestionnairesSection
      caseId="1"
      isOncologyCase={false}
      isRegulatoryCase={false}
      patientId="1"
      toggleQuestionnaireStatus={[Function]}
    />
    <TasksSection
      caseId="1"
      onSaveTaskEntrySuccess={[Function]}
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CommentsAndNotesSection
        caseId="1"
      />
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this case?"
    disabledConfirm={true}
    isOpen={true}
    onCancel={[MockFunction]}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete Case"
  />
  <GenericCaseTypeModal
    body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
    caseId="1"
    facilityId="1"
    isOpen={false}
    onChangeLater={[Function]}
    onSaveSuccess={[Function]}
    title="1"
  />
</div>
`;
