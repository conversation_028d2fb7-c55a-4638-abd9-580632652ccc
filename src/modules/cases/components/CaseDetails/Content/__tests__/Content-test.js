import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import Content from "..";
import mocks from "../../graphql/mocks";
import wait from "waait";

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useParams: () => ({
    id: "1"
  }),
  useNavigate: () => jest.fn()
}));
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  TextArea: "TextArea",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("../QuestionnairesSection", () => "QuestionnairesSection");
jest.mock("../TasksSection", () => "TasksSection");
jest.mock("../CaseDetailsSection", () => "CaseDetailsSection");
jest.mock("shared/components/CaseReassignment", () => "CaseReassignment");
jest.mock("../CommentsAndNotesSection", () => "CommentsAndNotesSection");
jest.mock("../GenericCaseTypeModal", () => "GenericCaseTypeModal");
jest.mock("shared/components/DeleteModal", () => "DeleteModal");

const mockProps = {
  shouldShowCaseReassignment: false,
  deleteModalIsOpen: false,
  toggleCaseReassignment: jest.fn(),
  toggleDeleteModal: jest.fn()
};

describe("Content", () => {
  function render(
    props,
    apolloMocks = mocks,
    initialAppValues = { facility: { standalone: false } }
  ) {
    return create(
      decoratedApollo({
        component: Content,
        props,
        initialAppValues,
        apolloMocks
      })
    );
  }

  test("it renders component correctly with default props", async () => {
    const component = render(mockProps);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with Case Reassignment panel", async () => {
    const component = render({
      ...mockProps,
      shouldShowCaseReassignment: true
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with delete modal open", async () => {
    const component = render({ ...mockProps, deleteModalIsOpen: true });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly for external users", async () => {
    const mocksWithExternalUser = mocks.map(mock => {
      if (mock.request.query.definitions[0].name.value === "currentUser") {
        return {
          ...mock,
          result: {
            data: {
              currentUser: {
                ...mock.result.data.currentUser,
                isInternalUser: false
              }
            }
          }
        };
      }

      return mock;
    });

    const component = render(mockProps, mocksWithExternalUser);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly for internal users and standalone facility", async () => {
    const mocksWithInternalUser = mocks.map(mock => {
      if (mock.request.query.definitions[0].name.value === "currentUser") {
        return {
          ...mock,
          result: {
            data: {
              currentUser: {
                ...mock.result.data.currentUser,
                isInternalUser: true
              }
            }
          }
        };
      }

      return mock;
    });

    const component = render(mockProps, mocksWithInternalUser, {
      facility: { standalone: true }
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
