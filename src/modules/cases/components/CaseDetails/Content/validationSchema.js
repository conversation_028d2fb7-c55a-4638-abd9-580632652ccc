import * as yup from "yup";

const formSchema = yup
  .object({
    facilityId: yup
      .object({
        value: yup.string().required("Facility is required")
      })
      .required("Facility is required"),
    caseTypeId: yup
      .object({
        value: yup.string().required("Case Type is required")
      })
      .required("Case Type is required"),
    cancerSiteId: yup
      .object()
      .notRequired()
      .when("caseTypeId", {
        is: caseTypeId => <PERSON><PERSON><PERSON>(caseTypeId?.isOncology),
        then: schema =>
          schema
            .shape({
              value: yup.string().required("Cancer Type is required!")
            })
            .required("Cancer Type is required!")
      }),
    losOverride: yup.boolean().default(false),
    visitNumber: yup.string().required("Visit Number is required"),
    mrn: yup.string().required("MRN is required"),
    gwtgId: yup.string().notRequired(),
    lastName: yup.string().notRequired(),
    firstName: yup.string().notRequired(),
    middleName: yup.string().notRequired(),
    bornOn: yup.date().notRequired(),
    firstContact: yup.date().notRequired(),
    arrivedAt: yup.date().notRequired(),
    admittedAt: yup.date().notRequired(),
    surgeryDate: yup.date().notRequired(),
    hospitalDischargedAt: yup.date().notRequired()
  })
  .required();

export default formSchema;
