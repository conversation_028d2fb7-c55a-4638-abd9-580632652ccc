import { TextArea } from "@q-centrix/q-components-react";
import QuestionnairesSection from "./QuestionnairesSection";
import TasksSection from "./TasksSection";
import CaseReassignment from "shared/components/CaseReassignment";
import CommentsAndNotesSection from "./CommentsAndNotesSection";
import GenericCaseTypeModal from "./GenericCaseTypeModal";
import { useComponentLogic } from "./hooks";
import DeleteModal from "shared/components/DeleteModal";
import { GET_CASE_DETAILS } from "../graphql/query";
import CaseDetailsSection from "./CaseDetailsSection";
import classnames from "classnames";
import ExpandablePanel from "shared/components/ExpandablePanel";
import { trim } from "ramda";

// eslint-disable-next-line complexity
const Content = ({
  shouldShowCaseReassignment,
  toggleCaseReassignment,
  toggleDeleteModal,
  deleteModalIsOpen
}) => {
  const {
    isRightPanelOpen,
    setIsRightPanelOpen,
    genericCaseTypeModalIsOpen,
    checkIfGenericCaseTypeModalShouldOpen,
    closeGenericCaseTypeModal,
    caseReassignmentCommentValue,
    handleCaseReassignmentCommentChange,
    handleCaseReassignmentSuccess,
    caseReassignmentCommentIsSubmitting,
    loggedInUser,
    caseId,
    form,
    caseDetails,
    caseDetailsLoading,
    caseDetailsCalled,
    caseDetailsError,
    editMode,
    questionnaireLocked,
    toggleEditMode,
    handleUpdateCase,
    handleCancelEdit,
    handleDeleteCase,
    toggleQuestionnaireStatus,
    hasAbstractionTask,
    formattedCaseStatus,
    formattedCaseStatusOptions,
    showTasksSection,
    isRegulatoryCase,
    userHasTimeTrackingDisabled,
    patientId,
    isOncologyCase,
    reason,
    handleReasonChange
  } = useComponentLogic({
    shouldShowCaseReassignment,
    toggleDeleteModal,
    toggleCaseReassignment
  });

  const containerClassName = classnames(
    "tw-absolute tw-left-0 tw-top-0 tw-flex tw-flex-col tw-gap-5 tw-h-full tw-p-2.5 tw-transition-all tw-duration-500 tw-ease-in-out tw-overflow-auto",
    {
      "tw-w-[calc(100%-15px)]": !isRightPanelOpen,
      "tw-w-[calc(100%-429px)]": isRightPanelOpen
    }
  );

  return (
    <div className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25">
      <div className={containerClassName}>
        <CaseDetailsSection
          caseId={caseId}
          editMode={editMode}
          form={form}
          caseDetails={caseDetails}
          loading={caseDetailsLoading}
          loaded={!caseDetailsLoading && caseDetailsCalled}
          error={caseDetailsError}
          caseStatus={formattedCaseStatus}
          caseStatusOptions={formattedCaseStatusOptions}
          onEditClick={toggleEditMode}
          onCancelEdit={handleCancelEdit}
          onUpdateCase={handleUpdateCase}
          questionnaireLocked={questionnaireLocked}
        />
        <QuestionnairesSection
          caseId={caseId}
          toggleQuestionnaireStatus={toggleQuestionnaireStatus}
          isRegulatoryCase={isRegulatoryCase}
          patientId={patientId}
          isOncologyCase={isOncologyCase}
        />
        {showTasksSection && (
          <TasksSection
            caseId={caseId}
            onSaveTaskEntrySuccess={checkIfGenericCaseTypeModalShouldOpen}
            userHasTimeTrackingDisabled={userHasTimeTrackingDisabled}
          />
        )}
      </div>
      <ExpandablePanel
        isPanelOpen={isRightPanelOpen}
        handlePanelToggle={setIsRightPanelOpen}
      >
        {shouldShowCaseReassignment ? (
          <CaseReassignment
            caseIds={[caseId]}
            onCancel={toggleCaseReassignment}
            onSuccess={handleCaseReassignmentSuccess}
            refetchQueries={[GET_CASE_DETAILS]}
            hideAssignNewOwner={hasAbstractionTask}
            reassignmentType="cases"
          >
            <TextArea
              id="case-reassignment-comment"
              value={caseReassignmentCommentValue}
              onChange={handleCaseReassignmentCommentChange}
              label="Comment"
              placeholder="Enter comments here..."
              textareaClassName="tw-min-h-[120px]"
              disabled={caseReassignmentCommentIsSubmitting}
            />
          </CaseReassignment>
        ) : (
          <CommentsAndNotesSection
            caseId={caseId}
            loggedInUser={loggedInUser}
          />
        )}
      </ExpandablePanel>
      <DeleteModal
        title="Delete Case"
        body="Are you sure you want to permanently delete this case?"
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteCase}
        reasonRequired
        reason={reason}
        onReasonChange={handleReasonChange}
        disabledConfirm={!trim(reason)}
      />
      <GenericCaseTypeModal
        caseId={caseId}
        facilityId={caseDetails?.facility?.id}
        title={caseDetails?.patient?.mrn}
        body="This case is currently using a generic case type. Please select a detailed case type to get paid for the case."
        isOpen={genericCaseTypeModalIsOpen}
        onChangeLater={closeGenericCaseTypeModal}
        onSaveSuccess={closeGenericCaseTypeModal}
      />
    </div>
  );
};

export default Content;
