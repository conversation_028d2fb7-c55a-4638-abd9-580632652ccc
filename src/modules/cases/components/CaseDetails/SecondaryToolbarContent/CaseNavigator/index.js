import { useComponentLogic } from "./hooks";

const spanClass = "tw-text-qcNeutrals-700 tw-text-xs tw-font-medium";
const butttonClass =
  "tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none";

export const CaseNavigator = () => {
  const { currentIndex, totalCases, handlePrevClick, handleNextClick } =
    useComponentLogic();

  if (totalCases <= 0) {
    return null;
  }

  return (
    <div className="tw-flex tw-items-center tw-gap-x-4 tw-rounded-md tw-bg-qc-frost tw-px-3 tw-py-1">
      <span className={spanClass}>Case</span>
      <span className={spanClass}>
        <strong>{currentIndex + 1}</strong> of <strong>{totalCases}</strong>
      </span>
      <div className="items-center tw-flex tw-gap-x-1">
        <button
          type="button"
          className={butttonClass}
          disabled={currentIndex <= 0}
          onClick={handlePrevClick}
        >
          <i className="fa fa-chevron-left" />
        </button>
        <button
          type="button"
          className={butttonClass}
          disabled={currentIndex >= totalCases - 1}
          onClick={handleNextClick}
        >
          <i className="fa fa-chevron-right" />
        </button>
      </div>
    </div>
  );
};

export default CaseNavigator;
