import Cases from "modules/cases/redux/selectors";
import { changeCurrentCaseIndex } from "modules/cases/redux/slice";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toCaseDetails } from "utils/toCaseDetails";

export const useComponentLogic = () => {
  const currentIndex = useSelector(state => Cases.getCurrentCaseIndex(state));
  const activeCaseListIds = useSelector(state =>
    Cases.getActiveCaseListIds(state)
  );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const handleNextClick = () => {
    if (currentIndex < activeCaseListIds.length - 1) {
      dispatch(changeCurrentCaseIndex(1));
      toCaseDetails(activeCaseListIds[currentIndex + 1], navigate);
    }
  };
  const handlePrevClick = () => {
    if (currentIndex > 0) {
      dispatch(changeCurrentCaseIndex(-1));
      toCaseDetails(activeCaseListIds[currentIndex - 1], navigate);
    }
  };

  return {
    currentIndex,
    totalCases: activeCaseListIds.length,
    handlePrevClick,
    handleNextClick
  };
};
