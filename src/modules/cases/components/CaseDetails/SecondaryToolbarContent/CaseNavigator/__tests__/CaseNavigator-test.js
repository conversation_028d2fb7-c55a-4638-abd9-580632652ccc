import { create } from "react-test-renderer";
import CaseNavigator from "..";
import decorated from "utils/tests/decorated";
import { MemoryRouter } from "react-router-dom";

const TextComponent = () => (
  <MemoryRouter>
    <CaseNavigator />
  </MemoryRouter>
);

describe("CaseNavigator", () => {
  function render(caseList = [], index = -1) {
    return create(
      decorated(
        TextComponent,
        {},
        {},
        { cases: { activeCaseListIds: caseList, currentCaseIndex: index } }
      )
    );
  }

  test("it renders component correctly for one case", () => {
    const component = render([1], 0);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly for first case", () => {
    const component = render([1, 2, 3], 0);

    expect(component).toMatchSnapshot();
  });
  test("it renders component correctly for middle case", () => {
    const component = render([1, 2, 3], 1);

    expect(component).toMatchSnapshot();
  });
  test("it renders component correctly for last case", () => {
    const component = render([1, 2, 3], 2);

    expect(component).toMatchSnapshot();
  });
  test("it renders null for empty list", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
