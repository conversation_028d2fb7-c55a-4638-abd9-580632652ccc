// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseNavigator it renders component correctly for first case 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-4 tw-rounded-md tw-bg-qc-frost tw-px-3 tw-py-1"
>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    Case
  </span>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    <strong>
      1
    </strong>
     of 
    <strong>
      3
    </strong>
  </span>
  <div
    className="items-center tw-flex tw-gap-x-1"
  >
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-left"
      />
    </button>
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-right"
      />
    </button>
  </div>
</div>
`;

exports[`CaseNavigator it renders component correctly for last case 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-4 tw-rounded-md tw-bg-qc-frost tw-px-3 tw-py-1"
>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    Case
  </span>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    <strong>
      3
    </strong>
     of 
    <strong>
      3
    </strong>
  </span>
  <div
    className="items-center tw-flex tw-gap-x-1"
  >
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-left"
      />
    </button>
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-right"
      />
    </button>
  </div>
</div>
`;

exports[`CaseNavigator it renders component correctly for middle case 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-4 tw-rounded-md tw-bg-qc-frost tw-px-3 tw-py-1"
>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    Case
  </span>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    <strong>
      2
    </strong>
     of 
    <strong>
      3
    </strong>
  </span>
  <div
    className="items-center tw-flex tw-gap-x-1"
  >
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-left"
      />
    </button>
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-right"
      />
    </button>
  </div>
</div>
`;

exports[`CaseNavigator it renders component correctly for one case 1`] = `
<div
  className="tw-flex tw-items-center tw-gap-x-4 tw-rounded-md tw-bg-qc-frost tw-px-3 tw-py-1"
>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    Case
  </span>
  <span
    className="tw-text-qcNeutrals-700 tw-text-xs tw-font-medium"
  >
    <strong>
      1
    </strong>
     of 
    <strong>
      1
    </strong>
  </span>
  <div
    className="items-center tw-flex tw-gap-x-1"
  >
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-left"
      />
    </button>
    <button
      className="tw-h-6 tw-w-6 tw-text-center tw-text-sm tw-text-qcInfo-700 tw-bg-qcNeutrals-300 disabled:tw-opacity-40 tw-border tw-border-qcNeutrals-400 tw-rounded-md hover:tw-shadow-card disabled:hover:tw-shadow-none"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa fa-chevron-right"
      />
    </button>
  </div>
</div>
`;

exports[`CaseNavigator it renders null for empty list 1`] = `null`;
