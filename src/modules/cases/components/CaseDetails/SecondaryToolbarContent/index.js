import { But<PERSON> } from "@q-centrix/q-components-react";
import CaseNavigator from "./CaseNavigator";

const SecondaryToolbarContent = ({
  caseReassignmentDisabled,
  onReassignCase,
  onDeleteCase,
  onCreateNew
}) => (
  <>
    <Button
      outline
      bg="danger"
      customStyle="tw-flex tw-gap-2.5 tw-items-center"
      onClick={onDeleteCase}
    >
      <i className="fa-regular fa-trash" />
      Delete Case
    </Button>
    <Button
      outline
      bg="main"
      customStyle="tw-flex tw-gap-2.5 tw-items-center"
      onClick={onReassignCase}
      disabled={caseReassignmentDisabled}
    >
      <i className="fa-regular fa-arrow-right-arrow-left" />
      Reassign Case
    </Button>
    <CaseNavigator />
    <Button
      customStyle="tw-flex tw-gap-2.5 tw-items-center"
      onClick={onCreateNew}
    >
      <i className="fa-regular fa-plus" />
      Create New
    </Button>
  </>
);

export default SecondaryToolbarContent;
