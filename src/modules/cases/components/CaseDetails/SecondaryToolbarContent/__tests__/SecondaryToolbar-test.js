import { create } from "react-test-renderer";
import SecondaryToolbarContent from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));

jest.mock("../CaseNavigator", () => "CaseNavigator");

describe("SecondaryToolbarContent", () => {
  function render() {
    return create(<SecondaryToolbarContent />);
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
