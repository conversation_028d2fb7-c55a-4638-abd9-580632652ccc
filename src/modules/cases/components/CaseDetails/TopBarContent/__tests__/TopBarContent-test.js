import { create } from "react-test-renderer";
import TopBarContent from "..";

jest.mock("shared/widgets/LastLogInTile", () => "LastLogInTile");
jest.mock("shared/widgets/Hours", () => "Hours");
jest.mock("shared/widgets/Earnings", () => "Earnings");
jest.mock("shared/widgets/QPoints", () => "QPoints");

describe("TopBarContent", () => {
  function render(title) {
    return create(<TopBarContent title={title} />);
  }

  test("it renders component correctly", () => {
    const component = render("Case Details");

    expect(component).toMatchSnapshot();
  });
});
