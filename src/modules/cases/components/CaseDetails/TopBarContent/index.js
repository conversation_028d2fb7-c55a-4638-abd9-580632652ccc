import LastLogInTile from "shared/widgets/LastLogInTile";
import Hours from "shared/widgets/Hours";
import QPoints from "shared/widgets/QPoints";
import Earnings from "shared/widgets/Earnings";

const TopBarContent = ({ title }) => (
  <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
    <h1 className="tw-text-2xl tw-font-semibold">{title}</h1>
    <div className="tw-flex tw-items-center tw-gap-5">
      <Hours />
      <QPoints />
      <Earnings />
      <LastLogInTile />
    </div>
  </div>
);

export default TopBarContent;
