import { create } from "react-test-renderer";
import CaseDetails from "modules/cases/components/CaseDetails";

jest.mock("react-router-dom", () => ({
  useNavigate: jest.fn()
}));

jest.mock("@q-centrix/q-components-react", () => ({
  Topbar: "Topbar",
  Button: "Button"
}));
jest.mock("../Content", () => "Content");
jest.mock("shared/components/Layout", () => "Layout");

describe("CaseDetails", () => {
  function render() {
    return create(<CaseDetails />);
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
