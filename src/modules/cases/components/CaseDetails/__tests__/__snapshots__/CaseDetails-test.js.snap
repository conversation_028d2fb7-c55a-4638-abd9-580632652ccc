// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseDetails it renders component correctly 1`] = `
<Layout
  onBackArrowClick={[Function]}
  stChildren={
    <SecondaryToolbarContent
      caseReassignmentDisabled={false}
      onCreateNew={[Function]}
      onDeleteCase={[Function]}
      onReassignCase={[Function]}
    />
  }
  stChildrenClassName="tw-gap-5"
  tbChildren={
    <TopBarContent
      title="Case Details"
    />
  }
  tbLabel="PC"
>
  <Content
    deleteModalIsOpen={false}
    shouldShowCaseReassignment={false}
    toggleCaseReassignment={[Function]}
    toggleDeleteModal={[Function]}
  />
</Layout>
`;
