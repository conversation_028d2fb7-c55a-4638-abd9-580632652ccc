import { useState } from "react";
import { not } from "ramda";
import { useNavigate } from "react-router-dom";

export const useComponentLogic = () => {
  const navigate = useNavigate();
  const [shouldShowCaseReassignment, setShouldShowCaseReassignment] =
    useState(false);
  const toggleCaseReassignment = () => setShouldShowCaseReassignment(not);
  const [deleteModalIsOpen, setDeleteModalIsOpen] = useState(false);
  const toggleDeleteModal = () => setDeleteModalIsOpen(not);

  const handleBackArrowClick = () => navigate("/cases");

  const handleCreateNew = () => navigate("/cases/new");

  return {
    shouldShowCaseReassignment,
    handleBackArrowClick,
    toggleCaseReassignment,
    deleteModalIsOpen,
    toggleDeleteModal,
    handleCreateNew
  };
};
