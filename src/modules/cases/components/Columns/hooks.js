import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { assoc, filter, map, prop, propEq, when, reduce } from "ramda";
import casesSelectors from "modules/cases/redux/selectors";
import { updateSelectedColumns } from "modules/cases/redux/slice";
import { sortColumns } from "modules/cases/utils/sortColumns";
import useLocalStorage from "shared/hooks/useLocalStorage";

export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const { columns, key: currentTabKey } =
    useSelector(casesSelectors.getSelectedTab) || {};
  const [columnsValues, setColumnsValues] = useState([]);

  const [savedColumnsValues, setSavedColumnsValues] = useLocalStorage(
    "abstractionSavedColumnsValues",
    {}
  );

  const sortedColumns = useMemo(() => sortColumns(columns), [columns]);

  const handleReset = useCallback(() => {
    setColumnsValues(
      map(column => assoc("checked", column.isDefault, column), sortedColumns)
    );
  }, [sortedColumns]);

  const handleColumnValueChange = useCallback(
    ({ target: { name, checked } }) => {
      setColumnsValues(
        map(when(propEq("name", name), assoc("checked", checked)))
      );
    },
    []
  );

  const handleSubmit = event => {
    event.preventDefault();
    const currentTabColumnsValues = reduce(
      (acc, currColumn) => assoc(currColumn.key, currColumn.checked, acc),
      {},
      columnsValues
    );

    setSavedColumnsValues(assoc(currentTabKey, currentTabColumnsValues));

    dispatch(updateSelectedColumns(filter(prop("checked"), columnsValues)));
  };

  useEffect(() => {
    if (savedColumnsValues[currentTabKey]) {
      const sortedColumnsWithSavedColumnValues = map(column => {
        const checkValue =
          savedColumnsValues[currentTabKey][column.key] ?? column.isDefault;

        return assoc("checked", checkValue, column);
      }, sortedColumns);

      setColumnsValues(sortedColumnsWithSavedColumnValues);
      dispatch(
        updateSelectedColumns(
          filter(prop("checked"), sortedColumnsWithSavedColumnValues)
        )
      );
    } else if (sortedColumns) {
      setColumnsValues(
        map(column => assoc("checked", column.isDefault, column), sortedColumns)
      );
    }
  }, [sortedColumns, currentTabKey, savedColumnsValues]);

  return {
    columnsValues,
    handleColumnValueChange,
    handleSubmit,
    handleReset,
    columns: sortedColumns
  };
};
