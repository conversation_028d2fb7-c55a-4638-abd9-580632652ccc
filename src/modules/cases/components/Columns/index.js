import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "modules/cases/components/Columns/hooks";
import CheckboxList from "modules/cases/components/Columns/CheckboxList";

const Columns = () => {
  const {
    columnsValues,
    handleColumnValueChange,
    handleSubmit,
    handleReset,
    columns
  } = useComponentLogic();

  return (
    <form
      onSubmit={handleSubmit}
      autoComplete="off"
      className="tw-scrollbar-gutter-stable tw-flex tw-h-full tw-flex-col tw-overflow-y-auto"
    >
      <div className="tw-flex tw-h-full tw-justify-between tw-border-b tw-border-gray-200 tw-p-5">
        <h3 className="tw-text-xl tw-font-semibold">Table Columns</h3>
        <Button type="button" bg="main" onClick={handleReset}>
          <i className="fa-solid fa-arrow-rotate-left tw-mr-2" />
          Restore Defaults
        </Button>
      </div>
      <div className="tw-flex tw-flex-col tw-gap-5 tw-p-7">
        <CheckboxList
          columns={columns}
          columnsValues={columnsValues}
          handleColumnValueChange={handleColumnValueChange}
        />
      </div>
      <div className="tw-sticky tw-bottom-0 tw-left-0 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md">
        <Button
          type="submit"
          bg="success"
          customStyle="tw-w-full tw-flex tw-gap-2"
        >
          <i className="fa-regular fa-floppy-disk" />
          Apply
        </Button>
      </div>
    </form>
  );
};

export default Columns;
