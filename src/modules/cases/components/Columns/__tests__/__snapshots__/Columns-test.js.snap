// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Columns renders Columns 1`] = `
<form
  autoComplete="off"
  className="tw-scrollbar-gutter-stable tw-flex tw-h-full tw-flex-col tw-overflow-y-auto"
  onSubmit={[Function]}
>
  <div
    className="tw-flex tw-h-full tw-justify-between tw-border-b tw-border-gray-200 tw-p-5"
  >
    <h3
      className="tw-text-xl tw-font-semibold"
    >
      Table Columns
    </h3>
    <Button
      bg="main"
      onClick={[Function]}
      type="button"
    >
      <i
        className="fa-solid fa-arrow-rotate-left tw-mr-2"
      />
      Restore Defaults
    </Button>
  </div>
  <div
    className="tw-flex tw-flex-col tw-gap-5 tw-p-7"
  >
    <Checkbox
      checked={false}
      id="definitiveParentNetwork-table-columns-checkbox"
      label="Definitive Parent Network"
      name="definitiveParentNetwork"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="definitiveNetwork-table-columns-checkbox"
      label="Definitive Network"
      name="definitiveNetwork"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="facility-table-columns-checkbox"
      label="Facility"
      name="facility"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="caseType-table-columns-checkbox"
      label="Case Type"
      name="caseType"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="caseTypeProductCategory-table-columns-checkbox"
      label="Product Category"
      name="caseTypeProductCategory"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="cancerType-table-columns-checkbox"
      label="Cancer Type"
      name="cancerType"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="caseId-table-columns-checkbox"
      label="Case ID"
      name="caseId"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="gwtgId-table-columns-checkbox"
      label="GWTG ID"
      name="gwtgId"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="visitNumber-table-columns-checkbox"
      label="Visit Number"
      name="visitNumber"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="mrn-table-columns-checkbox"
      label="MRN"
      name="mrn"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="owner-table-columns-checkbox"
      label="Owner"
      name="owner"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="assignee-table-columns-checkbox"
      label="Assignee"
      name="assignee"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="status-table-columns-checkbox"
      label="Status"
      name="status"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="exceptionQuestionnaireStatus-table-columns-checkbox"
      label="Exception Questionnaire Status"
      name="exceptionQuestionnaireStatus"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="dateOfirstContact-table-columns-checkbox"
      label="Date of First Contact"
      name="dateOfirstContact"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="visitArrivalDate-table-columns-checkbox"
      label="Arrival Date"
      name="visitArrivalDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="visitAdmissionDate-table-columns-checkbox"
      label="Admission Date"
      name="visitAdmissionDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="procedureDate-table-columns-checkbox"
      label="Procedure Date"
      name="procedureDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={true}
      id="visitDischargedAt-table-columns-checkbox"
      label="Discharge Date"
      name="visitDischargedAt"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="deadline-table-columns-checkbox"
      label="Deadline"
      name="deadline"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="patientFirstName-table-columns-checkbox"
      label="Patient First Name"
      name="patientFirstName"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="patientMiddleName-table-columns-checkbox"
      label="Patient Middle Name"
      name="patientMiddleName"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="patientLastName-table-columns-checkbox"
      label="Patient Last Name"
      name="patientLastName"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="patientDOB-table-columns-checkbox"
      label="Patient DOB"
      name="patientDOB"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="caseCreationDate-table-columns-checkbox"
      label="Case Creation Date"
      name="caseCreationDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="caseCompletionDate-table-columns-checkbox"
      label="Case Completion Date"
      name="caseCompletionDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="lastUpdatedDate-table-columns-checkbox"
      label="Last Updated Date"
      name="lastUpdatedDate"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="creationCode-table-columns-checkbox"
      label="Creation Code"
      name="creationCode"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="reportable-table-columns-checkbox"
      label="Reportable"
      name="reportable"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="firstComment-table-columns-checkbox"
      label="First Comment"
      name="firstComment"
      onChange={[Function]}
    />
    <Checkbox
      checked={false}
      id="reviewDeadline-table-columns-checkbox"
      label="Review Deadline"
      name="reviewDeadline"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-sticky tw-bottom-0 tw-left-0 tw-h-[60px] tw-w-full tw-bg-qc-blue-50 tw-p-2.5 tw-shadow-qc-md"
  >
    <Button
      bg="success"
      customStyle="tw-w-full tw-flex tw-gap-2"
      type="submit"
    >
      <i
        className="fa-regular fa-floppy-disk"
      />
      Apply
    </Button>
  </div>
</form>
`;
