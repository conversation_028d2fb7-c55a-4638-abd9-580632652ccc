/* eslint-disable react/button-has-type */
/* eslint-disable react/display-name */
import { act, create } from "react-test-renderer";
import { fireEvent, screen } from "@testing-library/react";
import Columns from "modules/cases/components/Columns";
import { decoratedWithDispatch } from "utils/tests/decorated";
import tabs from "modules/cases/tabs";
import { renderWithProviders } from "utils/tests/renderWithProviders";
import * as qComponents from "@q-centrix/q-components-react";
import wait from "waait";

jest.mock("@q-centrix/q-components-react", () => ({
  __esModule: true,
  Button: "Button",
  Checkbox: "Checkbox"
}));

const [firstTab] = tabs.filter(tab => tab.key !== "case_finding");

describe("Columns", () => {
  test("renders Columns", async () => {
    const { component } = decoratedWithDispatch(
      Columns,
      {},
      {
        cases: {
          selectedTab: firstTab,
          selectedColumns: firstTab.columns
        }
      }
    );

    const columnsComponent = create(component);

    await act(() => wait(100));
    expect(columnsComponent).toMatchSnapshot();
  });

  test("shows the checkboxes selected by default", () => {
    qComponents.Button = ({ ...props }) => <button {...props} />;
    qComponents.Checkbox = ({ ...props }) => (
      <input {...props} type="checkbox" />
    );

    const { columns } = firstTab;
    const defaultColumns = columns.filter(column => column.default);

    renderWithProviders(<Columns />, {
      preloadedState: {
        app: {
          cases: {
            selectedTab: firstTab,
            selectedColumns: columns
          }
        }
      }
    });

    const checkboxes = screen.getAllByRole("checkbox");

    checkboxes.forEach(box => {
      defaultColumns.forEach(column => {
        if (column.name === box.name) {
          expect(box).toBeChecked();
        } else {
          expect(box).not.toBeChecked();
        }
      });
    });
  });

  test("restores the default values", () => {
    qComponents.Button = ({ ...props }) => <button {...props} />;
    qComponents.Checkbox = ({ ...props }) => (
      <input {...props} type="checkbox" />
    );

    const { columns } = firstTab;

    renderWithProviders(<Columns />, {
      preloadedState: {
        app: {
          cases: {
            selectedTab: firstTab,
            selectedColumns: columns
          }
        }
      }
    });

    const defaultColumns = columns.filter(column => column.default);

    const checkboxes = screen.getAllByRole("checkbox");

    const [firstCheckbox] = checkboxes;

    fireEvent.click(firstCheckbox);
    expect(firstCheckbox).toBeChecked();

    fireEvent.click(
      screen.getByRole("button", {
        name: /restore defaults/i
      })
    );

    checkboxes.forEach(box => {
      defaultColumns.forEach(column => {
        if (column.name === box.name) {
          expect(box).toBeChecked();
        } else {
          expect(box).not.toBeChecked();
        }
      });
    });
  });
});
