import { Checkbox } from "@q-centrix/q-components-react";

const CheckboxList = ({ columns, columnsValues, handleColumnValueChange }) => {
  if (!columns) return null;

  return columnsValues.map(({ key, label, name, checked }) => (
    <Checkbox
      key={key}
      id={`${key}-table-columns-checkbox`}
      checked={checked}
      name={name}
      label={label}
      onChange={handleColumnValueChange}
    />
  ));
};

export default CheckboxList;
