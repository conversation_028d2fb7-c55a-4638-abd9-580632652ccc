/* eslint-disable max-statements */
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  keys,
  pipe,
  propEq,
  reject,
  includes,
  __,
  any,
  cond,
  T,
  map,
  prop,
  findIndex
} from "ramda";
import {
  addOrRemoveFromSelectedRows,
  setActiveCaseList
} from "modules/cases/redux/slice";
import { toCaseDetails } from "utils/toCaseDetails";
import { toHourDetails } from "utils/toHourDetails";
import { toActivityDetails } from "utils/toActivityDetails";
import casesSelectors from "modules/cases/redux/selectors";
import { useNavigate } from "react-router-dom";
import { isNotNullOrEmpty } from "utils/fp";

const nonDisplayableColumns = [
  "id",
  "isRegulatory",
  "isAdminOutpatient",
  "isAdminInpatient",
  "hasAbstractionTask"
];

export const useComponentLogic = ({ row, rows, shouldShowSearchResult }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const rowKeys = pipe(keys, reject(includes(__, nonDisplayableColumns)))(row);
  const selectedTab = useSelector(casesSelectors.getSelectedTab);
  const handleRowClick = useCallback(
    () =>
      cond([
        [
          () => isNotNullOrEmpty(shouldShowSearchResult),
          () => {
            dispatch(
              setActiveCaseList({
                caseListIds: map(prop("id"), rows || []),
                currentCaseIndex: findIndex(propEq("id", row.id), rows || [])
              })
            );
            toCaseDetails(row.id, navigate);
          }
        ],
        [propEq("type", "hours"), () => toHourDetails(row.id, navigate)],
        [
          propEq("type", "activities"),
          () => toActivityDetails(row.id, navigate)
        ],
        [
          T,
          () => {
            dispatch(
              setActiveCaseList({
                caseListIds: map(prop("id"), rows || []),
                currentCaseIndex: findIndex(propEq("id", row.id), rows || [])
              })
            );
            toCaseDetails(row.id, navigate);
          }
        ]
      ])(selectedTab),
    [row.id, selectedTab, navigate, dispatch, rows]
  );
  const selectedRows = useSelector(casesSelectors.getSelectedRows);
  const selected = any(propEq("id", row.id), selectedRows);
  const handleCheckbox = () => dispatch(addOrRemoveFromSelectedRows(row));

  return {
    rowKeys,
    handleRowClick,
    handleCheckbox,
    selected
  };
};
