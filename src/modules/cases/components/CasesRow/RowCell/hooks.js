import { always, cond, includes, propOr, T, replace } from "ramda";

const getStatusCategory = status =>
  cond([
    [includes("Billable"), always("success")],
    [includes("Ineligible"), always("success")],
    [includes("Not Started"), always("neutral")],
    [includes("Pending-Incomplete Abstraction"), always("main")],
    [includes("Pending"), always("warning")],
    [T, always("unknown")]
  ])(status);

export const useComponentLogic = props => {
  const { row } = props;
  const status = propOr("", "status", row || {});
  const statusCategory = getStatusCategory(status);
  const label = replace(/-/g, " - ", status);

  return { statusCategory, label };
};
