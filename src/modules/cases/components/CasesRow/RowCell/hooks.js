import { always, cond, includes, propOr, replace, T } from "ramda";

const getStatusCategory = status =>
  cond([
    [includes("Billable"), always("success")],
    [includes("Ineligible"), always("success")],
    [includes("Not Started"), always("neutral")],
    [includes("Pending-Incomplete Abstraction"), always("main")],
    [includes("Pending"), always("warning")],
    [T, always("unknown")]
  ])(status);

const getIsBorderless = status =>
  cond([
    [includes("Billable"), always(true)],
    [T, always(false)]
  ])(status);

export const useComponentLogic = props => {
  const { row } = props;
  const status = propOr("", "status", row || {});
  const statusCategory = getStatusCategory(status);
  const label = replace(/-/g, " - ", status);
  const isBorderless = getIsBorderless(status);

  return { statusCategory, label, isBorderless };
};
