import { TableCell, TagWithDropdown } from "@q-centrix/q-components-react";
import classNames from "classnames";
import { useComponentLogic } from "./hooks";

const RowCell = props => {
  const { row, rowKey, shouldShowSearchResult } = props;
  const { statusCategory, label, isBorderless } = useComponentLogic(props);

  const tablePaddingClass = classNames("tw-p-2.5", {
    "tw-pl-[20px]": shouldShowSearchResult
  });

  return (
    <TableCell key={rowKey} className={tablePaddingClass}>
      <div title={row[rowKey]} className="tw-max-w-[250px] tw-truncate">
        {rowKey === "status" ? (
          <TagWithDropdown
            isTagOnly
            status={statusCategory}
            text={label}
            isDisabled
            isBorderless={isBorderless}
          />
        ) : (
          row[rowKey]
        )}
      </div>
    </TableCell>
  );
};

export default RowCell;
