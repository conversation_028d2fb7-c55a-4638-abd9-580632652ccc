import { create } from "react-test-renderer";
import RowCell from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  TableCell: "TableCell",
  TagWithDropdown: "TagWithDropdown"
}));

const row = { status: "Billable" };
const rowKey = "status";
const shouldShowSearchResult = true;

describe("RowCell", () => {
  function render() {
    return create(
      <RowCell
        row={row}
        rowKey={rowKey}
        shouldShowSearchResult={shouldShowSearchResult}
      />
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
