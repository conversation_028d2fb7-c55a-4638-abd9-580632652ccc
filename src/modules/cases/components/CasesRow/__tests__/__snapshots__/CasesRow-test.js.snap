// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cases renders CasesRow 1`] = `
<TableRow
  onClick={[Function]}
  onContextMenu={[Function]}
  selected={false}
>
  <TableCell
    className="tw-p-5 tw-text-center"
    onClick={[Function]}
  >
    <Checkbox
      checked={false}
      disabled={false}
      name="4"
      onChange={[Function]}
    />
  </TableCell>
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="assignee"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="facility"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="status"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitDischargedAt"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitNumber"
  />
  <TableCell
    className="tw-text-right"
  >
    <i
      className="fa-solid fa-chevron-right tw-text-qc-blue-800"
    />
  </TableCell>
</TableRow>
`;

exports[`Cases renders CasesRow disabled 1`] = `
<TableRow
  onClick={[Function]}
  onContextMenu={[Function]}
  selected={false}
>
  <TableCell
    className="tw-p-5 tw-text-center"
    onClick={[Function]}
  >
    <Checkbox
      checked={false}
      disabled={true}
      name="4"
      onChange={[Function]}
    />
  </TableCell>
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="assignee"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="facility"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="status"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitDischargedAt"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitNumber"
  />
  <TableCell
    className="tw-text-right"
  >
    <i
      className="fa-solid fa-chevron-right tw-text-qc-blue-800"
    />
  </TableCell>
</TableRow>
`;

exports[`Cases renders CasesRow with checked true 1`] = `
<TableRow
  onClick={[Function]}
  onContextMenu={[Function]}
  selected={false}
>
  <TableCell
    className="tw-p-5 tw-text-center"
    onClick={[Function]}
  >
    <Checkbox
      checked={false}
      disabled={false}
      name="4"
      onChange={[Function]}
    />
  </TableCell>
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="assignee"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="facility"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="status"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitDischargedAt"
  />
  <RowCell
    row={
      Object {
        "assignee": null,
        "facility": "Small Medical Center",
        "id": "4",
        "status": "Billable",
        "visitDischargedAt": "2020-09-14T17:00:00-07:00",
        "visitNumber": "45994234",
      }
    }
    rowKey="visitNumber"
  />
  <TableCell
    className="tw-text-right"
  >
    <i
      className="fa-solid fa-chevron-right tw-text-qc-blue-800"
    />
  </TableCell>
</TableRow>
`;
