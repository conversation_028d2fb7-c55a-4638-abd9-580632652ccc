import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import CasesRow from "modules/cases/components/CasesRow";
import mocks from "modules/cases/graphql/mocks";
import { MemoryRouter } from "react-router-dom";

jest.mock("../RowCell", () => "RowCell");

jest.mock("@q-centrix/q-components-react", () => ({
  TableRow: "TableRow",
  TableCell: "TableCell",
  Checkbox: "Checkbox"
}));

const CasesRowWithRouter = props => (
  <MemoryRouter>
    <CasesRow {...props} />
  </MemoryRouter>
);

const mockedRow = {
  assignee: null,
  facility: "Small Medical Center",
  id: "4",
  status: "Billable",
  visitDischargedAt: "2020-09-14T17:00:00-07:00",
  visitNumber: "45994234"
};

describe("Cases", () => {
  function render(selectionIsDisabled = false, selected = false) {
    return create(
      decoratedA<PERSON>lo({
        component: CasesRowWithRouter,
        props: {
          selectionIsDisabled,
          selected,
          row: mockedRow
        },
        initialAppValues: {
          cases: {
            selectedRows: []
          },
          enabledFeatureToggles: { enabledFeatureToggles: [] }
        },
        apolloMocks: mocks
      })
    );
  }

  test("renders CasesRow", () => {
    const casesRowComponent = render();

    expect(casesRowComponent).toMatchSnapshot();
  });

  test("renders CasesRow with checked true", () => {
    const casesRowComponent = render(false, true);

    expect(casesRowComponent).toMatchSnapshot();
  });

  test("renders CasesRow disabled", () => {
    const casesRowComponent = render(true, false);

    expect(casesRowComponent).toMatchSnapshot();
  });
});
