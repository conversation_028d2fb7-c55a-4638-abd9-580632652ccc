import { TableRow, TableCell, Checkbox } from "@q-centrix/q-components-react";
import Row<PERSON><PERSON> from "./RowCell";
import { stopPropagation } from "utils/stopPropagation";
import { useComponentLogic } from "./hooks";

const CasesRow = ({
  selectionIsDisabled,
  row,
  shouldShowSearchResult,
  rows,
  onContextMenu
}) => {
  const { rowKeys, handleRowClick, handleCheckbox, selected } =
    useComponentLogic({ row, rows, shouldShowSearchResult });

  return (
    <TableRow
      selected={selected}
      onClick={handleRowClick}
      onContextMenu={e => {
        e.preventDefault();
        onContextMenu(e, row);
      }}
    >
      {!shouldShowSearchResult && (
        <TableCell onClick={stopPropagation} className="tw-p-5 tw-text-center">
          <Checkbox
            name={row.id}
            checked={selected}
            onChange={handleCheckbox}
            disabled={selectionIsDisabled}
          />
        </TableCell>
      )}

      {rowKeys.map(rowKey => (
        <RowCell
          key={rowKey}
          rowKey={rowKey}
          row={row}
          shouldShowSearchResult={shouldShowSearchResult}
        />
      ))}
      <TableCell className="tw-text-right">
        <i className="fa-solid fa-chevron-right tw-text-qc-blue-800" />
      </TableCell>
    </TableRow>
  );
};

export default CasesRow;
