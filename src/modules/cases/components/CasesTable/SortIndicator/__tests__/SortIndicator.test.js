/* eslint-disable react/button-has-type */
import { create } from "react-test-renderer";
import SortIndicator from "modules/cases/components/CasesTable/SortIndicator";
import { ORDER } from "modules/cases/constants";

describe("SortIndicator", () => {
  test("renders default SortIndicator", () => {
    expect(
      create(<SortIndicator id="mock" sort={{ orderBy: null, order: null }} />)
    ).toMatchSnapshot();
  });

  test("renders Asc SortIndicator", () => {
    expect(
      create(
        <SortIndicator id="mock" sort={{ orderBy: "mock", order: ORDER.ASC }} />
      )
    ).toMatchSnapshot();
  });

  test("renders Desc SortIndicator", () => {
    expect(
      create(
        <SortIndicator
          id="mock"
          sort={{ orderBy: "mock", order: ORDER.DESC }}
        />
      )
    ).toMatchSnapshot();
  });
});
