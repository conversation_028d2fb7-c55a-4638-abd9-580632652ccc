import classnames from "classnames";
import { ORDER } from "modules/cases/constants";

const getArrowIcon = (selected, order) => {
  if (!selected) return "fa-arrow-up-arrow-down";

  return order === ORDER.ASC ? "fa-arrow-up" : "fa-arrow-down";
};

const SortIndicator = ({ sort, id }) => {
  const selected = sort.orderBy === id;

  const iconClass = classnames(
    "fa-solid tw-mr-1 tw-w-4",
    getArrowIcon(selected, sort.order)
  );

  return <i className={iconClass} />;
};

export default SortIndicator;
