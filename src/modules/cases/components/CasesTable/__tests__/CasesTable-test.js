import { create, act } from "react-test-renderer";
import { decoratedApolloWithDispatch } from "utils/tests/decorated";
import tabs from "modules/cases/tabs";
import wait from "waait";
import CasesTable from "modules/cases/components/CasesTable";
import mocks from "modules/cases/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Table: "Table",
  TableHead: "TableHead",
  TableBody: "TableBody",
  TableHeadRow: "TableHeadRow",
  TableHeadCell: "TableHeadCell",
  TableContainer: "TableContainer",
  TablePagination: "TablePagination",
  Checkbox: "Checkbox"
}));

jest.mock("../SortIndicator", () => "SortIndicator");
jest.mock("../../CasesRow", () => "CasesRow");

describe("Cases", () => {
  test("renders incomplete cases tab table", async () => {
    const incompleteCasesTab = tabs.find(tab => tab.key === "my_incomplete");

    const { component } = decoratedApolloWithDispatch({
      component: CasesTable,
      initialAppValues: {
        cases: {
          selectedTab: incompleteCasesTab,
          selectedColumns: incompleteCasesTab.columns,
          selectedRows: [],
          selectedFilters: [],
          page: 1
        }
      },
      apolloMocks: mocks
    });

    const casesTableComponent = create(component);

    await act(() => wait(100));
    expect(casesTableComponent).toMatchSnapshot();
  });

  test("renders incomplete hours tab table", async () => {
    const hoursCasesTab = tabs.find(tab => tab.key === "hours");

    const { component } = decoratedApolloWithDispatch({
      component: CasesTable,
      initialAppValues: {
        cases: {
          selectedTab: hoursCasesTab,
          selectedColumns: hoursCasesTab.columns,
          selectedRows: [],
          selectedFilters: [],
          page: 1
        }
      },
      apolloMocks: mocks
    });

    const casesTableComponentWithHours = create(component);

    await act(() => wait(100));
    expect(casesTableComponentWithHours).toMatchSnapshot();
  });
});
