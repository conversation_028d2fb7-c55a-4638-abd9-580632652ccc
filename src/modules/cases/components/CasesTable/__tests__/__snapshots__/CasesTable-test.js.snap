// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cases renders incomplete cases tab table 1`] = `
<TableContainer
  className="tw-flex tw-h-full tw-w-full tw-flex-col tw-justify-between tw-rounded-b-[5px] tw-bg-white tw-shadow-lg"
>
  <div
    className="tw-h-96 tw-flex-grow tw-overflow-auto"
  >
    <Table
      loaded={true}
      loading={false}
    >
      <TableHead
        className="tw-sticky tw-top-0 tw-z-10 tw-bg-white tw-shadow-tableHeader"
      >
        <TableHeadRow>
          <TableHeadCell
            className="tw-w-3 tw-p-5 tw-text-center"
          >
            <Checkbox
              checked={false}
              indeterminate={false}
              name="check-all"
              onChange={[Function]}
            />
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="definitiveParentNetwork"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="definitiveParentNetwork"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Definitive Parent Network
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="definitiveNetwork"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="definitiveNetwork"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Definitive Network
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="facility"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="facility"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Facility
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="caseType"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="caseType"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Case Type
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="caseTypeProductCategory"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="caseTypeProductCategory"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Product Category
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="cancerType"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="cancerType"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Cancer Type
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="caseId"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="caseId"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Case ID
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="gwtgId"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="gwtgId"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            GWTG ID
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="visitNumber"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="visitNumber"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Visit Number
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="mrn"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="mrn"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            MRN
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="owner"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="owner"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Owner
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="assignee"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="assignee"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Assignee
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="status"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="status"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Status
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="exceptionQuestionnaireStatus"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="exceptionQuestionnaireStatus"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Exception Questionnaire Status
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="dateOfirstContact"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="dateOfirstContact"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Date of First Contact
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="visitArrivalDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="visitArrivalDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Arrival Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="visitAdmissionDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="visitAdmissionDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Admission Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="procedureDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="procedureDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Procedure Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="visitDischargedAt"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="visitDischargedAt"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Discharge Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="deadline"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="deadline"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Deadline
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="patientFirstName"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="patientFirstName"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Patient First Name
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="patientMiddleName"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="patientMiddleName"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Patient Middle Name
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="patientLastName"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="patientLastName"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Patient Last Name
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="patientDOB"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="patientDOB"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Patient DOB
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="caseCreationDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="caseCreationDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Case Creation Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="caseCompletionDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="caseCompletionDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Case Completion Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="lastUpdatedDate"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="lastUpdatedDate"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Last Updated Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="creationCode"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="creationCode"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Creation Code
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="reportable"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="reportable"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Reportable
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="firstComment"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="firstComment"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            First Comment
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="reviewDeadline"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="reviewDeadline"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Review Deadline
          </TableHeadCell>
          <TableHeadCell
            className="tw-p-4"
          />
        </TableHeadRow>
      </TableHead>
      <TableBody
        loading={false}
      >
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "2",
              "caseType": "SCPC HF",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Large Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "2",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "85682522",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "1",
              "caseType": "GWTG Resuscitation MET",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Large Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "1",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "85682522",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "3",
              "caseType": "AMI",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Large Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "3",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "85682522",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "4",
              "caseType": "Trauma",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Small Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "4",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "33316278",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "5",
              "caseType": "STS-CHS - Anesthesia only",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Small Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "5",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "33316278",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
        <CasesRow
          onContextMenu={[Function]}
          row={
            Object {
              "assignee": undefined,
              "cancerType": undefined,
              "caseCompletionDate": undefined,
              "caseCreationDate": undefined,
              "caseId": "6",
              "caseType": "AMI",
              "caseTypeProductCategory": undefined,
              "creationCode": undefined,
              "dateOfirstContact": undefined,
              "deadline": undefined,
              "definitiveNetwork": undefined,
              "definitiveParentNetwork": undefined,
              "exceptionQuestionnaireStatus": undefined,
              "facility": "Small Medical Center",
              "firstComment": undefined,
              "gwtgId": undefined,
              "hasAbstractionTask": undefined,
              "id": "6",
              "isAdminInpatient": undefined,
              "isAdminOutpatient": undefined,
              "lastUpdatedDate": undefined,
              "mrn": undefined,
              "owner": undefined,
              "patientDOB": undefined,
              "patientFirstName": undefined,
              "patientLastName": undefined,
              "patientMiddleName": undefined,
              "procedureDate": undefined,
              "reportable": undefined,
              "reviewDeadline": undefined,
              "status": undefined,
              "visitAdmissionDate": undefined,
              "visitArrivalDate": undefined,
              "visitDischargedAt": "04/03/2023",
              "visitNumber": "33316278",
            }
          }
          rows={
            Array [
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "2",
                "caseType": "SCPC HF",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "2",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "1",
                "caseType": "GWTG Resuscitation MET",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "1",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "3",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Large Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "3",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "85682522",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "4",
                "caseType": "Trauma",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "4",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "5",
                "caseType": "STS-CHS - Anesthesia only",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "5",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
              Object {
                "assignee": undefined,
                "cancerType": undefined,
                "caseCompletionDate": undefined,
                "caseCreationDate": undefined,
                "caseId": "6",
                "caseType": "AMI",
                "caseTypeProductCategory": undefined,
                "creationCode": undefined,
                "dateOfirstContact": undefined,
                "deadline": undefined,
                "definitiveNetwork": undefined,
                "definitiveParentNetwork": undefined,
                "exceptionQuestionnaireStatus": undefined,
                "facility": "Small Medical Center",
                "firstComment": undefined,
                "gwtgId": undefined,
                "hasAbstractionTask": undefined,
                "id": "6",
                "isAdminInpatient": undefined,
                "isAdminOutpatient": undefined,
                "lastUpdatedDate": undefined,
                "mrn": undefined,
                "owner": undefined,
                "patientDOB": undefined,
                "patientFirstName": undefined,
                "patientLastName": undefined,
                "patientMiddleName": undefined,
                "procedureDate": undefined,
                "reportable": undefined,
                "reviewDeadline": undefined,
                "status": undefined,
                "visitAdmissionDate": undefined,
                "visitArrivalDate": undefined,
                "visitDischargedAt": "04/03/2023",
                "visitNumber": "33316278",
              },
            ]
          }
        />
      </TableBody>
    </Table>
  </div>
  <TablePagination
    cacheKey="my_incomplete"
    count={100}
    loading={false}
    numberOfRows={6}
    onPageChange={[Function]}
    onRowsPerPageChange={[Function]}
    page={1}
    rowsPerPage={100}
    rowsPerPageOptions={
      Array [
        100,
        200,
        300,
        400,
        500,
      ]
    }
    wrapperClassname="tw-w-full"
  />
</TableContainer>
`;

exports[`Cases renders incomplete hours tab table 1`] = `
<TableContainer
  className="tw-flex tw-h-full tw-w-full tw-flex-col tw-justify-between tw-rounded-b-[5px] tw-bg-white tw-shadow-lg"
>
  <div
    className="tw-h-96 tw-flex-grow tw-overflow-auto"
  >
    <Table
      loaded={true}
      loading={false}
    >
      <TableHead
        className="tw-sticky tw-top-0 tw-z-10 tw-bg-white tw-shadow-tableHeader"
      >
        <TableHeadRow>
          <TableHeadCell
            className="tw-w-3 tw-p-5 tw-text-center"
          >
            <Checkbox
              checked={false}
              indeterminate={false}
              name="check-all"
              onChange={[Function]}
            />
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="owner"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="owner"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Owner
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="totalTime"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="totalTime"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Hours Worked
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="hourType"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="hourType"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Hour Type
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="productCategory"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="productCategory"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Product Category
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="facilityNames"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="facilityNames"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Facility
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="createdAt"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="createdAt"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Date of Entry
          </TableHeadCell>
          <TableHeadCell
            className="tw-whitespace-nowrap tw-p-2.5 "
            id="updatedAt"
            onClick={[Function]}
            selected={false}
          >
            <SortIndicator
              id="updatedAt"
              sort={
                Object {
                  "order": null,
                  "orderBy": null,
                }
              }
            />
            Last Updated Date
          </TableHeadCell>
          <TableHeadCell
            className="tw-p-4"
          />
        </TableHeadRow>
      </TableHead>
      <TableBody
        loading={false}
      />
    </Table>
  </div>
  <TablePagination
    cacheKey="hours"
    count={0}
    loading={false}
    numberOfRows={0}
    onPageChange={[Function]}
    onRowsPerPageChange={[Function]}
    page={1}
    rowsPerPage={100}
    rowsPerPageOptions={
      Array [
        100,
        200,
        300,
        400,
        500,
      ]
    }
    wrapperClassname="tw-w-full"
  />
</TableContainer>
`;
