import {
  Table,
  TableBody,
  TableHead,
  TableHeadCell,
  TableHeadRow,
  TableContainer,
  Checkbox,
  TablePagination
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "modules/cases/components/CasesTable/hooks";
import SortIndicator from "modules/cases/components/CasesTable/SortIndicator";
import CasesRow from "../CasesRow";
import CaseFindingProvider from "../CaseFindingProvider";
import ContextMenu from "shared/components/ContextMenu";
import { AnimatePresence } from "framer-motion";

// eslint-disable-next-line complexity
const CasesTable = ({ selectionIsDisabled }) => {
  const {
    tableScrollRef,
    contextMenuItems,
    contextMenuFields,
    handleContextMenu,
    rows,
    handleToggleAllRows,
    selectedColumns,
    sort,
    handleSort,
    allRowsHaveBeenSelected,
    checkAllIsIndeterminate,
    page,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage,
    count,
    key,
    maxCountReached,
    loading,
    called
  } = useComponentLogic();
  const { visible: showContextMenu, x, y } = contextMenuFields;

  if (key === "case_finding") {
    return <CaseFindingProvider />;
  }
  return (
    <TableContainer className="tw-flex tw-h-full tw-w-full tw-flex-col tw-justify-between tw-rounded-b-[5px] tw-bg-white tw-shadow-lg">
      <div
        ref={tableScrollRef}
        className="tw-h-96 tw-flex-grow tw-overflow-auto"
      >
        <Table loading={loading} loaded={!loading && called}>
          <TableHead className="tw-sticky tw-top-0 tw-z-10 tw-bg-white tw-shadow-tableHeader">
            <TableHeadRow>
              <TableHeadCell className="tw-w-3 tw-p-5 tw-text-center">
                <Checkbox
                  name="check-all"
                  checked={allRowsHaveBeenSelected}
                  indeterminate={checkAllIsIndeterminate}
                  onChange={handleToggleAllRows}
                  disabled={selectionIsDisabled}
                />
              </TableHeadCell>
              {selectedColumns.map(item => (
                <TableHeadCell
                  key={item.key}
                  id={item.key}
                  onClick={handleSort}
                  selected={item.key === sort.orderBy}
                  className={`tw-whitespace-nowrap tw-p-2.5 ${
                    item.withSort ? "" : "tw-pointer-events-none"
                  }`}
                >
                  {item.withSort && <SortIndicator id={item.key} sort={sort} />}
                  {item.label}
                </TableHeadCell>
              ))}
              <TableHeadCell className="tw-p-4" />
            </TableHeadRow>
          </TableHead>
          <TableBody loading={loading}>
            {rows.map(row => (
              <CasesRow
                key={row.id}
                row={row}
                selectionIsDisabled={selectionIsDisabled}
                rows={rows}
                onContextMenu={handleContextMenu}
              />
            ))}
          </TableBody>
        </Table>
        <AnimatePresence>
          {showContextMenu && (
            <ContextMenu
              menuItems={contextMenuItems}
              visible={showContextMenu}
              x={x}
              y={y}
            />
          )}
        </AnimatePresence>
      </div>
      <TablePagination
        wrapperClassname="tw-w-full"
        count={count}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        cacheKey={key}
        maxCountReached={maxCountReached}
        numberOfRows={rows.length}
        loading={loading}
        rowsPerPageOptions={[100, 200, 300, 400, 500]}
      />
    </TableContainer>
  );
};

export default CasesTable;
