/* eslint-disable complexity */
/* eslint-disable max-statements */
import { useCallback, useEffect, useMemo, useState, useRef } from "react";
import casesSelectors from "modules/cases/redux/selectors";
import { useDispatch, useSelector } from "react-redux";
import {
  T,
  __,
  all,
  always,
  and,
  any,
  applySpec,
  ascend,
  assoc,
  both,
  complement,
  concat,
  cond,
  defaultTo,
  descend,
  equals,
  find,
  gt,
  ifElse,
  includes,
  is,
  isNil,
  map,
  path,
  pipe,
  prop,
  propEq,
  reduce,
  reject,
  sortWith,
  toLower,
  uniq,
  when
} from "ramda";
import { ORDER } from "modules/cases/constants";
import { useQuery } from "@apollo/client";
import {
  CASE_COUNT,
  getCaseListQuery,
  getHoursListQuery,
  getActivityListQuery
} from "modules/cases/graphql/query";
import {
  changePage,
  clearAllFromSelectedRows,
  setSelectedRows
} from "modules/cases/redux/slice";
import { convertFiltersToArray } from "modules/cases/utils/convertFiltersToArray";
import useLocalStorage from "shared/hooks/useLocalStorage";
import tabs, { DATE_TYPE_COLUMNS } from "modules/cases/tabs";
import { formatISODateString } from "utils/formatISODateString";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";
import { formatISOToDate } from "utils/formatISOToDate";
import useOnClickOutside from "shared/hooks/useOnClickOutside";

const INITIAL_PAGE = 1;
const INITIAL_ROWS_PER_PAGE = 100;
const HOURS_TYPE = "hours";
const CASES_TYPE = "cases";
const ACTIVITIES_TYPE = "activities";

const initialSortsSettingState = reduce(
  (acc, curr) => assoc(curr.key, { orderBy: null, order: null }, acc),
  {},
  tabs
);

const getActivitiesObject = data => ({
  data,
  path: "activities",
  initialValue: applySpec({
    id: prop("id")
  })
});
const getHoursObject = data => ({
  data,
  path: "hourGroups",
  initialValue: applySpec({
    id: prop("id")
  })
});
const getCaseObject = data => ({
  data,
  path: "cases",
  initialValue: applySpec({
    id: prop("id"),
    isAdminOutpatient: path(["caseType", "isAdminOutpatient"]),
    isAdminInpatient: path(["caseType", "isAdminInpatient"]),
    hasAbstractionTask: prop("hasAbstractionTask")
  })
});

const processListData = selectedColumns => obj =>
  pipe(
    prop("data"),
    ifElse(
      isNullOrEmpty,
      always([]),
      pipe(
        prop(obj.path),
        map(row =>
          reduce(
            (acc, col) =>
              pipe(
                prop("path"),
                path(__, row),
                when(
                  both(always(col.isDate), complement(isNil)),
                  ifElse(
                    always(col.isDate?.showTime),
                    formatISODateString(__, "MM/dd/yyyy, h:mm aaa"),
                    formatISOToDate(__, "MM/dd/yyyy")
                  )
                ),
                assoc(col.name, __, acc)
              )(col),
            obj.initialValue(row),
            selectedColumns
          )
        )
      )
    )
  )(obj);

const openItemInNewTab = (row, selectedTab, newTab = false) => {
  const url = cond([
    [propEq("type", "hours"), () => `/web/hours/${row.id}`],
    [propEq("type", "activities"), () => `/web/activities/${row.id}`],
    [T, () => `/web/cases/${row.id}`]
  ])(selectedTab);

  if (newTab) {
    window.open(url, "_blank", "noopener,noreferrer");
  }
};

export const useComponentLogic = () => {
  const tableScrollRef = useRef();
  const [contextMenuFields, setContextMenuFields] = useState({
    visible: false,
    x: 0,
    y: 0,
    row: {}
  });

  const dispatch = useDispatch();
  const { key, type, filters } = useSelector(casesSelectors.getSelectedTab);

  const selectedColumns = useSelector(casesSelectors.getSelectedColumns);
  const selectedFilters = useSelector(casesSelectors.getSelectedFilters);
  const selectedRows = useSelector(casesSelectors.getSelectedRows);
  const selectedTab = useSelector(casesSelectors.getSelectedTab);
  const page = useSelector(casesSelectors.getPage);
  const [sortSettings, setSortSettings] = useLocalStorage(
    "abstractionSavedTableSortSettings",
    initialSortsSettingState
  );

  const [rowsPerPage, setRowsPerPage] = useLocalStorage(
    "abstractionTableRowsPerPage",
    INITIAL_ROWS_PER_PAGE
  );

  const queryFilters = useMemo(
    () => convertFiltersToArray(filters, selectedFilters[key]),
    [filters, selectedFilters[key]]
  );

  const activitiesListQuery = useMemo(
    () => getActivityListQuery(selectedColumns),
    [selectedColumns]
  );

  const {
    data: { activityList } = { activityList: { activities: [] } },
    loading: activityListLoading,
    called: activityListCalled
  } = useQuery(activitiesListQuery, {
    variables: {
      profile: key,
      page,
      perPage: rowsPerPage,
      filters: queryFilters
    },
    skip: type !== ACTIVITIES_TYPE
  });

  const hoursListQuery = useMemo(
    () => getHoursListQuery(selectedColumns),
    [selectedColumns]
  );

  const {
    data: { hourGroupList } = { hourGroupList: { hourGroups: [] } },
    loading: hoursListLoading,
    called: hoursListCalled
  } = useQuery(hoursListQuery, {
    variables: {
      page,
      perPage: rowsPerPage,
      filters: queryFilters
    },
    skip: type !== HOURS_TYPE
  });

  const caseListQuery = useMemo(
    () => getCaseListQuery(selectedColumns),
    [selectedColumns]
  );

  const {
    data: { caseList } = { caseList: { cases: [] } },
    loading: caseListLoading,
    called: caseListCalled
  } = useQuery(caseListQuery, {
    variables: {
      profile: key,
      page,
      perPage: rowsPerPage,
      filters: queryFilters
    },
    skip: type !== CASES_TYPE || key === "case_finding"
  });

  const {
    data: { caseList: { count, maxCountReached } } = { caseList: { count: 0 } }
  } = useQuery(CASE_COUNT, {
    variables: {
      profile: key,
      filters: queryFilters
    },
    skip: type !== CASES_TYPE || key === "case_finding"
  });

  const rows = pipe(
    cond([
      [equals("hours"), () => getHoursObject(hourGroupList)],
      [equals("activities"), () => getActivitiesObject(activityList)],
      [T, () => getCaseObject(caseList)]
    ]),
    processListData(selectedColumns)
  )(type);

  const loading = cond([
    [equals("hours"), always(hoursListLoading)],
    [equals("activities"), always(activityListLoading)],
    [T, always(caseListLoading)]
  ])(type);

  const called = cond([
    [equals("hours"), always(hoursListCalled)],
    [equals("activities"), always(activityListCalled)],
    [T, always(caseListCalled)]
  ])(type);

  const allRowsHaveBeenSelected = useMemo(
    () =>
      and(
        gt(selectedRows.length, 0),
        all(row => find(propEq("id", row.id), selectedRows))(rows)
      ),
    [rows, selectedRows]
  );

  const atLeastARowHasBeenSelected = useMemo(
    () =>
      and(
        gt(selectedRows.length, 0),
        any(row => find(propEq("id", row.id), selectedRows))(rows)
      ),
    [rows, selectedRows]
  );

  const checkAllIsIndeterminate =
    !allRowsHaveBeenSelected && atLeastARowHasBeenSelected;

  const handleToggleAllRows = event =>
    pipe(
      ifElse(
        always(event.target.checked && !checkAllIsIndeterminate),
        pipe(concat(selectedRows), uniq),
        currentPageRows => reject(includes(__, currentPageRows), selectedRows)
      ),
      newSelectedRows => dispatch(setSelectedRows(newSelectedRows))
    )(rows);

  const handleChangePage = cb => {
    if (typeof cb === "function") {
      return dispatch(changePage(cb(page)));
    }
    return dispatch(changePage(cb));
  };

  const handleChangeRowsPerPage = value => {
    setRowsPerPage(value);
    dispatch(changePage(INITIAL_PAGE));
  };

  useEffect(() => {
    dispatch(changePage(INITIAL_PAGE));

    return () => dispatch(clearAllFromSelectedRows());
  }, [key]);

  const sort = sortSettings[selectedTab.key] ?? {
    orderBy: null,
    order: null
  };

  const sortOrder = sort.order === ORDER.ASC ? ascend : descend;

  const convertIfisDate = pipe(
    prop(__),
    when(is(String), dateString => new Date(dateString)),
    defaultTo("")
  );
  const convertToLowerCase = pipe(
    prop(__),
    when(is(String), toLower),
    defaultTo("")
  );

  const handleSort = useCallback(
    orderBy => {
      setSortSettings(prevSortSettings =>
        pipe(
          prop(selectedTab.key),
          applySpec({
            orderBy: ifElse(
              both(propEq("order", ORDER.DESC), propEq("orderBy", orderBy)),
              always(null),
              always(orderBy)
            ),
            order: cond([
              [complement(propEq("orderBy", orderBy)), always(ORDER.ASC)],
              [pipe(prop("order"), isNullOrEmpty), always(ORDER.ASC)],
              [propEq("order", ORDER.ASC), always(ORDER.DESC)],
              [propEq("order", ORDER.DESC), always(null)]
            ])
          }),
          assoc(selectedTab.key, __, prevSortSettings)
        )(prevSortSettings)
      );
    },
    [selectedTab.key, setSortSettings]
  );

  const sortedRows = useMemo(
    () =>
      when(
        () => isNotNullOrEmpty(sort.order),
        sortWith([
          sortOrder(
            ifElse(
              includes(__, DATE_TYPE_COLUMNS),
              convertIfisDate,
              convertToLowerCase
            )(sort.orderBy)
          )
        ])
      )(rows),
    [rows, sort]
  );

  const handleCloseContextMenu = () => {
    setContextMenuFields({
      visible: false,
      x: 0,
      y: 0,
      row: null
    });
  };

  const handleContextMenu = (e, row) => {
    e.preventDefault();

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 200;
    const menuHeight = 100;

    let x = e.clientX;
    let y = e.clientY;

    // Adjust position if menu would go off-screen
    if (x + menuWidth > viewportWidth) {
      x = viewportWidth - menuWidth - 10;
    }
    if (y + menuHeight > viewportHeight) {
      y = viewportHeight - menuHeight - 10;
    }

    setContextMenuFields({
      visible: true,
      x,
      y,
      row
    });
  };

  const handleOpenInNewTab = () => {
    if (contextMenuFields.row) {
      openItemInNewTab(contextMenuFields.row, selectedTab, true);
    }
    handleCloseContextMenu();
  };

  const contextMenuItems = [
    { id: 1, text: "Open in new tab", onClick: handleOpenInNewTab }
  ];

  useOnClickOutside(tableScrollRef, handleCloseContextMenu);

  useEffect(() => {
    const container = tableScrollRef.current;

    const handleScroll = () => {
      if (contextMenuFields.visible) {
        handleCloseContextMenu();
      }
    };

    if (container && contextMenuFields.visible) {
      container.addEventListener("scroll", handleScroll);
    }

    return () =>
      container && container.removeEventListener("scroll", handleScroll);
  }, [contextMenuFields.visible]);

  return {
    tableScrollRef,
    contextMenuItems,
    contextMenuFields,
    handleContextMenu,
    rows: sortedRows,
    handleToggleAllRows,
    selectedColumns,
    sort,
    handleSort,
    allRowsHaveBeenSelected,
    checkAllIsIndeterminate,
    page,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage,
    count,
    key,
    maxCountReached,
    loading,
    called
  };
};
