import { always, both, count, either, pipe, toPairs, whereAny } from "ramda";
import { isNotNullOrEmpty } from "utils/fp";

const isValidSelectDateRange = whereAny({
  select: isNotNullOrEmpty,
  dateRange: isNotNullOrEmpty,
  timestampModifier: isNotNullOrEmpty
});

const getNumberOfActiveFilters = pipe(
  toPairs,
  count(([key, value]) =>
    both(
      isNotNullOrEmpty,
      either(always(key !== "filterDateBy"), isValidSelectDateRange)
    )(value)
  )
);

export const useComponentLogic = ({ filterValues }) => ({
  count: getNumberOfActiveFilters(filterValues)
});
