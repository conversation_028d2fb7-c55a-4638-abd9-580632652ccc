import { create } from "react-test-renderer";
import DateFilter from "modules/cases/components/Filters/DateFilter";

jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));

describe("DateFilter", () => {
  const dateFilter = {
    type: "DateFilter",
    name: "patientDOB",
    label: "Patient DOB"
  };

  function render() {
    return create(
      <DateFilter
        filterConfig={dateFilter}
        filterValues={{
          [dateFilter.name]: null
        }}
      />
    );
  }

  test("renders DateFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
