import { DateInput } from "@q-centrix/q-components-react";
import { formatDateifLocalStorage } from "utils/formatDateifLocalStorage";

const DateFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const { name, label } = filterConfig;
  const dateValue = formatDateifLocalStorage(filterValues[name]);
  const isActive = Boolean(filterValues[name]);

  return (
    <div key={name} className="tw-flex tw-flex-col tw-gap-2">
      <DateInput
        inputClassName={
          isActive && shouldHighligthFilters && "tw-bg-yellow-400"
        }
        label={label}
        value={dateValue}
        onChange={newDate => handleFilterValueChange(name, newDate)}
        selectsStart
      />
    </div>
  );
};

export default DateFilter;
