import { Checkbox } from "@q-centrix/q-components-react";

const CheckboxFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange
}) => {
  const { name, label } = filterConfig;

  return (
    <Checkbox
      name={name}
      label={label}
      checked={filterValues[name]}
      onChange={() => {
        handleFilterValueChange(name, !filterValues[name]);
      }}
    />
  );
};

export default CheckboxFilter;
