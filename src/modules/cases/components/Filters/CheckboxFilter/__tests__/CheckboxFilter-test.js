import { create } from "react-test-renderer";
import CheckboxFilter from "modules/cases/components/Filters/CheckboxFilter";

jest.mock("@q-centrix/q-components-react", () => ({
  Checkbox: "Checkbox"
}));

describe("CheckboxFilter", () => {
  const checkboxFilter = {
    type: "CheckboxFilter",
    name: "showDisabledUsers",
    label: "Show Disabled Users"
  };

  function render() {
    return create(
      <CheckboxFilter
        filterConfig={checkboxFilter}
        filterValues={{
          [checkboxFilter.name]: false
        }}
      />
    );
  }

  test("renders Checkbox", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
