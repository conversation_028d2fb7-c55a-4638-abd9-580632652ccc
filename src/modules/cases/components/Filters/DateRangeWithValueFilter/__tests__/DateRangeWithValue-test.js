import { create } from "react-test-renderer";
import DateRangeWithValueFilter from "modules/cases/components/Filters/DateRangeWithValueFilter";

jest.mock("@q-centrix/q-components-react", () => ({
  Label: "Label",
  DateInput: "DateInput"
}));

const dateRangeValue = {
  key: "filterDateBy",
  type: "DateRangeWithValueFilter",
  name: "filterDateBy",
  operation: "range",
  defaultValue: {
    dateRange: {
      startDate: null,
      endDate: null
    }
  },
  select: {
    name: "dateBy",
    value: "date_of_first_contact",
    label: "Date of First Contact",
    timestampModifier: "YYYYMMDD"
  },
  dateRange: {
    name: "dateRange",
    label: ["From", "To"]
  }
};

describe("DateRangeWithValueFilter", () => {
  function render() {
    return create(
      <DateRangeWithValueFilter
        filterConfig={dateRangeValue}
        filterValues={{
          [dateRangeValue.name]: {}
        }}
      />
    );
  }

  test("renders DateRangeWithValueFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
