// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRangeWithValueFilter renders DateRangeWithValueFilter 1`] = `
<div
  className="tw-flex tw-flex-col"
>
  <Label>
    Date of First Contact
  </Label>
  <div
    className="tw-flex tw-gap-4"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <DateInput
        inputClassName={false}
        label="From"
        onChange={[Function]}
        selectsStart={true}
      />
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <DateInput
        inputClassName={false}
        label="To"
        onChange={[Function]}
        selectsEnd={true}
      />
    </div>
  </div>
</div>
`;
