/* eslint-disable complexity */
import { DateInput, Label } from "@q-centrix/q-components-react";
import { formatDateifLocalStorage } from "utils/formatDateifLocalStorage";

const DateRangeWithValueFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const { name, dateRange, select } = filterConfig;

  const startDateValue = formatDateifLocalStorage(
    filterValues[name]?.dateRange?.startDate
  );
  const endDateValue = formatDateifLocalStorage(
    filterValues[name]?.dateRange?.endDate
  );

  const isStartDateActive = Boolean(filterValues[name]?.dateRange?.startDate);
  const isEndDateActive = Boolean(filterValues[name]?.dateRange?.endDate);

  return (
    <div className="tw-flex tw-flex-col">
      <Label>{select.label}</Label>
      <div key={dateRange.name} className="tw-flex tw-gap-4">
        <div className="tw-flex tw-flex-col tw-gap-2">
          <DateInput
            inputClassName={
              isStartDateActive && shouldHighligthFilters && "tw-bg-yellow-400"
            }
            label={dateRange.label[0]}
            value={startDateValue}
            onChange={newDate =>
              handleFilterValueChange(name, {
                ...filterValues[name],
                select,
                dateRange: {
                  ...filterValues[name]?.dateRange,
                  startDate: newDate
                }
              })
            }
            selectsStart
            endDate={endDateValue}
            maxDate={endDateValue}
          />
        </div>
        <div className="tw-flex tw-flex-col tw-gap-2">
          <DateInput
            inputClassName={
              isEndDateActive && shouldHighligthFilters && "tw-bg-yellow-400"
            }
            label={dateRange.label[1]}
            value={endDateValue}
            onChange={newDate =>
              handleFilterValueChange(name, {
                ...filterValues[name],
                select,
                dateRange: {
                  ...filterValues[name].dateRange,
                  endDate: newDate
                }
              })
            }
            startDate={startDateValue}
            minDate={startDateValue}
            selectsEnd
          />
        </div>
      </div>
    </div>
  );
};

export default DateRangeWithValueFilter;
