/* eslint-disable complexity */
import { InputDropdown, DateInput } from "@q-centrix/q-components-react";
import { always, ifElse, pipe, prop } from "ramda";
import { formatDateifLocalStorage } from "utils/formatDateifLocalStorage";
import { isNullOrEmpty, isNotNullOrEmpty } from "utils/fp";

const SelectDateRangeFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const { name, select, dateRange } = filterConfig;

  const startDateValue = formatDateifLocalStorage(
    filterValues[name]?.dateRange?.startDate
  );
  const endDateValue = formatDateifLocalStorage(
    filterValues[name]?.dateRange?.endDate
  );

  const inputValue = ifElse(
    pipe(prop("select"), isNullOrEmpty),
    always(null),
    prop("select")
  )(filterValues[name]);

  const isStartDateActive = Boolean(filterValues[name]?.dateRange?.startDate);
  const isEndDateActive = Boolean(filterValues[name]?.dateRange?.endDate);
  const isSelectActive = isNotNullOrEmpty(filterValues[name]?.select);

  return (
    <div key={dateRange.name} className="tw-flex tw-flex-col tw-gap-4">
      <div className="tw-flex tw-gap-4">
        <div className="tw-flex tw-flex-col tw-gap-2">
          <DateInput
            inputClassName={
              isStartDateActive && shouldHighligthFilters && "tw-bg-yellow-400"
            }
            label={dateRange.label[0]}
            value={startDateValue}
            onChange={newDate =>
              handleFilterValueChange(name, {
                ...filterValues[name],
                dateRange: {
                  ...filterValues[name].dateRange,
                  startDate: newDate
                }
              })
            }
            selectsStart
            endDate={endDateValue}
            maxDate={endDateValue}
          />
        </div>
        <div className="tw-flex tw-flex-col tw-gap-2">
          <DateInput
            inputClassName={
              isEndDateActive && shouldHighligthFilters && "tw-bg-yellow-400"
            }
            label={dateRange.label[1]}
            value={endDateValue}
            onChange={newDate =>
              handleFilterValueChange(name, {
                ...filterValues[name],
                dateRange: {
                  ...filterValues[name].dateRange,
                  endDate: newDate
                }
              })
            }
            startDate={startDateValue}
            minDate={startDateValue}
            selectsEnd
          />
        </div>
      </div>
      <div key={select.name} className="tw-flex tw-flex-col tw-gap-2">
        <InputDropdown
          inputClassName={
            isSelectActive && shouldHighligthFilters && "!tw-bg-yellow-400"
          }
          label={select.label}
          name={select.name}
          placeholder={`Select ${select.placeholder}`}
          iconClass="fa-solid fa-chevron-down"
          options={select.values}
          value={inputValue}
          onChange={value =>
            handleFilterValueChange(name, {
              ...filterValues[name],
              select: value
            })
          }
        />
      </div>
    </div>
  );
};

export default SelectDateRangeFilter;
