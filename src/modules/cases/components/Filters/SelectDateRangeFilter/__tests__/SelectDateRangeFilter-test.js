import { create } from "react-test-renderer";
import SelectDateRangeFilter from "modules/cases/components/Filters/SelectDateRangeFilter";
import tabs from "modules/cases/tabs";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown",
  DateInput: "DateInput"
}));

describe("SelectDateRangeFilter", () => {
  const [, { filters }] = tabs;
  const selectDateRangeFilter = filters.find(
    filter => filter.type === "SelectDateRangeFilter"
  );

  function render() {
    return create(
      <SelectDateRangeFilter
        filterConfig={selectDateRangeFilter}
        filterValues={{
          [selectDateRangeFilter.name]: {}
        }}
      />
    );
  }

  test("renders SelectDateRangeFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
