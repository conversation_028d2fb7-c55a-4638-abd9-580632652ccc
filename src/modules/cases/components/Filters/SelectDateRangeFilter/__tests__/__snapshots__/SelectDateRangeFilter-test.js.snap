// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectDateRangeFilter renders SelectDateRangeFilter 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-4"
>
  <div
    className="tw-flex tw-gap-4"
  >
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <DateInput
        inputClassName={false}
        label="From"
        onChange={[Function]}
        selectsStart={true}
      />
    </div>
    <div
      className="tw-flex tw-flex-col tw-gap-2"
    >
      <DateInput
        inputClassName={false}
        label="To"
        onChange={[Function]}
        selectsEnd={true}
      />
    </div>
  </div>
  <div
    className="tw-flex tw-flex-col tw-gap-2"
  >
    <InputDropdown
      iconClass="fa-solid fa-chevron-down"
      inputClassName={false}
      label="Filter Date By"
      name="dateBy"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Date of first contact",
            "timestampModifier": "YYYYMMDD",
            "value": "date_of_first_contact",
          },
          Object {
            "label": "Arrival date",
            "value": "arrived_at",
          },
          Object {
            "label": "Admission date",
            "value": "admitted_at",
          },
          Object {
            "label": "Discharge date",
            "value": "hospital_discharged_at",
          },
          Object {
            "label": "Procedure date",
            "value": "surgery_date",
          },
          Object {
            "label": "Case creation date",
            "value": "created_at",
          },
          Object {
            "label": "Last updated date",
            "value": "updated_at",
          },
          Object {
            "label": "Deadline",
            "timestampModifier": "YYYYMMDD",
            "value": "client_due_date",
          },
        ]
      }
      placeholder="Select Filter Date By"
      value={null}
    />
  </div>
</div>
`;
