// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RadioGroupFilter renders RadioGroupFilter 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-0"
  role="radiogroup"
>
  <label
    className="tw-text-sm tw-font-semibold tw-text-black-70"
    htmlFor="locked"
  >
    Locked
  </label>
  <div
    className="tw-ml-2.5 tw-w-fit tw-p-2.5"
  >
    <RadioButton
      checked={false}
      label="Yes"
      name="yes"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-ml-2.5 tw-w-fit tw-p-2.5"
  >
    <RadioButton
      checked={false}
      label="No"
      name="no"
      onChange={[Function]}
    />
  </div>
</div>
`;
