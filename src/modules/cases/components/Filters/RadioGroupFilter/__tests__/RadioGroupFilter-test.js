import { create } from "react-test-renderer";
import RadioGroupFilter from "modules/cases/components/Filters/RadioGroupFilter";

jest.mock("@q-centrix/q-components-react", () => ({
  RadioButton: "RadioButton"
}));

describe("RadioGroupFilter", () => {
  const radioGroupFilter = {
    type: "RadioGroupFilter",
    name: "locked",
    label: "Locked",
    values: [
      { value: "yes", label: "Yes" },
      { value: "no", label: "No" }
    ]
  };

  function render() {
    return create(
      <RadioGroupFilter
        filterConfig={radioGroupFilter}
        filterValues={{
          [radioGroupFilter.name]: null
        }}
      />
    );
  }

  test("renders RadioGroupFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
