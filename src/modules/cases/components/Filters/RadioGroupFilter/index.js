/* eslint-disable no-shadow */
import { RadioButton } from "@q-centrix/q-components-react";
import classNames from "classnames";

const SelectFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const { name, label, values, subLabel } = filterConfig;

  const isActive = Boolean(filterValues[name]);

  const radioContainerClass = value =>
    classNames("tw-ml-2.5 tw-w-fit tw-p-2.5", {
      " tw-rounded-[5px] tw-bg-yellow-400 ":
        isActive && shouldHighligthFilters && value === filterValues[name]
    });

  return (
    <div role="radiogroup" key={name} className="tw-flex tw-flex-col tw-gap-0">
      <label
        htmlFor={name}
        className="tw-text-sm tw-font-semibold tw-text-black-70"
      >
        {label}
      </label>
      {subLabel && <p className="tw-text-sm tw-italic">{subLabel}</p>}

      {values.map(({ value, label }) => (
        <div key={value} className={radioContainerClass(value)}>
          <RadioButton
            name={value}
            label={label}
            checked={filterValues[name] === value}
            onChange={() => {
              handleFilterValueChange(name, value);
            }}
          />
        </div>
      ))}
    </div>
  );
};

export default SelectFilter;
