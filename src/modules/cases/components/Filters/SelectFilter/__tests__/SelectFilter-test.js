import { create } from "react-test-renderer";
import SelectFilter from "modules/cases/components/Filters/SelectFilter";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

describe("SelectFilter", () => {
  const selectFilter = {
    type: "SelectFilter",
    name: "facility",
    label: "Facility",
    placeholder: "Facility",
    values: [
      { value: 1, label: "Test1" },
      { value: 2, label: "Test2" },
      { value: 3, label: "Test3" }
    ]
  };

  function render() {
    return create(
      <SelectFilter
        filterConfig={selectFilter}
        filterValues={{
          [selectFilter.name]: {}
        }}
      />
    );
  }

  test("renders SelectFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
