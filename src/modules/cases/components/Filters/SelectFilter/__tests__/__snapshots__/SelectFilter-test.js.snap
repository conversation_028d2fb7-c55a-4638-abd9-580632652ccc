// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectFilter renders SelectFilter 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-2"
>
  <InputDropdown
    clearable={true}
    iconClass="fa-solid fa-chevron-down"
    inputClassName={false}
    isLoading={false}
    label="Facility"
    name="facility"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Test1",
          "value": 1,
        },
        Object {
          "label": "Test2",
          "value": 2,
        },
        Object {
          "label": "Test3",
          "value": 3,
        },
      ]
    }
    placeholder="Select Facility"
    value={null}
  />
</div>
`;
