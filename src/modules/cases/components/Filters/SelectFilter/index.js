import { InputDropdown } from "@q-centrix/q-components-react";
import { always, ifElse, pipe, prop } from "ramda";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";

const SelectFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const {
    name,
    label,
    placeholder,
    values,
    isMulti,
    loading = false
  } = filterConfig;

  const inputValue = ifElse(
    pipe(prop(name), isNullOrEmpty),
    always(null),
    prop(name)
  )(filterValues);
  const isActive = isNotNullOrEmpty(filterValues[name]);

  return (
    <div key={name} className="tw-flex tw-flex-col tw-gap-2">
      <InputDropdown
        inputClassName={
          isActive && shouldHighligthFilters && "tw-bg-yellow-400"
        }
        label={label}
        name={name}
        placeholder={`Select ${placeholder}`}
        iconClass="fa-solid fa-chevron-down"
        options={values}
        value={inputValue}
        onChange={value => handleFilterValueChange(name, value)}
        isMulti={isMulti}
        isLoading={loading}
        clearable
      />
    </div>
  );
};

export default SelectFilter;
