import { create } from "react-test-renderer";
import tabs from "modules/cases/tabs";
import SelectWithQueryAndPaginationFilter from "..";
import { decoratedApollo } from "utils/tests/decorated";
import mocks from "modules/cases/graphql/mocks";

jest.mock("shared/components/Select", () => "Select");

describe("SelectWithQueryAndPaginationFilter", () => {
  const [, { filters }] = tabs;
  const selectWithQueryAndPaginationFilter = filters.find(
    filter => filter.type === "SelectWithQueryAndPaginationFilter"
  );

  function render() {
    return create(
      decoratedApollo({
        component: SelectWithQueryAndPaginationFilter,
        props: {
          filterConfig: selectWithQueryAndPaginationFilter,
          filterValues: {
            [selectWithQueryAndPaginationFilter?.name || "test"]: {}
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
