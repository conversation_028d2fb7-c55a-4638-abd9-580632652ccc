// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectWithQueryAndPaginationFilter renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-col tw-gap-2"
>
  <Select
    fields={
      Array [
        "id",
        "name",
      ]
    }
    id="facility"
    initialValue={null}
    inputClassName={false}
    isMulti={true}
    isPageable={true}
    isSearchable={true}
    label="Facility"
    name="facility"
    onChange={[Function]}
    path={
      Array [
        "currentUserAvailableFacilities",
        "userAvailableFacilities",
      ]
    }
    placeholder="Select Facility"
    query={
      Object {
        "definitions": Array [
          Object {
            "directives": Array [],
            "kind": "OperationDefinition",
            "name": undefined,
            "operation": "query",
            "selectionSet": Object {
              "kind": "SelectionSet",
              "selections": Array [
                Object {
                  "alias": undefined,
                  "arguments": Array [
                    Object {
                      "kind": "Argument",
                      "name": Object {
                        "kind": "Name",
                        "value": "perPage",
                      },
                      "value": Object {
                        "kind": "IntValue",
                        "value": "25",
                      },
                    },
                    Object {
                      "kind": "Argument",
                      "name": Object {
                        "kind": "Name",
                        "value": "page",
                      },
                      "value": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "page",
                        },
                      },
                    },
                    Object {
                      "kind": "Argument",
                      "name": Object {
                        "kind": "Name",
                        "value": "search",
                      },
                      "value": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "search",
                        },
                      },
                    },
                  ],
                  "directives": Array [],
                  "kind": "Field",
                  "name": Object {
                    "kind": "Name",
                    "value": "currentUserAvailableFacilities",
                  },
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "userAvailableFacilities",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "id",
                              },
                              "selectionSet": undefined,
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "name",
                              },
                              "selectionSet": undefined,
                            },
                          ],
                        },
                      },
                      Object {
                        "alias": undefined,
                        "arguments": Array [],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "count",
                        },
                        "selectionSet": undefined,
                      },
                    ],
                  },
                },
              ],
            },
            "variableDefinitions": Array [
              Object {
                "defaultValue": undefined,
                "directives": Array [],
                "kind": "VariableDefinition",
                "type": Object {
                  "kind": "NamedType",
                  "name": Object {
                    "kind": "Name",
                    "value": "Int",
                  },
                },
                "variable": Object {
                  "kind": "Variable",
                  "name": Object {
                    "kind": "Name",
                    "value": "page",
                  },
                },
              },
              Object {
                "defaultValue": undefined,
                "directives": Array [],
                "kind": "VariableDefinition",
                "type": Object {
                  "kind": "NamedType",
                  "name": Object {
                    "kind": "Name",
                    "value": "String",
                  },
                },
                "variable": Object {
                  "kind": "Variable",
                  "name": Object {
                    "kind": "Name",
                    "value": "search",
                  },
                },
              },
            ],
          },
        ],
        "kind": "Document",
        "loc": Object {
          "end": 160,
          "start": 0,
        },
      }
    }
    variables={
      Object {
        "perPage": 25,
      }
    }
  />
</div>
`;
