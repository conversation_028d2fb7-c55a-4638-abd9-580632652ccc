import { DateInput } from "@q-centrix/q-components-react";
import { formatDateifLocalStorage } from "utils/formatDateifLocalStorage";

const DateRangeFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange
}) => {
  const { name, label } = filterConfig;
  const startDateValue = formatDateifLocalStorage(
    filterValues[name]?.startDate
  );
  const endDateValue = formatDateifLocalStorage(filterValues[name]?.endDate);

  return (
    <div key={name} className="tw-flex tw-gap-4">
      <div className="tw-flex tw-flex-col tw-gap-2">
        <DateInput
          label={label[0]}
          value={startDateValue}
          onChange={newDate =>
            handleFilterValueChange(name, {
              ...filterValues[name],
              startDate: newDate
            })
          }
          selectsStart
          endDate={filterValues[name]?.endDate}
          maxDate={filterValues[name]?.endDate}
        />
      </div>
      <div className="tw-flex tw-flex-col tw-gap-2">
        <DateInput
          label={label[1]}
          value={endDateValue}
          onChange={newDate =>
            handleFilterValueChange(name, {
              ...filterValues[name],
              endDate: newDate
            })
          }
          startDate={filterValues[name]?.startDate}
          minDate={filterValues[name]?.startDate}
          selectsEnd
        />
      </div>
    </div>
  );
};

export default DateRangeFilter;
