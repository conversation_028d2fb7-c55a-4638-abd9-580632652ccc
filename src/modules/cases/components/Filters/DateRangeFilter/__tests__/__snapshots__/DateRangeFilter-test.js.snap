// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRangeFilter renders DateRangeFilter 1`] = `
<div
  className="tw-flex tw-gap-4"
>
  <div
    className="tw-flex tw-flex-col tw-gap-2"
  >
    <DateInput
      label="Start Date"
      onChange={[Function]}
      selectsStart={true}
    />
  </div>
  <div
    className="tw-flex tw-flex-col tw-gap-2"
  >
    <DateInput
      label="End Date"
      onChange={[Function]}
      selectsEnd={true}
    />
  </div>
</div>
`;
