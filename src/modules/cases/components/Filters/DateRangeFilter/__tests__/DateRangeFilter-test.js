import { create } from "react-test-renderer";
import DateRangeFilter from "modules/cases/components/Filters/DateRangeFilter";
import tabs from "modules/cases/tabs";

jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));

describe("DateRangeFilter", () => {
  const [{ filters }] = tabs;
  const dateRangeFilter = filters.find(
    filter => filter.type === "DateRangeFilter"
  );

  function render() {
    return create(
      <DateRangeFilter
        filterConfig={dateRangeFilter}
        filterValues={{
          [dateRangeFilter.name]: {}
        }}
      />
    );
  }

  test("renders DateRangeFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
