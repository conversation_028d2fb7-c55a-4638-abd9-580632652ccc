import { create } from "react-test-renderer";
import OpenFilter from "modules/cases/components/Filters/OpenFilter";
import tabs from "modules/cases/tabs";

jest.mock("@q-centrix/q-components-react", () => ({
  Input: "Input"
}));

describe("OpenFilter", () => {
  const [{ filters }] = tabs;
  const openFilter = filters.find(filter => filter.type === "OpenFilter");

  function render() {
    return create(
      <OpenFilter
        filterConfig={openFilter}
        filterValues={{
          [openFilter.name]: {}
        }}
      />
    );
  }

  test("renders OpenFilter", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
