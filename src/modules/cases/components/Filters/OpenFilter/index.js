import { Input } from "@q-centrix/q-components-react";

const OpenFilter = ({
  filterConfig,
  filterValues,
  handleFilterValueChange,
  shouldHighligthFilters
}) => {
  const { name, label, placeholder } = filterConfig;
  const isActive = <PERSON>olean(filterValues[name]);

  return (
    <div key={name} className="tw-flex tw-flex-col tw-gap-2">
      <Input
        inputClassName={
          isActive && shouldHighligthFilters && "tw-bg-yellow-400"
        }
        label={label}
        id={name}
        name={name}
        value={filterValues[name]}
        onChange={e => handleFilterValueChange(name, e.target.value)}
        placeholder={placeholder}
      />
    </div>
  );
};

export default OpenFilter;
