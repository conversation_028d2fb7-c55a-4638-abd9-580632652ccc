/* eslint-disable max-statements */
import { useState, useMemo, useCallback } from "react";
import { useSelector } from "react-redux";
import casesSelectors from "modules/cases/redux/selectors";
import {
  __,
  always,
  applySpec,
  ascend,
  assoc,
  both,
  complement,
  cond,
  defaultTo,
  descend,
  identity,
  ifElse,
  includes,
  is,
  isNil,
  map,
  not,
  path,
  pathOr,
  pipe,
  prop,
  propEq,
  reduce,
  sortWith,
  toLower,
  when
} from "ramda";
import { useLazyQuery } from "@apollo/client";
import { CASES_SEARCH } from "../../graphql/query";
import { DATE_TYPE_COLUMNS } from "modules/cases/tabs";
import searchColumns from "./searchColumns";
import { formatISODateString } from "utils/formatISODateString";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";
import useLocalStorage from "shared/hooks/useLocalStorage";
import { formatISOToDate } from "utils/formatISOToDate";

const ORDER = {
  ASC: "asc",
  DESC: "desc"
};
const convertIfisDate = pipe(
  prop(__),
  when(is(String), dateString => new Date(dateString)),
  defaultTo("")
);
const convertToLowerCase = pipe(
  prop(__),
  when(is(String), toLower),
  defaultTo("")
);

// eslint-disable-next-line complexity
export const useComponentLogic = () => {
  const [shouldShowEditColumnBar, setShouldShowEditColumnBar] = useState(false);
  const [shouldShowCaseReassignment, setShouldShowCaseReassignment] =
    useState(false);
  const selectedRows = useSelector(casesSelectors.getSelectedRows);
  const selectedTab = useSelector(casesSelectors.getSelectedTab);
  const isHoursTab = selectedTab?.type === "hours";
  const [deleteModalIsOpen, setDeleteModalIsOpen] = useState(false);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useLocalStorage(
    "isFilterPanelOpen",
    true
  );
  const [searchInput, setSearchInput] = useState("");
  const [areButtonsHidden, setAreButtonsHidden] = useState(false);
  const [shouldSearchExactMatch, setShouldSearchExactMatch] = useState(false);
  const [searchSortSettings, setSearchSortSettings] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(100);
  const [searchResults, setSearchResults] = useState([]);
  const [completed, setCompleted] = useState(false);

  const clearSearchInput = useCallback(() => setSearchInput(""), []);

  const toggleCaseReassignment = () => {
    setShouldShowCaseReassignment(not);
    setIsFilterPanelOpen(not);
  };
  const toggleDeleteModal = () => setDeleteModalIsOpen(not);
  // eslint-disable-next-line complexity
  const toggleFilterPanelDisplay = () => {
    if (
      (shouldShowEditColumnBar || shouldShowCaseReassignment) &&
      !isFilterPanelOpen
    ) {
      setShouldShowEditColumnBar(false);
      setShouldShowCaseReassignment(false);
      setIsFilterPanelOpen(true);
    } else {
      setIsFilterPanelOpen(not);
    }
  };

  const rowsHaveBeenSelected = selectedRows.length > 0;
  const shouldShowExportCsvAndEditCols = both(
    identity,
    complement(propEq("key", "case_finding"))
  )(selectedTab);

  const shouldShowDeleteAndReassignButtons =
    rowsHaveBeenSelected && !shouldShowCaseReassignment;

  const shouldNotShowButtons =
    rowsHaveBeenSelected && shouldShowCaseReassignment;

  const sortOrder = searchSortSettings.order === ORDER.ASC ? ascend : descend;

  const [searchCases, { loading, data: searchData }] = useLazyQuery(
    CASES_SEARCH,
    {
      onCompleted: () => {
        setSearchResults(pathOr([], ["casesSearch", "cases"])(searchData));
        setCompleted(true);
      }
    }
  );

  const rows = useMemo(
    () =>
      map(
        row =>
          reduce(
            (acc, col) =>
              pipe(
                prop("path"),
                path(__, row),
                when(
                  both(always(col.isDate), complement(isNil)),
                  ifElse(
                    always(col.isDate?.showTime),
                    formatISODateString(__, "MM/dd/yyyy, h:mm aaa"),
                    formatISOToDate(__, "MM/dd/yyyy")
                  )
                ),
                assoc(col.name, __, acc)
              )(col),
            {
              id: row.id
            },
            searchColumns
          ),
        searchResults
      ),
    [searchResults]
  );

  const handleChangePage = useCallback(newPage => {
    setCurrentPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback(
    value => {
      setRowsPerPage(value);
      handleChangePage(1);
    },
    [handleChangePage]
  );

  const handleCaseResearch = useCallback(
    e => {
      if (e.key === "Enter" && isNotNullOrEmpty(searchInput)) {
        setIsFilterPanelOpen(false);
        setAreButtonsHidden(true);
        searchCases({
          variables: {
            search: searchInput,
            exactMatch: shouldSearchExactMatch,
            page: currentPage,
            perPage: rowsPerPage
          }
        });
      }
    },
    [searchCases, searchInput, shouldSearchExactMatch, currentPage, rowsPerPage]
  );

  const handleSearchSort = useCallback(orderBy => {
    setSearchSortSettings(prevSortSettings =>
      pipe(
        applySpec({
          orderBy: ifElse(
            both(propEq("order", ORDER.DESC), propEq("orderBy", orderBy)),
            always(null),
            always(orderBy)
          ),
          order: cond([
            [complement(propEq("orderBy", orderBy)), always(ORDER.ASC)],
            [pipe(prop("order"), isNullOrEmpty), always(ORDER.ASC)],
            [propEq("order", ORDER.ASC), always(ORDER.DESC)],
            [propEq("order", ORDER.DESC), always(null)]
          ])
        })
      )(prevSortSettings)
    );
  }, []);

  const sortedRows = useMemo(
    () =>
      sortWith([
        sortOrder(
          ifElse(
            includes(__, DATE_TYPE_COLUMNS),
            convertIfisDate,
            convertToLowerCase
          )(searchSortSettings.orderBy)
        )
      ])(rows),
    [rows, searchSortSettings]
  );

  const handleExactMatchChange = useCallback(() => {
    setShouldSearchExactMatch(not);

    searchCases({
      variables: {
        search: searchInput,
        exactMatch: !shouldSearchExactMatch,
        page: currentPage,
        perPage: rowsPerPage
      }
    });
  }, [
    searchCases,
    searchInput,
    shouldSearchExactMatch,
    currentPage,
    rowsPerPage
  ]);

  const handleExitSearchMode = useCallback(() => {
    clearSearchInput();
    setSearchResults([]);
    setIsFilterPanelOpen(true);
    setAreButtonsHidden(false);
    setShouldSearchExactMatch(false);
    setCompleted(false);
  }, [clearSearchInput]);

  const handleSearchInputChange = useCallback(
    e => {
      e.preventDefault();
      setSearchInput(e.target.value);

      if (!e.target.value) {
        handleExitSearchMode();
      }
    },
    [handleExitSearchMode]
  );

  return {
    shouldShowEditColumnBar,
    shouldShowCaseReassignment,
    shouldShowExportCsvAndEditCols,
    shouldShowDeleteAndReassignButtons,
    shouldNotShowButtons,
    toggleCaseReassignment,
    rowsHaveBeenSelected,
    deleteModalIsOpen,
    toggleDeleteModal,
    isFilterPanelOpen,
    setIsFilterPanelOpen,
    toggleFilterPanelDisplay,
    handleCaseResearch,
    handleSearchInputChange,
    handleExitSearchMode,
    searchInput,
    areButtonsHidden,
    searchResultCases: sortedRows,
    shouldSearchExactMatch,
    handleExactMatchChange,
    searchSortSettings,
    handleChangePage,
    currentPage,
    handleChangeRowsPerPage,
    rowsPerPage,
    handleSearchSort,
    loading,
    searchColumns,
    completed,
    isHoursTab
  };
};
