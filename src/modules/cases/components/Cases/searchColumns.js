const searchColumns = [
  {
    isDefault: true,
    key: "facility",
    label: "Facility",
    name: "facility",
    order: 1,
    withSort: true,
    path: ["facility", "name"]
  },

  {
    isDefault: true,
    key: "caseType",
    label: "Case Type",
    name: "caseType",
    order: 2,
    withSort: true,
    path: ["caseType", "name"]
  },
  {
    isDefault: true,
    key: "visitNumber",
    label: "Visit Number",
    name: "visitNumber",
    order: 3,
    withSort: true,
    path: ["visit", "number"]
  },
  {
    isDefault: true,
    key: "mrn",
    label: "MRN",
    name: "mrn",
    order: 4,
    withSort: true,
    path: ["patient", "mrn"]
  },
  {
    isDefault: true,
    key: "assignee",
    label: "Assignee",
    name: "assignee",
    order: 5,
    withSort: true,
    path: ["assignee", "fullName"]
  },
  {
    isDefault: true,
    key: "status",
    label: "Status",
    name: "status",
    order: 6,
    withSort: true,
    path: ["status"]
  },
  {
    isDefault: true,
    key: "admittedAt",
    label: "Admission Date",
    name: "admittedAt",
    order: 7,
    withSort: true,
    path: ["visit", "admittedAt"],
    isDate: { showTime: false }
  },
  {
    isDefault: true,
    key: "hospitalDischargedAt",
    label: "Discharge Date",
    name: "hospitalDischargedAt",
    order: 8,
    withSort: true,
    path: ["visit", "hospitalDischargedAt"],
    isDate: { showTime: false }
  },
  {
    isDefault: true,
    key: "clientDueDate",
    label: "Deadline",
    name: "clientDueDate",
    order: 9,
    withSort: true,
    path: ["clientDueDate"],
    isDate: { showTime: false }
  }
];

export default searchColumns;
