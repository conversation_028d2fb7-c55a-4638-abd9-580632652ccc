import { ToggleFilterPanelButton } from "./ToggleFilterPanelButton";
/* eslint-disable react/sort-prop-types */
/* eslint-disable complexity */
import PropTypes from "prop-types";
import {
  <PERSON><PERSON>,
  Spinner,
  SearchBar,
  Checkbox
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import { Link } from "react-router-dom";
import DeleteButton from "./DeleteButton";

const SecondaryToolbarContent = ({
  shouldShowDeleteAndReassignButtons,
  shouldNotShowButtons,
  shouldShowExportCsvAndEditCols,
  onReassignCases,
  onDelete,
  onUploadCases,
  isFilterPanelOpen,
  setIsFilterPanelOpen,
  toggleFilterPanelDisplay,
  onSearchInputChange,
  onExitSearchMode,
  onCaseSearch,
  searchInput,
  areButtonsHidden,
  onExactMatchChange,
  shouldSearchExactMatch,
  isHoursTab
}) => {
  const {
    handleExportCaseList,
    loading,
    disabledExportButton,
    getCreateNewLink,
    isCasesTab
  } = useComponentLogic({ setIsFilterPanelOpen });

  if (shouldShowDeleteAndReassignButtons) {
    return (
      <>
        <DeleteButton onDelete={onDelete} />
        {!isHoursTab && (
          <Button
            outline
            bg="warning"
            onClick={onReassignCases}
            customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[35px]"
          >
            <i className="fa-solid fa-shuffle" />
            Reassign
          </Button>
        )}
      </>
    );
  }

  if (shouldNotShowButtons) {
    return <></>;
  }

  return (
    <>
      {areButtonsHidden && (
        <Checkbox
          name="exact-match"
          checked={shouldSearchExactMatch}
          onChange={onExactMatchChange}
          label="Only show exact matches"
        />
      )}
      {!areButtonsHidden && (
        <>
          <>
            <ToggleFilterPanelButton
              toggleFilterPanelDisplay={toggleFilterPanelDisplay}
              isFilterPanelOpen={isFilterPanelOpen}
            />

            {isCasesTab && (
              <Button
                outline
                bg="main"
                customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
                onClick={onUploadCases}
              >
                <i className="fa-regular fa-arrow-up-from-line" />
                Upload Cases
              </Button>
            )}
          </>

          <>
            {shouldShowExportCsvAndEditCols && (
              <Button
                outline
                bg="main"
                customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
                onClick={handleExportCaseList}
                disabled={disabledExportButton}
              >
                {loading ? (
                  <Spinner size={18} />
                ) : (
                  <i className="fa-solid fa-file-csv" />
                )}
                Export CSV
              </Button>
            )}
          </>
        </>
      )}
      <div className="tw-ml-auto tw-flex tw-flex-row">
        <SearchBar
          placeholder="Search cases by visit number or MRN"
          searchInput={searchInput}
          onKeyDown={onCaseSearch}
          handleChange={onSearchInputChange}
          handleClear={onExitSearchMode}
          iconClass="tw-absolute tw-inset-y-2 tw-right-4"
        />
        {!areButtonsHidden && (
          <Link to={getCreateNewLink}>
            <Button
              bg="main"
              customStyle="tw-flex tw-ml-2.5 tw-gap-2.5 tw-h-[35px]"
            >
              <i className="fa-regular fa-plus" />
              Create New
            </Button>
          </Link>
        )}
      </div>
    </>
  );
};

SecondaryToolbarContent.propTypes = {
  shouldShowDeleteAndReassignButtons: PropTypes.bool.isRequired,
  shouldNotShowButtons: PropTypes.bool.isRequired,
  shouldShowExportCsvAndEditCols: PropTypes.func.isRequired,
  onReassignCases: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onUploadCases: PropTypes.func.isRequired
};

export default SecondaryToolbarContent;
