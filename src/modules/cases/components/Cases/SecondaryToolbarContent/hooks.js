import { useMutation } from "@apollo/client";
import { useSelector } from "react-redux";
import {
  EXPORT_CASE_LIST,
  EXPORT_HOURS_LIST,
  EXPORT_ACTIVITIES_LIST
} from "modules/cases/graphql/mutation";
import casesSelectors from "modules/cases/redux/selectors";
import { applySpec, ifElse, map, prop, propOr } from "ramda";
import { convertFiltersToArray } from "modules/cases/utils/convertFiltersToArray";
import { useToast } from "@q-centrix/q-components-react";
import { useEffect, useMemo, useState } from "react";

const exportConfig = {
  cases: {
    exportMutation: EXPORT_CASE_LIST,
    exportResults: "exportCaseList"
  },
  hours: {
    exportMutation: EXPORT_HOURS_LIST,
    exportResults: "exportHoursList"
  },
  activities: {
    exportMutation: EXPORT_ACTIVITIES_LIST,
    exportResults: "exportActivities"
  }
};

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const { toast } = useToast();
  const selectedFilters = useSelector(casesSelectors.getSelectedFilters);
  const selectedColumns = useSelector(casesSelectors.getSelectedColumns);
  const selectedTab = useSelector(casesSelectors.getSelectedTab);
  const getTabSettings = useMemo(
    () => propOr(exportConfig.cases, selectedTab?.type)(exportConfig),
    [selectedTab]
  );
  const getCreateNewLink = `/${selectedTab?.type}/new`;
  const [exportMutation, { loading }] = useMutation(
    getTabSettings?.exportMutation
  );

  const [disabledExportButton, setDisabledExportButton] = useState(true);

  useEffect(() => {
    setDisabledExportButton(false);
  }, [selectedFilters, selectedColumns, selectedTab]);
  const isCasesTab = selectedTab?.type === "cases";
  const isHoursTab = selectedTab?.type === "hours";
  const handleExportCaseList = () => {
    const selectedColumnsProperties = map(
      applySpec({
        key: ifElse(prop("exportKey"), prop("exportKey"), prop("key")),
        label: prop("label"),
        path: ifElse(prop("exportPath"), prop("exportPath"), prop("path"))
      })
    )(selectedColumns);

    const filters = convertFiltersToArray(
      selectedTab?.filters,
      selectedFilters[selectedTab?.key]
    );

    const loadingToast = toast({
      variant: "loading",
      description: "Export being built. Check your email shortly."
    });

    exportMutation({
      variables: {
        ...(!isHoursTab && { profile: selectedTab?.key }),
        filters,
        fields: selectedColumnsProperties
      },
      onCompleted: data => {
        const exportResponse = prop(getTabSettings?.exportResults)(data);

        if (exportResponse?.response) {
          loadingToast.update({
            description: "Export built successfully!",
            variant: "success"
          });
          setDisabledExportButton(true);
        } else {
          loadingToast.update({
            description: "Export failed!",
            variant: "error"
          });
        }
      },
      onError: ({ graphQLErrors }) => {
        loadingToast.update({
          description: "Export failed!",
          variant: "error"
        });

        if (graphQLErrors.length) {
          graphQLErrors.forEach(error => {
            toast({
              variant: "error",
              description: error.message
            });
          });
        }
      }
    });
  };

  return {
    handleExportCaseList,
    disabledExportButton,
    loading,
    getCreateNewLink,
    isCasesTab
  };
};
