// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ToggleFilterPanelButton it renders component correctly when filter panel is closed 1`] = `
<Button
  bg="main"
  customStyle="tw-flex tw-gap-2.5"
  onClick={[MockFunction]}
  outline={false}
>
  <i
    className="fa-sharp fa-solid fa-eye"
  />
  Show Left Panel
</Button>
`;

exports[`ToggleFilterPanelButton it renders component correctly when filter panel is open 1`] = `
<Button
  bg="main"
  customStyle="tw-flex tw-gap-2.5"
  onClick={[MockFunction]}
  outline={true}
>
  <i
    className="fa-sharp fa-solid fa-eye-slash"
  />
  Hide Left Panel
</Button>
`;
