import { create } from "react-test-renderer";
import { ToggleFilterPanelButton } from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));

const toggleFilterPanelDisplay = jest.fn();

describe("ToggleFilterPanelButton", () => {
  function render(isFilterPanelOpen) {
    return create(
      <ToggleFilterPanelButton
        toggleFilterPanelDisplay={toggleFilterPanelDisplay}
        isFilterPanelOpen={isFilterPanelOpen}
      />
    );
  }

  test("it renders component correctly when filter panel is open", () => {
    const filterPanelIsOpen = true;
    const component = render(filterPanelIsOpen);

    expect(component).toMatchSnapshot();
  });
  test("it renders component correctly when filter panel is closed", () => {
    const filterPanelIsClosed = false;
    const component = render(filterPanelIsClosed);

    expect(component).toMatchSnapshot();
  });
});
