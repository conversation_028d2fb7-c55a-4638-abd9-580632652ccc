import { Button } from "@q-centrix/q-components-react";

const buttonConfig = {
  true: {
    label: "Hide Left Panel",
    iconClass: "fa-sharp fa-solid fa-eye-slash",
    outline: true
  },
  false: {
    label: "Show Left Panel",
    iconClass: "fa-sharp fa-solid fa-eye",
    outline: false
  }
};

export const ToggleFilterPanelButton = ({
  toggleFilterPanelDisplay,
  isFilterPanelOpen
}) => {
  const { label, iconClass, outline } = buttonConfig[isFilterPanelOpen];

  return (
    <Button
      outline={outline}
      bg="main"
      customStyle="tw-flex tw-gap-2.5"
      onClick={toggleFilterPanelDisplay}
    >
      <i className={iconClass} />
      {label}
    </Button>
  );
};
