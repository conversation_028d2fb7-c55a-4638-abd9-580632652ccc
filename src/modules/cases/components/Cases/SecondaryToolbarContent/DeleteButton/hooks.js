import { useQuery } from "@apollo/client";
import { useMemo } from "react";
import { pathOr } from "ramda";
import { GET_CURRENT_USER_DATA } from "shared/graphql/query";
import { userRoleAccess } from "modules/facility-groups/utils/userRoleAccess";

const ROLES_ALLOWED_FOR_DELETE = ["client_administrator", "case_administrator"];

export const useComponentLogic = () => {
  const { data } = useQuery(GET_CURRENT_USER_DATA);

  const qappsRole = pathOr("", ["currentUser", "qappsRole"])(data);
  const userHasAccessToDelete = useMemo(
    () => userRoleAccess(qappsRole, ROLES_ALLOWED_FOR_DELETE),
    [qappsRole]
  );

  return {
    userHasAccessToDelete
  };
};
