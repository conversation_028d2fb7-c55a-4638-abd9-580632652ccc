import { GET_CURRENT_USER_DATA } from "shared/graphql/query";

export const permittedDeleteMock = [
  {
    request: {
      query: GET_CURRENT_USER_DATA
    },
    result: {
      data: {
        currentUser: {
          qappsRole: "case_administrator"
        }
      }
    }
  }
];

export const notPermittedDeleteMock = {
  request: {
    query: GET_CURRENT_USER_DATA
  },
  result: {
    data: {
      currentUser: {
        qappsRole: "abstractor"
      }
    }
  }
};
