import { create, act } from "react-test-renderer";
import wait from "waait";
import DeleteButton from "modules/cases/components/Cases/SecondaryToolbarContent/DeleteButton";
import { decoratedApollo } from "utils/tests/decorated";
import { permittedDeleteMock, notPermittedDeleteMock } from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));

const onDelete = jest.fn();

describe("DeleteButton", () => {
  function render(mock) {
    return create(
      decoratedApollo({
        component: DeleteButton,
        props: { onDelete },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mock
      })
    );
  }

  it("renders correctly when user has access to delete", async () => {
    const component = render(permittedDeleteMock);

    await act(() => wait(100));
    expect(component).toMatchSnapshot();
  });

  it("does not render when user does not have access to delete", async () => {
    const component = render([notPermittedDeleteMock]);

    await act(() => wait(100));
    expect(component).toMatchSnapshot();
  });
});
