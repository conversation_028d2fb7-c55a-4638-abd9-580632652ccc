// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DeleteButton does not render when user does not have access to delete 1`] = `null`;

exports[`DeleteButton renders correctly when user has access to delete 1`] = `
<Button
  bg="danger"
  customStyle="tw-flex tw-gap-2.5 tw-items-center"
  onClick={[MockFunction]}
  outline={true}
>
  <i
    className="fa-regular fa-trash"
  />
  Delete
</Button>
`;
