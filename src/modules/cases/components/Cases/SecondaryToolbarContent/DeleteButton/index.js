import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const DeleteButton = ({ onDelete }) => {
  const { userHasAccessToDelete } = useComponentLogic();

  if (!userHasAccessToDelete) return null;

  return (
    <Button
      outline
      bg="danger"
      customStyle="tw-flex tw-gap-2.5 tw-items-center"
      onClick={onDelete}
    >
      <i className="fa-regular fa-trash" />
      Delete
    </Button>
  );
};

export default DeleteButton;
