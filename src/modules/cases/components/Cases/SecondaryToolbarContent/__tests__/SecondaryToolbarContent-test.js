import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import SecondaryToolbarContent from "..";
import mocks from "modules/cases/graphql/mocks";

const onReassignCases = jest.fn();
const onDelete = jest.fn();
const onUploadCases = jest.fn();

jest.mock("../DeleteButton", () => "DeleteButton");
jest.mock("../ToggleFilterPanelButton", () => ({
  ToggleFilterPanelButton: "ToggleFilterPanelButton"
}));

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  SearchBar: "SearchBar",
  // eslint-disable-next-line no-empty-function
  useToast: () => ({ toast: () => {} })
}));

jest.mock("react-router-dom", () => ({
  Link: "Link"
}));

describe("SecondaryToolbarContent", () => {
  function render(props, type = "cases") {
    return create(
      decoratedApollo({
        component: SecondaryToolbarContent,
        props,
        initialValues: {},
        initialAppValues: {
          cases: {
            selectedFilters: {},
            selectedTab: {
              key: "case_finding",
              label: "Case Finding",
              type,
              filters: [],
              columns: []
            },
            selectedColumns: [
              {
                key: "assignee",
                label: "Assignee",
                name: "assignee",
                isDefault: true,
                path: ["assignee", "fullName"]
              }
            ]
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("renders default SecondaryToolbarContent when no rows have been selected", () => {
    const component = render({
      shouldShowDeleteAndReassignButtons: false,
      shouldNotShowButtons: false,
      shouldShowExportCsvAndEditCols: true,
      onReassignCases,
      onDelete,
      onUploadCases
    });

    expect(component).toMatchSnapshot();
  });

  test("renders SecondaryToolbarContent for Case Finding Tab with no Export CSV nor Edit Table Columns buttons when no rows have been selected", () => {
    const component = render({
      shouldShowDeleteAndReassignButtons: false,
      shouldNotShowButtons: false,
      shouldShowExportCsvAndEditCols: false,
      onReassignCases,
      onDelete,
      onUploadCases
    });

    expect(component).toMatchSnapshot();
  });

  test("renders appropriate SecondaryToolbarContent when rows have been selected and case reassignment should show", () => {
    const component = render({
      shouldShowDeleteAndReassignButtons: true,
      shouldNotShowButtons: false,
      shouldShowExportCsvAndEditCols: true,
      onReassignCases,
      onDelete,
      onUploadCases
    });

    expect(component).toMatchSnapshot();
  });

  test("renders an empty SecondaryToolbarContent when rows have been selected", () => {
    const component = render({
      shouldShowDeleteAndReassignButtons: false,
      shouldNotShowButtons: true,
      shouldShowExportCsvAndEditCols: true,
      onReassignCases,
      onDelete,
      onUploadCases
    });

    expect(component).toMatchSnapshot();
  });

  test("renders Create New links to Hours", () => {
    const component = render(
      {
        shouldShowDeleteAndReassignButtons: false,
        shouldNotShowButtons: false,
        shouldShowExportCsvAndEditCols: true,
        onReassignCases,
        onDelete,
        onUploadCases
      },
      "hours"
    );

    expect(component).toMatchSnapshot();
  });

  test("renders Create New links to Activities", () => {
    const component = render(
      {
        shouldShowDeleteAndReassignButtons: false,
        shouldNotShowButtons: false,
        shouldShowExportCsvAndEditCols: true,
        onReassignCases,
        onDelete,
        onUploadCases
      },
      "activities"
    );

    expect(component).toMatchSnapshot();
  });
});
