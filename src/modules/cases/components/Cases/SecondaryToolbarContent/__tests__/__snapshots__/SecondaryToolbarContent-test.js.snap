// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SecondaryToolbarContent renders Create New links to Activities 1`] = `
Array [
  <ToggleFilterPanelButton />,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
    disabled={false}
    onClick={[Function]}
    outline={true}
  >
    <i
      className="fa-solid fa-file-csv"
    />
    Export CSV
  </Button>,
  <div
    className="tw-ml-auto tw-flex tw-flex-row"
  >
    <SearchBar
      iconClass="tw-absolute tw-inset-y-2 tw-right-4"
      placeholder="Search cases by visit number or MRN"
    />
    <Link
      to="/activities/new"
    >
      <Button
        bg="main"
        customStyle="tw-flex tw-ml-2.5 tw-gap-2.5 tw-h-[35px]"
      >
        <i
          className="fa-regular fa-plus"
        />
        Create New
      </Button>
    </Link>
  </div>,
]
`;

exports[`SecondaryToolbarContent renders Create New links to Hours 1`] = `
Array [
  <ToggleFilterPanelButton />,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
    disabled={true}
    onClick={[Function]}
    outline={true}
  >
    <i
      className="fa-solid fa-file-csv"
    />
    Export CSV
  </Button>,
  <div
    className="tw-ml-auto tw-flex tw-flex-row"
  >
    <SearchBar
      iconClass="tw-absolute tw-inset-y-2 tw-right-4"
      placeholder="Search cases by visit number or MRN"
    />
    <Link
      to="/hours/new"
    >
      <Button
        bg="main"
        customStyle="tw-flex tw-ml-2.5 tw-gap-2.5 tw-h-[35px]"
      >
        <i
          className="fa-regular fa-plus"
        />
        Create New
      </Button>
    </Link>
  </div>,
]
`;

exports[`SecondaryToolbarContent renders SecondaryToolbarContent for Case Finding Tab with no Export CSV nor Edit Table Columns buttons when no rows have been selected 1`] = `
Array [
  <ToggleFilterPanelButton />,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
    onClick={[MockFunction]}
    outline={true}
  >
    <i
      className="fa-regular fa-arrow-up-from-line"
    />
    Upload Cases
  </Button>,
  <div
    className="tw-ml-auto tw-flex tw-flex-row"
  >
    <SearchBar
      iconClass="tw-absolute tw-inset-y-2 tw-right-4"
      placeholder="Search cases by visit number or MRN"
    />
    <Link
      to="/cases/new"
    >
      <Button
        bg="main"
        customStyle="tw-flex tw-ml-2.5 tw-gap-2.5 tw-h-[35px]"
      >
        <i
          className="fa-regular fa-plus"
        />
        Create New
      </Button>
    </Link>
  </div>,
]
`;

exports[`SecondaryToolbarContent renders an empty SecondaryToolbarContent when rows have been selected 1`] = `null`;

exports[`SecondaryToolbarContent renders appropriate SecondaryToolbarContent when rows have been selected and case reassignment should show 1`] = `
Array [
  <DeleteButton
    onDelete={[MockFunction]}
  />,
  <Button
    bg="warning"
    customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[35px]"
    onClick={[MockFunction]}
    outline={true}
  >
    <i
      className="fa-solid fa-shuffle"
    />
    Reassign
  </Button>,
]
`;

exports[`SecondaryToolbarContent renders default SecondaryToolbarContent when no rows have been selected 1`] = `
Array [
  <ToggleFilterPanelButton />,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
    onClick={[MockFunction]}
    outline={true}
  >
    <i
      className="fa-regular fa-arrow-up-from-line"
    />
    Upload Cases
  </Button>,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-h-[35px]"
    disabled={true}
    onClick={[Function]}
    outline={true}
  >
    <i
      className="fa-solid fa-file-csv"
    />
    Export CSV
  </Button>,
  <div
    className="tw-ml-auto tw-flex tw-flex-row"
  >
    <SearchBar
      iconClass="tw-absolute tw-inset-y-2 tw-right-4"
      placeholder="Search cases by visit number or MRN"
    />
    <Link
      to="/cases/new"
    >
      <Button
        bg="main"
        customStyle="tw-flex tw-ml-2.5 tw-gap-2.5 tw-h-[35px]"
      >
        <i
          className="fa-regular fa-plus"
        />
        Create New
      </Button>
    </Link>
  </div>,
]
`;
