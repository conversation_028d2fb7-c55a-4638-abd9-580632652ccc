/* eslint-disable complexity */
import apolloClient from "base/apolloClient";
import classnames from "classnames";
import Content from "modules/cases/components/Cases/Content";
import Layout from "shared/components/Layout";
import SecondaryToolbarContent from "./SecondaryToolbarContent";
import { useComponentLogic } from "modules/cases/components/Cases/hooks";
import QPoints from "shared/widgets/QPoints";
import Earnings from "shared/widgets/Earnings";
import Hours from "shared/widgets/Hours";
import LastLogInTile from "shared/widgets/LastLogInTile";
import redirectToRoot from "utils/redirectToRoot";
import redirectToUploadCases from "utils/redirectToUploadCases";
import { withApolloProvider } from "shared/hocs/withApolloProvider";

export const client = apolloClient("/qapps/graphql");
export const clientRRM = apolloClient("/api/rrm/graphql");

const Cases = () => {
  const {
    shouldShowEditColumnBar,
    shouldShowCaseReassignment,
    shouldShowExportCsvAndEditCols,
    shouldShowDeleteAndReassignButtons,
    shouldNotShowButtons,
    toggleCaseReassignment,
    rowsHaveBeenSelected,
    deleteModalIsOpen,
    toggleDeleteModal,
    isFilterPanelOpen,
    toggleFilterPanelDisplay,
    setIsFilterPanelOpen,
    handleCaseResearch,
    handleSearchInputChange,
    handleExitSearchMode,
    searchInput,
    areButtonsHidden,
    searchResultCases,
    shouldSearchExactMatch,
    handleExactMatchChange,
    handleSortTest,
    searchSortSettings,
    handleChangePage,
    currentPage,
    handleChangeRowsPerPage,
    rowsPerPage,
    handleSearchSort,
    loading,
    searchColumns,
    completed,
    isHoursTab
  } = useComponentLogic();

  const tbChildren = (
    <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
      <h1 className="tw-text-2xl tw-font-semibold">Workflow</h1>
      <div className="tw-flex tw-items-center tw-gap-5">
        <Hours />
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  );

  const stChildren = (
    <SecondaryToolbarContent
      shouldShowEditColumnBar={shouldShowEditColumnBar}
      shouldShowCaseReassignment={shouldShowCaseReassignment}
      isFilterPanelOpen={isFilterPanelOpen}
      setIsFilterPanelOpen={setIsFilterPanelOpen}
      toggleFilterPanelDisplay={toggleFilterPanelDisplay}
      shouldShowDeleteAndReassignButtons={shouldShowDeleteAndReassignButtons}
      shouldNotShowButtons={shouldNotShowButtons}
      shouldShowExportCsvAndEditCols={shouldShowExportCsvAndEditCols}
      onReassignCases={toggleCaseReassignment}
      onDelete={toggleDeleteModal}
      onUploadCases={redirectToUploadCases}
      onSearchInputChange={handleSearchInputChange}
      onExitSearchMode={handleExitSearchMode}
      onCaseSearch={handleCaseResearch}
      searchInput={searchInput}
      areButtonsHidden={areButtonsHidden}
      onExactMatchChange={handleExactMatchChange}
      shouldSearchExactMatch={shouldSearchExactMatch}
      isHoursTab={isHoursTab}
    />
  );

  const stChildrenClassName = classnames("tw-gap-2", {
    "tw-justify-start": !rowsHaveBeenSelected
  });

  return (
    <Layout
      tbChildren={tbChildren}
      stChildren={stChildren}
      stChildrenClassName={stChildrenClassName}
      onLogoClick={redirectToRoot}
    >
      <Content
        shouldShowEditColumnBar={shouldShowEditColumnBar}
        toggleCaseReassignment={toggleCaseReassignment}
        shouldShowCaseReassignment={shouldShowCaseReassignment}
        shouldShowExportCsvAndEditCols={shouldShowExportCsvAndEditCols}
        rowsHaveBeenSelected={rowsHaveBeenSelected}
        deleteModalIsOpen={deleteModalIsOpen}
        toggleDeleteModal={toggleDeleteModal}
        isFilterPanelOpen={isFilterPanelOpen}
        searchResultCases={searchResultCases}
        searchInput={searchInput}
        onExitSearchMode={handleExitSearchMode}
        handleSortTest={handleSortTest}
        searchSortSettings={searchSortSettings}
        handleChangePage={handleChangePage}
        currentPage={currentPage}
        handleChangeRowsPerPage={handleChangeRowsPerPage}
        rowsPerPage={rowsPerPage}
        onSearchResultSort={handleSearchSort}
        loading={loading}
        searchColumns={searchColumns}
        shouldSearchExactMatch={shouldSearchExactMatch}
        completed={completed}
      />
    </Layout>
  );
};

export default withApolloProvider(Cases, client);
