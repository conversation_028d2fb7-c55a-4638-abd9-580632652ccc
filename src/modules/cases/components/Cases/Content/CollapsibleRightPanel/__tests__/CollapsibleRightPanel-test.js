import { create } from "react-test-renderer";
import CollapsibleRightPanel from "..";

describe("CollapsibleRightPanel", () => {
  const render = props => create(<CollapsibleRightPanel {...props} />);
  const mockChild = <div>Mock child</div>;

  test("renders collapsed CollapsibleRightPanel", () => {
    const component = render({
      children: mockChild,
      showPanel: false
    });

    expect(component).toMatchSnapshot();
  });

  test("renders visible CollapsibleRightPanel", () => {
    const component = render({
      children: mockChild,
      showPanel: true
    });

    expect(component).toMatchSnapshot();
  });
});
