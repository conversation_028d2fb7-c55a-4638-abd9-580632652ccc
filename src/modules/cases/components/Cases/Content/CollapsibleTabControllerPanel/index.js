import { propOr, __ } from "ramda";
import classnames from "classnames";
import capitalize from "utils/fp/capitalize";
import { TabControllerBar, Tab } from "@q-centrix/q-components-react";
import Filters from "modules/cases/components/Filters";
import Columns from "modules/cases/components/Columns";
import { useComponentLogic } from "./hooks";

const type = propOr(Filters, __, {
  Columns
});

const CollapisbleTabControllerPanel = props => {
  const { shouldHideFilters, shouldShowExportCsvAndEditCols } = props;
  const { tabs, activeTab, handleTabClick } = useComponentLogic({
    shouldShowExportCsvAndEditCols
  });

  const tabControllerContainerClass = classnames(
    "tw-absolute tw-top-0 tw-h-full tw-w-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-pt-5 tw-transition-all tw-duration-500 tw-ease-in-out tw-flex tw-flex-col",
    {
      "tw-translate-x-[-414px]": !shouldHideFilters,
      "tw-translate-x-0": shouldHideFilters
    }
  );

  const GeneratedType = type(capitalize(activeTab.id));

  return (
    <div className={tabControllerContainerClass}>
      {shouldShowExportCsvAndEditCols && (
        <div className="tw-px-5 tw-pb-4">
          <TabControllerBar className="tw-w-full">
            {tabs.map(tab => (
              <Tab
                layoutId="filterTabController"
                key={tab.id}
                tab={tab}
                onClick={() => handleTabClick(tab.id)}
                className="tw-w-full tw-text-sm tw-font-semibold tw-text-black"
                activeTabClassName="tw-bg-qc-blue-800"
              />
            ))}
          </TabControllerBar>
        </div>
      )}
      <GeneratedType {...props} />
    </div>
  );
};

export default CollapisbleTabControllerPanel;
