import { useState } from "react";
import { find, propEq, assoc, map, ifElse } from "ramda";

const defaultTabs = [
  {
    id: "filters",
    label: "Filters",
    icon: "fa-regular fa-filters",
    active: true
  },
  {
    id: "columns",
    label: "Table Columns",
    icon: "fa-solid fa-table-layout",
    active: false
  }
];

export const useComponentLogic = ({ shouldShowExportCsvAndEditCols }) => {
  const [tabs, setTabs] = useState(defaultTabs);

  const handleTabClick = id => {
    setTabs(
      map(
        ifElse(propEq("id", id), assoc("active", true), assoc("active", false))
      )
    );
  };

  const activeTab = shouldShowExportCsvAndEditCols
    ? find(propEq("active", true))(tabs)
    : tabs[0];

  return {
    activeTab,
    tabs,
    handleTabClick
  };
};
