// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CollapisbleTabControllerPanel renders collapsed CollapisbleTabControllerPanel 1`] = `
<div
  className="tw-absolute tw-top-0 tw-h-full tw-w-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-pt-5 tw-transition-all tw-duration-500 tw-ease-in-out tw-flex tw-flex-col tw-translate-x-0"
>
  <div
    className="tw-px-5 tw-pb-4"
  >
    <TabControllerBar
      className="tw-w-full"
    >
      <Tab
        activeTabClassName="tw-bg-qc-blue-800"
        className="tw-w-full tw-text-sm tw-font-semibold tw-text-black"
        layoutId="filterTabController"
        onClick={[Function]}
        tab={
          Object {
            "active": true,
            "icon": "fa-regular fa-filters",
            "id": "filters",
            "label": "Filters",
          }
        }
      />
      <Tab
        activeTabClassName="tw-bg-qc-blue-800"
        className="tw-w-full tw-text-sm tw-font-semibold tw-text-black"
        layoutId="filterTabController"
        onClick={[Function]}
        tab={
          Object {
            "active": false,
            "icon": "fa-solid fa-table-layout",
            "id": "columns",
            "label": "Table Columns",
          }
        }
      />
    </TabControllerBar>
  </div>
  <Filters
    shouldHideFilters={true}
    shouldShowExportCsvAndEditCols={true}
  />
</div>
`;

exports[`CollapisbleTabControllerPanel renders visible CollapisbleTabControllerPanel 1`] = `
<div
  className="tw-absolute tw-top-0 tw-h-full tw-w-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-pt-5 tw-transition-all tw-duration-500 tw-ease-in-out tw-flex tw-flex-col tw-translate-x-[-414px]"
>
  <div
    className="tw-px-5 tw-pb-4"
  >
    <TabControllerBar
      className="tw-w-full"
    >
      <Tab
        activeTabClassName="tw-bg-qc-blue-800"
        className="tw-w-full tw-text-sm tw-font-semibold tw-text-black"
        layoutId="filterTabController"
        onClick={[Function]}
        tab={
          Object {
            "active": true,
            "icon": "fa-regular fa-filters",
            "id": "filters",
            "label": "Filters",
          }
        }
      />
      <Tab
        activeTabClassName="tw-bg-qc-blue-800"
        className="tw-w-full tw-text-sm tw-font-semibold tw-text-black"
        layoutId="filterTabController"
        onClick={[Function]}
        tab={
          Object {
            "active": false,
            "icon": "fa-solid fa-table-layout",
            "id": "columns",
            "label": "Table Columns",
          }
        }
      />
    </TabControllerBar>
  </div>
  <Filters
    shouldHideFilters={false}
    shouldShowExportCsvAndEditCols={true}
  />
</div>
`;

exports[`CollapisbleTabControllerPanel renders visible CollapisbleTabControllerPanel without tab controller 1`] = `
<div
  className="tw-absolute tw-top-0 tw-h-full tw-w-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-pt-5 tw-transition-all tw-duration-500 tw-ease-in-out tw-flex tw-flex-col tw-translate-x-[-414px]"
>
  <Filters
    shouldHideFilters={false}
    shouldShowExportCsvAndEditCols={false}
  />
</div>
`;
