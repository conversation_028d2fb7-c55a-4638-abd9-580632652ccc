import { create } from "react-test-renderer";
import CollapisbleTabControllerPanel from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  TabControllerBar: "TabControllerBar",
  Tab: "Tab"
}));

jest.mock("../../../../Filters", () => "Filters");
jest.mock("../../../../Columns", () => "Columns");

describe("CollapisbleTabControllerPanel", () => {
  const render = props => create(<CollapisbleTabControllerPanel {...props} />);

  test("renders collapsed CollapisbleTabControllerPanel", () => {
    const component = render({
      shouldHideFilters: true,
      shouldShowExportCsvAndEditCols: true
    });

    expect(component).toMatchSnapshot();
  });

  test("renders visible CollapisbleTabControllerPanel", () => {
    const component = render({
      shouldHideFilters: false,
      shouldShowExportCsvAndEditCols: true
    });

    expect(component).toMatchSnapshot();
  });

  test("renders visible CollapisbleTabControllerPanel without tab controller", () => {
    const component = render({
      shouldHideFilters: false,
      shouldShowExportCsvAndEditCols: false
    });

    expect(component).toMatchSnapshot();
  });
});
