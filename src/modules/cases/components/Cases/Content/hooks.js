/* eslint-disable max-statements */
import { useEffect, useMemo, useState, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  all,
  any,
  complement,
  either,
  eqProps,
  filter,
  map,
  prop,
  propOr,
  when
} from "ramda";
import { isNullOrEmpty } from "utils/fp";
import { useQuery, useMutation } from "@apollo/client";
import { CASES_LIST_TABS } from "modules/cases/graphql/query";
import {
  DELETE_CASES,
  DELETE_ACTIVITIES
} from "modules/cases/graphql/mutation";
import { changeTab, setSelectedRows } from "modules/cases/redux/slice";
import casesSelectors from "modules/cases/redux/selectors";
import allTabs from "modules/cases/tabs";
import { useToast } from "@q-centrix/q-components-react";
import { useLocalStorage } from "shared/hooks/useLocalStorage";
import { DELETE_HOUR_GROUPS } from "modules/hours/graphql/mutation";

const deleteConfig = {
  cases: {
    modalValue: "case(s)",
    mutation: DELETE_CASES,
    queryName: "caseList",
    variableName: "caseIds",
    mutationResults: "deleteCases",
    reasonRequired: true
  },
  activities: {
    modalValue: "activity(ies)",
    mutation: DELETE_ACTIVITIES,
    queryName: "activityList",
    variableName: "activityIds",
    mutationResults: "deleteActivities"
  },
  hours: {
    modalValue: "hour(s)",
    mutation: DELETE_HOUR_GROUPS,
    queryName: "hourGroupList",
    variableName: "hourGroupIds",
    mutationResults: "deleteHourGroups"
  }
};

export const useComponentLogic = ({
  toggleCaseReassignment,
  toggleDeleteModal,
  searchInput,
  onExitSearchMode
}) => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  const selectedTab = useSelector(casesSelectors.getSelectedTab);
  const getTabSettings = useMemo(
    () => propOr(deleteConfig.cases, selectedTab?.type)(deleteConfig),
    [selectedTab]
  );
  const selectedRows = useSelector(casesSelectors.getSelectedRows);
  const [localStoragePinnedTab, setLocalStoragePinnedTab] = useLocalStorage(
    "pinnedTab",
    null
  );
  const [currentTabNonPinnedIconHovered, setCurrentTabNonPinnedIconHovered] =
    useState(false);
  const [disableHover, setDisableHover] = useState(false);

  const handleMouseEnter = () => {
    setCurrentTabNonPinnedIconHovered(true);
  };
  const handleMouseLeave = () => {
    setCurrentTabNonPinnedIconHovered(false);
    setDisableHover(false);
  };
  const isRRTAdmin = any(
    either(prop("isAdminOutPatient"), prop("isAdminInpatient"))
  )(selectedRows);
  const [reason, setReason] = useState("");
  const handleReasonChange = useCallback(e => {
    setReason(e.target.value);
  }, []);

  const { data: { caseListTabs } = { caseListTabs: [] } } =
    useQuery(CASES_LIST_TABS);

  const tabs = useMemo(
    () =>
      when(complement(isNullOrEmpty), () =>
        filter(tab => any(eqProps("key", tab), caseListTabs), allTabs)
      )(caseListTabs),
    [caseListTabs]
  );

  useEffect(() => {
    if (tabs.length === 0) return;
    const [firstTab] = tabs;
    const currentKey = localStoragePinnedTab
      ? localStoragePinnedTab
      : firstTab.key;

    dispatch(
      changeTab({
        key: currentKey
      })
    );
  }, [tabs]);

  const handleSelectTab = key => {
    if (searchInput) onExitSearchMode();

    if (selectedTab.key === key) return;

    dispatch(changeTab({ key }));
  };

  const clearSelectedRowsAndCloseCaseReassignment = () => {
    dispatch(setSelectedRows([]));
    toggleCaseReassignment();
  };

  const [deleteCases] = useMutation(getTabSettings?.mutation);

  const selectedCasesIds = useMemo(
    () => map(row => row.id, selectedRows),
    [selectedRows]
  );

  const handleDeleteCases = () => {
    deleteCases({
      variables: {
        [getTabSettings?.variableName]: selectedCasesIds,
        reason: getTabSettings?.reasonRequired ? reason : undefined
      },
      onCompleted: data => {
        const deleteResults = prop(getTabSettings?.mutationResults)(data);

        if (deleteResults?.errors) {
          deleteResults.errors?.messages.forEach(errorMessage => {
            toast({
              variant: "error",
              description: errorMessage
            });
          });
        }
        if (deleteResults?.successful) {
          deleteResults.successful?.messages.forEach(successMessage => {
            toast({
              variant: "success",
              description: successMessage
            });
          });
        }
        dispatch(setSelectedRows([]));
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      // invalidate list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            [getTabSettings.queryName]: (_value, { DELETE }) => DELETE
          }
        });
      }
    });

    setReason("");
    toggleDeleteModal();
  };

  const fileTabsRef = useRef(null);
  const [selectedPage, setSelectedPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageWidth, setPageWidth] = useState(0);
  const reachedEnd = selectedPage === totalPages;
  const reachedStart = selectedPage === 1;

  useEffect(() => {
    const handleResize = () => {
      if (fileTabsRef.current) {
        const { clientWidth } = fileTabsRef.current;
        const { scrollWidth } = fileTabsRef.current;

        setTotalPages(Math.ceil(scrollWidth / clientWidth));
        setPageWidth(clientWidth);
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [fileTabsRef.current]);
  useEffect(() => {
    if (fileTabsRef.current) {
      const { scrollWidth } = fileTabsRef.current;
      const { clientWidth } = fileTabsRef.current;

      setTotalPages(Math.ceil(scrollWidth / clientWidth));
      setPageWidth(clientWidth);
    }
  }, [fileTabsRef.current, selectedTab]);

  const handleNextArrow = () => {
    if (reachedEnd) return;

    fileTabsRef.current.scrollBy({
      left: pageWidth,
      behavior: "smooth"
    });
    setSelectedPage(prev => prev + 1);
  };

  const handlePreviousArrow = () => {
    if (reachedStart) return;

    fileTabsRef.current.scrollBy({
      left: -pageWidth,
      behavior: "smooth"
    });
    setSelectedPage(prev => prev - 1);
  };

  const hideAssignNewOwner = all(prop("hasAbstractionTask"), selectedRows);

  return {
    setLocalStoragePinnedTab,
    localStoragePinnedTab,
    tabs,
    selectedTab,
    selectedCasesIds,
    handleSelectTab,
    clearSelectedRowsAndCloseCaseReassignment,
    handleDeleteCases,
    isRRTAdmin,
    handleNextArrow,
    handlePreviousArrow,
    reachedEnd,
    reachedStart,
    fileTabsRef,
    currentTabNonPinnedIconHovered,
    setCurrentTabNonPinnedIconHovered,
    handleMouseEnter,
    handleMouseLeave,
    disableHover,
    setDisableHover,
    hideAssignNewOwner,
    getTabSettings,
    reason,
    handleReasonChange
  };
};
