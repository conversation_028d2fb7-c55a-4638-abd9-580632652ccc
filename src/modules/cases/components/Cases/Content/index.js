import classnames from "classnames";
import { FileTabs } from "@q-centrix/q-components-react";
import TabWrapper from "modules/cases/components/TabWrapper";
import CasesTable from "modules/cases/components/CasesTable";
import CaseReassignment from "shared/components/CaseReassignment";
import DeleteModal from "shared/components/DeleteModal";
import { useComponentLogic } from "modules/cases/components/Cases/Content/hooks";
import CollapisbleTabControllerPanel from "./CollapsibleTabControllerPanel";
import CollapsibleRightPanel from "./CollapsibleRightPanel";
import SearchResultTable from "modules/cases/components/SearchResultTable";
import { AnimatePresence } from "framer-motion";
import { trim } from "ramda";

// eslint-disable-next-line complexity
const Content = ({
  shouldShowEditColumnBar,
  shouldShowExportCsvAndEditCols,
  toggleCaseReassignment,
  shouldShowCaseReassignment,
  rowsHaveBeenSelected,
  deleteModalIsOpen,
  toggleDeleteModal,
  isFilterPanelOpen,
  searchResultCases,
  searchInput,
  onExitSearchMode,
  handleSortTest,
  searchSortSettings,
  handleChangePage,
  currentPage,
  handleChangeRowsPerPage,
  rowsPerPage,
  onSearchResultSort,
  loading,
  searchColumns,
  shouldSearchExactMatch,
  completed
}) => {
  const {
    setLocalStoragePinnedTab,
    localStoragePinnedTab,
    tabs,
    selectedTab,
    selectedCasesIds,
    handleSelectTab,
    clearSelectedRowsAndCloseCaseReassignment,
    handleDeleteCases,
    isRRTAdmin,
    handleNextArrow,
    handlePreviousArrow,
    reachedEnd,
    reachedStart,
    fileTabsRef,
    currentTabNonPinnedIconHovered,
    setCurrentTabNonPinnedIconHovered,
    handleMouseEnter,
    handleMouseLeave,
    disableHover,
    setDisableHover,
    hideAssignNewOwner,
    getTabSettings,
    reason,
    handleReasonChange
  } = useComponentLogic({
    toggleCaseReassignment,
    toggleDeleteModal,
    searchInput,
    onExitSearchMode
  });

  const rightPanelIsOpen =
    shouldShowEditColumnBar || shouldShowCaseReassignment;

  const shouldHideFiltersPanel = isFilterPanelOpen || rightPanelIsOpen;

  const casesTableContainerClassName = classnames(
    "tw-absolute tw-left-0 tw-top-0 tw-h-full  tw-transition-all tw-duration-500 tw-ease-in-out",
    {
      "tw-translate-x-[414px]": isFilterPanelOpen && !rightPanelIsOpen,
      "tw-translate-x-[0px]": rightPanelIsOpen,
      "tw-w-[calc(100%-414px)]": isFilterPanelOpen || rightPanelIsOpen,
      "tw-w-[calc(100%)]": !rightPanelIsOpen
    }
  );

  const shouldShowSearchResult =
    (searchInput && searchResultCases.length) ||
    (searchInput && shouldSearchExactMatch) ||
    (searchInput && loading) ||
    completed;

  return (
    <div className="tw-relative tw-h-full tw-w-full tw-overflow-hidden">
      <CollapisbleTabControllerPanel
        shouldHideFilters={shouldHideFiltersPanel}
        shouldShowExportCsvAndEditCols={shouldShowExportCsvAndEditCols}
      />
      <div className={casesTableContainerClassName}>
        <div className="tw-flex tw-h-full tw-flex-col tw-bg-gray-400 tw-p-2.5 tw-shadow-md">
          {selectedTab && (
            <AnimatePresence>
              <FileTabs
                onNextArrowClick={handleNextArrow}
                onPreviousArrowClick={handlePreviousArrow}
                reachedEnd={reachedEnd}
                reachedStart={reachedStart}
                showArrows
                ref={fileTabsRef}
              >
                {tabs.map(({ key, label }) => (
                  <TabWrapper
                    disableHover={disableHover}
                    setDisableHover={setDisableHover}
                    localStoragePinnedTab={localStoragePinnedTab}
                    setLocalStoragePinnedTab={setLocalStoragePinnedTab}
                    id={key}
                    isDisabled={selectedTab.key !== key && rowsHaveBeenSelected}
                    key={key}
                    isSelected={
                      selectedTab.key === key && !shouldShowSearchResult
                    }
                    onClick={handleSelectTab}
                    label={label}
                    profile={key}
                    currentTabNonPinnedIconHovered={
                      currentTabNonPinnedIconHovered
                    }
                    setCurrentTabNonPinnedIconHovered={
                      setCurrentTabNonPinnedIconHovered
                    }
                    handleMouseEnter={handleMouseEnter}
                    handleMouseLeave={handleMouseLeave}
                  />
                ))}
              </FileTabs>
              {shouldShowSearchResult ? (
                <SearchResultTable
                  cases={searchResultCases}
                  handleSortTest={handleSortTest}
                  searchSortSettings={searchSortSettings}
                  handleChangePage={handleChangePage}
                  currentPage={currentPage}
                  handleChangeRowsPerPage={handleChangeRowsPerPage}
                  rowsPerPage={rowsPerPage}
                  onSearchResultSort={onSearchResultSort}
                  loading={loading}
                  shouldShowSearchResult={shouldShowSearchResult}
                  searchColumns={searchColumns}
                />
              ) : (
                <CasesTable
                  // key prop used to force re-render when tab is changed, this guarantees fresh data
                  key={selectedTab.key}
                  selectionIsDisabled={rightPanelIsOpen}
                />
              )}
            </AnimatePresence>
          )}
        </div>
      </div>
      <CollapsibleRightPanel showPanel={shouldShowCaseReassignment}>
        <CaseReassignment
          caseIds={selectedCasesIds}
          onCancel={clearSelectedRowsAndCloseCaseReassignment}
          onSuccess={clearSelectedRowsAndCloseCaseReassignment}
          hideAssignNewOwner={hideAssignNewOwner}
          reassignmentType={selectedTab?.type}
        />
      </CollapsibleRightPanel>
      <DeleteModal
        title={`Delete selected ${getTabSettings?.modalValue}?`}
        body={
          isRRTAdmin
            ? "One or more of these cases are a Regulatory case.  All associated cases for the visit will be deleted."
            : `Are you sure you want to permanently delete the selected ${getTabSettings?.modalValue}?`
        }
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteCases}
        reasonRequired={getTabSettings.reasonRequired}
        reason={reason}
        onReasonChange={handleReasonChange}
        disabledConfirm={getTabSettings.reasonRequired && !trim(reason)}
      />
    </div>
  );
};

export default Content;
