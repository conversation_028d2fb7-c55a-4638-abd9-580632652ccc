import { create } from "react-test-renderer";
import { decoratedApolloWithDispatch } from "utils/tests/decorated";
import tabs from "modules/cases/tabs";
import Content from "modules/cases/components/Cases/Content";
import mocks from "modules/cases/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  FileTabs: "FileTabs",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("../../../Filters", () => "Filters");
jest.mock("../../../Columns", () => "Columns");
jest.mock("../../../TabWrapper", () => "TabWrapper");
jest.mock("../../../CasesTable", () => "CasesTable");
jest.mock("../../../SearchResultTable", () => "SearchResultTable");
jest.mock("shared/components/DeleteModal", () => "DeleteModal");
jest.mock("../CollapsibleRightPanel", () => "CollapsibleRightPanel");
jest.mock(
  "../../../../../../shared/components/CaseReassignment",
  () => "CaseReassignment"
);

describe("Content", () => {
  test("renders Content", () => {
    // eslint-disable-next-line no-unused-vars
    const [_firstTab, secondTab] = tabs;

    const { component } = decoratedApolloWithDispatch({
      component: Content,
      initialAppValues: {
        cases: {
          selectedTab: secondTab,
          selectedRows: []
        }
      },
      apolloMocks: mocks
    });

    const contentComponent = create(component);

    expect(contentComponent).toMatchSnapshot();
  });
});
