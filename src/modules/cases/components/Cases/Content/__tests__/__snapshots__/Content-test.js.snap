// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content renders Content 1`] = `
<div
  className="tw-relative tw-h-full tw-w-full tw-overflow-hidden"
>
  <div
    className="tw-absolute tw-top-0 tw-h-full tw-w-[414px] tw-border tw-border-gray-50 tw-bg-qc-blue-50 tw-pt-5 tw-transition-all tw-duration-500 tw-ease-in-out tw-flex tw-flex-col tw-translate-x-[-414px]"
  >
    <Filters />
  </div>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-h-full  tw-transition-all tw-duration-500 tw-ease-in-out tw-w-[calc(100%)]"
  >
    <div
      className="tw-flex tw-h-full tw-flex-col tw-bg-gray-400 tw-p-2.5 tw-shadow-md"
    >
      <FileTabs
        onNextArrowClick={[Function]}
        onPreviousArrowClick={[Function]}
        reachedEnd={true}
        reachedStart={true}
        showArrows={true}
      />
      <CasesTable />
    </div>
  </div>
  <CollapsibleRightPanel>
    <CaseReassignment
      caseIds={Array []}
      hideAssignNewOwner={true}
      onCancel={[Function]}
      onSuccess={[Function]}
      reassignmentType="cases"
    />
  </CollapsibleRightPanel>
  <DeleteModal
    body="Are you sure you want to permanently delete the selected case(s)?"
    disabledConfirm={true}
    onConfirm={[Function]}
    onReasonChange={[Function]}
    reason=""
    reasonRequired={true}
    title="Delete selected case(s)?"
  />
</div>
`;
