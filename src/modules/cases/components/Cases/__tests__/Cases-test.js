/* eslint-disable react/button-has-type */
import { create } from "react-test-renderer";
import Cases from "modules/cases/components/Cases";
import { decorated } from "utils/tests/decorated";

jest.mock("@q-centrix/q-components-react", () => ({
  Topbar: "Topbar",
  SecondaryToolbar: "SecondaryToolbar",
  Button: "Button",
  SideNavMenu: "SideNavMenu"
}));
jest.mock("../Content", () => "Content");
jest.mock("../SecondaryToolbarContent", () => "SecondaryToolbarContent");
jest.mock("shared/components/Layout", () => "Layout");

describe("Cases", () => {
  function render() {
    return create(
      decorated(Cases, {}, null, {
        cases: {
          selectedRows: [],
          selectedColumns: [],
          selectedFilters: {}
        }
      })
    );
  }
  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
