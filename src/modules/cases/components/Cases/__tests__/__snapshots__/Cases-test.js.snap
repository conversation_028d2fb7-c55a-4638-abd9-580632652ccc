// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cases it renders component correctly 1`] = `
<Layout
  onLogoClick={[Function]}
  stChildren={
    <SecondaryToolbarContent
      areButtonsHidden={false}
      isFilterPanelOpen={true}
      isHoursTab={false}
      onCaseSearch={[Function]}
      onDelete={[Function]}
      onExactMatchChange={[Function]}
      onExitSearchMode={[Function]}
      onReassignCases={[Function]}
      onSearchInputChange={[Function]}
      onUploadCases={[Function]}
      searchInput=""
      setIsFilterPanelOpen={[Function]}
      shouldNotShowButtons={false}
      shouldSearchExactMatch={false}
      shouldShowCaseReassignment={false}
      shouldShowDeleteAndReassignButtons={false}
      shouldShowEditColumnBar={false}
      toggleFilterPanelDisplay={[Function]}
    />
  }
  stChildrenClassName="tw-gap-2 tw-justify-start"
  tbChildren={
    <div
      className="tw-flex tw-w-full tw-items-center tw-justify-between"
    >
      <h1
        className="tw-text-2xl tw-font-semibold"
      >
        Workflow
      </h1>
      <div
        className="tw-flex tw-items-center tw-gap-5"
      >
        <Hours />
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  }
>
  <Content
    completed={false}
    currentPage={1}
    deleteModalIsOpen={false}
    handleChangePage={[Function]}
    handleChangeRowsPerPage={[Function]}
    isFilterPanelOpen={true}
    loading={false}
    onExitSearchMode={[Function]}
    onSearchResultSort={[Function]}
    rowsHaveBeenSelected={false}
    rowsPerPage={100}
    searchColumns={
      Array [
        Object {
          "isDefault": true,
          "key": "facility",
          "label": "Facility",
          "name": "facility",
          "order": 1,
          "path": Array [
            "facility",
            "name",
          ],
          "withSort": true,
        },
        Object {
          "isDefault": true,
          "key": "caseType",
          "label": "Case Type",
          "name": "caseType",
          "order": 2,
          "path": Array [
            "caseType",
            "name",
          ],
          "withSort": true,
        },
        Object {
          "isDefault": true,
          "key": "visitNumber",
          "label": "Visit Number",
          "name": "visitNumber",
          "order": 3,
          "path": Array [
            "visit",
            "number",
          ],
          "withSort": true,
        },
        Object {
          "isDefault": true,
          "key": "mrn",
          "label": "MRN",
          "name": "mrn",
          "order": 4,
          "path": Array [
            "patient",
            "mrn",
          ],
          "withSort": true,
        },
        Object {
          "isDefault": true,
          "key": "assignee",
          "label": "Assignee",
          "name": "assignee",
          "order": 5,
          "path": Array [
            "assignee",
            "fullName",
          ],
          "withSort": true,
        },
        Object {
          "isDefault": true,
          "key": "status",
          "label": "Status",
          "name": "status",
          "order": 6,
          "path": Array [
            "status",
          ],
          "withSort": true,
        },
        Object {
          "isDate": Object {
            "showTime": false,
          },
          "isDefault": true,
          "key": "admittedAt",
          "label": "Admission Date",
          "name": "admittedAt",
          "order": 7,
          "path": Array [
            "visit",
            "admittedAt",
          ],
          "withSort": true,
        },
        Object {
          "isDate": Object {
            "showTime": false,
          },
          "isDefault": true,
          "key": "hospitalDischargedAt",
          "label": "Discharge Date",
          "name": "hospitalDischargedAt",
          "order": 8,
          "path": Array [
            "visit",
            "hospitalDischargedAt",
          ],
          "withSort": true,
        },
        Object {
          "isDate": Object {
            "showTime": false,
          },
          "isDefault": true,
          "key": "clientDueDate",
          "label": "Deadline",
          "name": "clientDueDate",
          "order": 9,
          "path": Array [
            "clientDueDate",
          ],
          "withSort": true,
        },
      ]
    }
    searchInput=""
    searchResultCases={Array []}
    searchSortSettings={Array []}
    shouldSearchExactMatch={false}
    shouldShowCaseReassignment={false}
    shouldShowEditColumnBar={false}
    toggleCaseReassignment={[Function]}
    toggleDeleteModal={[Function]}
  />
</Layout>
`;
