import { create } from "react-test-renderer";
// import wait from "waait";
import { CaseFindingContainerRows } from "..";

jest.mock("../CaseFindingContainer", () => "CaseFindingContainer");

const render = props => create(<CaseFindingContainerRows {...props} />);

describe("CaseFindingContainerRows", () => {
  test("renders component without issue", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
  test("renders component with data", () => {
    const component = render({
      checked: { 306: false },
      data: [
        {
          facility: "Test Facility",
          icd10: 1,
          id: "306",
          mrn: "1122334455",
          assignee: "<PERSON>",
          firstContact: "2021-03-01T00:00:00.000Z"
        }
      ]
    });

    expect(component).toMatchSnapshot();
  });
});
