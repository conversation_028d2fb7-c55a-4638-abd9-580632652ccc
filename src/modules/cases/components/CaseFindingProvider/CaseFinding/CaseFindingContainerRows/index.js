import CaseFindingContainer from "./CaseFindingContainer";

export const CaseFindingContainerRows = ({
  data,
  checked,
  onCheckClicked,
  refetchVariables,
  currentUser,
  facilityIds,
  caseMatchFilter
}) => (
  <div className="case-finding-rows">
    {data.map(finding => (
      <CaseFindingContainer
        key={finding.id}
        {...finding}
        checked={checked[finding.id]}
        onCheckClicked={onCheckClicked}
        refetchVariables={refetchVariables}
        currentUser={currentUser}
        facilityIds={facilityIds}
        caseMatchFilter={caseMatchFilter}
      />
    ))}
  </div>
);

CaseFindingContainerRows.defaultProps = {
  data: []
};

export default CaseFindingContainerRows;
