import { GET_CASE_TYPES, GET_CANCER_SITES } from "./query";
import mutation from "./mutation";

const typesSuccessResult = {
  data: {
    caseTypes: [
      {
        id: 1,
        name: "Type 1",
        __typename: "CaseFindingTypes"
      },
      {
        id: 2,
        name: "Type 2",
        __typename: "CaseFindingTypes"
      },
      {
        id: 3,
        name: "Type 3",
        __typename: "CaseFindingTypes"
      }
    ]
  }
};

const sitesSuccessResult = {
  data: {
    cancerSites: [
      {
        id: 1,
        name: "Site 1",
        __typename: "CaseFindingSite"
      },
      {
        id: 2,
        name: "Site 2",
        __typename: "CaseFindingSite"
      }
    ]
  }
};

const mutationSuccessResult = {
  data: { addNewPrimaryCase: { response: true, errors: null } }
};

const mocks = [
  {
    request: {
      query: mutation,
      variables: {
        caseFindingId: "306",
        assigneeId: 2,
        analytic: true,
        date: "2019-01-01",
        site: 1,
        type: 1
      }
    },
    result: mutationSuccessResult
  },
  {
    request: { query: GET_CASE_TYPES, variables: { facilityId: "40" } },
    result: typesSuccessResult
  },
  { request: { query: GET_CANCER_SITES }, result: sitesSuccessResult }
];

export default mocks;
