// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HistoricalCaseRow renders component 1`] = `
<tr>
  <td
    colSpan="9"
  >
    <div
      className="historical-case-row "
    >
      <div
        className="historical-case-row-left "
      >
        <div
          className="grid-header"
        >
          <HistoricalCaseGridCell
            match={true}
            title="Facility:"
          />
          <HistoricalCaseGridCell
            match={true}
            title="Cancer Type:"
            value="Digestive Other"
          />
          <HistoricalCaseGridCell
            match={true}
            title="ICD-10:"
            value="40"
          />
          <HistoricalCaseGridCell
            match={true}
            title="Owner:"
            value="<PERSON>as Russell"
          />
        </div>
        <div
          className="grid"
        >
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 1,
                  "title": "Last Name: ",
                  "value": "Test",
                },
                Object {
                  "id": 2,
                  "title": "First Name: ",
                  "value": "First",
                },
                Object {
                  "id": 3,
                  "isDate": true,
                  "title": "DOB: ",
                  "value": "1982-10-14T16:00:00-07:00",
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 4,
                  "title": "MRN: ",
                  "value": "34534334",
                },
                Object {
                  "id": 5,
                  "title": "Accession #: ",
                  "value": 4321,
                },
                Object {
                  "id": 6,
                  "title": "Sequence #: ",
                  "value": 1234,
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 7,
                  "title": "Primary Site Code: ",
                  "value": "C181 | Appendix",
                },
                Object {
                  "id": 8,
                  "title": "Histology: ",
                  "value": "8001/0 | Tumor cells, benign",
                },
                Object {
                  "id": 9,
                  "title": "Laterality: ",
                  "value": "Paired site: midline tumor",
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 10,
                  "title": "Class of Case: ",
                  "value": "12",
                },
                Object {
                  "id": 11,
                },
                Object {
                  "id": 12,
                },
              ]
            }
          />
          <UpdateCell
            caseId="1151"
          />
        </div>
      </div>
    </div>
  </td>
</tr>
`;

exports[`HistoricalCaseRow renders component with match 1`] = `
<tr>
  <td
    colSpan="9"
  >
    <div
      className="historical-case-row "
    >
      <div
        className="historical-case-row-left "
      >
        <div
          className="grid-header"
        >
          <HistoricalCaseGridCell
            match={true}
            title="Facility:"
          />
          <HistoricalCaseGridCell
            match={true}
            title="Cancer Type:"
            value="Digestive Other"
          />
          <HistoricalCaseGridCell
            match={true}
            title="ICD-10:"
            value="40"
          />
          <HistoricalCaseGridCell
            match={true}
            title="Owner:"
            value="Reas Russell"
          />
        </div>
        <div
          className="grid"
        >
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 1,
                  "title": "Last Name: ",
                  "value": "Test",
                },
                Object {
                  "id": 2,
                  "title": "First Name: ",
                  "value": "First",
                },
                Object {
                  "id": 3,
                  "isDate": true,
                  "title": "DOB: ",
                  "value": "1982-10-14T16:00:00-07:00",
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 4,
                  "title": "MRN: ",
                  "value": "34534334",
                },
                Object {
                  "id": 5,
                  "title": "Accession #: ",
                  "value": 4321,
                },
                Object {
                  "id": 6,
                  "title": "Sequence #: ",
                  "value": 1234,
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 7,
                  "title": "Primary Site Code: ",
                  "value": "C181 | Appendix",
                },
                Object {
                  "id": 8,
                  "title": "Histology: ",
                  "value": "8001/0 | Tumor cells, benign",
                },
                Object {
                  "id": 9,
                  "title": "Laterality: ",
                  "value": "Paired site: midline tumor",
                },
              ]
            }
          />
          <HistoricalCaseGridColumn
            rows={
              Array [
                Object {
                  "id": 10,
                  "title": "Class of Case: ",
                  "value": "12",
                },
                Object {
                  "id": 11,
                },
                Object {
                  "id": 12,
                },
              ]
            }
          />
          <UpdateCell
            caseId="1151"
          />
        </div>
      </div>
    </div>
  </td>
</tr>
`;
