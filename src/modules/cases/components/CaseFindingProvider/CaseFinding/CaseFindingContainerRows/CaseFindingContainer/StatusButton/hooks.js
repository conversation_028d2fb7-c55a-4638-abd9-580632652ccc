import { useCallback } from "react";
import { useMutation } from "@apollo/client";
import { GET_CASE_FINDINGS } from "../../../../query";
import mutation from "./mutation";

export const useUpdateCaseFindingStatus = ({
  findingId,
  nonReportableText,
  id: status,
  refetchVariables
}) => {
  const [updateCaseFinding] = useMutation(mutation, {
    variables: { findingId, nonReportableText, status },
    refetchQueries: [{ query: GET_CASE_FINDINGS, variables: refetchVariables }]
  });

  return { updateCaseFinding };
};

export const useComponentLogic = props => {
  const { id, selected, setReportableDialogOpen, toggleMenu } = props;
  const { updateCaseFinding } = useUpdateCaseFindingStatus(props);
  const handleReportableClick = useCallback(() => {
    !selected && updateCaseFinding();
  }, [selected, updateCaseFinding]);
  const handleClick = useCallback(() => {
    id === 2 ? setReportableDialogOpen(true) : handleReportableClick();
    toggleMenu();
  }, [handleReportableClick, id, setReportableDialogOpen, toggleMenu]);

  return { handleClick };
};
