import HistoricalCaseRow from "./HistoricalCaseRow";

export const HistoricalCaseRows = ({
  open,
  rows,
  caseFindingId,
  refetchVariables,
  caseFindingDiagnosisCode,
  currentUserAssigned
}) => {
  if (!open) return null;
  return rows.map(row => (
    <HistoricalCaseRow
      key={row.id}
      {...row}
      caseFindingId={caseFindingId}
      refetchVariables={refetchVariables}
      caseFindingDiagnosisCode={caseFindingDiagnosisCode}
      currentUserAssignedToCaseFinding={currentUserAssigned}
      facility={row.facility ? row.facility.name : null}
      owner={row.owner ? row.owner.fullName : null}
      status={row.status}
    />
  ));
};

HistoricalCaseRows.defaultProps = { open: false, rows: [] };

export default HistoricalCaseRows;
