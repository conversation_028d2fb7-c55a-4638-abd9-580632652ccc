import DatePicker from "react-datepicker";
import Select from "react-select";
import Menu from "shared/components/Menu";
import { filterDateFormat } from "base/constants/dateFormats";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
export const AddNewPrimaryMenu = props => {
  const { open, toggleMenu } = props;
  const {
    disabled,
    handleCheckboxChange,
    handleDateChange,
    handleSelectChange,
    handleSubmit,
    loadingAssignees,
    primaryData,
    siteOptions,
    sitesLoading,
    typeOptions,
    typesLoading,
    assigneeOptions
  } = useComponentLogic(props);
  const { analytic, date, type, site, assigneeId } = primaryData;

  return (
    <Menu
      menuClassName="add-new-primary-container"
      open={open}
      toggleMenu={toggleMenu}
    >
      <Select
        placeholder="Select Case Type"
        className="case-type-selector"
        classNamePrefix="case-type-selector"
        name="type"
        closeMenuOnSelect
        value={type || null}
        options={typeOptions}
        onChange={handleSelectChange}
        isLoading={typesLoading}
        hideSelectedOptions
      />
      <Select
        placeholder="Select Cancer Type"
        className="case-site-selector"
        classNamePrefix="case-site-selector"
        name="site"
        closeMenuOnSelect
        value={site || null}
        options={siteOptions}
        onChange={handleSelectChange}
        isLoading={sitesLoading}
        hideSelectedOptions
      />
      <DatePicker
        className="tw-w-full tw-p-2 tw-text-base"
        name="dateFirstContact"
        placeholderText="Date of First Contact"
        dateFormat={filterDateFormat}
        selected={date || null}
        onChange={handleDateChange}
        isClearable
        popperPlacement="top"
      />
      <Select
        placeholder="Assign To"
        className="case-assign-selector"
        classNamePrefix="case-assign-selector"
        name="assigneeId"
        closeMenuOnSelect
        value={assigneeId}
        options={assigneeOptions}
        onChange={handleSelectChange}
        isLoading={loadingAssignees}
        hideSelectedOptions
      />
      <label htmlFor="analytic">
        <input
          name="analytic"
          id="analytic"
          onChange={handleCheckboxChange}
          type="checkbox"
          checked={analytic}
        />
        <span>Analytic</span>
      </label>
      <button
        type="button"
        className="success-btn"
        disabled={disabled}
        onClick={handleSubmit}
      >
        create new primary
      </button>
    </Menu>
  );
};

export default AddNewPrimaryMenu;
