import { MockedProvider } from "@apollo/client/testing";
import { act, create } from "react-test-renderer";
import wait from "waait";
import { IntlProvider } from "react-intl";
import mocks from "modules/cases/components/CaseFindingProvider/mocks";
import { CaseFindingContainer } from "..";
import NonReportableMenu from "../NonReportableMenu";
import StatusMenu from "../StatusMenu";
import localeData from "base/locales/data";

jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner"
}));

jest.mock("react-tooltip", () => "ReactTooltip");
jest.mock("../StatusMenu", () => "StatusMenu");
jest.mock("../StatusButton", () => "StatusButton");
jest.mock("../AddNewPrimaryMenu", () => "AddNewPrimaryMenu");
jest.mock("../HistoricalCaseRows", () => "HistoricalCaseRows");
jest.mock("../NonReportableMenu", () => "NonReportableMenu");

const defaultProps = {
  id: "1",
  firstContact: "Mon, 01 Mar 2021 00:00:00 GMT",
  facility: { name: "Test Facility" },
  dischargedAt: "2019-01-12T00:00:00",
  patient: {
    mrn: "00112234"
  }
};
const locale = "en";
const messages = localeData[locale] || localeData.en;
const render = props =>
  create(
    <IntlProvider locale={locale} messages={messages}>
      <MockedProvider mocks={mocks} addTypename={false}>
        <CaseFindingContainer {...{ ...defaultProps, ...props }} />
      </MockedProvider>
    </IntlProvider>
  );

describe("CaseFindingContainer", () => {
  test("renders component with hasICDMatch=undefined, defaults to false", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
  test("renders component with hasICDMatch=true", () => {
    const component = render({ hasICDMatch: true });

    expect(component).toMatchSnapshot();
  });
  test("tests handleChange", async () => {
    const onCheckClicked = jest.fn();
    const component = render({ onCheckClicked });

    // eslint-disable-next-line require-await
    await act(async () => wait(0));
    const instance = component.root;
    const earlyInput = instance.findByType("input");

    // const checkedBeforeClick = earlyInput.props.checked;
    await act(() => earlyInput.props.onChange({ target: { checked: true } }));
    await act(() => wait(0));
    // const checkedAfterClick = instance.findByType("input").props.checked;

    // expect(checkedBeforeClick).toBe(false);
    // expect(checkedAfterClick).toBe(true);
    expect(onCheckClicked).toHaveBeenCalledWith("1", true);
  });
  test("tests handleOpen", async () => {
    const component = render();

    await act(() => wait(0));
    const instance = component.root;
    const tableClassNameBeforeClick =
      instance.findByType("table").props.className;
    const [first] = instance.findAllByType("button");

    await act(() => first.props.onClick());
    await act(() => wait(3));
    const tableClassNameAfterClick =
      instance.findByType("table").props.className;

    expect(tableClassNameBeforeClick).not.toContain("open");
    expect(tableClassNameAfterClick).toContain("open");
  });
  test("tests toggleReportableMenu", async () => {
    const component = render();

    await act(() => wait(0));
    const instance = component.root;
    const statusMenuBeforeClick = instance.findByType(StatusMenu);
    const reportableMenuBeforeClick = instance.findByType(NonReportableMenu);

    expect(reportableMenuBeforeClick.props.open).toBeFalsy();
    await act(() => statusMenuBeforeClick.props.setReportableDialogOpen(true));
    await act(() => wait(0));
    const reportableMenuAfterClick = instance.findByType(NonReportableMenu);

    expect(reportableMenuAfterClick.props.open).toBeTruthy();
    await act(() => reportableMenuAfterClick.props.toggleMenu());
    await act(() => wait(0));
    const reportableMenuAfterClose = instance.findByType(NonReportableMenu);

    expect(reportableMenuAfterClose.props.open).toBeFalsy();
  });
});
