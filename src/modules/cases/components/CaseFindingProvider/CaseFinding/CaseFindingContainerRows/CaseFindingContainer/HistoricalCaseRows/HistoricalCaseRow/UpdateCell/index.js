import classnames from "classnames";
import Select, { components } from "react-select";
import { useComponentLogic } from "./hooks";

export const UpdateCell = ({ currentUserAssignedToCaseFinding, ...props }) => {
  const {
    showCompleteNow,
    urlCompleteNow,
    options,
    selectedTaskType,
    handleTaskTypeChange,
    handleSendToTeamClick,
    handleCompleteNowClick
  } = useComponentLogic(props);
  const buttonUpdateClasses = classnames("action", {
    disabled: !selectedTaskType
  });
  const buttonCompleteClasses = classnames("action", {
    disabled: !showCompleteNow
  });
  // eslint-disable-next-line no-shadow
  const DropdownIndicator = ({ ...props }) => (
    <components.DropdownIndicator {...props}>
      <i className="fas fa-caret-down" />
    </components.DropdownIndicator>
  );

  return (
    <div className="historical-case-row-right">
      <Select
        name="task-type"
        className="historical-case-row-selector"
        classNamePrefix="historical-case-row-selector"
        components={{
          IndicatorSeparator: () => null,
          DropdownIndicator
        }}
        onChange={handleTaskTypeChange}
        options={options}
        value={selectedTaskType}
        placeholder="Task Type"
        isDisabled={!currentUserAssignedToCaseFinding}
      />
      <div className="actions">
        {showCompleteNow && (
          <div>
            <a
              href={urlCompleteNow}
              className={buttonCompleteClasses}
              target="_blank"
              rel="noopener noreferrer"
              onClick={handleCompleteNowClick}
            >
              Complete Now
            </a>
          </div>
        )}
        <button
          type="button"
          disabled={!selectedTaskType}
          className={buttonUpdateClasses}
          onClick={handleSendToTeamClick}
        >
          Send to Team
        </button>
      </div>
    </div>
  );
};

export default UpdateCell;
