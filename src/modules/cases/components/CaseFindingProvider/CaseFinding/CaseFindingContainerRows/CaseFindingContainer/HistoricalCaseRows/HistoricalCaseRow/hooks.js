import { useMemo } from "react";
import { isNullOrEmpty } from "utils/fp";
import {
  always,
  both,
  complement,
  defaultTo,
  equals,
  head,
  ifElse,
  pipe,
  prop,
  split
} from "ramda";

const getFirstBlock = pipe(defaultTo(""), split("."), head);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  caseFindingDiagnosisCode,
  diagnosisCode,
  visit,
  patient,
  facility,
  owner,
  cancerSite,
  registryData
}) => {
  const icdMatch = useMemo(
    () =>
      both(
        complement(isNullOrEmpty),
        pipe(getFirstBlock, equals(getFirstBlock(caseFindingDiagnosisCode)))
      )(diagnosisCode),
    [caseFindingDiagnosisCode, diagnosisCode]
  );
  const { lastName, firstName, bornOn, mrn } = patient;
  const { dateOfDiagnosis } = visit;
  const noCancerSite = useMemo(() => isNullOrEmpty(cancerSite), [cancerSite]);
  const cancerType = useMemo(
    () => ifElse(always(noCancerSite), always(""), prop("name"))(cancerSite),
    [cancerSite]
  );
  const {
    accessionNumber,
    sequenceNumber,
    primarySite,
    histology,
    laterality,
    dateOfLastContact,
    classOfCase
  } = registryData;

  const column1 = useMemo(
    () => [
      { id: 1, title: "Last Name: ", value: lastName },
      { id: 2, title: "First Name: ", value: firstName },
      { id: 3, title: "DOB: ", value: bornOn, isDate: true }
    ],
    [bornOn, firstName, lastName]
  );
  const column2 = useMemo(
    () => [
      { id: 4, title: "MRN: ", value: mrn },
      { id: 5, title: "Accession #: ", value: accessionNumber },
      { id: 6, title: "Sequence #: ", value: sequenceNumber }
    ],
    [accessionNumber, mrn, sequenceNumber]
  );
  const column3 = useMemo(
    () => [
      { id: 7, title: "Primary Site Code: ", value: primarySite },
      { id: 8, title: "Histology: ", value: histology },
      { id: 9, title: "Laterality: ", value: laterality }
    ],
    [histology, laterality, primarySite]
  );
  const column4 = useMemo(
    () => [
      { id: 10, title: "Class of Case: ", value: classOfCase },
      { id: 11 },
      { id: 12 }
    ],
    [classOfCase]
  );
  const columns = useMemo(
    () => [
      { rows: column1, id: 1 },
      { rows: column2, id: 2 },
      { rows: column3, id: 3 },
      { rows: column4, id: 4 }
    ],
    [column1, column2, column3, column4]
  );
  const headers = useMemo(
    () => [
      { title: "Facility:", value: facility },
      { title: "Cancer Type:", value: cancerType },
      { title: "ICD-10:", value: diagnosisCode },
      { title: "Owner:", value: owner }
    ],
    [cancerType, dateOfDiagnosis, dateOfLastContact, diagnosisCode, facility]
  );

  return { columns, headers, icdMatch };
};
