import { useCallback, useState } from "react";
import { isNullOrEmpty } from "utils/fp";
import { useUpdateCaseFindingStatus } from "../StatusButton/hooks";

export const useComponentLogic = ({ refetchVariables, rowId, toggleMenu }) => {
  const [value, setValue] = useState("");
  const handleChange = useCallback(e => {
    e.preventDefault();
    setValue(e.target.value);
  });

  const { updateCaseFinding } = useUpdateCaseFindingStatus({
    findingId: rowId,
    id: 2,
    nonReportableText: value,
    refetchVariables
  });
  const handleClick = useCallback(
    e => {
      e.preventDefault();
      updateCaseFinding();
      toggleMenu();
    },
    [updateCaseFinding, toggleMenu]
  );

  return {
    disabled: isNullOrEmpty(value),
    handleChange,
    handleClick,
    value
  };
};
