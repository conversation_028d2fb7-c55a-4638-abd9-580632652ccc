// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NonReportableMenu renders component in loading 1`] = `
<Menu
  menuClassName="non-reportable-container"
  open={true}
  toggleMenu={[MockFunction]}
>
  <p>
    Please provide reason why this is not reportable
    <span
      className="red"
    >
      *
    </span>
  </p>
  <textarea
    name="non-reportable-input"
    onChange={[Function]}
    value=""
  />
  <button
    className="success-btn"
    disabled={true}
    onClick={[Function]}
    type="button"
  >
    Mark Non-Reportable
  </button>
</Menu>
`;

exports[`NonReportableMenu renders loaded component 1`] = `
<Menu
  menuClassName="non-reportable-container"
  open={true}
  toggleMenu={[MockFunction]}
>
  <p>
    Please provide reason why this is not reportable
    <span
      className="red"
    >
      *
    </span>
  </p>
  <textarea
    name="non-reportable-input"
    onChange={[Function]}
    value=""
  />
  <button
    className="success-btn"
    disabled={true}
    onClick={[Function]}
    type="button"
  >
    Mark Non-Reportable
  </button>
</Menu>
`;
