import { useMemo } from "react";
import {
  always,
  both,
  cond,
  equals,
  identity,
  ifElse,
  includes,
  T,
  __
} from "ramda";
import { isNullOrEmpty } from "utils/fp/isNullOrEmpty";
import { formatDate } from "utils/fp";
import { Tag } from "@q-centrix/q-components-react";

const statusCode = cond([
  [
    includes(__, [
      "Billable",
      "Ineligible",
      "Billable-Pending Client Response",
      "Done"
    ]),
    always("success")
  ],
  [equals("Pending"), always("warning")],
  [always, always("danger")]
]);

export const useComponentLogic = props => {
  const { title, match, status, value, isDate } = props;

  const formattedValue = useMemo(
    () =>
      ifElse(
        always(isDate),
        formatDate,
        ifElse(
          both(isNullOrEmpty, () => includes("Owner", title)),
          always("None"),
          identity
        )
      )(value),
    [isDate, value, title]
  );

  const renderTag = useMemo(
    () =>
      cond([
        [
          both(always(match), includes("ICD")),
          always(<Tag text="Match" status="success" />)
        ],
        [
          both(always(status), includes("Owner")),
          always(<Tag text={status} status={statusCode(status)} />)
        ],
        [T, always(null)]
      ])(title),
    [match, status, title]
  );

  return {
    renderTag,
    formattedValue
  };
};
