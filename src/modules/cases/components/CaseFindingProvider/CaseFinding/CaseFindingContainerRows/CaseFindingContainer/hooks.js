/* eslint-disable max-statements */
import { useCallback, useMemo, useState } from "react";
import {
  always,
  compose,
  descend,
  equals,
  ifElse,
  prop,
  sort,
  or,
  and
} from "ramda";
import { useQuery } from "@apollo/client";
import { isNullOrEmpty, toggle } from "utils/fp";
import { GET_CASE_FINDING_HISTORICAL_ROWS } from "../../../query";

const bySequenceNumber = descend(
  compose(prop("sequenceNumber"), prop("registryData"))
);

export const useComponentLogic = ({
  hasICDMatch,
  id,
  assignee = {},
  onCheckClicked,
  currentUser,
  caseMatchFilter
}) => {
  const { data: { caseFindingCases } = { caseFindingCases: [] }, loading } =
    useQuery(GET_CASE_FINDING_HISTORICAL_ROWS, {
      variables: { id }
    });
  const [primaryMenuOpen, setPrimaryMenuOpen] = useState(false);
  const togglePrimaryMenu = useCallback(() => toggle(setPrimaryMenuOpen), []);
  const [statusMenuOpen, setStatusMenuOpen] = useState(false);
  const toggleStatusMenu = useCallback(() => toggle(setStatusMenuOpen), []);
  const [open, setOpen] = useState(hasICDMatch);
  const [reportableDialogOpen, setReportableDialogOpen] = useState(false);

  const disabled = useMemo(
    () => equals(0, caseFindingCases.length),
    [caseFindingCases.length]
  );

  const showWithMatchesFilter = equals("match")(caseMatchFilter);
  const showNoWithMatchesFilter = equals("noMatch")(caseMatchFilter);

  const showHideCasesWithOrWithoutMatches = or(
    and(showWithMatchesFilter, disabled),
    and(showNoWithMatchesFilter, !disabled)
  );

  const unassigned = useMemo(() => isNullOrEmpty(assignee), [assignee]);
  const currentUserAssigned = useMemo(
    () =>
      assignee && currentUser ? equals(assignee.id, currentUser.id) : false,
    [assignee, currentUser]
  );
  const unassignedText = useMemo(
    () =>
      ifElse(
        always(unassigned),
        always("Unassigned"),
        prop("fullName")
      )(assignee),
    [assignee]
  );
  const handleChange = useCallback(
    e => {
      onCheckClicked(id, e.target.checked);
    },
    [onCheckClicked, id]
  );
  const handleOpen = useCallback(() => {
    toggle(setOpen);
  }, []);
  const toggleReportableMenu = useCallback(() => {
    toggle(setReportableDialogOpen);
  }, []);

  return {
    disabled,
    handleChange,
    handleOpen,
    loading,
    open,
    primaryMenuOpen,
    reportableDialogOpen,
    rows: sort(bySequenceNumber, caseFindingCases),
    setReportableDialogOpen,
    statusMenuOpen,
    togglePrimaryMenu,
    toggleReportableMenu,
    toggleStatusMenu,
    unassigned,
    unassignedText,
    currentUserAssigned,
    showHideCasesWithOrWithoutMatches
  };
};
