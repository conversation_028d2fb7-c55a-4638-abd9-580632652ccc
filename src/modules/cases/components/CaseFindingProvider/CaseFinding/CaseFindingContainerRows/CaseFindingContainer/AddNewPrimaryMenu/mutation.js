import gql from "graphql-tag";

export default gql`
  mutation addNewPrimaryCase(
    $analytic: Boolean!
    $date: ISO8601DateTime!
    $site: ID!
    $type: ID!
    $caseFindingId: ID!
    $assigneeId: ID
  ) {
    addNewPrimaryCase(
      caseFindingId: $caseFindingId
      assigneeId: $assigneeId
      analytic: $analytic
      dateOfFirstContact: $date
      cancerSiteId: $site
      caseTypeId: $type
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;
