import Menu from "shared/components/Menu";
import StatusButton from "../StatusButton";
import { STATUSES } from "modules/cases/constants";

export const StatusMenu = ({
  findingId,
  open,
  refetchVariables,
  setReportableDialogOpen,
  toggleMenu
}) => (
  <Menu menuClassName="status-menu" open={open} toggleMenu={toggleMenu}>
    {STATUSES.map(status => (
      <StatusButton
        findingId={findingId}
        key={status.id}
        refetchVariables={refetchVariables}
        setReportableDialogOpen={setReportableDialogOpen}
        toggleMenu={toggleMenu}
        {...status}
      />
    ))}
  </Menu>
);

StatusMenu.defaultProps = {
  open: false,
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusMenu;
