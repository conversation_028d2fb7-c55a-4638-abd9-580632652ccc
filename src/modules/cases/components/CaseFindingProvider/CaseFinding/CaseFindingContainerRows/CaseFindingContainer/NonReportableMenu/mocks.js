import mutation from "../StatusButton/mutation";

const mutationSuccessResult = {
  data: { updateCaseFinding: { response: true, errors: null } }
};

const mocks = [
  {
    request: {
      query: mutation,
      variables: {
        findingId: "1",
        nonReportableText: "",
        status: 2
      }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: mutation,
      variables: { findingId: "1", status: 1 }
    },
    result: mutationSuccessResult
  }
];

export default mocks;
