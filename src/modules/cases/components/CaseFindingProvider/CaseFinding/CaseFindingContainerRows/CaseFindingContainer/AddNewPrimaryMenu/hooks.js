import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import mutation from "./mutation";
import { GET_CASE_TYPES, GET_CANCER_SITES } from "./query";
import { GET_CASE_FINDING_ASSIGNEES } from "../../../CaseFindingHeaders/AssignCasesModal/query";
import { GET_CASE_FINDINGS } from "../../../../query";
import {
  allPass,
  always,
  applySpec,
  assoc,
  dissoc,
  has,
  head,
  identity,
  ifElse,
  insert,
  map,
  pipe,
  prop,
  split,
  __
} from "ramda";
import moment from "moment";
import { useToast } from "@q-centrix/q-components-react";

const createOptions = map(
  applySpec({ value: prop("id"), label: prop("name") })
);

const hasAllProps = allPass([
  has("type"),
  has("site"),
  has("date"),
  has("analytic")
]);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  dateFirstContact,
  facilityId,
  facilityIds,
  caseFindingId,
  refetchVariables,
  toggleMenu,
  currentUser
}) => {
  const [primaryData, setPrimaryData] = useState({
    analytic: false,
    date: ifElse(
      identity,
      date => moment(date),
      always(null)
    )(dateFirstContact),
    caseFindingId
  });
  const { toast } = useToast();
  const {
    loading: loadingAssignees,
    data: { caseFindingAssignees: assignees } = { caseFindingAssignees: [] }
  } = useQuery(GET_CASE_FINDING_ASSIGNEES, {
    variables: { facilityIds }
  });
  const assigneeOptions = useMemo(
    () =>
      pipe(
        insert(0, { id: currentUser.id, fullName: "Assign To Me" }),
        map(
          applySpec({
            value: prop("id"),
            label: prop("fullName")
          })
        )
      )(assignees),
    [assignees]
  );

  const [addNewPrimaryCase] = useMutation(mutation, {
    refetchQueries: [{ query: GET_CASE_FINDINGS, variables: refetchVariables }]
  });
  const { data: { caseTypes } = { caseTypes: [] }, loading: typesLoading } =
    useQuery(GET_CASE_TYPES, {
      variables: { facilityId }
    });
  const { data: { cancerSites } = { cancerSites: [] }, loading: sitesLoading } =
    useQuery(GET_CANCER_SITES);
  const disabled = useMemo(() => !hasAllProps(primaryData), [primaryData]);
  const handleCheckboxChange = useCallback(
    e => setPrimaryData(assoc(e.target.name, e.target.checked)),
    [setPrimaryData]
  );
  const handleDateChange = useCallback(
    val =>
      val ? setPrimaryData(assoc("date", val)) : setPrimaryData(dissoc("date")),
    [setPrimaryData]
  );
  const handleSelectChange = useCallback(
    (val, { name }) =>
      setPrimaryData(pipe(split("-"), head, assoc(__, val))(name)),
    [setPrimaryData]
  );
  const handleSubmit = useCallback(
    e => {
      e.preventDefault();
      if (!disabled) {
        addNewPrimaryCase({
          variables: {
            ...primaryData,
            assigneeId: ifElse(
              identity,
              type => type.value,
              always(null)
            )(primaryData.assigneeId),
            date: ifElse(
              identity,
              date => date.format("YYYY-MM-DD"),
              always(null)
            )(primaryData.date),
            type: ifElse(
              identity,
              type => type.value,
              always(null)
            )(primaryData.type),
            site: ifElse(
              identity,
              site => site.value,
              always(null)
            )(primaryData.site),
            analytic: primaryData.analytic
          },
          onCompleted: ({ addNewPrimaryCase: addNewPrimaryCaseResult }) => {
            if (addNewPrimaryCaseResult?.errors) {
              addNewPrimaryCaseResult.errors?.fullMessages?.forEach(message => {
                toast({
                  variant: "error",
                  description: message
                });
              });
            } else {
              toast({
                variant: "success",
                description: "New Case created."
              });
            }
          }
        });
        toggleMenu();
      }
    },
    [addNewPrimaryCase, toggleMenu, disabled, primaryData]
  );
  const typeOptions = useMemo(() => createOptions(caseTypes), [caseTypes]);
  const siteOptions = useMemo(() => createOptions(cancerSites), [cancerSites]);

  return {
    disabled,
    handleCheckboxChange,
    handleDateChange,
    handleSelectChange,
    handleSubmit,
    loadingAssignees,
    assigneeOptions,
    primaryData,
    siteOptions,
    sitesLoading,
    typeOptions,
    typesLoading
  };
};
