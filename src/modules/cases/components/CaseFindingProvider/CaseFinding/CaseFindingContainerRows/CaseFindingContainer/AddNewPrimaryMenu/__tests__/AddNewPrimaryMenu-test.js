import { create } from "react-test-renderer";
import { Provider } from "react-redux";
import { MockedProvider } from "@apollo/client/testing";
import { IntlProvider } from "react-intl";
import mocks from "../mocks";
import AddNewPrimaryMenu from "..";

jest.mock("react-select", () => "ReactSelect");
jest.mock("react-datepicker", () => "DatePicker");
jest.mock("shared/components/Menu", () => "Menu");

const render = props => {
  const store = {
    getState: jest.fn(() => ({ app: {} })),
    dispatch: jest.fn(),
    subscribe: jest.fn()
  };

  return {
    dispatch: store.dispatch,
    component: create(
      <Provider store={store}>
        <IntlProvider>
          <MockedProvider mocks={mocks} addTypename={false}>
            <AddNewPrimaryMenu {...props} />
          </MockedProvider>
        </IntlProvider>
      </Provider>
    )
  };
};

describe("AddNewPrimaryMenu", () => {
  test("renders component with select in loading", () => {
    const component = render({ currentUser: { id: 2 } });

    expect(component).toMatchSnapshot();
  });

  test("renders loaded component with query options", () => {
    const component = render({ currentUser: { id: 2 } });

    expect(component).toMatchSnapshot();
  });
});
