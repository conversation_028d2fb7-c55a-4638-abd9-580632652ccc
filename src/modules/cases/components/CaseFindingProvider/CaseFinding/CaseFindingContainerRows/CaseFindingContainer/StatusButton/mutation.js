import gql from "graphql-tag";

export const UPDATE_STATUS = gql`
  mutation updateCaseFinding(
    $findingId: ID!
    $status: Int!
    $nonReportableText: String
  ) {
    updateCaseFinding(
      findingId: $findingId
      status: $status
      nonReportableText: $nonReportableText
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export default UPDATE_STATUS;
