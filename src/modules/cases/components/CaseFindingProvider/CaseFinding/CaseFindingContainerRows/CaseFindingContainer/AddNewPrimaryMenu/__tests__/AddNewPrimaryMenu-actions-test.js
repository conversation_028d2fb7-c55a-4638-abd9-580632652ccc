import { act, create } from "react-test-renderer";
import { Provider } from "react-redux";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import DatePicker from "react-datepicker";
import Select from "react-select";
import { IntlProvider } from "react-intl";
import mocks from "../mocks";
import AddNewPrimaryMenu from "..";
import moment from "moment";

const mockMutationFn = jest.fn();

jest.mock("@apollo/client", () => ({
  ...jest.requireActual("@apollo/client"),
  useMutation: () => [mockMutationFn]
}));
jest.mock("shared/components/Menu", () => "Menu");

const render = props => {
  const store = {
    getState: jest.fn(() => ({ app: {} })),
    dispatch: jest.fn(),
    subscribe: jest.fn()
  };

  return {
    dispatch: store.dispatch,
    component: create(
      <Provider store={store}>
        <IntlProvider>
          <MockedProvider mocks={mocks} addTypename={false}>
            <AddNewPrimaryMenu {...props} />
          </MockedProvider>
        </IntlProvider>
      </Provider>
    )
  };
};

describe("AddNewPrimaryMenu", () => {
  test("tests handleSubmit when disabled", async () => {
    // const mockMutationFn = jest.fn();
    const { component } = render({ currentUser: { id: 2 } });

    await act(() => wait(0));
    const instance = component.root;
    const buttons = instance.findAllByType("button");
    const testFn = jest.fn();

    act(() =>
      buttons[buttons.length - 1].props.onClick({ preventDefault: testFn })
    );
    expect(testFn).toHaveBeenCalled();
    expect(mockMutationFn).not.toHaveBeenCalled();
  });
  test("tests handleSubmit when not disabled", async () => {
    //    const mockMutationFn = jest.fn();
    const { component } = render({
      dateFirstContact: "03/01/2021",
      toggleMenu: jest.fn(),
      currentUser: { id: 2 }
    });
    const testTypeVal = { value: 1, label: "Type 1" };
    const testSiteVal = { value: 2, label: "Site 2" };

    await act(() => wait(0));
    const instance = component.root;
    const buttons = instance.findAllByType("button");
    const selects = instance.findAllByType(Select);

    act(() => selects[0].props.onChange(testTypeVal, { name: "type" }));
    act(() => selects[1].props.onChange(testSiteVal, { name: "site" }));
    await act(() => wait(0));

    act(() =>
      buttons[buttons.length - 1].props.onClick({ preventDefault: jest.fn() })
    );
    await act(() => wait(3));

    expect(mockMutationFn).toHaveBeenCalled();
  });
  test("tests handleCheckboxChange", async () => {
    const { component } = render({ currentUser: { id: 2 } });
    const testVal = {
      target: { name: "analytic", checked: true }
    };

    await act(() => wait(0));
    const instance = component.root;
    const inputs = instance.findAllByType("input");

    act(() => inputs[inputs.length - 1].props.onChange(testVal));
    await act(() => wait(0));
    const updateInputs = instance.findAllByType("input");

    expect(updateInputs[inputs.length - 1].props.checked).toBe(true);
  });
  test("tests handleDateChange", async () => {
    const { component } = render({ currentUser: { id: 2 } });
    const testVal = moment("03/01/2021", "MM/DD/YYYY");

    await act(() => wait(0));
    const instance = component.root;
    const datePickers = instance.findAllByType(DatePicker);

    act(() => datePickers[0].props.onChange(testVal));
    await act(() => wait(0));
    const updatedDatePickers = instance.findAllByType(DatePicker);

    expect(updatedDatePickers[0].props.selected).toBe(testVal);
    const buttons = instance.findAllByType("button");

    act(() => buttons[0].props.onClick());
    await act(() => wait(0));
    const finalDatePickers = instance.findAllByType(DatePicker);

    expect(finalDatePickers[0].props.selected).toBe(null);
  });
  test("tests handleSelectChange", async () => {
    const { component } = render({ currentUser: { id: 2 } });
    const testTypeVal = { value: 1, label: "Type 1" };
    const testSiteVal = { value: 2, label: "Site 2" };

    await act(() => wait(0));
    const instance = component.root;
    const selects = instance.findAllByType(Select);

    act(() => selects[0].props.onChange(testTypeVal, { name: "type" }));
    act(() => selects[1].props.onChange(testSiteVal, { name: "site" }));
    await act(() => wait(0));
    const updatedSelects = instance.findAllByType(Select);

    expect(updatedSelects[0].props.value).toBe(testTypeVal);
    expect(updatedSelects[1].props.value).toBe(testSiteVal);
  });
});
