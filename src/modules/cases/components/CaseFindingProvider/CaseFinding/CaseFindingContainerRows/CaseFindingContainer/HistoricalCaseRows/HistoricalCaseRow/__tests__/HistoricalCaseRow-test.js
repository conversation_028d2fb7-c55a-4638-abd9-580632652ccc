import { create } from "react-test-renderer";
import HistoricalCaseRow from "..";

jest.mock("react-select", () => "Select");
jest.mock("../HistoricalCaseGridColumn", () => "HistoricalCaseGridColumn");
jest.mock(
  "../HistoricalCaseGridColumn/HistoricalCaseGridCell",
  () => "HistoricalCaseGridCell"
);
jest.mock("../UpdateCell", () => "UpdateCell");

const fakeRow = {
  id: "1151",
  patient: {
    firstContact: "2021-02-01T13:05:14-08:00",
    bornOn: "1982-10-14T16:00:00-07:00",
    firstName: "First",
    lastName: "Test",
    mrn: "34534334"
  },
  cancerSite: {
    name: "Digestive Other"
  },
  registryData: {
    accessionNumber: 4321,
    sequenceNumber: 1234,
    primarySite: "C181 | Appendix",
    histology: "8001/0 | Tumor cells, benign",
    laterality: "Paired site: midline tumor",
    dateOfLastContact: "07/23/2021",
    classOfCase: "12"
  },
  visit: {
    dateOfDiagnosis: "06/07/2021"
  },
  owner: "<PERSON><PERSON>",
  diagnosisCode: "40",
  caseFindingDiagnosisCode: "40"
};

describe("HistoricalCaseRow", () => {
  test("renders component", () => {
    const component = create(<HistoricalCaseRow {...fakeRow} />);

    expect(component).toMatchSnapshot();
  });
  test("renders component with match", () => {
    const component = create(<HistoricalCaseRow {...{ ...fakeRow }} />);

    expect(component).toMatchSnapshot();
  });
});
