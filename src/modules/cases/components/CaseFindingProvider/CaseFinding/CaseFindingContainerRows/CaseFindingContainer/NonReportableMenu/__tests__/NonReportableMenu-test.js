import { act, create } from "react-test-renderer";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import { IntlProvider } from "react-intl";
import mocks from "../mocks";
import { NonReportableMenu } from "..";

jest.mock("shared/components/Menu", () => "Menu");

const render = (props = { open: true, rowId: "1", toggleMenu: jest.fn() }) =>
  create(
    <IntlProvider>
      <MockedProvider mocks={mocks} addTypename={false}>
        <NonReportableMenu {...props} />
      </MockedProvider>
    </IntlProvider>
  );

describe("NonReportableMenu", () => {
  test("renders component in loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("renders loaded component", async () => {
    const component = render();

    await act(() => wait(300));

    expect(component).toMatchSnapshot();
  });
});
