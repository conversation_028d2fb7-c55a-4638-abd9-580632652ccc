/* eslint-disable max-statements */
import { and, applySpec, map, prop } from "ramda";
import { useCallback, useMemo, useState } from "react";
import { useMutation } from "@apollo/client";
import { GET_CASE_FINDINGS } from "../../../../../../query";
import { CREATE_CASE_FINDING_TASK, COMPLETE_CASE_FINDING } from "./mutation";
import { useToast } from "@q-centrix/q-components-react";

const taskTypes = [
  {
    id: 1,
    taskType: "Follow-Up",
    taskName: "Follow Up"
  },
  {
    id: 2,
    taskType: "Treatment Update",
    taskName: "Treatment Update"
  }
];

export const useComponentLogic = props => {
  const { caseFindingId, caseId, questionnaireResponseId, refetchVariables } =
    props;
  const urlCompleteNow = useMemo(
    () => `/registries2/abstraction/${questionnaireResponseId}`,
    [questionnaireResponseId]
  );
  const [selectedTaskType, setSelectedTaskType] = useState(null);
  const showCompleteNow = and(selectedTaskType, questionnaireResponseId);
  const options = useMemo(
    () =>
      map(
        applySpec({ value: prop("taskType"), label: prop("taskName") }),
        taskTypes
      ),
    [taskTypes]
  );
  const { toast } = useToast();
  const [createCaseFindingTask] = useMutation(CREATE_CASE_FINDING_TASK, {
    refetchQueries: [
      {
        query: GET_CASE_FINDINGS,
        variables: refetchVariables
      }
    ]
  });
  const [completeCaseFinding] = useMutation(COMPLETE_CASE_FINDING, {
    refetchQueries: [
      {
        query: GET_CASE_FINDINGS,
        variables: refetchVariables
      }
    ]
  });
  const handleTaskTypeChange = useCallback(
    value => setSelectedTaskType(value),
    [setSelectedTaskType]
  );
  const handleSendToTeamClick = useCallback(
    e => {
      e.preventDefault();
      if (selectedTaskType) {
        createCaseFindingTask({
          variables: {
            taskType: selectedTaskType.value,
            caseId,
            caseFindingId
          },
          onCompleted: ({
            createCaseFindingTask: createCaseFindingTaskResult
          }) => {
            if (createCaseFindingTaskResult?.errors) {
              createCaseFindingTaskResult.errors?.fullMessages?.forEach(
                message => {
                  toast({
                    variant: "error",
                    description: message
                  });
                }
              );
            } else {
              toast({
                variant: "success",
                description: "Case updated in My Team's Incomplete Cases."
              });
            }
          }
        });
      }
    },
    [selectedTaskType, createCaseFindingTask]
  );
  const handleCompleteNowClick = useCallback(
    e => {
      e.preventDefault();
      window.open(urlCompleteNow, "_blank");
      completeCaseFinding({
        variables: {
          caseFindingId
        }
      });
    },
    [completeCaseFinding]
  );

  return {
    options,
    selectedTaskType,
    showCompleteNow,
    urlCompleteNow,
    handleTaskTypeChange,
    handleSendToTeamClick,
    handleCompleteNowClick
  };
};
