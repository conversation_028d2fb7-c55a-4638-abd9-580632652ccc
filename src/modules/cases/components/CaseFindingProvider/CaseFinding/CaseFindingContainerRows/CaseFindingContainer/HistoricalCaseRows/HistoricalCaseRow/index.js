import classnames from "classnames";
import { useComponentLogic } from "./hooks";
import HistoricalCaseGridColumn from "./HistoricalCaseGridColumn";
import HistoricalCaseGridCell from "./HistoricalCaseGridColumn/HistoricalCaseGridCell";
import UpdateCell from "./UpdateCell";

export const HistoricalCaseRow = ({
  caseFindingId,
  id: caseId,
  questionnaireResponseId,
  refetchVariables,
  currentUserAssignedToCaseFinding,
  status,
  ...props
}) => {
  const { columns, headers, icdMatch } = useComponentLogic(props);
  const headerClassName = classnames("grid-header");

  return (
    <tr>
      <td colSpan="9">
        <div className="historical-case-row ">
          <div className="historical-case-row-left ">
            <div className={`${headerClassName}`}>
              {headers.map((header, _i) => (
                <HistoricalCaseGridCell
                  key={header}
                  match={icdMatch}
                  status={status}
                  {...header}
                />
              ))}
            </div>
            <div className="grid">
              {columns.map(({ rows, id }) => (
                <HistoricalCaseGridColumn key={id} rows={rows} />
              ))}
              <UpdateCell
                caseFindingId={caseFindingId}
                caseId={caseId}
                questionnaireResponseId={questionnaireResponseId}
                refetchVariables={refetchVariables}
                currentUserAssignedToCaseFinding={
                  currentUserAssignedToCaseFinding
                }
              />
            </div>
          </div>
        </div>
      </td>
    </tr>
  );
};

HistoricalCaseRow.defaultProps = {
  icdMatch: false,
  cancerSite: {},
  registryData: {}
};

export default HistoricalCaseRow;
