import gql from "graphql-tag";

export const CREATE_CASE_FINDING_TASK = gql`
  mutation createCaseFindingTask(
    $taskType: String!
    $caseId: ID!
    $caseFindingId: ID!
  ) {
    createCaseFindingTask(
      taskType: $taskType
      caseId: $caseId
      caseFindingId: $caseFindingId
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const COMPLETE_CASE_FINDING = gql`
  mutation completeCaseFinding($caseFindingId: ID!) {
    completeCaseFinding(caseFindingId: $caseFindingId) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export default {
  CREATE_CASE_FINDING_TASK,
  COMPLETE_CASE_FINDING
};
