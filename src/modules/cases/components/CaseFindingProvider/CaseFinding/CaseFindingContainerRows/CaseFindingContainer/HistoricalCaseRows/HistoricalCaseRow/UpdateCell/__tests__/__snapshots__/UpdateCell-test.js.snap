// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`UpdateCell renders component with select in loading 1`] = `
<div
  className="historical-case-row-right"
>
  <ReactSelect
    className="historical-case-row-selector"
    classNamePrefix="historical-case-row-selector"
    components={
      Object {
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    isDisabled={true}
    name="task-type"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Follow Up",
          "value": "Follow-Up",
        },
        Object {
          "label": "Treatment Update",
          "value": "Treatment Update",
        },
      ]
    }
    placeholder="Task Type"
    value={null}
  />
  <div
    className="actions"
  >
    <button
      className="action disabled"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      Send to Team
    </button>
  </div>
</div>
`;

exports[`UpdateCell renders correctly when follow up selected 1`] = `
<div
  className="historical-case-row-right"
>
  <ReactSelect
    className="historical-case-row-selector"
    classNamePrefix="historical-case-row-selector"
    components={
      Object {
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    isDisabled={true}
    name="task-type"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Follow Up",
          "value": "Follow-Up",
        },
        Object {
          "label": "Treatment Update",
          "value": "Treatment Update",
        },
      ]
    }
    placeholder="Task Type"
    value={
      Object {
        "label": "Follow Up",
        "value": "Follow-Up",
      }
    }
  />
  <div
    className="actions"
  >
    <div>
      <a
        className="action"
        href="/registries2/abstraction/1"
        onClick={[Function]}
        rel="noopener noreferrer"
        target="_blank"
      >
        Complete Now
      </a>
    </div>
    <button
      className="action"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      Send to Team
    </button>
  </div>
</div>
`;

exports[`UpdateCell renders loaded component with query options 1`] = `
<div
  className="historical-case-row-right"
>
  <ReactSelect
    className="historical-case-row-selector"
    classNamePrefix="historical-case-row-selector"
    components={
      Object {
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    isDisabled={true}
    name="task-type"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Follow Up",
          "value": "Follow-Up",
        },
        Object {
          "label": "Treatment Update",
          "value": "Treatment Update",
        },
      ]
    }
    placeholder="Task Type"
    value={null}
  />
  <div
    className="actions"
  >
    <button
      className="action disabled"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      Send to Team
    </button>
  </div>
</div>
`;

exports[`UpdateCell renders without 'Complete Now' button since no linked questionnaireId 1`] = `
<div
  className="historical-case-row-right"
>
  <ReactSelect
    className="historical-case-row-selector"
    classNamePrefix="historical-case-row-selector"
    components={
      Object {
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    isDisabled={true}
    name="task-type"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Follow Up",
          "value": "Follow-Up",
        },
        Object {
          "label": "Treatment Update",
          "value": "Treatment Update",
        },
      ]
    }
    placeholder="Task Type"
    value={
      Object {
        "label": "Follow Up",
        "value": "Follow-Up",
      }
    }
  />
  <div
    className="actions"
  >
    <button
      className="action"
      disabled={false}
      onClick={[Function]}
      type="button"
    >
      Send to Team
    </button>
  </div>
</div>
`;
