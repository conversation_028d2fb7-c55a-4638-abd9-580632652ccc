import shallowRender from "helpers/test/shallowRender";
import HistoricalCaseGridCell from "..";

const fakeCells = [
  { id: 1, title: "First Name: ", value: "<PERSON>" },
  { id: 2, title: "DOB: ", value: new Date("March 10, 2020"), isDate: true }
];

describe("HistoricalCaseGridCell", () => {
  test("renders component", () => {
    const component = shallowRender(
      <HistoricalCaseGridCell {...fakeCells[0]} />
    );

    expect(component).toMatchSnapshot();
  });
  test("renders component with isDate", () => {
    const component = shallowRender(
      <HistoricalCaseGridCell {...fakeCells[1]} />
    );

    expect(component).toMatchSnapshot();
  });
});
