import classnames from "classnames";
import { useComponentLogic } from "./hooks";

export const StatusButton = props => {
  const { id, selected, text, caret } = props;
  const buttonClassName = classnames({ [`btn-${id}`]: true, selected });
  const { handleClick } = useComponentLogic(props);

  return (
    <button
      className={`tw-border-[#015F86]; tw-flex tw-h-9 tw-cursor-pointer tw-flex-row tw-items-center tw-justify-center tw-rounded-[18px] tw-border-2 tw-border-solid tw-px-4 tw-py-2 tw-text-sm tw-font-extrabold ${buttonClassName}`}
      onClick={handleClick}
      type="button"
    >
      {text}
      {caret && <i className="fas fa-caret-down" />}
    </button>
  );
};

StatusButton.defaultProps = {
  selected: false,
  text: "Status Button",
  // eslint-disable-next-line no-empty-function
  toggleMenu: () => {}
};

export default StatusButton;
