import { MockedProvider } from "@apollo/client/testing";
import { act, create } from "react-test-renderer";
import wait from "waait";
import mocks from "modules/cases/components/CaseFindingProvider/mocks";
import StatusButton from "..";

const mockedComponent = props =>
  create(
    <MockedProvider mocks={mocks} addTypename={false}>
      <StatusButton {...props} />
    </MockedProvider>
  );

describe("StatusButton", () => {
  test("renders component", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
  test("tests handleClick with id === 2", async () => {
    const toggleMenu = jest.fn();
    const setReportableDialogOpen = jest.fn();
    const component = mockedComponent({
      id: 2,
      selected: false,
      toggleMenu,
      setReportableDialogOpen
    });
    const instance = component.root;
    const button = instance.findByType("button");

    act(() => button.props.onClick());
    await act(() => wait(100));
    expect(setReportableDialogOpen).toHaveBeenCalledWith(true);
  });
  test("tests handleClick with id !== 2", async () => {
    const toggleMenu = jest.fn();
    const component = mockedComponent({
      findingId: "1",
      id: 1,
      refetchVariables: {
        currentPage: 1,
        direction: "asc",
        key: "",
        rowsPerPage: 4
      },
      selected: false,
      toggleMenu
    });
    const instance = component.root;
    const button = instance.findByType("button");

    act(() => button.props.onClick());
    await act(() => wait(100));
    expect(toggleMenu).toHaveBeenCalled();
  });
});
