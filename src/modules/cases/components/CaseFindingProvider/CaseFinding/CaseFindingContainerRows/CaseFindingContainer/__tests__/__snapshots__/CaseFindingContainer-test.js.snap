// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseFindingContainer renders component with hasICDMatch=true 1`] = `
<div
  className="case-finding-container-row tw-mt-3 tw-px-0 tw-py-3.5 tw-text-xs"
>
  <table
    className="case-finding-table open"
  >
    <thead>
      <tr>
        <td>
          <button
            disabled={true}
            onClick={[Function]}
            type="button"
          >
            <Spinner />
          </button>
        </td>
        <td>
          <div
            className="checkbox-label-container tw-flex tw-items-center tw-justify-evenly tw-text-base tw-font-medium"
          >
            <label
              className="checkbox-container tw-relative tw-block tw-h-6 tw-w-6 tw-cursor-pointer tw-self-start tw-font-[normal] tw-text-[0.6875rem] tw-text-[#227b9c]"
            >
              <input
                checked={false}
                className="tw-m-0 tw-h-6 tw-w-6 tw-cursor-pointer tw-appearance-none tw-p-0"
                id="select-1"
                name="select-1"
                onChange={[Function]}
                type="checkbox"
              />
              <span
                className="custom-checkbox tw-top-0; tw-absolute tw-left-0 tw-flex tw-h-6 tw-w-6 tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-items-center tw-justify-between tw-rounded-[3px] tw-border-2 tw-border-solid tw-border-[rgba(1,95,134,0.87)] tw-bg-transparent tw-px-0 tw-py-1 tw-leading-[1.6] tw-text-[#227b9c]
"
              />
            </label>
            <label
              className="checkbox-label checkbox-label-empty tw-hidden tw-self-start tw-pl-8 tw-font-[normal] tw-text-[0.6875rem] tw-text-[#227b9c]
"
              htmlFor="select-1"
            />
          </div>
        </td>
        <td>
          Test Facility
        </td>
        <td />
        <td>
          00112234
        </td>
        <td
          className="unassigned"
        >
          Unassigned
        </td>
        <td>
          Mar 1, 2021 12:00
        </td>
        <td>
          <div
            className="discharge-date"
          >
            Jan 12, 2019 12:00
            <i
              className="fas fa-history"
            />
          </div>
        </td>
        <td>
          <StatusButton
            caret={true}
            disabled={true}
            findingId="1"
            selected={true}
            text="Reportable"
            toggleMenu={[Function]}
          />
          <StatusMenu
            findingId="1"
            open={false}
            setReportableDialogOpen={[Function]}
            toggleMenu={[Function]}
          />
          <NonReportableMenu
            open={false}
            rowId="1"
            toggleMenu={[Function]}
          />
        </td>
        <td
          className="tw-relative tw-w-[6%] tw-rounded-[0_2px_2px_0] tw-border-r tw-p-0 tw-text-center"
        >
          <button
            className="tw-w-full tw-cursor-pointer tw-border-0 tw-text-[#227b9c]"
            disabled={true}
            onClick={[Function]}
            type="button"
          >
            <i
              className="fal fa-plus tw-text-2xl tw-text-[rgba(1,95,134,0.7)]"
            />
          </button>
          <AddNewPrimaryMenu
            caseFindingId="1"
            dateFirstContact="Mon, 01 Mar 2021 00:00:00 GMT"
            open={false}
            toggleMenu={[Function]}
          />
        </td>
      </tr>
    </thead>
    <tbody
      className="tw-block tw-max-h-[33.5rem] tw-w-full tw-overflow-y-auto tw-rounded-sm tw-rounded-t-none"
    >
      <HistoricalCaseRows
        caseFindingId="1"
        currentUserAssigned={false}
        open={true}
        rows={Array []}
      />
    </tbody>
  </table>
</div>
`;

exports[`CaseFindingContainer renders component with hasICDMatch=undefined, defaults to false 1`] = `
<div
  className="case-finding-container-row tw-mt-3 tw-px-0 tw-py-3.5 tw-text-xs"
>
  <table
    className="case-finding-table"
  >
    <thead>
      <tr>
        <td>
          <button
            disabled={true}
            onClick={[Function]}
            type="button"
          >
            <Spinner />
          </button>
        </td>
        <td>
          <div
            className="checkbox-label-container tw-flex tw-items-center tw-justify-evenly tw-text-base tw-font-medium"
          >
            <label
              className="checkbox-container tw-relative tw-block tw-h-6 tw-w-6 tw-cursor-pointer tw-self-start tw-font-[normal] tw-text-[0.6875rem] tw-text-[#227b9c]"
            >
              <input
                checked={false}
                className="tw-m-0 tw-h-6 tw-w-6 tw-cursor-pointer tw-appearance-none tw-p-0"
                id="select-1"
                name="select-1"
                onChange={[Function]}
                type="checkbox"
              />
              <span
                className="custom-checkbox tw-top-0; tw-absolute tw-left-0 tw-flex tw-h-6 tw-w-6 tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-items-center tw-justify-between tw-rounded-[3px] tw-border-2 tw-border-solid tw-border-[rgba(1,95,134,0.87)] tw-bg-transparent tw-px-0 tw-py-1 tw-leading-[1.6] tw-text-[#227b9c]
"
              />
            </label>
            <label
              className="checkbox-label checkbox-label-empty tw-hidden tw-self-start tw-pl-8 tw-font-[normal] tw-text-[0.6875rem] tw-text-[#227b9c]
"
              htmlFor="select-1"
            />
          </div>
        </td>
        <td>
          Test Facility
        </td>
        <td />
        <td>
          00112234
        </td>
        <td
          className="unassigned"
        >
          Unassigned
        </td>
        <td>
          Mar 1, 2021 12:00
        </td>
        <td>
          <div
            className="discharge-date"
          >
            Jan 12, 2019 12:00
            <i
              className="fas fa-history"
            />
          </div>
        </td>
        <td>
          <StatusButton
            caret={true}
            disabled={true}
            findingId="1"
            selected={true}
            text="Reportable"
            toggleMenu={[Function]}
          />
          <StatusMenu
            findingId="1"
            open={false}
            setReportableDialogOpen={[Function]}
            toggleMenu={[Function]}
          />
          <NonReportableMenu
            open={false}
            rowId="1"
            toggleMenu={[Function]}
          />
        </td>
        <td
          className="tw-relative tw-w-[6%] tw-rounded-[0_2px_2px_0] tw-border-r tw-p-0 tw-text-center"
        >
          <button
            className="tw-w-full tw-cursor-pointer tw-border-0 tw-text-[#227b9c]"
            disabled={true}
            onClick={[Function]}
            type="button"
          >
            <i
              className="fal fa-plus tw-text-2xl tw-text-[rgba(1,95,134,0.7)]"
            />
          </button>
          <AddNewPrimaryMenu
            caseFindingId="1"
            dateFirstContact="Mon, 01 Mar 2021 00:00:00 GMT"
            open={false}
            toggleMenu={[Function]}
          />
        </td>
      </tr>
    </thead>
    <tbody
      className="tw-block tw-max-h-[33.5rem] tw-w-full tw-overflow-y-auto tw-rounded-sm tw-rounded-t-none"
    >
      <HistoricalCaseRows
        caseFindingId="1"
        currentUserAssigned={false}
        open={false}
        rows={Array []}
      />
    </tbody>
  </table>
</div>
`;
