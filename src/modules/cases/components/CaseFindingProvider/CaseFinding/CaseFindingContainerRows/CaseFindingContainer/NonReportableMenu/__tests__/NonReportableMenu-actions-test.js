import { act, create } from "react-test-renderer";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import { IntlProvider } from "react-intl";
import mocks from "../../../../../mocks";
import { NonReportableMenu } from "..";

jest.mock("shared/components/Menu", () => "Menu");

const render = (
  props = {
    open: true,
    refetchVariables: {
      currentPage: 1,
      direction: "asc",
      key: "",
      rowsPerPage: 4
    },
    rowId: "1",
    toggleMenu: jest.fn()
  }
) =>
  create(
    <IntlProvider>
      <MockedProvider mocks={mocks} addTypename={false}>
        <NonReportableMenu {...props} />
      </MockedProvider>
    </IntlProvider>
  );

describe("NonReportableMenu", () => {
  test("handleChange works", async () => {
    const event = {
      preventDefault: jest.fn(),
      target: { value: "Tester" }
    };
    const spy = jest.spyOn(event, "preventDefault");
    const component = render();

    await act(() => wait(300));
    const instance = component.root;
    const [beforeTextarea] = instance.findAllByType("textarea");

    act(() => beforeTextarea.props.onChange(event));
    await act(() => wait(3));
    const [afterTextarea] = instance.findAllByType("textarea");

    expect(spy).toHaveBeenCalled();
    expect(afterTextarea.props.value).toBe("Tester");
  });
  test("handleClick works", async () => {
    const event = {
      preventDefault: jest.fn(),
      target: { value: "Tester" }
    };
    const spy = jest.spyOn(event, "preventDefault");
    const component = render();

    await act(() => wait(300));
    const instance = component.root;
    const [beforeButton] = instance.findAllByType("button");

    await act(() => beforeButton.props.onClick(event));
    await act(() => wait(300));
    expect(spy).toHaveBeenCalled();
  });
});
