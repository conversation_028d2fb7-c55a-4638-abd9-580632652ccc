import { CREATE_CASE_FINDING_TASK, COMPLETE_CASE_FINDING } from "./mutation";

const mutationSuccessResult = {
  data: { createCaseFindingTask: { response: true, errors: null } }
};
const completeCaseFindingMutationSuccessResult = {
  data: { completeCaseFinding: { response: true, errors: null } }
};

const mocks = [
  {
    request: {
      query: CREATE_CASE_FINDING_TASK,
      variables: { taskType: "follow-up" }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: COMPLETE_CASE_FINDING,
      variables: { caseFindingId: 1 }
    },
    result: completeCaseFindingMutationSuccessResult
  }
];

export default mocks;
