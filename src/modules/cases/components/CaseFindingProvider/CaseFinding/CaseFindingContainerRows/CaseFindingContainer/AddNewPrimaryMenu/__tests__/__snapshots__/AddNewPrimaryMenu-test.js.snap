// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddNewPrimaryMenu renders component with select in loading 1`] = `
Object {
  "component": <Menu
    menuClassName="add-new-primary-container"
  >
    <ReactSelect
      className="case-type-selector"
      classNamePrefix="case-type-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="type"
      onChange={[Function]}
      options={Array []}
      placeholder="Select Case Type"
      value={null}
    />
    <ReactSelect
      className="case-site-selector"
      classNamePrefix="case-site-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="site"
      onChange={[Function]}
      options={Array []}
      placeholder="Select Cancer Type"
      value={null}
    />
    <DatePicker
      className="tw-w-full tw-p-2 tw-text-base"
      dateFormat="MM/DD/YYYY"
      isClearable={true}
      name="dateFirstContact"
      onChange={[Function]}
      placeholderText="Date of First Contact"
      popperPlacement="top"
      selected={null}
    />
    <ReactSelect
      className="case-assign-selector"
      classNamePrefix="case-assign-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="assigneeId"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Assign To Me",
            "value": 2,
          },
        ]
      }
      placeholder="Assign To"
    />
    <label
      htmlFor="analytic"
    >
      <input
        checked={false}
        id="analytic"
        name="analytic"
        onChange={[Function]}
        type="checkbox"
      />
      <span>
        Analytic
      </span>
    </label>
    <button
      className="success-btn"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      create new primary
    </button>
  </Menu>,
  "dispatch": [MockFunction],
}
`;

exports[`AddNewPrimaryMenu renders loaded component with query options 1`] = `
Object {
  "component": <Menu
    menuClassName="add-new-primary-container"
  >
    <ReactSelect
      className="case-type-selector"
      classNamePrefix="case-type-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="type"
      onChange={[Function]}
      options={Array []}
      placeholder="Select Case Type"
      value={null}
    />
    <ReactSelect
      className="case-site-selector"
      classNamePrefix="case-site-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="site"
      onChange={[Function]}
      options={Array []}
      placeholder="Select Cancer Type"
      value={null}
    />
    <DatePicker
      className="tw-w-full tw-p-2 tw-text-base"
      dateFormat="MM/DD/YYYY"
      isClearable={true}
      name="dateFirstContact"
      onChange={[Function]}
      placeholderText="Date of First Contact"
      popperPlacement="top"
      selected={null}
    />
    <ReactSelect
      className="case-assign-selector"
      classNamePrefix="case-assign-selector"
      closeMenuOnSelect={true}
      hideSelectedOptions={true}
      isLoading={true}
      name="assigneeId"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Assign To Me",
            "value": 2,
          },
        ]
      }
      placeholder="Assign To"
    />
    <label
      htmlFor="analytic"
    >
      <input
        checked={false}
        id="analytic"
        name="analytic"
        onChange={[Function]}
        type="checkbox"
      />
      <span>
        Analytic
      </span>
    </label>
    <button
      className="success-btn"
      disabled={true}
      onClick={[Function]}
      type="button"
    >
      create new primary
    </button>
  </Menu>,
  "dispatch": [MockFunction],
}
`;
