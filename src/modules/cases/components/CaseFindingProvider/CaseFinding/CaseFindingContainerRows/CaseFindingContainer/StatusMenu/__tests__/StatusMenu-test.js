import { create } from "react-test-renderer";
import StatusMenu from "..";

jest.mock("../../StatusButton", () => "StatusButton");
jest.mock("shared/components/Menu", () => "Menu");

const mockedComponent = props => create(<StatusMenu {...props} />);

describe("StatusMenu", () => {
  test("renders component", () => {
    const component = mockedComponent();

    expect(component).toMatchSnapshot();
  });
});
