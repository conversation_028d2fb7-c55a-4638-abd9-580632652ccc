// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StatusMenu renders component 1`] = `
<Menu
  menuClassName="status-menu"
  open={false}
  toggleMenu={[Function]}
>
  <StatusButton
    id={1}
    text="Reportable"
    toggleMenu={[Function]}
  />
  <StatusButton
    id={2}
    text="Non-Reportable"
    toggleMenu={[Function]}
  />
  <StatusButton
    id={3}
    text="Reportable by Agreement"
    toggleMenu={[Function]}
  />
</Menu>
`;
