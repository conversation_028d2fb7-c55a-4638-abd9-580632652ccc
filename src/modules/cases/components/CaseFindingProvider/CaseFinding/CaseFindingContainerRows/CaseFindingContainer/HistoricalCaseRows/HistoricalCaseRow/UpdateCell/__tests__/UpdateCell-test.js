import { act, create } from "react-test-renderer";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import Select from "react-select";
import mocks from "../mocks";
import { IntlProvider } from "react-intl";
import UpdateCell from "..";

jest.mock("react-select", () => "ReactSelect");

const render = ({ questionnaireResponseId = null }) =>
  create(
    <IntlProvider>
      <MockedProvider mocks={mocks} addTypename={false}>
        <UpdateCell questionnaireResponseId={questionnaireResponseId} />
      </MockedProvider>
    </IntlProvider>
  );

describe("UpdateCell", () => {
  test("renders component with select in loading", () => {
    const component = render({});

    expect(component).toMatchSnapshot();
  });

  test("renders loaded component with query options", async () => {
    const component = render({});

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("renders without 'Complete Now' button since no linked questionnaireId", async () => {
    const component = render({});
    const instance = component.root;
    const [select] = instance.findAllByType(Select);

    act(() =>
      select.props.onChange({ value: "Follow-Up", label: "Follow Up" })
    );
    await act(() => wait(3));

    expect(component).toMatchSnapshot();
  });

  test("renders correctly when follow up selected", async () => {
    const component = render({ questionnaireResponseId: 1 });
    const instance = component.root;
    const [select] = instance.findAllByType(Select);

    act(() =>
      select.props.onChange({ value: "Follow-Up", label: "Follow Up" })
    );
    await act(() => wait(3));

    expect(component).toMatchSnapshot();
  });
});
