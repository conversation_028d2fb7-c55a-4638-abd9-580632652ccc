import shallowRender from "helpers/test/shallowRender";
import HistoricalCaseGridColumn from "..";

const fakeRow = [
  { id: 1, title: "Last Name: ", value: "<PERSON><PERSON>" },
  { id: 2, title: "First Name: ", value: "<PERSON>" },
  { id: 3, title: "DOB: ", value: "2020-03-10T00:00:00.000Z", isDate: true }
];

jest.mock("../HistoricalCaseGridCell", () => "HistoricalCaseGridCell");

describe("HistoricalCaseGridColumn", () => {
  test("renders component", () => {
    const component = shallowRender(
      <HistoricalCaseGridColumn rows={fakeRow} />
    );

    expect(component).toMatchSnapshot();
  });
});
