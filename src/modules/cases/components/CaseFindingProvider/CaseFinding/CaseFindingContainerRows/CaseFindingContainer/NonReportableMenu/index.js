import Menu from "shared/components/Menu";
import { useComponentLogic } from "./hooks";

export const NonReportableMenu = props => {
  const { open, toggleMenu } = props;
  const { disabled, handleChange, handleClick, value } =
    useComponentLogic(props);

  return (
    <Menu
      menuClassName="non-reportable-container"
      open={open}
      toggleMenu={toggleMenu}
    >
      <p>
        Please provide reason why this is not reportable
        <span className="red">*</span>
      </p>
      <textarea
        name="non-reportable-input"
        onChange={handleChange}
        value={value}
      />
      <button
        className="success-btn"
        disabled={disabled}
        onClick={handleClick}
        type="button"
      >
        Mark Non-Reportable
      </button>
    </Menu>
  );
};

export default NonReportableMenu;
