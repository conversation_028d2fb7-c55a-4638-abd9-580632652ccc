import classnames from "classnames";
import { formatDateTime } from "utils/fp";
import { Spinner } from "@q-centrix/q-components-react";
import AddNewPrimaryMenu from "./AddNewPrimaryMenu";
import HistoricalCaseRows from "./HistoricalCaseRows";
import NonReportableMenu from "./NonReportableMenu";
import StatusButton from "./StatusButton";
import StatusMenu from "./StatusMenu";
import { useComponentLogic } from "./hooks";
import { Checkbox } from "shared/components/Checkbox";

// eslint-disable-next-line complexity
export const CaseFindingContainer = props => {
  const {
    checked,
    facility,
    diagnosisCode,
    dischargedAt,
    id,
    patient,
    refetchVariables,
    facilityIds,
    currentUser,
    firstContact
  } = props;
  const { mrn } = patient;
  const { name } = facility;
  const {
    disabled,
    handleChange,
    handleOpen,
    loading,
    open,
    primaryMenuOpen,
    reportableDialogOpen,
    rows,
    setReportableDialogOpen,
    statusMenuOpen,
    togglePrimaryMenu,
    toggleReportableMenu,
    toggleStatusMenu,
    unassigned,
    unassignedText,
    currentUserAssigned,
    showHideCasesWithOrWithoutMatches
  } = useComponentLogic(props);

  const assigneeClassName = classnames({ unassigned });
  const tableClassName = classnames("case-finding-table", { open });

  if (showHideCasesWithOrWithoutMatches) return null;

  return (
    <div className="case-finding-container-row tw-mt-3 tw-px-0 tw-py-3.5 tw-text-xs">
      <table className={`${tableClassName}`}>
        <thead>
          <tr>
            <td>
              <button type="button" disabled={disabled} onClick={handleOpen}>
                {loading ? <Spinner /> : <i className="fas fa-caret-down" />}
              </button>
            </td>
            <td>
              <Checkbox
                name={`select-${id}`}
                checked={checked || false}
                onChange={handleChange}
              />
            </td>
            <td>{name}</td>
            <td>{diagnosisCode}</td>
            <td>{mrn}</td>
            <td className={assigneeClassName}>{unassignedText}</td>
            <td>{formatDateTime(firstContact)}</td>
            <td>
              <div className="discharge-date">
                {formatDateTime(dischargedAt)}
                <i className="fas fa-history" />
              </div>
            </td>
            <td>
              <StatusButton
                findingId={id}
                text="Reportable"
                selected
                toggleMenu={toggleStatusMenu}
                disabled={!currentUserAssigned}
                caret
              />
              <StatusMenu
                findingId={id}
                open={statusMenuOpen}
                refetchVariables={refetchVariables}
                setReportableDialogOpen={setReportableDialogOpen}
                toggleMenu={toggleStatusMenu}
              />
              <NonReportableMenu
                open={reportableDialogOpen}
                refetchVariables={refetchVariables}
                rowId={id}
                toggleMenu={toggleReportableMenu}
              />
            </td>
            <td className="tw-relative tw-w-[6%] tw-rounded-[0_2px_2px_0] tw-border-r tw-p-0 tw-text-center">
              <button
                type="button"
                className="tw-w-full tw-cursor-pointer tw-border-0 tw-text-[#227b9c]"
                onClick={togglePrimaryMenu}
                disabled={!currentUserAssigned}
              >
                <i className="fal fa-plus tw-text-2xl tw-text-[rgba(1,95,134,0.7)]" />
              </button>
              <AddNewPrimaryMenu
                dateFirstContact={firstContact}
                open={primaryMenuOpen}
                toggleMenu={togglePrimaryMenu}
                facilityId={facility.id}
                caseFindingId={id}
                refetchVariables={refetchVariables}
                facilityIds={facilityIds}
                currentUser={currentUser}
              />
            </td>
          </tr>
        </thead>
        <tbody className="tw-block tw-max-h-[33.5rem] tw-w-full tw-overflow-y-auto tw-rounded-sm tw-rounded-t-none">
          <HistoricalCaseRows
            open={open}
            rows={rows}
            caseFindingId={id}
            refetchVariables={refetchVariables}
            caseFindingDiagnosisCode={diagnosisCode}
            currentUserAssigned={currentUserAssigned}
          />
        </tbody>
      </table>
    </div>
  );
};

CaseFindingContainer.defaultProps = {
  dateFirstContact: new Date("March 1, 2021"),
  dischargedAt: new Date("March 2, 2021"),
  hasICDMatch: false,
  facility: "",
  icd10: 0,
  mrn: "",
  assignee: null
};

export default CaseFindingContainer;
