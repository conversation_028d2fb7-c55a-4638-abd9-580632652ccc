import { act, create } from "react-test-renderer";
import { decoratedApollo } from "utils/tests/decorated";
import wait from "waait";
import Select from "react-select";
import mocks from "../mocks";
import UpdateCell from "..";

const mockMutationFn = jest.fn();

jest.mock("@apollo/client", () => ({
  ...jest.requireActual("@apollo/client"),
  useMutation: () => [mockMutationFn]
}));
describe("UpdateCell", () => {
  function render(mock = mocks) {
    return create(
      decoratedApollo({
        component: UpdateCell,
        props: {
          questionnaireResponseId: 12,
          caseFindingId: 1
        },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mock
      })
    );
  }
  test("buttons enabled after value is selected", async () => {
    const component = render();

    await act(() => wait(100));
    const instance = component.root;
    const [beforeActionSendToTeamButton] = instance.findAllByType("button");
    const [beforeActionCompleteNowButton] = instance.findAllByType("a");
    const [select] = instance.findAllByType(Select);

    expect(beforeActionSendToTeamButton.props.disabled).toBeTruthy();
    expect(beforeActionCompleteNowButton).toBeUndefined();
    act(() =>
      select.props.onChange({ value: "Follow-Up", label: "Follow Up" })
    );
    await act(() => wait(3));

    const [afterActionSendToTeamButton] = instance.findAllByType("button");
    const [afterActionCompleteNowButton] = instance.findAllByType("a");

    expect(afterActionSendToTeamButton.props.disabled).toBeFalsy();
    expect(afterActionCompleteNowButton).toBeDefined();
  });

  test("redirects and calls mutation after the Complete Now button is clicked", async () => {
    // const mockMutationFn = jest.fn();

    const component = render();

    await act(() => wait(100));
    const instance = component.root;
    const [select] = instance.findAllByType(Select);

    act(() =>
      select.props.onChange({ value: "Follow-Up", label: "Follow Up" })
    );
    await act(() => wait(3));

    const [completeNowButton] = instance.findAllByType("a");

    const spy = jest.spyOn(window, "open");

    // eslint-disable-next-line no-empty-function
    completeNowButton.props.onClick({ preventDefault: () => {} });

    expect(mockMutationFn).toHaveBeenCalledWith({
      variables: { caseFindingId: 1 }
    });
    await act(() => wait(3));
    expect(spy).toHaveBeenCalledWith("/registries2/abstraction/12", "_blank");
  });
});
