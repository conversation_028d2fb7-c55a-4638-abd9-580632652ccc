import Pagination from "shared/components/Pagination";

const options = [60, 80, 100, 120, 140];

export const CaseFindingInfo = ({
  currentPage,
  rowsPerPage,
  setCurrentPage,
  setRowsPerPage,
  records
}) => (
  <div className="case-finding-info">
    <Pagination
      currentPage={currentPage}
      options={options}
      records={records}
      rowsPerPage={rowsPerPage}
      setCurrentPage={setCurrentPage}
      setRowsPerPage={setRowsPerPage}
    />
  </div>
);
CaseFindingInfo.defaultProps = {
  currentPage: 1,
  rowsPerPage: 4,
  // eslint-disable-next-line no-empty-function
  setCurrentPage: () => {},
  // eslint-disable-next-line no-empty-function
  setRowsPerPage: () => {},
  records: 0
};

export default CaseFindingInfo;
