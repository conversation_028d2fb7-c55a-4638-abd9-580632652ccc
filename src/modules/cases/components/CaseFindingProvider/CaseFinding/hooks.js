/* eslint-disable max-statements */
/* eslint-disable complexity */
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useQuery } from "@apollo/client";
import { isNullOrEmpty } from "utils/fp";
import casesSelectors from "modules/cases/redux/selectors";
import {
  __,
  all,
  always,
  assoc,
  dissoc,
  equals,
  head,
  identity,
  keys,
  omit,
  map,
  none,
  not,
  path,
  pipe,
  prop,
  remove,
  reduce,
  sortBy,
  tap,
  uniq,
  values,
  reject,
  applySpec,
  ifElse,
  has
} from "ramda";
import moment from "moment";
import { GET_CASE_FINDINGS } from "../query";
import { pushFlashMessage } from "shared/components/FlashMessage/actions";
import { useLocalStorage } from "shared/hooks/useLocalStorage";

const transformations = {
  facility: { text: "Facility", id: "facility", index: 0 },
  diagnosisCode: { text: "ICD-10", id: "diagnosisCode", index: 1 },
  mrn: { text: "MRN", id: "mrn", index: 2 },
  assignee: { text: "Assignee", id: "assignee", index: 3 },
  firstContact: { text: "Date of First Contact", id: "firstContact", index: 4 },
  dischargedAt: { text: "Discharge Date", id: "dischargedAt", index: 5 }
};
const createHeaderObjects = key =>
  map(id =>
    pipe(prop(__, transformations), assoc("selected", equals(key, id)))(id)
  );

const createChecksMap = data =>
  pipe(
    map(prop("id")),
    reduce((acc, key) => assoc(key, false, acc), {})
  )(data);
const formatDate = date => moment(date).format("YYYY-MM-DD");

const filterFunction = pipe(
  applySpec({
    startDate: pipe(
      path(["dateRange", "startDate"]),
      ifElse(identity, formatDate, always(null))
    ),
    endDate: pipe(
      path(["dateRange", "endDate"]),
      ifElse(identity, formatDate, always(null))
    ),
    facility: pipe(
      path(["facility", "value"]),
      ifElse(identity, String, always(null))
    ),
    assignee: pipe(
      path(["assignee", "value"]),
      ifElse(identity, String, always(null))
    ),
    icd10Code: path(["icd10Code"]),
    mrn: path(["mrn"]),
    caseMatch: path(["caseMatch"])
  }),
  reject(isNullOrEmpty)
);

export const useComponentLogic = () => {
  const [savedHeaderFilter, setSavedHeaderFilter] =
    useLocalStorage("caseFindingHeaders");

  const [rowsPerPage, setRowsPerPage] = useState(
    savedHeaderFilter ? savedHeaderFilter.rowsPerPage : 4
  );
  const [currentPage, setCurrentPage] = useState(
    savedHeaderFilter ? savedHeaderFilter.currentPage : 1
  );

  const [key, setKey] = useState(
    savedHeaderFilter ? savedHeaderFilter.key : ""
  );
  const [isDescending, setIsDescending] = useState(
    savedHeaderFilter ? savedHeaderFilter.descending : false
  );

  const direction = useMemo(
    () => (isDescending ? "desc" : "asc"),
    [isDescending]
  );

  useEffect(() => {
    setSavedHeaderFilter({
      key,
      descending: isDescending,
      rowsPerPage,
      currentPage
    });
  }, [key, isDescending, rowsPerPage, currentPage]);

  const selectedFilters = useSelector(casesSelectors.getSelectedFilters);

  const caseFindingFilters = filterFunction(
    selectedFilters?.case_finding || {}
  );

  const queryCaseFindingFilters = omit(["caseMatch"], caseFindingFilters);

  const caseMatchFilter = ifElse(
    has("caseMatch"),
    prop("caseMatch"),
    always(null)
  )(caseFindingFilters);

  const caseFindingQueryVariables = useMemo(
    () => ({
      currentPage,
      direction,
      key,
      rowsPerPage,
      ...queryCaseFindingFilters
    }),
    [currentPage, direction, key, rowsPerPage, queryCaseFindingFilters]
  );
  const {
    data: { caseFindingsForCurrentUser, currentUser } = {
      caseFindingsForCurrentUser: { caseFindings: [], count: 0 },
      currentUser: {}
    },
    loading,
    error
  } = useQuery(GET_CASE_FINDINGS, {
    variables: caseFindingQueryVariables,
    fetchPolicy: "cache-and-network"
  });
  const headers = useMemo(
    () =>
      pipe(
        head,
        assoc("mrn", path(["patient", "mrn"])),
        dissoc("patient"),
        dissoc("__typename"),
        dissoc("currentUserAssigned"),
        keys,
        remove(2, 1),
        createHeaderObjects(key),
        sortBy(prop("index"))
      )(caseFindingsForCurrentUser.caseFindings),
    [caseFindingsForCurrentUser, key]
  );
  const handleHeaderClick = useCallback(
    index => {
      setIsDescending(d => (headers[index].selected ? not(d) : false));
      setKey(headers[index].id);
    },
    [headers]
  );

  const [checkedAll, setCheckedAll] = useState(false);
  const [checked, setChecked] = useState(
    createChecksMap(caseFindingsForCurrentUser.caseFindings)
  );

  const handleSelectChange = (id, value) => {
    setChecked(prevChecked =>
      pipe(
        assoc(id, value),
        tap(pipe(values, all(identity), val => setCheckedAll(val)))
      )(prevChecked)
    );
  };

  const handleSelectAllClick = value => {
    setCheckedAll(value);
    setChecked(map(always(value)));
  };

  const dispatch = useDispatch();
  const assignDisabled = useMemo(
    () => pipe(values, none(identity))(checked),
    [checked]
  );

  useEffect(() => {
    setChecked(createChecksMap(caseFindingsForCurrentUser.caseFindings));
  }, [caseFindingsForCurrentUser]);

  useEffect(() => {
    if (error) {
      dispatch(pushFlashMessage("There was an error retrieving data", "error"));
    }
  }, [dispatch, error]);

  const facilityIds = uniq(
    map(path(["facility", "id"]))(caseFindingsForCurrentUser.caseFindings)
  );

  useEffect(() => {
    if (selectedFilters.case_finding) {
      setCurrentPage(1);
    }
  }, [selectedFilters]);

  return {
    assignDisabled,
    caseFindingQueryVariables,
    currentPage,
    data: caseFindingsForCurrentUser.caseFindings,
    error,
    handleHeaderClick,
    headers,
    loading,
    rowsPerPage,
    setCurrentPage,
    setRowsPerPage,
    handleSelectChange,
    handleSelectAllClick,
    checked,
    checkedAll,
    records: caseFindingsForCurrentUser.count,
    facilityIds,
    currentUser,
    caseMatchFilter
  };
};
