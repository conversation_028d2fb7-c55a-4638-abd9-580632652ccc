import { act, create } from "react-test-renderer";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import mocks from "../../mocks";
import TableHeader from "../CaseFindingHeaders/TableHeader";
import { CaseFinding } from "..";

jest.mock("shared/components/ErrorHandler", () => "ErrorHandler");
jest.mock("../CaseFindingInfo", () => "CaseFindingInfo");
jest.mock("react-tooltip", () => "ReactTooltip");
jest.mock("../CaseFindingHeaders/AssignCasesModal", () => "AssignCasesModal");
jest.mock("../CaseFindingContainerRows", () => "CaseFindingContainerRows");

const render = mocksToRender =>
  create(
    <>
      <MockedProvider mocks={mocksToRender} addTypename={false}>
        <CaseFinding />
      </MockedProvider>
    </>
  );

describe("CaseFinding", () => {
  test.skip("renders component", async () => {
    // jest.mock("../CaseFindingHeaders", () => "CaseFindingHeaders");
    let component = null;

    act(() => {
      component = render(mocks);
    });
    await act(() => wait(0));
    expect(component).toMatchSnapshot();
  });
  test.skip("renders the loading", () => {
    let component = null;

    act(() => {
      component = render(mocks);
    });
    expect(component).toMatchSnapshot();
  });
  test.skip("renders with error", async () => {
    let component = null;

    act(() => {
      component = render([]);
    });
    await act(() => wait(0));

    expect(component).toMatchSnapshot();
  });
  test.skip("checks that a clicked header is selected and isDescending is false", async () => {
    const component = render(mocks);

    await act(() => wait(0));
    const [instance] = component.root;

    const [th] = instance.findAllByType(TableHeader);

    act(() => th.props.handleHeaderClick(th.props.index));
    await act(() => wait(3));
    const [newTh] = instance.findAllByType(TableHeader);

    expect(newTh.props.selected).toBe(true);
    expect(newTh.props.isDescending).toBe(false);
  });
  test.skip("checks that clicking a header twice isDescending is true", async () => {
    const component = render(mocks);

    await act(() => wait(0));
    const [instance] = component.root;

    const [th] = instance.findAllByType(TableHeader);

    act(() => th.props.handleHeaderClick(th.props.index));
    await act(() => wait(3));
    const [newTh] = instance.findAllByType(TableHeader);

    act(() => newTh.props.handleHeaderClick(newTh.props.index));
    await act(() => wait(3));
    const [finalTh] = instance.findAllByType(TableHeader);

    expect(finalTh.props.isDescending).toBe(true);
  });
});
