// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseFinding renders component 1`] = `
<div
  className="column case-finding"
>
  <CaseFindingInfo
    currentPage={1}
    onFilter={[Function]}
    rowsPerPage={4}
    setCurrentPage={[Function]}
    setRowsPerPage={[Function]}
  />
  <div
    className="case-finding-headers"
  >
    <table
      className="case-finding-table"
    >
      <tbody>
        <tr>
          <th
            colSpan="2"
            onClick={[Function]}
          >
            <span
              className=""
            >
              Facility
            </span>
          </th>
          <th
            onClick={[Function]}
          >
            <span
              className=""
            >
              ICD-10
            </span>
          </th>
          <th
            onClick={[Function]}
          >
            <span
              className=""
            >
              MRN
            </span>
          </th>
          <th
            onClick={[Function]}
          >
            <span
              className=""
            >
              Owner
            </span>
          </th>
          <th
            onClick={[Function]}
          >
            <span
              className=""
            >
              Date of First Contact
            </span>
          </th>
          <th />
          <th
            colSpan="2"
          >
            <div>
              <ReactTooltip
                border={true}
                borderColor="#d8d7d7"
                clickable={true}
                effect="solid"
                event="click"
                id="assign-cases"
                multiline={true}
                place="bottom"
                type="light"
              >
                <AssignCasesModal />
              </ReactTooltip>
              <button
                className="show-modal"
                data-for="assign-cases"
                data-tip={true}
              >
                Assign Records
              </button>
              <label
                htmlFor="select-all"
                onClick={[Function]}
              >
                <input
                  checked={false}
                  name="select-all"
                  onChange={[Function]}
                  type="checkbox"
                />
                Select All
              </label>
            </div>
          </th>
        </tr>
      </tbody>
    </table>
  </div>
  <CaseFindingContainerRows
    data={
      Array [
        Object {
          "dateFirstContact": 2020-06-18T09:39:00.000Z,
          "facility": "ABC Medical Center",
          "icd10": 40,
          "id": "1",
          "mrn": "**********",
          "owner": null,
        },
      ]
    }
  />
</div>
`;

exports[`CaseFinding renders the loading 1`] = `<Spinner />`;

exports[`CaseFinding renders with error 1`] = `<ErrorHandler />`;
