import { act, create } from "react-test-renderer";
import { TableHeader } from "..";

jest.mock("shared/components/Checkbox", () => "Checkbox");

describe("TableHeader", () => {
  const render = props => create(<TableHeader {...props} />);

  test("renders component", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
  test("renders component with selected=true", () => {
    const component = render({ selected: true });

    expect(component).toMatchSnapshot();
  });
  test("renders component with selected=true and isDescending=true", () => {
    const component = render({
      selected: true,
      isDescending: true,
      text: "Test header"
    });

    expect(component).toMatchSnapshot();
  });
  test("renders component with index=0", () => {
    const component = render({ index: 0 });

    expect(component).toMatchSnapshot();
  });
  test("renders component with selected=true and isDescending=true", () => {
    const onHeaderClick = jest.fn();
    const component = render({ onHeaderClick, index: 3 });
    const instance = component.root;
    const [span] = instance.findAllByType("span");

    // eslint-disable-next-line no-empty-function
    act(() => span.props.onClick({ preventDefault: () => {} }));

    expect(onHeaderClick).toHaveBeenCalledWith(3);
  });
});
