// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableHeader renders component 1`] = `
<th
  className="tw-w-[22%] tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-rounded-[2px_0_0_2px] tw-border-2 tw-border-l  tw-border-r-0 tw-border-solid  tw-border-[#D8D7D7] tw-px-3 tw-py-2 tw-font-[bold] tw-text-xs tw-text-[#227b9c]"
>
  <div
    className="tw-flex tw-items-center tw-justify-evenly"
  >
    <span
      className=" font-[bold] tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-px-0 tw-py-1 tw-text-xs tw-text-[#227b9c]"
      onClick={[Function]}
    >
      TableHeader
    </span>
  </div>
</th>
`;

exports[`TableHeader renders component with index=0 1`] = `
<th
  className="tw-w-[22%] tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-rounded-[2px_0_0_2px] tw-border-2 tw-border-l  tw-border-r-0 tw-border-solid  tw-border-[#D8D7D7] tw-px-3 tw-py-2 tw-font-[bold] tw-text-xs tw-text-[#227b9c]"
  colSpan="3"
>
  <div
    className="tw-flex tw-items-center tw-justify-evenly"
  >
    <Checkbox
      indeterminate={true}
      name="select-all"
      onChange={[Function]}
    />
    <span
      className=" font-[bold] tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-px-0 tw-py-1 tw-text-xs tw-text-[#227b9c]"
      onClick={[Function]}
    >
      TableHeader
    </span>
  </div>
</th>
`;

exports[`TableHeader renders component with selected=true 1`] = `
<th
  className="tw-w-[22%] tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-rounded-[2px_0_0_2px] tw-border-2 tw-border-l  tw-border-r-0 tw-border-solid  tw-border-[#D8D7D7] tw-px-3 tw-py-2 tw-font-[bold] tw-text-xs tw-text-[#227b9c]"
>
  <div
    className="tw-flex tw-items-center tw-justify-evenly"
  >
    <span
      className="selected font-[bold] tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-px-0 tw-py-1 tw-text-xs tw-text-[#227b9c]"
      onClick={[Function]}
    >
      TableHeader
      <i
        className="fas fa-triangle tw-hidden"
      />
    </span>
  </div>
</th>
`;

exports[`TableHeader renders component with selected=true and isDescending=true 1`] = `
<th
  className="tw-w-[22%] tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-rounded-[2px_0_0_2px] tw-border-2 tw-border-l  tw-border-r-0 tw-border-solid  tw-border-[#D8D7D7] tw-px-3 tw-py-2 tw-font-[bold] tw-text-xs tw-text-[#227b9c]"
>
  <div
    className="tw-flex tw-items-center tw-justify-evenly"
  >
    <span
      className="selected font-[bold] tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-px-0 tw-py-1 tw-text-xs tw-text-[#227b9c]"
      onClick={[Function]}
    >
      Test header
      <i
        className="fas fa-triangle tw-hidden"
      />
    </span>
  </div>
</th>
`;
