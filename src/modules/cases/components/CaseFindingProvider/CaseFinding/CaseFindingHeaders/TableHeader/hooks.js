import { useCallback, useMemo } from "react";
import { always, equals, ifElse } from "ramda";
import { useLocalStorage } from "shared/hooks/useLocalStorage";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = ({
  onHeaderClick,
  index,
  onSelectAllClick
}) => {
  const [savedHeaderFilter] = useLocalStorage("caseFindingHeaders");
  const showSortIcon = useMemo(
    () =>
      isNullOrEmpty(savedHeaderFilter?.descending) ? "tw-hidden" : "tw-block",
    [savedHeaderFilter?.descending]
  );
  const colSpan = useMemo(
    () => ifElse(equals(0), always("3"), always(undefined))(index),
    [index]
  );
  const handleClick = useCallback(
    () => onHeaderClick(index),
    [onHeaderClick, index]
  );
  const handleSelectAll = useCallback(
    e => {
      onSelectAllClick(e.target.checked);
    },
    [onSelectAllClick]
  );

  return {
    colSpan,
    handleClick,
    handleSelectAll,
    showSortIcon,
    isDescending: savedHeaderFilter?.descending
  };
};
