import classnames from "classnames";
import { useComponentLogic } from "./hooks";
import Checkbox from "shared/components/Checkbox";

export const TableHeader = props => {
  const { selected, text, checkedAll } = props;
  const { colSpan, handleClick, handleSelectAll, isDescending, showSortIcon } =
    useComponentLogic(props);
  const iconClass = classnames(`fas fa-triangle ${showSortIcon}`, {
    down: isDescending
  });
  const spanClass = classnames({ selected });

  return (
    <th
      className="tw-w-[22%] tw-border-separate tw-border-spacing-0 tw-cursor-pointer tw-rounded-[2px_0_0_2px] tw-border-2 tw-border-l  tw-border-r-0 tw-border-solid  tw-border-[#D8D7D7]
      tw-px-3 tw-py-2 tw-font-[bold] tw-text-xs tw-text-[#227b9c]"
      colSpan={colSpan}
    >
      <div className="tw-flex tw-items-center tw-justify-evenly">
        {colSpan && (
          <Checkbox
            checked={checkedAll}
            indeterminate
            name="select-all"
            onChange={handleSelectAll}
          />
        )}
        <span
          className={`${spanClass} font-[bold] tw-flex tw-cursor-pointer tw-items-center tw-justify-between tw-px-0 tw-py-1 tw-text-xs tw-text-[#227b9c]`}
          onClick={handleClick}
        >
          {text}
          {selected && <i className={iconClass} />}
        </span>
      </div>
    </th>
  );
};

TableHeader.defaultProps = {
  // eslint-disable-next-line no-empty-function
  onHeaderClick: () => {},
  index: 1,
  isDescending: false,
  selected: false,
  text: "TableHeader"
};

export default TableHeader;
