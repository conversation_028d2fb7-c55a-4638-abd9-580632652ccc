import { useState, useCallback } from "react";
import { motion } from "framer-motion";
import AssignCasesModal from "../AssignCasesModal";
import { useComponentLogic } from "./hooks";
import DeleteCaseModal from "../DeleteCaseModal";
import { not } from "ramda";
import { Button } from "@q-centrix/q-components-react";
import Checkbox from "shared/components/Checkbox";

const variants = {
  hidden: {
    y: 10,
    opacity: 0,
    transition: {
      duration: 0.1
    }
  },
  shown: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.1
    }
  }
};

export const AssignCasesTableHeader = props => {
  const {
    assignDisabled,
    checkedAll,
    checked,
    facilityIds,
    refetchVariables,
    onSelectAllClick,
    currentUser
  } = props;
  const { handleChange } = useComponentLogic(props);
  const [modal, setModal] = useState(false);
  const [assigneeMenu, setAssigneeMenu] = useState(false);
  const handleModalChange = useCallback(() => setModal(not), []);

  return (
    <div className="fixed-table-header-container">
      <DeleteCaseModal
        checked={checked}
        refetchVariables={refetchVariables}
        modal={modal}
        handleModalChange={handleModalChange}
      />
      <Checkbox
        name="select-all"
        indeterminate
        checked={checkedAll || false}
        onChange={handleChange}
      />
      <div className="assign-cases-container">
        <Button
          disabled={assignDisabled}
          bg="success"
          onClick={() => setAssigneeMenu(not)}
        >
          Assign Records
        </Button>
        <motion.div
          className="assign-cases"
          variants={variants}
          initial="hidden"
          animate={assigneeMenu ? "shown" : "hidden"}
        >
          <AssignCasesModal
            checked={checked}
            facilityIds={facilityIds}
            refetchVariables={refetchVariables}
            onSelectAllClick={onSelectAllClick}
            currentUser={currentUser}
          />
        </motion.div>
      </div>
      <Button disabled={assignDisabled} bg="danger" onClick={handleModalChange}>
        Delete Records
      </Button>
    </div>
  );
};

AssignCasesTableHeader.defaultProps = {
  checked: false
};

export default AssignCasesTableHeader;
