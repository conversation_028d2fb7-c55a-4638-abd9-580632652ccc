import { act, create } from "react-test-renderer";
import { AssignCasesTableHeader } from "..";

jest.mock("../../AssignCasesModal", () => "AssignCasesModal");
jest.mock("shared/components/Checkbox", () => "Checkbox");
jest.mock("../../AssignCasesModal", () => "AssignCasesModal");
jest.mock("../../DeleteCaseModal", () => "DeleteCaseModal");
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));
const render = props => create(<AssignCasesTableHeader {...props} />);

describe("AssignCasesTableHeader", () => {
  test("renders component", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
  test("tests setChecked", () => {
    const onSelectAllClick = jest.fn();
    const component = render({ onSelectAllClick });

    const instance = component.root;

    const input = instance.findByType("Checkbox");

    act(() => input.props.onChange({ target: { checked: false } }));

    expect(onSelectAllClick).toHaveBeenCalled();
  });
});
