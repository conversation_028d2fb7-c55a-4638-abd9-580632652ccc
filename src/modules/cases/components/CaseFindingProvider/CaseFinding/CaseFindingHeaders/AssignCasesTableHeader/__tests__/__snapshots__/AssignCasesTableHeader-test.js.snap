// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AssignCasesTableHeader renders component 1`] = `
<div
  className="fixed-table-header-container"
>
  <DeleteCaseModal
    checked={false}
    handleModalChange={[Function]}
    modal={false}
  />
  <Checkbox
    checked={false}
    indeterminate={true}
    name="select-all"
    onChange={[Function]}
  />
  <div
    className="assign-cases-container"
  >
    <Button
      bg="success"
      onClick={[Function]}
    >
      Assign Records
    </Button>
    <div
      className="assign-cases"
      style={
        Object {
          "opacity": 0,
          "transform": "translateY(10px) translateZ(0)",
        }
      }
    >
      <AssignCasesModal
        checked={false}
      />
    </div>
  </div>
  <Button
    bg="danger"
    onClick={[Function]}
  >
    Delete Records
  </Button>
</div>
`;
