import Modal from "react-modal";
import { useComponentLogic } from "./hooks";
import { Button } from "@q-centrix/q-components-react";

export const DeleteCaseModal = props => {
  const { handleDeleteCase, numberOfRecordsSelected } =
    useComponentLogic(props);
  const { modal, handleModalChange } = props;

  return (
    <>
      <Modal
        id="delete-case-modal-confirmation"
        parentSelector={() =>
          document.querySelector(".fixed-table-header-container")
        }
        appElement={document.getElementById("root")}
        className="modal-content-container"
        overlayClassName="modal-overlay-container"
        isOpen={modal}
      >
        <div>
          <p>
            Delete {numberOfRecordsSelected} selected{" "}
            {numberOfRecordsSelected > 1 ? "records" : "record"}?
          </p>
          <div>
            <Button
              bg="main"
              rounded
              onClick={() => {
                handleDeleteCase();
                handleModalChange();
              }}
            >
              Delete
            </Button>
            <Button bg="main" rounded outline onClick={handleModalChange}>
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default DeleteCaseModal;
