import { act, create } from "react-test-renderer";
import { Provider } from "react-redux";
import { MockedProvider } from "@apollo/client/testing";
import wait from "waait";
import { IntlProvider } from "react-intl";
import mocks from "../mocks";
import DeleteCaseModal from "..";
import { Button } from "@q-centrix/q-components-react";
import { pushFlashMessage } from "../../../../../../../../shared/components/FlashMessage/actions";
import { successfulCaseFindingResult } from "../../../../mocks";
import { GET_CASE_FINDINGS } from "../../../../query";

jest.mock("react-modal", () => "Modal");
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));
const render = props => {
  const store = {
    getState: jest.fn(() => ({ app: {} })),
    dispatch: jest.fn(),
    subscribe: jest.fn()
  };

  const localMocks = [
    ...mocks,
    {
      request: {
        query: GET_CASE_FINDINGS,
        variables: {
          currentPage: 1,
          direction: "asc",
          key: "",
          rowsPerPage: 4
        }
      },
      result: successfulCaseFindingResult
    }
  ];

  return {
    dispatch: store.dispatch,
    component: create(
      <Provider store={store}>
        <IntlProvider>
          <MockedProvider mocks={localMocks} addTypename={false}>
            <DeleteCaseModal
              refetchVariables={{
                key: "",
                direction: "asc",
                currentPage: 1,
                rowsPerPage: 4
              }}
              {...props}
            />
          </MockedProvider>
        </IntlProvider>
      </Provider>
    )
  };
};

describe("DeleteCaseModal", () => {
  test("renders component", () => {
    const { component } = render({ checked: { 101: true, 103: false } });

    expect(component).toMatchSnapshot();
  });

  test("flashMessage called with success", async () => {
    const handleModalChange = jest.fn();

    const { component, dispatch } = render({
      checked: { 306: true },
      handleModalChange
    });
    const instance = component.root;
    const [btn] = instance.findAllByType(Button);

    act(() => btn.props.onClick());
    await act(() => wait(100));

    expect(handleModalChange).toHaveBeenCalled();
    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("1 case(s) deleted.", "success")
    );
  });

  test("flashMessage called with error, case delete error", async () => {
    const handleModalChange = jest.fn();

    const { component, dispatch } = render({
      checked: { 301: true },
      handleModalChange
    });
    const instance = component.root;
    const [btn] = instance.findAllByType(Button);

    act(() => btn.props.onClick());
    await act(() => wait(100));

    expect(handleModalChange).toHaveBeenCalled();
    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("Error: Cannot delete case", "error")
    );
  });

  test("flashMessage called with error when request error", async () => {
    const handleModalChange = jest.fn();

    const { component, dispatch } = render({
      checked: { 1: true, 2: true },
      handleModalChange
    });
    const instance = component.root;
    const [btn] = instance.findAllByType(Button);

    act(() => btn.props.onClick());
    await act(() => wait(100));

    expect(handleModalChange).toHaveBeenCalled();
    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage(
        "Error deleting the selected Case Findings Record!",
        "errors"
      )
    );
  });

  test("handleChangeModal called when cancel clicked", () => {
    const handleModalChange = jest.fn();

    const { component } = render({
      checked: { 301: true },
      handleModalChange
    });
    const instance = component.root;
    // eslint-disable-next-line prefer-destructuring
    const cancel = instance.findAllByType(Button)[1];

    act(() => cancel.props.onClick());

    expect(handleModalChange).toHaveBeenCalled();
  });
});
