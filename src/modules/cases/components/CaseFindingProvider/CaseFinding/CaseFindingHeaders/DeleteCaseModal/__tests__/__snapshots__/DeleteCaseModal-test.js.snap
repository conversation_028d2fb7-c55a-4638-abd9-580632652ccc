// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DeleteCaseModal renders component 1`] = `
<Modal
  appElement={null}
  className="modal-content-container"
  id="delete-case-modal-confirmation"
  overlayClassName="modal-overlay-container"
  parentSelector={[Function]}
>
  <div>
    <p>
      Delete 
      1
       selected
       
      record
      ?
    </p>
    <div>
      <Button
        bg="main"
        onClick={[Function]}
        rounded={true}
      >
        Delete
      </Button>
      <Button
        bg="main"
        outline={true}
        rounded={true}
      >
        Cancel
      </Button>
    </div>
  </div>
</Modal>
`;
