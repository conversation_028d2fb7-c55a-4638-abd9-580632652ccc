import mutation from "./mutation";

const mutationSuccessResult = {
  data: {
    trashCaseFindings: {
      affectedRows: 1,
      successful: ["306"],
      errors: null,
      __typename: "DeleteCaseFindingRecord"
    }
  }
};
const mutationSuccessResultWithErrors = {
  data: {
    trashCaseFindings: {
      affectedRows: null,
      successful: null,
      errors: {
        messages: [
          {
            id: "301",
            attribute: "findings:",
            errors: ["Cannot delete case"]
          }
        ],
        failedIds: ["301"]
      }
    }
  }
};

const mutationErrorResult = {
  error: {}
};

const mocks = [
  {
    request: {
      query: mutation,
      variables: {
        caseFindingIds: ["306"]
      }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: mutation,
      variables: { caseFindingIds: ["301"] }
    },
    result: mutationSuccessResultWithErrors
  },
  {
    request: {
      query: mutation,
      variables: { caseFindingIds: ["1", "2"] }
    },
    error: mutationErrorResult
  }
];

export default mocks;
