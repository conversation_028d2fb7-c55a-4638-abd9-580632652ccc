import { keys, filter, equals, pipe, pickBy, length } from "ramda";
import { useMutation } from "@apollo/client";
import { useDispatch } from "react-redux";
import { pushFlashMessage } from "shared/components/FlashMessage/actions";
import DELETE_CASE from "./mutation";
import { GET_CASE_FINDINGS } from "../../../query";
import { isNullOrEmpty } from "utils/fp";
import { useIntl } from "react-intl";

export const useComponentLogic = props => {
  const { refetchVariables, checked } = props;

  const numberOfRecordsSelected = pipe(
    pickBy(val => val === true),
    keys,
    length
  )(checked);

  const dispatch = useDispatch();
  const intl = useIntl();
  const [deleteCase] = useMutation(DELETE_CASE, {
    onCompleted: ({ trashCaseFindings }) => {
      const { affectedRows, successful, errors } = trashCaseFindings;

      if (!isNullOrEmpty(errors)) {
        return dispatch(
          pushFlashMessage(
            intl.formatMessage(
              {
                id: "caseFinding.deleteModal.messages.error",
                defaultMessage: "Error: {errors}"
              },
              { errors: errors.messages[0].errors.join(", ") }
            ),
            "error"
          )
        );
      }
      dispatch(
        pushFlashMessage(
          intl.formatMessage(
            {
              id: "caseFinding.deleteModal.messages.success",
              defaultMessage: "{affectedRows} case(s) deleted."
            },
            { affectedRows, successful: successful.join(", ") }
          ),
          "success"
        )
      );
      return null;
    },
    onError: _data => {
      dispatch(
        pushFlashMessage(
          intl.formatMessage({
            id: "caseFinding.deleteModal.messages.errors",
            defaultMessage: "Error deleting the selected Case Findings Record!"
          }),
          "errors"
        )
      );
    }
  });

  const findingIds = keys(filter(equals(true))(checked));

  const handleDeleteCase = () => {
    deleteCase({
      variables: {
        caseFindingIds: findingIds
      },
      refetchQueries: [
        {
          query: GET_CASE_FINDINGS,
          variables: refetchVariables
        }
      ]
    });
  };

  return {
    handleDeleteCase,
    numberOfRecordsSelected
  };
};
