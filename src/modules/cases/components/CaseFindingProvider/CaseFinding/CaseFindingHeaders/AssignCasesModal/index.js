import Modal from "react-modal";
import { useComponentLogic } from "./hooks";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@q-centrix/q-components-react";

export const AssignCasesModal = props => {
  const {
    assignee,
    modal,
    handleModalChange,
    loadingAssignees,
    options,
    handleAssigneeChange,
    handleClick
  } = useComponentLogic(props);

  return (
    <>
      <Modal
        id="assign-case-modal-confirmation"
        parentSelector={() =>
          document.querySelector(".fixed-table-header-container")
        }
        appElement={document.getElementById("root")}
        className="modal-content-container"
        overlayClassName="modal-overlay-container"
        isOpen={modal}
      >
        <div>
          <p>Assign selected record(s) to {assignee && assignee.label}?</p>
          <div>
            <Button
              bg="main"
              rounded
              onClick={e => {
                handleClick(e);
                handleModalChange();
              }}
            >
              Assign
            </Button>
            <Button bg="main" rounded outline onClick={handleModalChange}>
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
      <div className="assign-case-content">
        {loadingAssignees && <Spinner />}
        {options.map(assignTo => (
          <div
            key={assignTo.value}
            id={assignTo.value}
            className="content-value"
            onClick={() => {
              handleModalChange();
              handleAssigneeChange(assignTo);
            }}
          >
            <p title={assignTo.label}>{assignTo.label}</p>
          </div>
        ))}
      </div>
    </>
  );
};
export default AssignCasesModal;
