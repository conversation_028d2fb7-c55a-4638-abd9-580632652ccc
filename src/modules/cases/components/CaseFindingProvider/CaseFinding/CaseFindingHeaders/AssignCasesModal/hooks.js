/* eslint-disable max-statements */
import {
  applySpec,
  equals,
  filter,
  insert,
  keys,
  map,
  not,
  pipe,
  prop
} from "ramda";
import { useCallback, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { useMutation, useQuery } from "@apollo/client";
import { GET_CASE_FINDING_ASSIGNEES } from "./query";
import UPDATE_ASSIGNEE from "./mutation";
import { pushFlashMessage } from "shared/components/FlashMessage/actions";
import { GET_CASE_FINDINGS } from "../../../query";
import { isNullOrEmpty } from "utils/fp";
import { useIntl } from "react-intl";
import { useLocalStorage } from "shared/hooks/useLocalStorage";

export const useComponentLogic = props => {
  const {
    checked = {},
    facilityIds,
    refetchVariables,
    onSelectAllClick,
    currentUser
  } = props;

  const intl = useIntl();
  const [modal, setModal] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [savedHeaderFilter, setSavedHeaderFilter] =
    useLocalStorage("caseFindingHeaders");
  const [assignee, setAssignee] = useState(null);
  const dispatch = useDispatch();
  const {
    loading: loadingAssignees,
    data: { caseFindingAssignees: assignees } = { caseFindingAssignees: [] }
  } = useQuery(GET_CASE_FINDING_ASSIGNEES, {
    variables: { facilityIds }
  });
  const [updateAssignee] = useMutation(UPDATE_ASSIGNEE, {
    onCompleted: ({ updateAssignee: { response, errors } }) => {
      if (!isNullOrEmpty(errors)) {
        return dispatch(
          pushFlashMessage(
            intl.formatMessage(
              { id: "caseFinding.assignModal.messages.error" },
              { errors: errors.messages[0].errors }
            ),
            "error"
          )
        );
      }
      dispatch(
        pushFlashMessage(
          intl.formatMessage(
            { id: "caseFinding.assignModal.messages.success" },
            { response }
          ),
          "success"
        )
      );
      return null;
    },
    onError: () => {
      dispatch(
        pushFlashMessage(
          intl.formatMessage({ id: "caseFinding.assignModal.messages.errors" }),
          "errors"
        )
      );
    }
  });

  const findingIds = keys(filter(equals(true))(checked));

  const handleClick = useCallback(
    e => {
      e.preventDefault();
      setSavedHeaderFilter({
        key: "",
        descending: null,
        currentPage: refetchVariables.currentPage,
        rowsPerPage: refetchVariables.rowsPerPage
      });
      updateAssignee({
        variables: { assigneeId: assignee.value, findingIds },
        refetchQueries: [
          {
            query: GET_CASE_FINDINGS,
            variables: {
              key: null,
              direction: null,
              facility: facilityIds.toString(),
              currentPage: refetchVariables.currentPage,
              rowsPerPage: refetchVariables.rowsPerPage
            }
          }
        ]
      });
      setAssignee(null);
      if (onSelectAllClick) onSelectAllClick(false);
    },
    [assignee, updateAssignee, onSelectAllClick]
  );
  const handleAssigneeChange = useCallback(
    value => {
      setAssignee(value);
    },
    [setAssignee]
  );
  const handleModalChange = useCallback(() => setModal(not), []);
  const options = useMemo(
    () =>
      pipe(
        insert(0, { id: currentUser.id, fullName: "Assign To Me" }),
        map(
          applySpec({
            value: prop("id"),
            label: prop("fullName")
          })
        )
      )(assignees),
    [assignees]
  );

  return {
    assignee,
    modal,
    loadingAssignees,
    options,
    handleAssigneeChange,
    handleModalChange,
    handleClick
  };
};
