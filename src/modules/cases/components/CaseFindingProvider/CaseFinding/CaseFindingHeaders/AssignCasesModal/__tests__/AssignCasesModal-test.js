import { create } from "react-test-renderer";
import { Provider } from "react-redux";
import { createStore } from "redux";
import { MockedProvider } from "@apollo/client/testing";
import { IntlProvider } from "react-intl";
import mocks from "../mocks";
import AssignCasesModal from "..";

jest.mock("react-modal", () => "Modal");
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Spinner: "Spinner"
}));
const store = createStore(() => ({
  app: {}
}));

const render = props =>
  create(
    <Provider store={store}>
      <IntlProvider>
        <MockedProvider mocks={mocks} addTypename={false}>
          <AssignCasesModal {...props} />
        </MockedProvider>
      </IntlProvider>
    </Provider>
  );

describe("AssignCasesModal", () => {
  test("renders component with select in loading", () => {
    const component = render({ currentUser: { id: 2 } });

    expect(component).toMatchSnapshot();
  });

  test("renders loaded component with query options", () => {
    const component = render({ currentUser: { id: 2 } });

    expect(component).toMatchSnapshot();
  });
});
