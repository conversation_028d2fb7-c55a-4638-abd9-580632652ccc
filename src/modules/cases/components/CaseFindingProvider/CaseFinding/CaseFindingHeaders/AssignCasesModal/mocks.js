import query from "./query";
import mutation from "./mutation";
import { GET_CASE_FINDINGS } from "../../../query";

const successResult = {
  data: {
    caseFindingAssignees: [
      {
        id: "1",
        fullName: "<PERSON>",
        __typename: "<PERSON>FindingAssignee"
      },
      {
        id: "2",
        fullName: "<PERSON>",
        __typename: "CaseFindingAssignee"
      },
      {
        id: "3",
        fullName: "<PERSON>",
        __typename: "CaseFindingAssignee"
      }
    ]
  }
};

const successfulCaseFindingResult = {
  data: {
    caseFindingsForCurrentUser: {
      caseFindings: [
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "1",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          id: "306",
          dischargedAt: "2019-12-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingPatient"
          },
          __typename: "CaseFindingData"
        }
      ],
      count: 1,
      __typename: "CaseFindings"
    }
  }
};

const caseFindings = {
  request: {
    query: GET_CASE_FINDINGS,
    variables: {
      facilityIds: ["1"]
    }
  },
  result: successfulCaseFindingResult
};

const mutationSuccessResult = {
  data: {
    updateAssignee: {
      response: "successful: 2; unsuccessful: 0",
      errors: null,
      __typename: "UpdateAssigneeSucceful"
    }
  }
};
const mutationSuccessResultWithErrors = {
  data: {
    updateAssignee: {
      response: null,
      errors: {
        messages: [
          {
            attribute: "findings:",
            errors: ["1 not assigned"]
          }
        ],
        fullMessages: ["Findings: 1 not assigned"]
      }
    }
  }
};

const mutationErrorResult = {
  error: {}
};

const mocks = [
  {
    request: { query, variables: { facilityIds: ["1"] } },
    result: successResult
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: 2, findingIds: ["1", "2"] }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: 2, findingIds: ["306"] }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: 3, findingIds: ["306"] }
    },
    result: mutationSuccessResult
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: 1, findingIds: ["1", "2"] }
    },
    result: mutationSuccessResultWithErrors
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: "1", findingIds: ["1", "2"] }
    },
    error: mutationErrorResult
  },
  {
    request: {
      query: mutation,
      variables: { assigneeId: 2, findingIds: ["301"] }
    },
    result: mutationSuccessResult
  },
  caseFindings
];

export default mocks;
