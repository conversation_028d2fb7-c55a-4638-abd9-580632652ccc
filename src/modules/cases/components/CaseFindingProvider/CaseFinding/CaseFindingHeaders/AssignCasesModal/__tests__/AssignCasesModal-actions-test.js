/* eslint-disable prefer-destructuring */
import { act, create } from "react-test-renderer";
import { Provider } from "react-redux";
import { IntlProvider } from "react-intl";
import { createStore } from "redux";
import { MockedProvider } from "@apollo/client/testing";
import localeData from "base/locales/data";
import wait from "waait";
import mocks from "../mocks";
import AssignCasesModal from "..";
import { pushFlashMessage } from "shared/components/FlashMessage/actions";

jest.mock("react-modal", () => "Modal");
jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Spinner: "Spinner"
}));

export const getStore = initialValues =>
  createStore(() => ({
    app: initialValues
  }));

const createStoreAndDispatch = initialValues => {
  const store = {
    getState: jest.fn(() => ({ app: initialValues })),
    dispatch: jest.fn(),
    subscribe: jest.fn()
  };

  return { store };
};

const initialFlashMessageState = { flashMessage: { feed: [] } };
const locale = "en";
const messages = localeData[locale] || localeData.en;
const decoratedApolloWithDispatch = ({
  Component,
  props,
  initialValues,
  apolloMocks
}) => {
  const { store } = createStoreAndDispatch(initialValues);

  return {
    component: (
      <Provider store={store}>
        <IntlProvider locale={locale} messages={messages}>
          <MockedProvider mocks={apolloMocks} addTypename={false}>
            <Component {...props} />
          </MockedProvider>
        </IntlProvider>
      </Provider>
    ),
    dispatch: store.dispatch
  };
};

describe("AssignCasesModal", () => {
  // eslint-disable-next-line no-unused-vars
  const render = (initialState = initialFlashMessageState, props = {}) => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      Component: AssignCasesModal,
      props,
      initialState: {},
      apolloMocks: mocks
    });

    return { component: create(component), dispatch };
  };

  test("assignee clicked on and opens Modal", async () => {
    const { component } = render(initialFlashMessageState, {
      currentUser: { id: 2 },
      facilityIds: ["1"]
    });

    await act(() => wait(100));
    const instance = component.root;

    const assignee = instance.findAllByType("div")[4];

    await act(() => assignee.props.onClick());
    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("assignee called after assign button clicked & successful", async () => {
    const { component, dispatch } = render(initialFlashMessageState, {
      checked: { 1: true, 2: true },
      currentUser: { id: 2 },
      facilityIds: ["1"],
      refetchVariables: {
        currentPage: 1,
        rowsPerPage: 4,
        facility: "1"
      }
    });

    await act(() => wait(0));
    const instance = component.root;
    const assignee = instance.findAllByType("div")[3];

    act(() => assignee.props.onClick());
    await act(() => wait(100));

    const [assignButton] = instance.findAllByType("Button");

    act(() =>
      assignButton.props.onClick({
        preventDefault: jest.fn()
      })
    );
    await act(() => wait(100));

    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("caseFinding.assignModal.messages.success", "success")
    );
  });

  test("assignee called after assign button clicked & get error", async () => {
    const { component, dispatch } = render(initialFlashMessageState, {
      checked: { 1: true, 2: true },
      currentUser: { id: 2 },
      facilityIds: ["1"],
      refetchVariables: {
        currentPage: 1,
        rowsPerPage: 4,
        facility: "1"
      }
    });

    await act(() => wait(300));
    const instance = component.root;
    const assignee = instance.findAllByType("div")[4];

    await act(() => assignee.props.onClick());
    await act(() => wait(300));

    const assignButton = instance.findAllByType("Button")[0];

    act(() =>
      assignButton.props.onClick({
        preventDefault: jest.fn()
      })
    );
    await act(() => wait(300));

    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("caseFinding.assignModal.messages.errors", "errors")
    );
  });

  test("assignee called after button clicked & graphql errors", async () => {
    const { component, dispatch } = render(initialFlashMessageState, {
      checked: { 1: true, 2: true },
      currentUser: { id: 2 },
      facilityIds: ["1"],
      refetchVariables: {
        currentPage: 1,
        rowsPerPage: 4,
        facility: "1"
      }
    });

    await act(() => wait(300));
    const instance = component.root;
    const assignee = instance.findAllByType("div")[6];

    await act(() => assignee.props.onClick());
    await act(() => wait(300));

    const [assignButton] = instance.findAllByType("Button");

    act(() =>
      assignButton.props.onClick({
        preventDefault: jest.fn()
      })
    );
    await act(() => wait(100));

    expect(dispatch).toHaveBeenCalledWith(
      pushFlashMessage("caseFinding.assignModal.messages.errors", "errors")
    );
  });
});
