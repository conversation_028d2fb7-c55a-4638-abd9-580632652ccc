// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AssignCasesModal renders component with select in loading 1`] = `
Array [
  <Modal
    appElement={null}
    className="modal-content-container"
    id="assign-case-modal-confirmation"
    isOpen={false}
    overlayClassName="modal-overlay-container"
    parentSelector={[Function]}
  >
    <div>
      <p>
        Assign selected record(s) to 
        ?
      </p>
      <div>
        <Button
          bg="main"
          onClick={[Function]}
          rounded={true}
        >
          Assign
        </Button>
        <Button
          bg="main"
          onClick={[Function]}
          outline={true}
          rounded={true}
        >
          Cancel
        </Button>
      </div>
    </div>
  </Modal>,
  <div
    className="assign-case-content"
  >
    <Spinner />
    <div
      className="content-value"
      id={2}
      onClick={[Function]}
    >
      <p
        title="Assign To Me"
      >
        Assign To Me
      </p>
    </div>
  </div>,
]
`;

exports[`AssignCasesModal renders loaded component with query options 1`] = `
Array [
  <Modal
    appElement={null}
    className="modal-content-container"
    id="assign-case-modal-confirmation"
    isOpen={false}
    overlayClassName="modal-overlay-container"
    parentSelector={[Function]}
  >
    <div>
      <p>
        Assign selected record(s) to 
        ?
      </p>
      <div>
        <Button
          bg="main"
          onClick={[Function]}
          rounded={true}
        >
          Assign
        </Button>
        <Button
          bg="main"
          onClick={[Function]}
          outline={true}
          rounded={true}
        >
          Cancel
        </Button>
      </div>
    </div>
  </Modal>,
  <div
    className="assign-case-content"
  >
    <Spinner />
    <div
      className="content-value"
      id={2}
      onClick={[Function]}
    >
      <p
        title="Assign To Me"
      >
        Assign To Me
      </p>
    </div>
  </div>,
]
`;
