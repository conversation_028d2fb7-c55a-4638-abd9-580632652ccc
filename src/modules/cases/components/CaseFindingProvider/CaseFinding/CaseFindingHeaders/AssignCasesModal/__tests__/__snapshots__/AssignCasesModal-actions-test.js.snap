// Jest Snapshot v1, https://goo.gl/fbAQL<PERSON>

exports[`AssignCasesModal assignee clicked on and opens Modal 1`] = `
Array [
  <Modal
    appElement={null}
    className="modal-content-container"
    id="assign-case-modal-confirmation"
    isOpen={true}
    overlayClassName="modal-overlay-container"
    parentSelector={[Function]}
  >
    <div>
      <p>
        Assign selected record(s) to 
        <PERSON>
        ?
      </p>
      <div>
        <Button
          bg="main"
          onClick={[Function]}
          rounded={true}
        >
          Assign
        </Button>
        <Button
          bg="main"
          onClick={[Function]}
          outline={true}
          rounded={true}
        >
          Cancel
        </Button>
      </div>
    </div>
  </Modal>,
  <div
    className="assign-case-content"
  >
    <div
      className="content-value"
      id={2}
      onClick={[Function]}
    >
      <p
        title="Assign To Me"
      >
        Assign To Me
      </p>
    </div>
    <div
      className="content-value"
      id="1"
      onClick={[Function]}
    >
      <p
        title="<PERSON>"
      >
        <PERSON>
      </p>
    </div>
    <div
      className="content-value"
      id="2"
      onClick={[Function]}
    >
      <p
        title="Russell Reas"
      >
        Russell Reas
      </p>
    </div>
    <div
      className="content-value"
      id="3"
      onClick={[Function]}
    >
      <p
        title="Jon Snow"
      >
        Jon Snow
      </p>
    </div>
  </div>,
]
`;
