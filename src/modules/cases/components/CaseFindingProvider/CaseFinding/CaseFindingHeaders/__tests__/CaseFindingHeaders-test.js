import { create } from "react-test-renderer";
import { CaseFindingHeaders } from "..";

jest.mock("../TableHeader", () => "TableHeader");
jest.mock("../AssignCasesTableHeader", () => "AssignCasesTableHeader");

const render = props => create(<CaseFindingHeaders {...props} />);

describe("CaseFindingHeaders", () => {
  test("renders component", () => {
    const component = render({ assignDisabled: false });

    expect(component).toMatchSnapshot();
  });
  test("renders component with headers prop", () => {
    const component = render({
      assignDisabled: true,
      headers: [{ text: "Test", id: "tester", selected: false, index: 0 }]
    });

    expect(component).toMatchSnapshot();
  });
});
