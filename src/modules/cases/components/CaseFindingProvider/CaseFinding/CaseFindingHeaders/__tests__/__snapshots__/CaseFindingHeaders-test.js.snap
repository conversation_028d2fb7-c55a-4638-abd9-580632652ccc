// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CaseFindingHeaders renders component 1`] = `
<div
  className="case-finding-headers tw-mt-2 tw-block tw-text-base tw-leading-[1.6] tw-text-[#284758]"
>
  <table
    className="case-finding-table tw-min-h-[64px] tw-w-full tw-table-fixed tw-border-separate tw-border-spacing-0 tw-border-[gray] tw-text-base tw-leading-[1.6] tw-text-[#284758]"
  >
    <tbody
      className="tw-border-separate tw-border-spacing-0"
    >
      <tr>
        <th>
          <AssignCasesTableHeader
            assignDisabled={false}
          />
        </th>
        <th
          colSpan="2"
        />
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`CaseFindingHeaders renders component with headers prop 1`] = `
<div
  className="case-finding-headers tw-mt-2 tw-block tw-text-base tw-leading-[1.6] tw-text-[#284758]"
>
  <table
    className="case-finding-table tw-min-h-[64px] tw-w-full tw-table-fixed tw-border-separate tw-border-spacing-0 tw-border-[gray] tw-text-base tw-leading-[1.6] tw-text-[#284758]"
  >
    <tbody
      className="tw-border-separate tw-border-spacing-0"
    >
      <tr>
        <TableHeader
          assignDisabled={true}
          id="tester"
          index={0}
          onHeaderClick={[Function]}
          selected={false}
          text="Test"
        />
        <th
          colSpan="2"
        />
      </tr>
    </tbody>
  </table>
</div>
`;
