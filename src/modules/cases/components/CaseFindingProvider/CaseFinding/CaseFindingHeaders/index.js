import TableHeader from "./TableHeader";
import AssignCasesTableHeader from "./AssignCasesTableHeader";

export const CaseFindingHeaders = props => {
  const {
    checked,
    assignDisabled,
    onHeaderClick,
    headers,
    checkedAll,
    onSelectAllClick,
    facilityIds,
    refetchVariables,
    currentUser
  } = props;

  return (
    <div className="case-finding-headers tw-mt-2 tw-block tw-text-base tw-leading-[1.6] tw-text-[#284758]">
      <table className="case-finding-table tw-min-h-[64px] tw-w-full tw-table-fixed tw-border-separate tw-border-spacing-0 tw-border-[gray] tw-text-base tw-leading-[1.6] tw-text-[#284758]">
        <tbody className="tw-border-separate tw-border-spacing-0">
          <tr>
            {assignDisabled ? (
              headers.map(header => (
                <TableHeader
                  key={header.id}
                  index={header.index}
                  onHeaderClick={onHeaderClick}
                  assignDisabled={assignDisabled}
                  checkedAll={checkedAll}
                  checked={checked}
                  onSelectAllClick={onSelectAllClick}
                  facilityIds={facilityIds}
                  refetchVariables={refetchVariables}
                  {...header}
                />
              ))
            ) : (
              <th>
                <AssignCasesTableHeader
                  assignDisabled={assignDisabled}
                  checkedAll={checkedAll}
                  checked={checked}
                  onSelectAllClick={onSelectAllClick}
                  facilityIds={facilityIds}
                  refetchVariables={refetchVariables}
                  currentUser={currentUser}
                />
              </th>
            )}
            <th colSpan="2" />
          </tr>
        </tbody>
      </table>
    </div>
  );
};
CaseFindingHeaders.defaultProps = {
  // eslint-disable-next-line no-empty-function
  onHeaderClick: () => {},
  headers: [],
  isDescending: false
};
export default CaseFindingHeaders;
