import { Spinner } from "@q-centrix/q-components-react";
import CaseFindingHeaders from "./CaseFindingHeaders";
import CaseFindingInfo from "./CaseFindingInfo";
import CaseFindingContainerRows from "./CaseFindingContainerRows";
import { useComponentLogic } from "./hooks";
import FlashMessage from "shared/components/FlashMessage";

export const CaseFinding = () => {
  const {
    assignDisabled,
    caseFindingQueryVariables,
    currentPage,
    data,
    handleHeaderClick,
    headers,
    loading,
    rowsPerPage,
    setCurrentPage,
    setRowsPerPage,
    handleFilter,
    handleSelectChange,
    handleSelectAllClick,
    checked,
    checkedAll,
    records,
    facilityIds,
    currentUser,
    caseMatchFilter
  } = useComponentLogic();

  if (loading)
    return (
      <div className="tw-flex tw-h-full tw-justify-center tw-rounded-b-[5px] tw-bg-white ">
        <Spinner />
      </div>
    );

  if (data.length === 0)
    return (
      <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-rounded-b-[5px] tw-bg-white">
        <div className="tw-mb-4 tw-text-2xl tw-font-bold tw-text-qc-blue-800">
          No results found
        </div>
        <div className="tw-text-base tw-leading-[1.6] tw-text-black-54">
          Please try adjusting your search criteria
        </div>
      </div>
    );

  return (
    <div className="column case-finding tw-h-full">
      <FlashMessage />
      <CaseFindingInfo
        currentPage={currentPage}
        rowsPerPage={rowsPerPage}
        setCurrentPage={setCurrentPage}
        setRowsPerPage={setRowsPerPage}
        onFilter={handleFilter}
        records={records}
      />
      <CaseFindingHeaders
        checked={checked}
        assignDisabled={assignDisabled}
        headers={headers}
        currentUser={currentUser}
        onHeaderClick={handleHeaderClick}
        checkedAll={checkedAll}
        onSelectAllClick={handleSelectAllClick}
        facilityIds={facilityIds}
        refetchVariables={caseFindingQueryVariables}
      />
      <CaseFindingContainerRows
        data={data}
        facilityIds={facilityIds}
        currentUser={currentUser}
        checked={checked}
        onCheckClicked={handleSelectChange}
        refetchVariables={caseFindingQueryVariables}
        caseMatchFilter={caseMatchFilter}
      />
    </div>
  );
};

export default CaseFinding;
