/* eslint-disable max-params */
import nonReportableMocks from "./CaseFinding/CaseFindingContainerRows/CaseFindingContainer/NonReportableMenu/mocks";
import addNewPrimaryMocks from "./CaseFinding/CaseFindingContainerRows/CaseFindingContainer/AddNewPrimaryMenu/mocks";
import updateCellMocks from "./CaseFinding/CaseFindingContainerRows/CaseFindingContainer/HistoricalCaseRows/HistoricalCaseRow/UpdateCell/mocks";
import assigneeMocks from "./CaseFinding/CaseFindingHeaders/AssignCasesModal/mocks";
import deleteCaseMocks from "./CaseFinding/CaseFindingHeaders/DeleteCaseModal/mocks";

import { GET_CASE_FINDINGS, GET_CASE_FINDING_HISTORICAL_ROWS } from "./query";

const successfulCaseFindingHistoricalRows = {
  request: {
    query: GET_CASE_FINDING_HISTORICAL_ROWS,
    variables: { id: "306" }
  },
  result: {
    data: {
      caseFindingCases: [
        {
          id: "1151",
          questionnaireResponseId: "12",
          diagnosisCode: "Z12.31",
          visit: {
            dateOfDiagnosis: null,
            __typename: "CaseFindingCaseDateOfDiagnosis"
          },
          patient: {
            bornOn: "1982-10-14T00:00:00",
            firstName: "First",
            lastName: "Test",
            mrn: "34534334",
            __typename: "CaseFindingCasePatient"
          },
          owner: {
            id: "1",
            fullName: "Jon Snow",
            __typename: "CaseFindingCaseOwner"
          },
          status: "Billable",
          cancerSite: {
            name: "Digestive Other",
            __typename: "CaseFindingCaseCancerSite"
          },
          registryData: {
            accessionNumber: "",
            sequenceNumber: "",
            primarySite: "",
            histology: "",
            laterality: "",
            dateOfLastContact: null,
            classOfCase: "",
            __typename: "CaseFindingCaseRegistryData"
          },
          facility: {
            name: "Osceola Regional Medical Center",
            __typename: "CaseFindingCaseFacility"
          },
          __typename: "CaseFindingCaseData"
        },
        {
          id: "1150",
          questionnaireResponseId: null,
          diagnosisCode: "j93.05",
          visit: {
            dateOfDiagnosis: null,
            __typename: "CaseFindingCaseDateOfDiagnosis"
          },
          patient: {
            bornOn: "1982-10-14T00:00:00",
            firstName: "First",
            lastName: "Test",
            mrn: "34534334",
            __typename: "CaseFindingCasePatient"
          },
          owner: {
            id: "1",
            fullName: "Jon Snow",
            __typename: "CaseFindingCaseOwner"
          },
          status: "Pending",
          cancerSite: {
            name: "Hip",
            __typename: "CaseFindingCaseCancerSite"
          },
          registryData: {
            accessionNumber: "",
            sequenceNumber: "",
            primarySite: "",
            histology: "",
            laterality: "",
            dateOfLastContact: null,
            classOfCase: "",
            __typename: "CaseFindingCaseRegistryData"
          },
          facility: {
            name: "Osceola Regional Medical Center",
            __typename: "CaseFindingCaseFacility"
          },
          __typename: "CaseFindingCaseData"
        }
      ]
    }
  }
};

export const successfulCaseFindingResult = {
  data: {
    caseFindingsForCurrentUser: {
      caseFindings: [
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "306",
          firstContact: "2019-01-01T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "j93.04",
          dischargedAt: "2020-01-12T00:00:00",
          id: "301",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "j91.07",
          dischargedAt: "2019-07-12T00:00:00",
          id: "300",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C41.05",
          dischargedAt: "2019-03-12T00:00:00",
          id: "299",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C33.24",
          dischargedAt: "2019-10-12T00:00:00",
          id: "298",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C41.04",
          dischargedAt: "2019-01-15T00:00:00",
          id: "295",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "K12.30.",
          dischargedAt: "2019-04-12T00:00:00",
          id: "294",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "d12.2",
          dischargedAt: "2020-01-01T00:00:00",
          id: "293",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C00.364",
          dischargedAt: "2019-01-12T00:00:00",
          id: "296",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Test Medical Facility",
            id: "8",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "z08",
          dischargedAt: "2019-01-12T00:00:00",
          id: "304",
          firstContact: "2020-11-22T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "123456",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        }
      ],
      count: 18,
      __typename: "CaseFindings"
    },
    currentUser: {
      id: 2,
      __typename: "CaseFindingCurrentUser"
    }
  }
};
export const successfulCaseFindingResultAfterAssigneeSelected = {
  data: {
    caseFindingsForCurrentUser: {
      caseFindings: [
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "306",
          firstContact: "2019-01-01T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "j93.04",
          dischargedAt: "2020-01-12T00:00:00",
          id: "301",
          firstContact: "2019-01-01T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "j91.07",
          dischargedAt: "2019-07-12T00:00:00",
          id: "300",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C41.05",
          dischargedAt: "2019-03-12T00:00:00",
          id: "299",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C33.24",
          dischargedAt: "2019-10-12T00:00:00",
          id: "298",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C41.04",
          dischargedAt: "2019-01-15T00:00:00",
          id: "295",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "K12.30.",
          dischargedAt: "2019-04-12T00:00:00",
          id: "294",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "d12.2",
          dischargedAt: "2020-01-01T00:00:00",
          id: "293",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Small Medical Center",
            id: "2",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "C00.364",
          dischargedAt: "2019-01-12T00:00:00",
          id: "296",
          firstContact: "2019-12-02T00:00:00",
          assignee: null,
          patient: {
            mrn: "6676756",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Test Medical Facility",
            id: "8",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "z08",
          dischargedAt: "2019-01-12T00:00:00",
          id: "304",
          firstContact: "2020-11-22T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "123456",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        }
      ],
      count: 18,
      __typename: "CaseFindings"
    },
    currentUser: {
      id: 2,
      __typename: "CaseFindingCurrentUser"
    }
  }
};
const successfulCaseFindingResultRefetch = {
  data: {
    caseFindingsForCurrentUser: {
      caseFindings: [
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "306",
          firstContact: "2019-01-01T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "j93.04",
          dischargedAt: "2020-01-12T00:00:00",
          id: "301",
          firstContact: "2019-01-01T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Test Medical Facility",
            id: "8",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "z08",
          dischargedAt: "2019-01-12T00:00:00",
          id: "304",
          firstContact: "2020-11-22T00:00:00",
          assignee: {
            id: 2,
            fullName: "Russell Reas",
            __typename: "CaseFindingDataAssignee"
          },
          patient: {
            mrn: "123456",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        }
      ],
      count: 3,
      __typename: "CaseFindings"
    },
    currentUser: {
      id: 2,
      __typename: "CaseFindingCurrentUser"
    }
  }
};
const successfulCaseFindingResultAlternative = {
  data: {
    caseFindingsForCurrentUser: {
      caseFindings: [
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "306",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "785643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "307",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "985643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "308",
          assignee: null,
          firstContact: "2019-01-01T00:00:00",
          patient: {
            mrn: "885643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "309",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "105643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "310",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "685643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        },
        {
          facility: {
            name: "Osceola Regional Medical Center",
            id: "40",
            __typename: "CaseFindingFacility"
          },
          diagnosisCode: "Z12.31",
          dischargedAt: "2019-01-12T00:00:00",
          id: "311",
          firstContact: "2019-01-01T00:00:00",
          assignee: null,
          patient: {
            mrn: "85643",
            __typename: "CaseFindingCasePatient"
          },
          __typename: "CaseFindingData"
        }
      ],
      count: 5,
      __typename: "CaseFindings"
    },
    currentUser: {
      id: 2,
      __typename: "CaseFindingCurrentUser"
    }
  }
};
const createSuccessfulHeaderSortMockAlternative = (
  key,
  direction,
  currentPage,
  rowsPerPage,
  filters = {}
) => ({
  request: {
    query: GET_CASE_FINDINGS,
    variables: { currentPage, direction, key, rowsPerPage, ...filters }
  },
  result: successfulCaseFindingResultAlternative
});
const createSuccessfulHeaderSortMockRefetch = (
  key,
  direction,
  currentPage,
  rowsPerPage,
  filters = {}
) => ({
  request: {
    query: GET_CASE_FINDINGS,
    variables: {
      currentPage,
      direction,
      key,
      rowsPerPage,
      assignee: 1,
      ...filters
    }
  },
  result: successfulCaseFindingResultRefetch
});
const createSuccessfulHeaderSortMock = (
  key,
  direction,
  currentPage,
  rowsPerPage,
  filters = {}
  // eslint-disable-next-line max-params
) => ({
  request: {
    query: GET_CASE_FINDINGS,
    variables: { currentPage, direction, key, rowsPerPage, ...filters }
  },
  result: successfulCaseFindingResult
});

const createSuccessfulHeaderSortMock2 = (
  key,
  direction,
  currentPage,
  rowsPerPage,
  filters = {}
) => ({
  request: {
    query: GET_CASE_FINDINGS,
    variables: { currentPage, direction, key, rowsPerPage, ...filters }
  },
  result: {
    data: {
      caseFindingsForCurrentUser: {
        caseFindings: [
          {
            facility: {
              name: "Test Medical Center",
              id: "420",
              __typename: "CaseFindingFacility"
            },
            diagnosisCode: "Z12.37",
            dischargedAt: "2019-01-12T00:00:00",
            id: "3067",
            assignee: null,
            patient: {
              mrn: "785643",
              __typename: "CaseFindingCasePatient"
            },
            __typename: "CaseFindingData"
          }
        ],
        count: 18,
        __typename: "CaseFindings"
      },
      currentUser: {
        id: 2,
        __typename: "CaseFindingCurrentUser"
      }
    }
  }
});

const caseFindings = {
  request: {
    query: GET_CASE_FINDINGS,
    variables: { currentPage: 1, direction: "asc", key: "", rowsPerPage: 4 }
  },
  result: successfulCaseFindingResult
};
const caseFindingsAfterAssignee = {
  request: {
    query: GET_CASE_FINDINGS,
    variables: { currentPage: 1, direction: "asc", key: "", rowsPerPage: 4 }
  },
  result: successfulCaseFindingResultAfterAssigneeSelected
};

const mock1Of4Pages = [1, 4];
const mock2Of4Pages = [2, 4];
const mockFilters = {
  startDate: "2022-06-22",
  endDate: "2022-06-23",
  facility: "1",
  assignee: "2",
  icd10Code: "123"
};
const mockFilters2 = {
  startDate: "2022-10-22",
  endDate: "2022-10-23",
  facility: "1",
  assignee: "2",
  icd10Code: "123"
};

const mocks = [
  ...assigneeMocks,
  ...deleteCaseMocks,
  ...updateCellMocks,
  ...addNewPrimaryMocks,
  ...nonReportableMocks,
  successfulCaseFindingHistoricalRows,
  caseFindings,
  caseFindingsAfterAssignee,
  createSuccessfulHeaderSortMock("", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("", "asc", ...mock2Of4Pages),
  createSuccessfulHeaderSortMock("", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("facility", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("icd10", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("icd10", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("mrn", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("mrn", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("assignee", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("assignee", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("dateFirstContact", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("dateFirstContact", "desc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMock("", "asc", ...mock1Of4Pages, mockFilters),
  createSuccessfulHeaderSortMock("", "asc", ...mock2Of4Pages, mockFilters),
  createSuccessfulHeaderSortMock2("", "asc", ...mock1Of4Pages, mockFilters),
  createSuccessfulHeaderSortMock2("facility", "asc", ...mock1Of4Pages),
  createSuccessfulHeaderSortMockRefetch(
    "",
    "asc",
    ...mock1Of4Pages,
    mockFilters2
  ),
  createSuccessfulHeaderSortMockAlternative("", "asc", ...mock1Of4Pages, {
    ...mockFilters,
    facility: "2"
  }),
  createSuccessfulHeaderSortMockAlternative("", "asc", ...mock2Of4Pages, {
    ...mockFilters,
    facility: "2"
  })
];

export default mocks;
