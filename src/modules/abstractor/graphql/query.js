import { gql } from "@apollo/client";

export const GET_EFFICIENCY_DATA = gql`
  query efficiencyHistoryForCurrentUser {
    efficiencyHistoryForCurrentUser {
      history {
        date
        amount
        thresholdMet
      }
      average {
        amount
        thresholdMet
      }
    }
  }
`;

export const GET_ABSTRACTOR_SPECIFIC_DATA_POINTS = gql`
  query earningsForCurrentPayPeriod {
    earningsForCurrentPayPeriod {
      userTotalPoint {
        timePoints
        abstractionPoints
        totalQPoints
      }
      totalPay
    }
  }
`;

export const GET_ABSTRACTOR_SPECIFIC_DATA_HOURS = gql`
  query hoursForCurrentWeek {
    hoursForCurrentWeek {
      abstractionHours
      nonAbstractionHours
      ptoHours
      totalHours
    }
  }
`;

export const GET_COMMITTED_HOURS_DATA = gql`
  query committedHoursHistoryForCurrentUser {
    committedHoursHistoryForCurrentUser {
      history {
        fromDate
        toDate
        amount
        thresholdMet
      }
      average {
        amount
        thresholdMet
      }
    }
  }
`;

export const DATA_ELEMENT_MATCH = gql`
  query dataElementMatchForCurrentUser {
    dataElementMatchForCurrentUser {
      history {
        date
        amount
        thresholdMet
      }
      average {
        amount
        thresholdMet
      }
    }
  }
`;

export const GET_ACTIVE_CASES_DATA = gql`
  query activeCasesForCurrentUser($sortBy: String, $sortDirection: String) {
    activeCasesForCurrentUser {
      facilities {
        facilityId
        facilityName
        caseFindings
        abstractions
        reviews
        irrs
        exceptionReportings
        nextDeadline
      }
      totals {
        caseFindings
        abstractions
        reviews
        irrs
        exceptionReportings
      }
    }
  }
`;
