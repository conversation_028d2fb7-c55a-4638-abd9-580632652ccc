import irrDataMocks from "modules/abstractor/components/Abstractor/Content/DataElementMatch/mocks";
import efficiencyDataMock from "modules/abstractor/components/Abstractor/Content/AverageEfficiency/mocks";
import availableAppDataMock from "modules/abstractor/components/Abstractor/Content/AccessMyApps/MyApps/mocks";
import facilitiesSuccessMocks from "modules/abstractor/components/Abstractor/Content/AccessMyApps/AccessDropDown/mocks";
import committedHoursDataMock from "modules/abstractor/components/Abstractor/Content/CommittedHours/mocks";
import activeCasesDataMock from "modules/abstractor/components/Abstractor/Content/ActiveCasesTable/mocks";

export default [
  ...irrDataMocks,
  ...efficiencyDataMock,
  ...committedHoursDataMock,
  ...availableAppDataMock,
  ...facilitiesSuccessMocks,
  ...activeCasesDataMock
];
