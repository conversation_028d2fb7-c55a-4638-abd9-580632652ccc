// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Abstractor renders Abstractor 1`] = `
<Layout
  onLogoClick={[Function]}
  tbChildren={
    <div
      className="tw-flex tw-w-full tw-items-center tw-justify-between"
    >
      <h1
        className="tw-text-2xl tw-font-semibold"
      >
        Workflow
      </h1>
      <div
        className="tw-flex tw-items-center tw-gap-5"
      >
        <Hours />
        <QPoints />
        <Earnings />
        <LastLogInTile />
      </div>
    </div>
  }
>
  <div
    className="abstractor-container"
  >
    <div
      className="abstractor-content"
    >
      <Content />
    </div>
  </div>
</Layout>
`;
