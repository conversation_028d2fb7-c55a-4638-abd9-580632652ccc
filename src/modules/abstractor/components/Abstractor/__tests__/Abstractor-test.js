import { create } from "react-test-renderer";
import Abstractor from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Navbar: "Navbar",
  Topbar: "Topbar",
  SideNavMenu: "SideNavMenu"
}));

jest.mock("shared/components/Layout", () => "Layout");
jest.mock("shared/widgets/QPoints", () => "QPoints");
jest.mock("shared/widgets/Earnings", () => "Earnings");
jest.mock("../Content", () => "Content");

describe("Abstractor", () => {
  function render() {
    return create(<Abstractor />);
  }

  test("renders Abstractor", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
