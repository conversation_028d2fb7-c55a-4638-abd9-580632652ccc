import { useComponentLogic } from "./hooks";
import { DataCard, Spinner } from "@q-centrix/q-components-react";

// eslint-disable-next-line complexity
const MyIncompleteActivities = props => {
  const { count, loading, error, isHoursEnabled } = useComponentLogic(props);

  if (!isHoursEnabled) return null;

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (loading)
    return (
      <div className="data-element-match">
        <Spinner />
      </div>
    );
  return (
    <DataCard
      title="My Incomplete Activities"
      value={count}
      type="number"
      threshold={count <= 10}
    />
  );
};

export default MyIncompleteActivities;
