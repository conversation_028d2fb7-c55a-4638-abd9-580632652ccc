import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import MyIncompleteActivities from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  DataCard: "DataCard",
  Spinner: "Spinner"
}));

describe("MyIncompleteActivities", () => {
  function render() {
    return create(
      decoratedApollo({
        component: MyIncompleteActivities,
        props: {},
        initialValues: {},
        initialAppValues: {
          enabledFeatureToggles: {
            enabledFeatureToggles: ["Hours Redesign"]
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
