import { ACTIVITIES_COUNT } from "modules/cases/graphql/query";

const efficiencyDataMock = [
  {
    request: {
      query: ACTIVITIES_COUNT,
      variables: {
        profile: "my_activities",
        filters: [{ key: "status", values: ["2", "3", "1"] }]
      }
    },
    result: {
      data: {
        activityList: {
          count: 5
        }
      }
    }
  }
];

export default efficiencyDataMock;
