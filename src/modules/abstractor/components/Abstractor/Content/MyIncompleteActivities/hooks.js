import { useQuery } from "@apollo/client";
import { ACTIVITIES_COUNT } from "modules/cases/graphql/query";
import { userFeatureToggles } from "modules/facility-groups/utils/userFeatureToggles";

export const useComponentLogic = ({ client }) => {
  const { isFeatureEnabled } = userFeatureToggles();
  const isHoursEnabled = isFeatureEnabled("Hours Redesign");

  const {
    data: { activityList: { count = null } } = { activityList: {} },
    loading,
    error
  } = useQuery(ACTIVITIES_COUNT, {
    client,
    variables: {
      profile: "my_activities",
      filters: [{ key: "status", values: ["2", "3", "1"] }]
    }
  });

  return {
    count,
    loading,
    error,
    isHoursEnabled
  };
};
