// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content renders Content 1`] = `
<div
  className="content"
>
  <div
    className="data"
  >
    <div
      className="apps"
    >
      <AccessMyApps />
    </div>
  </div>
  <div
    className="widgets"
  >
    <DataElementMatch
      client={
        ApolloClient {
          "cache": InMemoryCache {
            "addTypename": true,
            "addTypenameTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "stableCacheKeys": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "transform": [Function],
            },
            "assumeImmutableResults": true,
            "config": Object {
              "addTypename": true,
              "canonizeResults": false,
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "CaseReview": Array [
                  "FullReview",
                ],
              },
              "resultCaching": true,
            },
            "data": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": [Circular],
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "toReference": [Function],
            },
            "getFragmentDoc": [Function],
            "makeVar": [Function],
            "maybeBroadcastWatch": [Function],
            "optimisticData": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": [Circular],
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "CaseReview" => Set {},
                "FullReview" => Set {
                  "CaseReview",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "storeReader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
            "storeWriter": StoreWriter {
              "cache": [Circular],
              "fragments": undefined,
              "reader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
            },
            "txCount": 0,
            "watches": Set {},
          },
          "clearStoreCallbacks": Array [],
          "defaultOptions": Object {},
          "disableNetworkFetches": false,
          "link": ApolloLink {
            "request": [Function],
          },
          "localState": LocalState {
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "client": [Circular],
            "selectionsToResolveCache": WeakMap {},
          },
          "mutate": [Function],
          "query": [Function],
          "queryDeduplication": true,
          "queryManager": QueryManager {
            "assumeImmutableResults": true,
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "clientAwareness": Object {
              "name": undefined,
              "version": undefined,
            },
            "defaultOptions": Object {},
            "documentTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "transform": [Function],
            },
            "fetchCancelFns": Map {},
            "inFlightLinkObservables": Map {},
            "link": ApolloLink {
              "request": [Function],
            },
            "localState": LocalState {
              "cache": InMemoryCache {
                "addTypename": true,
                "addTypenameTransform": DocumentTransform {
                  "resultCache": WeakSet {},
                  "stableCacheKeys": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "transform": [Function],
                },
                "assumeImmutableResults": true,
                "config": Object {
                  "addTypename": true,
                  "canonizeResults": false,
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "resultCaching": true,
                },
                "data": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": Stump {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": CacheGroup {
                        "caching": true,
                        "d": [Function],
                        "keyMaker": Trie {
                          "makeData": [Function],
                          "weakness": true,
                        },
                        "parent": null,
                      },
                    },
                    "id": "EntityStore.Stump",
                    "parent": [Circular],
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "replay": [Function],
                    "rootIds": Object {},
                    "toReference": [Function],
                  },
                  "toReference": [Function],
                },
                "getFragmentDoc": [Function],
                "makeVar": [Function],
                "maybeBroadcastWatch": [Function],
                "optimisticData": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": Root {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "rootIds": Object {},
                    "storageTrie": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "stump": [Circular],
                    "toReference": [Function],
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "storeReader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
                "storeWriter": StoreWriter {
                  "cache": [Circular],
                  "fragments": undefined,
                  "reader": StoreReader {
                    "canon": ObjectCanon {
                      "empty": Object {},
                      "keysByJSON": Map {
                        "[]" => Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "known": WeakSet {},
                      "passes": WeakMap {},
                      "pool": Trie {
                        "data": Object {
                          "keys": Object {
                            "json": "[]",
                            "sorted": Array [],
                          },
                        },
                        "makeData": [Function],
                        "weak": WeakMap {},
                        "weakness": true,
                      },
                    },
                    "config": Object {
                      "addTypename": true,
                      "cache": [Circular],
                      "canonizeResults": false,
                    },
                    "executeSelectionSet": [Function],
                    "executeSubSelectedArray": [Function],
                    "knownResults": WeakMap {},
                  },
                },
                "txCount": 0,
                "watches": Set {},
              },
              "client": [Circular],
              "selectionsToResolveCache": WeakMap {},
            },
            "mutationIdCounter": 1,
            "mutationStore": Object {},
            "onBroadcast": [Function],
            "queries": Map {},
            "queryDeduplication": true,
            "queryIdCounter": 1,
            "requestIdCounter": 1,
            "ssrMode": false,
            "transformCache": WeakMap {},
          },
          "reFetchObservableQueries": [Function],
          "resetStore": [Function],
          "resetStoreCallbacks": Array [],
          "typeDefs": undefined,
          "version": "3.8.4",
          "watchQuery": [Function],
        }
      }
    />
    <AverageEfficiency
      client={
        ApolloClient {
          "cache": InMemoryCache {
            "addTypename": true,
            "addTypenameTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "stableCacheKeys": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "transform": [Function],
            },
            "assumeImmutableResults": true,
            "config": Object {
              "addTypename": true,
              "canonizeResults": false,
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "CaseReview": Array [
                  "FullReview",
                ],
              },
              "resultCaching": true,
            },
            "data": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": [Circular],
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "toReference": [Function],
            },
            "getFragmentDoc": [Function],
            "makeVar": [Function],
            "maybeBroadcastWatch": [Function],
            "optimisticData": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": [Circular],
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "CaseReview" => Set {},
                "FullReview" => Set {
                  "CaseReview",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "storeReader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
            "storeWriter": StoreWriter {
              "cache": [Circular],
              "fragments": undefined,
              "reader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
            },
            "txCount": 0,
            "watches": Set {},
          },
          "clearStoreCallbacks": Array [],
          "defaultOptions": Object {},
          "disableNetworkFetches": false,
          "link": ApolloLink {
            "request": [Function],
          },
          "localState": LocalState {
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "client": [Circular],
            "selectionsToResolveCache": WeakMap {},
          },
          "mutate": [Function],
          "query": [Function],
          "queryDeduplication": true,
          "queryManager": QueryManager {
            "assumeImmutableResults": true,
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "clientAwareness": Object {
              "name": undefined,
              "version": undefined,
            },
            "defaultOptions": Object {},
            "documentTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "transform": [Function],
            },
            "fetchCancelFns": Map {},
            "inFlightLinkObservables": Map {},
            "link": ApolloLink {
              "request": [Function],
            },
            "localState": LocalState {
              "cache": InMemoryCache {
                "addTypename": true,
                "addTypenameTransform": DocumentTransform {
                  "resultCache": WeakSet {},
                  "stableCacheKeys": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "transform": [Function],
                },
                "assumeImmutableResults": true,
                "config": Object {
                  "addTypename": true,
                  "canonizeResults": false,
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "resultCaching": true,
                },
                "data": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": Stump {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": CacheGroup {
                        "caching": true,
                        "d": [Function],
                        "keyMaker": Trie {
                          "makeData": [Function],
                          "weakness": true,
                        },
                        "parent": null,
                      },
                    },
                    "id": "EntityStore.Stump",
                    "parent": [Circular],
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "replay": [Function],
                    "rootIds": Object {},
                    "toReference": [Function],
                  },
                  "toReference": [Function],
                },
                "getFragmentDoc": [Function],
                "makeVar": [Function],
                "maybeBroadcastWatch": [Function],
                "optimisticData": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": Root {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "rootIds": Object {},
                    "storageTrie": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "stump": [Circular],
                    "toReference": [Function],
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "storeReader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
                "storeWriter": StoreWriter {
                  "cache": [Circular],
                  "fragments": undefined,
                  "reader": StoreReader {
                    "canon": ObjectCanon {
                      "empty": Object {},
                      "keysByJSON": Map {
                        "[]" => Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "known": WeakSet {},
                      "passes": WeakMap {},
                      "pool": Trie {
                        "data": Object {
                          "keys": Object {
                            "json": "[]",
                            "sorted": Array [],
                          },
                        },
                        "makeData": [Function],
                        "weak": WeakMap {},
                        "weakness": true,
                      },
                    },
                    "config": Object {
                      "addTypename": true,
                      "cache": [Circular],
                      "canonizeResults": false,
                    },
                    "executeSelectionSet": [Function],
                    "executeSubSelectedArray": [Function],
                    "knownResults": WeakMap {},
                  },
                },
                "txCount": 0,
                "watches": Set {},
              },
              "client": [Circular],
              "selectionsToResolveCache": WeakMap {},
            },
            "mutationIdCounter": 1,
            "mutationStore": Object {},
            "onBroadcast": [Function],
            "queries": Map {},
            "queryDeduplication": true,
            "queryIdCounter": 1,
            "requestIdCounter": 1,
            "ssrMode": false,
            "transformCache": WeakMap {},
          },
          "reFetchObservableQueries": [Function],
          "resetStore": [Function],
          "resetStoreCallbacks": Array [],
          "typeDefs": undefined,
          "version": "3.8.4",
          "watchQuery": [Function],
        }
      }
    />
    <CommittedHours
      client={
        ApolloClient {
          "cache": InMemoryCache {
            "addTypename": true,
            "addTypenameTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "stableCacheKeys": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "transform": [Function],
            },
            "assumeImmutableResults": true,
            "config": Object {
              "addTypename": true,
              "canonizeResults": false,
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "CaseReview": Array [
                  "FullReview",
                ],
              },
              "resultCaching": true,
            },
            "data": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": [Circular],
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "toReference": [Function],
            },
            "getFragmentDoc": [Function],
            "makeVar": [Function],
            "maybeBroadcastWatch": [Function],
            "optimisticData": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": [Circular],
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "CaseReview" => Set {},
                "FullReview" => Set {
                  "CaseReview",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "storeReader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
            "storeWriter": StoreWriter {
              "cache": [Circular],
              "fragments": undefined,
              "reader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
            },
            "txCount": 0,
            "watches": Set {},
          },
          "clearStoreCallbacks": Array [],
          "defaultOptions": Object {},
          "disableNetworkFetches": false,
          "link": ApolloLink {
            "request": [Function],
          },
          "localState": LocalState {
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "client": [Circular],
            "selectionsToResolveCache": WeakMap {},
          },
          "mutate": [Function],
          "query": [Function],
          "queryDeduplication": true,
          "queryManager": QueryManager {
            "assumeImmutableResults": true,
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "clientAwareness": Object {
              "name": undefined,
              "version": undefined,
            },
            "defaultOptions": Object {},
            "documentTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "transform": [Function],
            },
            "fetchCancelFns": Map {},
            "inFlightLinkObservables": Map {},
            "link": ApolloLink {
              "request": [Function],
            },
            "localState": LocalState {
              "cache": InMemoryCache {
                "addTypename": true,
                "addTypenameTransform": DocumentTransform {
                  "resultCache": WeakSet {},
                  "stableCacheKeys": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "transform": [Function],
                },
                "assumeImmutableResults": true,
                "config": Object {
                  "addTypename": true,
                  "canonizeResults": false,
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "resultCaching": true,
                },
                "data": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": Stump {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": CacheGroup {
                        "caching": true,
                        "d": [Function],
                        "keyMaker": Trie {
                          "makeData": [Function],
                          "weakness": true,
                        },
                        "parent": null,
                      },
                    },
                    "id": "EntityStore.Stump",
                    "parent": [Circular],
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "replay": [Function],
                    "rootIds": Object {},
                    "toReference": [Function],
                  },
                  "toReference": [Function],
                },
                "getFragmentDoc": [Function],
                "makeVar": [Function],
                "maybeBroadcastWatch": [Function],
                "optimisticData": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": Root {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "rootIds": Object {},
                    "storageTrie": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "stump": [Circular],
                    "toReference": [Function],
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "storeReader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
                "storeWriter": StoreWriter {
                  "cache": [Circular],
                  "fragments": undefined,
                  "reader": StoreReader {
                    "canon": ObjectCanon {
                      "empty": Object {},
                      "keysByJSON": Map {
                        "[]" => Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "known": WeakSet {},
                      "passes": WeakMap {},
                      "pool": Trie {
                        "data": Object {
                          "keys": Object {
                            "json": "[]",
                            "sorted": Array [],
                          },
                        },
                        "makeData": [Function],
                        "weak": WeakMap {},
                        "weakness": true,
                      },
                    },
                    "config": Object {
                      "addTypename": true,
                      "cache": [Circular],
                      "canonizeResults": false,
                    },
                    "executeSelectionSet": [Function],
                    "executeSubSelectedArray": [Function],
                    "knownResults": WeakMap {},
                  },
                },
                "txCount": 0,
                "watches": Set {},
              },
              "client": [Circular],
              "selectionsToResolveCache": WeakMap {},
            },
            "mutationIdCounter": 1,
            "mutationStore": Object {},
            "onBroadcast": [Function],
            "queries": Map {},
            "queryDeduplication": true,
            "queryIdCounter": 1,
            "requestIdCounter": 1,
            "ssrMode": false,
            "transformCache": WeakMap {},
          },
          "reFetchObservableQueries": [Function],
          "resetStore": [Function],
          "resetStoreCallbacks": Array [],
          "typeDefs": undefined,
          "version": "3.8.4",
          "watchQuery": [Function],
        }
      }
    />
    <MyIncompleteActivities
      client={
        ApolloClient {
          "cache": InMemoryCache {
            "addTypename": true,
            "addTypenameTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "stableCacheKeys": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "transform": [Function],
            },
            "assumeImmutableResults": true,
            "config": Object {
              "addTypename": true,
              "canonizeResults": false,
              "dataIdFromObject": [Function],
              "possibleTypes": Object {
                "CaseReview": Array [
                  "FullReview",
                ],
              },
              "resultCaching": true,
            },
            "data": Root {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": null,
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "rootIds": Object {},
              "storageTrie": Trie {
                "makeData": [Function],
                "weakness": true,
              },
              "stump": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": [Circular],
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "toReference": [Function],
            },
            "getFragmentDoc": [Function],
            "makeVar": [Function],
            "maybeBroadcastWatch": [Function],
            "optimisticData": Stump {
              "canRead": [Function],
              "data": Object {},
              "getFieldValue": [Function],
              "group": CacheGroup {
                "caching": true,
                "d": [Function],
                "keyMaker": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "parent": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
              },
              "id": "EntityStore.Stump",
              "parent": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": [Circular],
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "refs": Object {},
              "replay": [Function],
              "rootIds": Object {},
              "toReference": [Function],
            },
            "policies": Policies {
              "cache": [Circular],
              "config": Object {
                "cache": [Circular],
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "typePolicies": undefined,
              },
              "fuzzySubtypes": Map {},
              "rootIdsByTypename": Object {
                "Mutation": "ROOT_MUTATION",
                "Query": "ROOT_QUERY",
                "Subscription": "ROOT_SUBSCRIPTION",
              },
              "rootTypenamesById": Object {
                "ROOT_MUTATION": "Mutation",
                "ROOT_QUERY": "Query",
                "ROOT_SUBSCRIPTION": "Subscription",
              },
              "supertypeMap": Map {
                "CaseReview" => Set {},
                "FullReview" => Set {
                  "CaseReview",
                },
              },
              "toBeAdded": Object {},
              "typePolicies": Object {},
              "usingPossibleTypes": true,
            },
            "storeReader": StoreReader {
              "canon": ObjectCanon {
                "empty": Object {},
                "keysByJSON": Map {
                  "[]" => Object {
                    "json": "[]",
                    "sorted": Array [],
                  },
                },
                "known": WeakSet {},
                "passes": WeakMap {},
                "pool": Trie {
                  "data": Object {
                    "keys": Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "makeData": [Function],
                  "weak": WeakMap {},
                  "weakness": true,
                },
              },
              "config": Object {
                "addTypename": true,
                "cache": [Circular],
                "canonizeResults": false,
              },
              "executeSelectionSet": [Function],
              "executeSubSelectedArray": [Function],
              "knownResults": WeakMap {},
            },
            "storeWriter": StoreWriter {
              "cache": [Circular],
              "fragments": undefined,
              "reader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
            },
            "txCount": 0,
            "watches": Set {},
          },
          "clearStoreCallbacks": Array [],
          "defaultOptions": Object {},
          "disableNetworkFetches": false,
          "link": ApolloLink {
            "request": [Function],
          },
          "localState": LocalState {
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "client": [Circular],
            "selectionsToResolveCache": WeakMap {},
          },
          "mutate": [Function],
          "query": [Function],
          "queryDeduplication": true,
          "queryManager": QueryManager {
            "assumeImmutableResults": true,
            "cache": InMemoryCache {
              "addTypename": true,
              "addTypenameTransform": DocumentTransform {
                "resultCache": WeakSet {},
                "stableCacheKeys": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "transform": [Function],
              },
              "assumeImmutableResults": true,
              "config": Object {
                "addTypename": true,
                "canonizeResults": false,
                "dataIdFromObject": [Function],
                "possibleTypes": Object {
                  "CaseReview": Array [
                    "FullReview",
                  ],
                },
                "resultCaching": true,
              },
              "data": Root {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": null,
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "rootIds": Object {},
                "storageTrie": Trie {
                  "makeData": [Function],
                  "weakness": true,
                },
                "stump": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": [Circular],
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "toReference": [Function],
              },
              "getFragmentDoc": [Function],
              "makeVar": [Function],
              "maybeBroadcastWatch": [Function],
              "optimisticData": Stump {
                "canRead": [Function],
                "data": Object {},
                "getFieldValue": [Function],
                "group": CacheGroup {
                  "caching": true,
                  "d": [Function],
                  "keyMaker": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "parent": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                },
                "id": "EntityStore.Stump",
                "parent": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": [Circular],
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "refs": Object {},
                "replay": [Function],
                "rootIds": Object {},
                "toReference": [Function],
              },
              "policies": Policies {
                "cache": [Circular],
                "config": Object {
                  "cache": [Circular],
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "typePolicies": undefined,
                },
                "fuzzySubtypes": Map {},
                "rootIdsByTypename": Object {
                  "Mutation": "ROOT_MUTATION",
                  "Query": "ROOT_QUERY",
                  "Subscription": "ROOT_SUBSCRIPTION",
                },
                "rootTypenamesById": Object {
                  "ROOT_MUTATION": "Mutation",
                  "ROOT_QUERY": "Query",
                  "ROOT_SUBSCRIPTION": "Subscription",
                },
                "supertypeMap": Map {
                  "CaseReview" => Set {},
                  "FullReview" => Set {
                    "CaseReview",
                  },
                },
                "toBeAdded": Object {},
                "typePolicies": Object {},
                "usingPossibleTypes": true,
              },
              "storeReader": StoreReader {
                "canon": ObjectCanon {
                  "empty": Object {},
                  "keysByJSON": Map {
                    "[]" => Object {
                      "json": "[]",
                      "sorted": Array [],
                    },
                  },
                  "known": WeakSet {},
                  "passes": WeakMap {},
                  "pool": Trie {
                    "data": Object {
                      "keys": Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "makeData": [Function],
                    "weak": WeakMap {},
                    "weakness": true,
                  },
                },
                "config": Object {
                  "addTypename": true,
                  "cache": [Circular],
                  "canonizeResults": false,
                },
                "executeSelectionSet": [Function],
                "executeSubSelectedArray": [Function],
                "knownResults": WeakMap {},
              },
              "storeWriter": StoreWriter {
                "cache": [Circular],
                "fragments": undefined,
                "reader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
              },
              "txCount": 0,
              "watches": Set {},
            },
            "clientAwareness": Object {
              "name": undefined,
              "version": undefined,
            },
            "defaultOptions": Object {},
            "documentTransform": DocumentTransform {
              "resultCache": WeakSet {},
              "transform": [Function],
            },
            "fetchCancelFns": Map {},
            "inFlightLinkObservables": Map {},
            "link": ApolloLink {
              "request": [Function],
            },
            "localState": LocalState {
              "cache": InMemoryCache {
                "addTypename": true,
                "addTypenameTransform": DocumentTransform {
                  "resultCache": WeakSet {},
                  "stableCacheKeys": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "transform": [Function],
                },
                "assumeImmutableResults": true,
                "config": Object {
                  "addTypename": true,
                  "canonizeResults": false,
                  "dataIdFromObject": [Function],
                  "possibleTypes": Object {
                    "CaseReview": Array [
                      "FullReview",
                    ],
                  },
                  "resultCaching": true,
                },
                "data": Root {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": null,
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "rootIds": Object {},
                  "storageTrie": Trie {
                    "makeData": [Function],
                    "weakness": true,
                  },
                  "stump": Stump {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": CacheGroup {
                        "caching": true,
                        "d": [Function],
                        "keyMaker": Trie {
                          "makeData": [Function],
                          "weakness": true,
                        },
                        "parent": null,
                      },
                    },
                    "id": "EntityStore.Stump",
                    "parent": [Circular],
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "replay": [Function],
                    "rootIds": Object {},
                    "toReference": [Function],
                  },
                  "toReference": [Function],
                },
                "getFragmentDoc": [Function],
                "makeVar": [Function],
                "maybeBroadcastWatch": [Function],
                "optimisticData": Stump {
                  "canRead": [Function],
                  "data": Object {},
                  "getFieldValue": [Function],
                  "group": CacheGroup {
                    "caching": true,
                    "d": [Function],
                    "keyMaker": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "parent": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                  },
                  "id": "EntityStore.Stump",
                  "parent": Root {
                    "canRead": [Function],
                    "data": Object {},
                    "getFieldValue": [Function],
                    "group": CacheGroup {
                      "caching": true,
                      "d": [Function],
                      "keyMaker": Trie {
                        "makeData": [Function],
                        "weakness": true,
                      },
                      "parent": null,
                    },
                    "policies": Policies {
                      "cache": [Circular],
                      "config": Object {
                        "cache": [Circular],
                        "dataIdFromObject": [Function],
                        "possibleTypes": Object {
                          "CaseReview": Array [
                            "FullReview",
                          ],
                        },
                        "typePolicies": undefined,
                      },
                      "fuzzySubtypes": Map {},
                      "rootIdsByTypename": Object {
                        "Mutation": "ROOT_MUTATION",
                        "Query": "ROOT_QUERY",
                        "Subscription": "ROOT_SUBSCRIPTION",
                      },
                      "rootTypenamesById": Object {
                        "ROOT_MUTATION": "Mutation",
                        "ROOT_QUERY": "Query",
                        "ROOT_SUBSCRIPTION": "Subscription",
                      },
                      "supertypeMap": Map {
                        "CaseReview" => Set {},
                        "FullReview" => Set {
                          "CaseReview",
                        },
                      },
                      "toBeAdded": Object {},
                      "typePolicies": Object {},
                      "usingPossibleTypes": true,
                    },
                    "refs": Object {},
                    "rootIds": Object {},
                    "storageTrie": Trie {
                      "makeData": [Function],
                      "weakness": true,
                    },
                    "stump": [Circular],
                    "toReference": [Function],
                  },
                  "policies": Policies {
                    "cache": [Circular],
                    "config": Object {
                      "cache": [Circular],
                      "dataIdFromObject": [Function],
                      "possibleTypes": Object {
                        "CaseReview": Array [
                          "FullReview",
                        ],
                      },
                      "typePolicies": undefined,
                    },
                    "fuzzySubtypes": Map {},
                    "rootIdsByTypename": Object {
                      "Mutation": "ROOT_MUTATION",
                      "Query": "ROOT_QUERY",
                      "Subscription": "ROOT_SUBSCRIPTION",
                    },
                    "rootTypenamesById": Object {
                      "ROOT_MUTATION": "Mutation",
                      "ROOT_QUERY": "Query",
                      "ROOT_SUBSCRIPTION": "Subscription",
                    },
                    "supertypeMap": Map {
                      "CaseReview" => Set {},
                      "FullReview" => Set {
                        "CaseReview",
                      },
                    },
                    "toBeAdded": Object {},
                    "typePolicies": Object {},
                    "usingPossibleTypes": true,
                  },
                  "refs": Object {},
                  "replay": [Function],
                  "rootIds": Object {},
                  "toReference": [Function],
                },
                "policies": Policies {
                  "cache": [Circular],
                  "config": Object {
                    "cache": [Circular],
                    "dataIdFromObject": [Function],
                    "possibleTypes": Object {
                      "CaseReview": Array [
                        "FullReview",
                      ],
                    },
                    "typePolicies": undefined,
                  },
                  "fuzzySubtypes": Map {},
                  "rootIdsByTypename": Object {
                    "Mutation": "ROOT_MUTATION",
                    "Query": "ROOT_QUERY",
                    "Subscription": "ROOT_SUBSCRIPTION",
                  },
                  "rootTypenamesById": Object {
                    "ROOT_MUTATION": "Mutation",
                    "ROOT_QUERY": "Query",
                    "ROOT_SUBSCRIPTION": "Subscription",
                  },
                  "supertypeMap": Map {
                    "CaseReview" => Set {},
                    "FullReview" => Set {
                      "CaseReview",
                    },
                  },
                  "toBeAdded": Object {},
                  "typePolicies": Object {},
                  "usingPossibleTypes": true,
                },
                "storeReader": StoreReader {
                  "canon": ObjectCanon {
                    "empty": Object {},
                    "keysByJSON": Map {
                      "[]" => Object {
                        "json": "[]",
                        "sorted": Array [],
                      },
                    },
                    "known": WeakSet {},
                    "passes": WeakMap {},
                    "pool": Trie {
                      "data": Object {
                        "keys": Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "makeData": [Function],
                      "weak": WeakMap {},
                      "weakness": true,
                    },
                  },
                  "config": Object {
                    "addTypename": true,
                    "cache": [Circular],
                    "canonizeResults": false,
                  },
                  "executeSelectionSet": [Function],
                  "executeSubSelectedArray": [Function],
                  "knownResults": WeakMap {},
                },
                "storeWriter": StoreWriter {
                  "cache": [Circular],
                  "fragments": undefined,
                  "reader": StoreReader {
                    "canon": ObjectCanon {
                      "empty": Object {},
                      "keysByJSON": Map {
                        "[]" => Object {
                          "json": "[]",
                          "sorted": Array [],
                        },
                      },
                      "known": WeakSet {},
                      "passes": WeakMap {},
                      "pool": Trie {
                        "data": Object {
                          "keys": Object {
                            "json": "[]",
                            "sorted": Array [],
                          },
                        },
                        "makeData": [Function],
                        "weak": WeakMap {},
                        "weakness": true,
                      },
                    },
                    "config": Object {
                      "addTypename": true,
                      "cache": [Circular],
                      "canonizeResults": false,
                    },
                    "executeSelectionSet": [Function],
                    "executeSubSelectedArray": [Function],
                    "knownResults": WeakMap {},
                  },
                },
                "txCount": 0,
                "watches": Set {},
              },
              "client": [Circular],
              "selectionsToResolveCache": WeakMap {},
            },
            "mutationIdCounter": 1,
            "onBroadcast": undefined,
            "queries": Map {},
            "queryDeduplication": true,
            "queryIdCounter": 1,
            "requestIdCounter": 1,
            "ssrMode": false,
            "transformCache": WeakMap {},
          },
          "reFetchObservableQueries": [Function],
          "resetStore": [Function],
          "resetStoreCallbacks": Array [],
          "typeDefs": undefined,
          "version": "3.8.4",
          "watchQuery": [Function],
        }
      }
    />
  </div>
</div>
`;
