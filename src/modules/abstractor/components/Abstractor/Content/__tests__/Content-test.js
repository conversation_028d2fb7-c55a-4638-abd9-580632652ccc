import { create } from "react-test-renderer";
import Content from "..";

jest.mock("../DataElementMatch", () => "DataElementMatch");
jest.mock("../AverageEfficiency", () => "AverageEfficiency");
jest.mock("../AccessMyApps", () => "AccessMyApps");
jest.mock("../CommittedHours", () => "CommittedHours");
jest.mock("../MyIncompleteActivities", () => "MyIncompleteActivities");

describe("Content", () => {
  function render() {
    return create(<Content />);
  }

  test("renders Content", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
