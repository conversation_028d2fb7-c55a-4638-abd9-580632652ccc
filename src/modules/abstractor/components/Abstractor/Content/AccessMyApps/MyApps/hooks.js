import { useMemo } from "react";
import { useQuery } from "@apollo/client";
import { prop, filter } from "ramda";
import { GET_AVAILABLE_APPS_DATA } from "modules/app/graphql/query";

const updateApps = data => {
  const { userApps = [] } = data;

  return filter(prop("enabled"), userApps);
};

export const useComponentLogic = () => {
  const {
    data = {},
    loading,
    called,
    error
  } = useQuery(GET_AVAILABLE_APPS_DATA, {
    // eslint-disable-next-line no-empty-function
    onError: () => {}
  });

  const apps = useMemo(() => updateApps(data), [data]);

  return {
    apps,
    loading,
    called,
    error
  };
};
