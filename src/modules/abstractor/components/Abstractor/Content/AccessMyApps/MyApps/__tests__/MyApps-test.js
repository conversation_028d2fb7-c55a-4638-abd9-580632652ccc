import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import wait from "waait";
import MyApps from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner"
}));

describe("MyApps", () => {
  function render() {
    return create(
      decoratedApollo({
        component: MyApps,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
