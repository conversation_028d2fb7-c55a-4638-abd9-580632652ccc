// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MyApps it renders component correctly 1`] = `
<div
  className="apps loaded"
>
  <a
    href="/case_detail"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-case_detail"
        />
      </div>
      <div
        className="app-name"
      >
        Case Detail
      </div>
    </div>
  </a>
  <a
    href="/qapps/upload/case_uploads"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-case_upload"
        />
      </div>
      <div
        className="app-name"
      >
        Case Upload
      </div>
    </div>
  </a>
  <a
    href="/client_dashboard"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-client_dashboard"
        />
      </div>
      <div
        className="app-name"
      >
        Q-Card
      </div>
    </div>
  </a>
  <a
    href="/concurrent"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-concurrent"
        />
      </div>
      <div
        className="app-name"
      >
        Concurrent Review
      </div>
    </div>
  </a>
  <a
    href="http://localhost:4000/quality-report"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-ecqm"
        />
      </div>
      <div
        className="app-name"
      >
        eCQM
      </div>
    </div>
  </a>
  <a
    href="/exception_report"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-exception_report"
        />
      </div>
      <div
        className="app-name"
      >
        Exception Reporting
      </div>
    </div>
  </a>
  <a
    href="/qapps"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-qapps"
        />
      </div>
      <div
        className="app-name"
      >
        Workflow
      </div>
    </div>
  </a>
  <a
    href="/admin/patients"
  >
    <div
      className="app-card"
    >
      <div
        className="app-icon"
      >
        <i
          className="fa-kit icon-registry"
        />
      </div>
      <div
        className="app-name"
      >
        Universal Registry
      </div>
    </div>
  </a>
</div>
`;
