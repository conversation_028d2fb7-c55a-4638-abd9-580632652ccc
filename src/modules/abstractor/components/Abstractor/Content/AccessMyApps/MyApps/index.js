import { useComponentLogic } from "./hooks";
import { Spinner } from "@q-centrix/q-components-react";
import CardInfo from "./CardInfo";
import classNames from "classnames";

// eslint-disable-next-line complexity
const MyApps = () => {
  const { apps, loading, called, error } = useComponentLogic();

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (loading)
    return (
      <div className="data-element-match">
        <Spinner />
      </div>
    );

  const containerClass = classNames("apps", {
    loaded: !loading && called,
    loading
  });

  return (
    <div className={containerClass}>
      {apps.map(app => (
        <CardInfo key={app.key} name={app.name} url={app.path} icon={app.key} />
      ))}
    </div>
  );
};

export default MyApps;
