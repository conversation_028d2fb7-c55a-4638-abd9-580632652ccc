import { GET_AVAILABLE_APPS_DATA } from "modules/app/graphql/query";

const availableAppDataMock = [
  {
    request: {
      query: GET_AVAILABLE_APPS_DATA
    },
    result: {
      data: {
        userApps: [
          {
            name: "Case Detail",
            path: "/case_detail",
            key: "case_detail",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Case Upload",
            path: "/qapps/upload/case_uploads",
            key: "case_upload",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Q-Card",
            path: "/client_dashboard",
            key: "client_dashboard",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Concurrent Review",
            path: "/concurrent",
            key: "concurrent",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "eCQM",
            path: "http://localhost:4000/quality-report",
            key: "ecqm",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Exception Reporting",
            path: "/exception_report",
            key: "exception_report",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Infection Prevention",
            path: "/infection_control",
            key: "infection_control",
            enabled: false,
            description:
              "Review your cultures using National Healthcare Safety Network protocols for evidence of surgical site infections, central line-associated bloodstream infections, sepsis, urinary tract infections, MRSA, C. Diff and ventilator-associated infections.",
            icon: "infection_control"
          },
          {
            name: "Workflow",
            path: "/qapps",
            key: "qapps",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Universal Registry",
            path: "/admin/patients",
            key: "registry",
            enabled: true,
            description: "",
            __typename: "UserApp"
          },
          {
            name: "Peer Review",
            description:
              "We perform focused, impartial evaluations of your clinical documentation to streamline your peer review process and help you deliver targeted provider training.",
            key: "peer_review",
            enabled: false,
            path: "",
            __typename: "UserApp"
          }
        ]
      }
    }
  }
];

export default availableAppDataMock;
