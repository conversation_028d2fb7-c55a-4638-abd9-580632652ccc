import { GET_CURRENT_USER_AVAILABLE_FACILITIES } from "modules/app/graphql/query";

const facilitiesSuccess = {
  data: {
    currentUserAvailableFacilities: {
      count: 2,
      page: 1,
      perPage: 100,
      __typename: "UserFacilities",
      userAvailableFacilities: [
        { id: "1", name: "Large Medical Center", __typename: "Facility" },
        { id: "2", name: "Small Medical Center", __typename: "Facility" }
      ]
    }
  }
};

export default [
  {
    request: {
      query: GET_CURRENT_USER_AVAILABLE_FACILITIES,
      variables: { perPage: 25 }
    },
    result: facilitiesSuccess
  }
];
