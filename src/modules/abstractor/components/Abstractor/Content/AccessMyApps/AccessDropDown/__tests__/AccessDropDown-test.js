import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import AccessDropDown from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner"
}));

const appValues = {
  facility: {
    id: "1",
    name: "Large Medical Facility",
    isLoaded: true
  }
};

describe("AccessDropDown", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: AccessDropDown,
        props,
        initialValues: {},
        initialAppValues: appValues,
        apolloMocks: mocks
      })
    );
  }

  test("renders component while loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("renders component", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
