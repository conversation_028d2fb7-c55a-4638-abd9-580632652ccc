import { useMemo } from "react";
import { useSelector } from "react-redux";
import facilitySelector from "modules/app/redux/selectors/facility";
import useSelect from "shared/hooks/useSelect";
import formatSelectOption from "utils/fp/formatSelectOption";
import { GET_CURRENT_USER_AVAILABLE_FACILITIES } from "modules/app/graphql/query";
import { always, both, identity, ifElse, pipe, prop } from "ramda";

const formatFacility = ifElse(
  both(identity, pipe(prop("id"), identity)),
  formatSelectOption(["id", "name"]),
  always(undefined)
);

export const useComponentLogic = ({ handleSave }) => {
  const facility = useSelector(state => facilitySelector.facility(state));
  const formattedFacility = useMemo(() => formatFacility(facility), [facility]);
  const {
    value = formattedFacility,
    options,
    error,
    loading,
    called,
    onChange
  } = useSelect({
    baseOptions: [],
    inputValue: formattedFacility,
    onSave: selectedFacility => {
      handleSave(selectedFacility.value);
    },
    callQuery: facility.isLoaded,
    query: GET_CURRENT_USER_AVAILABLE_FACILITIES,
    queryObjectNamePath: [
      "currentUserAvailableFacilities",
      "userAvailableFacilities"
    ],
    fields: ["id", "name"],
    queryVariables: { perPage: 100 },
    defaultToFirst: false
  });

  return {
    error,
    loading,
    called,
    onChange,
    options,
    value
  };
};
