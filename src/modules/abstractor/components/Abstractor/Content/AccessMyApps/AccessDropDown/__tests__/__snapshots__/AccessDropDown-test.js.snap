// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AccessDropDown renders component 1`] = `
<div
  className="apps-dropdown"
>
  <h2>
    Access my apps
  </h2>
  <h2>
    <div
      className="facility-select-container css-2b097c-container"
      onKeyDown={[Function]}
    >
      <span
        aria-atomic="false"
        aria-live="polite"
        aria-relevant="additions text"
        className="css-1f43avz-a11yText-A11yText"
      />
      <div
        className="facility-select__control css-yk16xz-control"
        onMouseDown={[Function]}
        onTouchEnd={[Function]}
      >
        <div
          className="facility-select__value-container facility-select__value-container--has-value css-g1d714-ValueContainer"
        >
          <div
            className="facility-select__single-value css-1uccc91-singleValue"
          >
            Large Medical Facility
          </div>
          <div
            className="css-b8ldur-Input"
          >
            <div
              className="facility-select__input"
              style={
                Object {
                  "display": "inline-block",
                }
              }
            >
              <input
                aria-autocomplete="list"
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect="off"
                disabled={false}
                id="react-select-3-input"
                onBlur={[Function]}
                onChange={[Function]}
                onFocus={[Function]}
                spellCheck="false"
                style={
                  Object {
                    "background": 0,
                    "border": 0,
                    "boxSizing": "content-box",
                    "color": "inherit",
                    "fontSize": "inherit",
                    "label": "input",
                    "opacity": 1,
                    "outline": 0,
                    "padding": 0,
                    "width": "1px",
                  }
                }
                tabIndex="0"
                type="text"
                value=""
              />
              <div
                style={
                  Object {
                    "height": 0,
                    "left": 0,
                    "overflow": "scroll",
                    "position": "absolute",
                    "top": 0,
                    "visibility": "hidden",
                    "whiteSpace": "pre",
                  }
                }
              />
            </div>
          </div>
        </div>
        <div
          className="facility-select__indicators css-1hb7zxy-IndicatorsContainer"
        >
          <span
            className="facility-select__indicator-separator css-1okebmr-indicatorSeparator"
          />
          <div
            aria-hidden="true"
            className="facility-select__indicator facility-select__dropdown-indicator css-tlfecz-indicatorContainer"
            onMouseDown={[Function]}
            onTouchEnd={[Function]}
          >
            <svg
              aria-hidden="true"
              className="css-tj5bde-Svg"
              focusable="false"
              height={20}
              viewBox="0 0 20 20"
              width={20}
            >
              <path
                d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
              />
            </svg>
          </div>
        </div>
      </div>
      <input
        name="facility-select"
        type="hidden"
        value="1"
      />
    </div>
  </h2>
</div>
`;

exports[`AccessDropDown renders component while loading 1`] = `<Spinner />`;
