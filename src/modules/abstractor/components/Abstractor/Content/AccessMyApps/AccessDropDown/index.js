import Select from "react-select";
import { Spinner } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import classNames from "classnames";

export const AccessDropDown = props => {
  const { loading, called, onChange, options, value } =
    useComponentLogic(props);

  if (loading) return <Spinner />;

  const containerClass = classNames("apps-dropdown", {
    loaded: !loading && called,
    loading
  });

  return (
    <div className={containerClass}>
      <h2>Access my apps</h2>
      <h2>
        <Select
          classNamePrefix="facility-select"
          className="facility-select-container"
          name="facility-select"
          onChange={onChange}
          options={options}
          placeholder="Change Facility"
          value={value}
        />
      </h2>
    </div>
  );
};

export default AccessDropDown;
