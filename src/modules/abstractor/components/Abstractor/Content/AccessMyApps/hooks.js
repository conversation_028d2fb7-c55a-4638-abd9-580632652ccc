import { UPDATE_USER_FACILITY } from "modules/abstractor/graphql/mutation";
import { useMutation } from "@apollo/client";
import { GET_AVAILABLE_APPS_DATA } from "modules/app/graphql/query";
import { fetchFacility } from "modules/app/redux/actions";
import { useDispatch } from "react-redux";
import { useCallback } from "react";

export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const [handleUpdateFacility] = useMutation(UPDATE_USER_FACILITY, {
    refetchQueries: [GET_AVAILABLE_APPS_DATA]
  });

  const handleSave = useCallback(
    facilityId => {
      handleUpdateFacility({
        variables: {
          facilityId
        },
        onCompleted: () => {
          dispatch(fetchFacility());
        }
      });
    },
    [handleUpdateFacility]
  );

  return {
    handleSave
  };
};
