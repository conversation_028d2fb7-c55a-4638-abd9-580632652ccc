import DataElementMatch from "./DataElementMatch";
import AverageEfficiency from "./AverageEfficiency";
import AccessMyApps from "./AccessMyApps";
import CommittedHours from "./CommittedHours";
import MyIncompleteActivities from "./MyIncompleteActivities";
import { apolloClient } from "../../../../../base";

const client = apolloClient("/api/reports/data_mart/graphql");
const qappsClient = apolloClient("/qapps/graphql");
// ActiveCasesTable was removed as per UI-1904. Will need to be re-added in the future once configured.

const Content = () => (
  <div className="content">
    <div className="data">
      <div className="apps">
        <AccessMyApps />
      </div>
    </div>
    <div className="widgets">
      <DataElementMatch client={client} />
      <AverageEfficiency client={client} />
      <CommittedHours client={client} />
      <MyIncompleteActivities client={qappsClient} />
    </div>
  </div>
);

export default Content;
