import { useComponentLogic } from "./hooks";
import { DataCard, Spinner } from "@q-centrix/q-components-react";
import CardInfo from "./CardInfo";
import { formatToPercentageNumber } from "utils/formatPercentage";
import classNames from "classnames";

// eslint-disable-next-line complexity
const CommittedHours = props => {
  const { data, loading, called, error } = useComponentLogic(props);

  const containerClass = classNames("committed-hours", {
    loading,
    loaded: !loading && called
  });

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (loading)
    return (
      <div className="data-element-match">
        <Spinner />
      </div>
    );

  return (
    <DataCard
      title="Committed hours worked"
      subTitle="Past 4 weeks"
      value={formatToPercentageNumber(
        data.committedHoursHistoryForCurrentUser.average.amount
      )}
      type="percent"
      threshold={data.committedHoursHistoryForCurrentUser.average.thresholdMet}
    >
      <div className={containerClass}>
        {data.committedHoursHistoryForCurrentUser.history.map(detail => (
          <CardInfo key={detail.fromDate} detail={detail} />
        ))}
      </div>
    </DataCard>
  );
};

export default CommittedHours;
