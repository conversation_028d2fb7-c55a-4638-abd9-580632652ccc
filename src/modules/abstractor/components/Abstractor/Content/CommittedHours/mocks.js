import { GET_COMMITTED_HOURS_DATA } from "modules/abstractor/graphql/query";

const committedHoursDataMock = [
  {
    request: {
      query: GET_COMMITTED_HOURS_DATA
    },
    result: {
      data: {
        committedHoursHistoryForCurrentUser: {
          average: {
            amount: 0.97012,
            thresholdMet: false
          },
          history: [
            {
              amount: 1.0,
              fromDate: "2022-02-06",
              toDate: "2022-02-12",
              thresholdMet: true
            },
            {
              amount: 1.0,
              fromDate: "2022-02-13",
              toDate: "2022-02-19",
              thresholdMet: true
            },
            {
              amount: 1.0,
              fromDate: "2022-02-20",
              toDate: "2022-02-26",
              thresholdMet: true
            },
            {
              amount: 0.88045,
              fromDate: "2022-02-27",
              toDate: "2022-03-05",
              thresholdMet: false
            }
          ]
        }
      }
    }
  }
];

export default committedHoursDataMock;
