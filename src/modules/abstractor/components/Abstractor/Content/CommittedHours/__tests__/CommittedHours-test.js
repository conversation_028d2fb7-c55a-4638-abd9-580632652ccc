import { act, create } from "react-test-renderer";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import CommittedHours from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  DataCard: "DataCard",
  Spinner: "Spinner"
}));

describe("CommittedHours", () => {
  function render() {
    return create(
      decoratedApollo({
        component: CommittedHours,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders Loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders CommittedHours component", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
