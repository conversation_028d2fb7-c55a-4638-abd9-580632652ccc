// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommittedHours it renders CommittedHours component 1`] = `
<DataCard
  subTitle="Past 4 weeks"
  threshold={false}
  title="Committed hours worked"
  type="percent"
  value="97.01"
>
  <div
    className="committed-hours loaded"
  >
    <div
      className="value-container"
    >
      <div
        className="above"
      >
        100.00%
      </div>
      <div
        className="hours-date"
      >
        2/6
         - 
        2/12
      </div>
    </div>
    <div
      className="value-container"
    >
      <div
        className="above"
      >
        100.00%
      </div>
      <div
        className="hours-date"
      >
        2/13
         - 
        2/19
      </div>
    </div>
    <div
      className="value-container"
    >
      <div
        className="above"
      >
        100.00%
      </div>
      <div
        className="hours-date"
      >
        2/20
         - 
        2/26
      </div>
    </div>
    <div
      className="value-container"
    >
      <div
        className="below"
      >
        88.05%
      </div>
      <div
        className="hours-date"
      >
        2/27
         - 
        3/5
      </div>
    </div>
  </div>
</DataCard>
`;

exports[`CommittedHours it renders Loading 1`] = `
<div
  className="data-element-match"
>
  <Spinner />
</div>
`;
