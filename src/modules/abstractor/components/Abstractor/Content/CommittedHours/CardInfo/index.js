import classnames from "classnames";
import { formatISODateString } from "utils/formatISODateString";
import formatPercentage from "utils/formatPercentage";

const CardInfo = props => {
  const { detail } = props;
  const formattedStartDate = formatISODateString(detail.fromDate, "M/d");
  const formattedEndDate = formatISODateString(detail.toDate, "M/d");
  const dataElement = classnames({
    above: detail.thresholdMet,
    below: !detail.thresholdMet
  });

  return (
    <div className="value-container">
      <div className={dataElement}>{formatPercentage(detail.amount)}</div>
      <div className="hours-date">
        {formattedStartDate} - {formattedEndDate}
      </div>
    </div>
  );
};

export default CardInfo;
