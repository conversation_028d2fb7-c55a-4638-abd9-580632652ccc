import TableFooter from "./TableFooter";
import TableRow from "./TableRow";
import { useComponentLogic } from "./hooks";
import RowHeader from "./RowHeader";
import "styles/active-cases-table.scss";
import { keys } from "ramda";
import { Overlay } from "@q-centrix/q-components-react";

const ActiveCasesTable = () => {
  const {
    sortBy,
    sortDirection,
    activeFacilities,
    totalsCount,
    headers,
    handleHeaderClick
  } = useComponentLogic();

  return (
    <Overlay filterSize="4px">
      <table className="active-cases-table">
        <RowHeader
          headers={headers}
          handleHeaderClick={handleHeaderClick}
          sortBy={sortBy}
          sortDirection={sortDirection}
        />

        <tbody>
          {activeFacilities?.map(facility => (
            <TableRow key={facility.facilityId} facility={facility} />
          ))}
        </tbody>
        <tfoot>
          <tr>
            <td>Total</td>
            {totalsCount &&
              keys(totalsCount).map(key => (
                <TableFooter key={key} total={totalsCount[key]} />
              ))}
            <td />
          </tr>
          <tr>
            <td colSpan="7" />
          </tr>
        </tfoot>
      </table>
    </Overlay>
  );
};

export default ActiveCasesTable;
