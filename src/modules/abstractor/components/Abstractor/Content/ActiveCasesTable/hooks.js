// eslint-disable-next-line no-unused-vars
import { useQuery } from "@apollo/client";
import { useState, useCallback } from "react";
// eslint-disable-next-line no-unused-vars
import { GET_ACTIVE_CASES_DATA } from "modules/abstractor/graphql/query";
import { pipe, always, path } from "ramda";
import mocks from "./mocks";

const headers = [
  { title: "Facility" },
  { title: "Case Finding" },
  { title: "Abstractions" },
  { title: "Reviews" },
  { title: "IRR" },
  { title: "Exception Reporting" },
  { title: "Next Deadline" }
];

const toggleDirection = direction => (direction === "ASC" ? "DES" : "ASC");
const data = path([0, "result", "data", "activeCasesForCurrentUser"])(mocks);

export const useComponentLogic = () => {
  const [sortDirection, setSortDirection] = useState("ASC");
  const [sortBy, setSortBy] = useState(headers[0].title);
  // Uncomment lines below once My Active Case table is connected to BE
  // const [activeFacilities, setActiveFacilities] = useState([]);
  // const [totalsCount, setTotalsCount] = useState({});
  // const { loading, error } = useQuery(GET_ACTIVE_CASES_DATA, {
  //   variables: {
  //     sortBy,
  //     sortDirection
  //   },
  //   onCompleted: (data = {}) => {
  //     const {
  //       activeCasesForCurrentUser: { facilities = [], totals = {} }
  //     } = data;

  //     setTotalsCount(totals);
  //     setActiveFacilities(facilities);
  //   }
  // });

  const handleHeaderClick = useCallback(
    header => {
      setSortBy(
        pipe(
          oldSortBy =>
            setSortDirection(d =>
              header.title === oldSortBy ? toggleDirection(d) : "DES"
            ),
          always(header.title)
        )
      );
    },
    [setSortDirection, setSortBy]
  );

  return {
    // loading,
    // error,
    sortBy,
    sortDirection,
    activeFacilities: data.facilities,
    totalsCount: data.totals,
    headers,
    handleHeaderClick
  };
};
