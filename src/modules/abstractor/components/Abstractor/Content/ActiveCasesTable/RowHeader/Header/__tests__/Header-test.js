import { create } from "react-test-renderer";
import Header from "..";

describe("Header", () => {
  function render(props = {}) {
    return create(<Header {...props} />);
  }

  test("it renders component correctly", () => {
    const component = render({
      header: {
        title: "Test 1"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with ascending icon", () => {
    const component = render({
      header: {
        title: "Test 1"
      },
      sortDirection: "ASC",
      sortBy: "Test 1"
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with descending icon", () => {
    const component = render({
      header: {
        title: "Test 1"
      },
      sortDirection: "DES",
      sortBy: "Test 1"
    });

    expect(component).toMatchSnapshot();
  });
});
