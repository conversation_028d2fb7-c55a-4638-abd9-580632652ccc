import { useComponentLogic } from "./hooks";
import classnames from "classnames";

export const Header = props => {
  const { header, sortDirection } = props;
  // eslint-disable-next-line no-unused-vars
  const { handleHeaderClick, isSorting } = useComponentLogic(props);
  const sortClass = classnames("fa", {
    "fa-arrow-up": sortDirection === "ASC",
    "fa-arrow-down": sortDirection === "DES"
  });
  // Add handleHeaderClick to onClick once My Active Case table connected to BE

  return (
    // eslint-disable-next-line no-empty-function
    <th onClick={() => {}}>
      <div className="header-title">
        {header.title}
        {isSorting && (
          <span>
            <i className={sortClass} />
          </span>
        )}
      </div>
    </th>
  );
};

export default Header;
