import { create } from "react-test-renderer";
import RowHeader from "..";

jest.mock("../../TableHeader", () => "TableHeader");
jest.mock("../Header", () => "Header");

describe("RowHeader", () => {
  const headers = [
    {
      title: "Test 1"
    },
    { title: "Test 2" }
  ];

  function render() {
    return create(<RowHeader headers={headers} />);
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
