import Header from "./Header";
import TableHeader from "../TableHeader";

export const RowHeader = props => {
  const { headers, handleHeaderClick, sortDirection, sortBy } = props;

  return (
    <thead>
      <TableHeader />
      <tr>
        {headers.map(header => (
          <Header
            key={header}
            u
            header={header}
            onClick={handleHeaderClick}
            sortDirection={sortDirection}
            sortBy={sortBy}
          />
        ))}
      </tr>
    </thead>
  );
};

export default RowHeader;
