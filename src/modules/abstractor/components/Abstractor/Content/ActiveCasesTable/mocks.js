import { GET_ACTIVE_CASES_DATA } from "modules/abstractor/graphql/query";

const activeCasesDataMock = [
  {
    request: {
      query: GET_ACTIVE_CASES_DATA,
      variables: {
        sortBy: "Facility",
        sortDirection: "ASC"
      }
    },
    result: {
      data: {
        activeCasesForCurrentUser: {
          facilities: [
            {
              facilityId: 1,
              facilityName: "Chicago Hospital",
              caseFindings: 1,
              abstractions: 64,
              reviews: 90,
              irrs: 52,
              exceptionReportings: 18,
              nextDeadline: "02-15-2022"
            },
            {
              facilityId: 2,
              facilityName: "Quality Medical Center",
              caseFindings: 0,
              abstractions: 95,
              reviews: 88,
              irrs: 30,
              exceptionReportings: 73,
              nextDeadline: "02-28-2022"
            },
            {
              facilityId: 3,
              facilityName: "Q-Centrix University Hospital",
              caseFindings: 0,
              abstractions: 14,
              reviews: 62,
              irrs: 5,
              exceptionReportings: 89,
              nextDeadline: "02-15-2022"
            },
            {
              facilityId: 4,
              facilityName: "San Diego Medical Center",
              caseFindings: 0,
              abstractions: 67,
              reviews: 8,
              irrs: 31,
              exceptionReportings: 22,
              nextDeadline: "02-28-2022"
            }
          ],

          totals: {
            caseFindings: 1,
            abstractions: 240,
            reviews: 251,
            irrs: 118,
            exceptionReportings: 202
          }
        }
      }
    }
  },
  {
    request: {
      query: GET_ACTIVE_CASES_DATA,
      variables: {
        sortBy: "Facility",
        sortDirection: "DES"
      }
    },
    result: {
      data: {
        activeCasesForCurrentUser: {
          facilities: [
            {
              facilityId: 3,
              facilityName: "San Diego Medical Center",
              caseFindings: 0,
              abstractions: 67,
              reviews: 8,
              irrs: 31,
              exceptionReportings: 22,
              nextDeadline: "02-28-2022"
            },
            {
              facilityId: 4,
              facilityName: "Q-Centrix University Hospital",
              caseFindings: 0,
              abstractions: 14,
              reviews: 62,
              irrs: 5,
              exceptionReportings: 89,
              nextDeadline: "02-15-2022"
            },
            {
              facilityId: 2,
              facilityName: "Quality Medical Center",
              caseFindings: 0,
              abstractions: 95,
              reviews: 88,
              irrs: 30,
              exceptionReportings: 73,
              nextDeadline: "02-28-2022"
            },

            {
              facilityId: 1,
              facilityName: "Chicago Hospital",
              caseFindings: 1,
              abstractions: 64,
              reviews: 90,
              irrs: 52,
              exceptionReportings: 18,
              nextDeadline: "02-15-2022"
            }
          ],

          totals: {
            caseFindings: 1,
            abstractions: 240,
            reviews: 251,
            irrs: 118,
            exceptionReportings: 202
          }
        }
      }
    }
  }
];

export default [...activeCasesDataMock];
