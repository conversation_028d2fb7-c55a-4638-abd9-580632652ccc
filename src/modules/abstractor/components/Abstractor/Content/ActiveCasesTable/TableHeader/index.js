import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const TableHeader = () => {
  const { casesURL } = useComponentLogic();

  return (
    <tr>
      <th colSpan="5">
        <div className="table-top-title">My active cases</div>
      </th>
      <th colSpan="2">
        <div className="table-top-button">
          <Button bg="main">
            <a
              className="table-header-link"
              href={casesURL}
              target="_blank"
              rel="noreferrer"
            >
              <i className="far fa-eye" />
              View in Workflow
            </a>
          </Button>
        </div>
      </th>
    </tr>
  );
};

export default TableHeader;
