// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableHeader it renders component correctly 1`] = `
<tr>
  <th
    colSpan="5"
  >
    <div
      className="table-top-title"
    >
      My active cases
    </div>
  </th>
  <th
    colSpan="2"
  >
    <div
      className="table-top-button"
    >
      <Button
        bg="main"
      >
        <a
          className="table-header-link"
          href="/cases"
          rel="noreferrer"
          target="_blank"
        >
          <i
            className="far fa-eye"
          />
          View in Workflow
        </a>
      </Button>
    </div>
  </th>
</tr>
`;
