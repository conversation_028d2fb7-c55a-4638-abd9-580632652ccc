// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActiveCasesTable it renders component correctly 1`] = `
<Overlay
  filterSize="4px"
>
  <table
    className="active-cases-table"
  >
    <RowHeader
      handleHeaderClick={[Function]}
      headers={
        Array [
          Object {
            "title": "Facility",
          },
          Object {
            "title": "Case Finding",
          },
          Object {
            "title": "Abstractions",
          },
          Object {
            "title": "Reviews",
          },
          Object {
            "title": "IRR",
          },
          Object {
            "title": "Exception Reporting",
          },
          Object {
            "title": "Next Deadline",
          },
        ]
      }
      sortBy="Facility"
      sortDirection="ASC"
    />
    <tbody>
      <TableRow
        facility={
          Object {
            "abstractions": 64,
            "caseFindings": 1,
            "exceptionReportings": 18,
            "facilityId": 1,
            "facilityName": "Chicago Hospital",
            "irrs": 52,
            "nextDeadline": "02-15-2022",
            "reviews": 90,
          }
        }
      />
      <TableRow
        facility={
          Object {
            "abstractions": 95,
            "caseFindings": 0,
            "exceptionReportings": 73,
            "facilityId": 2,
            "facilityName": "Quality Medical Center",
            "irrs": 30,
            "nextDeadline": "02-28-2022",
            "reviews": 88,
          }
        }
      />
      <TableRow
        facility={
          Object {
            "abstractions": 14,
            "caseFindings": 0,
            "exceptionReportings": 89,
            "facilityId": 3,
            "facilityName": "Q-Centrix University Hospital",
            "irrs": 5,
            "nextDeadline": "02-15-2022",
            "reviews": 62,
          }
        }
      />
      <TableRow
        facility={
          Object {
            "abstractions": 67,
            "caseFindings": 0,
            "exceptionReportings": 22,
            "facilityId": 4,
            "facilityName": "San Diego Medical Center",
            "irrs": 31,
            "nextDeadline": "02-28-2022",
            "reviews": 8,
          }
        }
      />
    </tbody>
    <tfoot>
      <tr>
        <td>
          Total
        </td>
        <TableFooter
          total={1}
        />
        <TableFooter
          total={240}
        />
        <TableFooter
          total={251}
        />
        <TableFooter
          total={118}
        />
        <TableFooter
          total={202}
        />
        <td />
      </tr>
      <tr>
        <td
          colSpan="7"
        />
      </tr>
    </tfoot>
  </table>
</Overlay>
`;
