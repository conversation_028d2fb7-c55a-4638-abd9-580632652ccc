import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedApollo } from "utils/tests/decorated";
import ActiveCasesTable from "..";
import mocks from "../mocks";

jest.mock("../TableHeader", () => "TableHeader");
jest.mock("../RowHeader", () => "RowHeader");
jest.mock("../TableRow", () => "TableRow");
jest.mock("../TableFooter", () => "TableFooter");
jest.mock("@q-centrix/q-components-react", () => ({
  Overlay: "Overlay"
}));

describe("ActiveCasesTable", () => {
  function render() {
    return create(
      decoratedApollo({
        component: ActiveCasesTable,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
