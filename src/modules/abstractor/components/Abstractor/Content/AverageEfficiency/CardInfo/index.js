import classnames from "classnames";
import { formatISODateString } from "utils/formatISODateString";
import formatPercentage from "utils/formatPercentage";

const CardInfo = props => {
  const { detail } = props;
  const dataElement = classnames({
    above: detail.thresholdMet,
    below: !detail.thresholdMet
  });

  return (
    <div className="value-container">
      <div className={dataElement}>{formatPercentage(detail.amount)}</div>
      <div className="efficiency-date">
        {formatISODateString(detail.date, "MMM yyy")}
      </div>
    </div>
  );
};

export default CardInfo;
