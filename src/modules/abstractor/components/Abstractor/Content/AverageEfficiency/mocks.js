import { GET_EFFICIENCY_DATA } from "modules/abstractor/graphql/query";

const efficiencyDataMock = [
  {
    request: {
      query: GET_EFFICIENCY_DATA
    },
    result: {
      data: {
        efficiencyHistoryForCurrentUser: {
          average: {
            amount: 1.65,
            thresholdMet: true
          },
          history: [
            {
              date: "2022-05-01",
              amount: 2.2,
              thresholdMet: true
            },
            {
              date: "2022-06-01",
              amount: 0.9,
              thresholdMet: false
            },
            {
              date: "2022-07-01",
              amount: 1.7,
              thresholdMet: true
            }
          ]
        }
      }
    }
  }
];

export default efficiencyDataMock;
