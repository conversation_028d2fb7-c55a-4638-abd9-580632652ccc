import { act, create } from "react-test-renderer";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import AverageEfficiency from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  DataCard: "DataCard",
  Spinner: "Spinner"
}));

describe("AverageEfficiency", () => {
  function render() {
    return create(
      decoratedApollo({
        component: AverageEfficiency,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
