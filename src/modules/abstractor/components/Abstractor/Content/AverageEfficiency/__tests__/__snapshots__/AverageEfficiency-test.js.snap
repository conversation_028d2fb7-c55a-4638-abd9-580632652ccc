// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AverageEfficiency it renders component correctly 1`] = `
<DataCard
  subTitle="Past 3 months"
  threshold={true}
  title="Efficiency"
  type="percent"
  value="165.00"
>
  <div
    className="average-efficiency loaded"
  >
    <div
      className="value-container"
    >
      <div
        className="above"
      >
        220.00%
      </div>
      <div
        className="efficiency-date"
      >
        May 2022
      </div>
    </div>
    <div
      className="value-container"
    >
      <div
        className="below"
      >
        90.00%
      </div>
      <div
        className="efficiency-date"
      >
        Jun 2022
      </div>
    </div>
    <div
      className="value-container"
    >
      <div
        className="above"
      >
        170.00%
      </div>
      <div
        className="efficiency-date"
      >
        Jul 2022
      </div>
    </div>
  </div>
</DataCard>
`;
