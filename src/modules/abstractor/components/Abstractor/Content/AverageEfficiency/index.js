import { useComponentLogic } from "./hooks";
import { DataCard, Spinner } from "@q-centrix/q-components-react";
import CardInfo from "./CardInfo";
import { formatToPercentageNumber } from "utils/formatPercentage";
import classNames from "classnames";

// eslint-disable-next-line complexity
const AverageEfficiency = props => {
  const { data, loading, called, error } = useComponentLogic(props);

  const containerClass = classNames("average-efficiency", {
    loading,
    loaded: !loading && called
  });

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (loading)
    return (
      <div className="data-element-match">
        <Spinner />
      </div>
    );
  return (
    <DataCard
      title="Efficiency"
      subTitle="Past 3 months"
      value={formatToPercentageNumber(
        data.efficiencyHistoryForCurrentUser.average.amount
      )}
      type="percent"
      threshold={data.efficiencyHistoryForCurrentUser.average.thresholdMet}
    >
      <div className={containerClass}>
        {data.efficiencyHistoryForCurrentUser.history.map(detail => (
          <CardInfo key={detail.date} detail={detail} />
        ))}
      </div>
    </DataCard>
  );
};

export default AverageEfficiency;
