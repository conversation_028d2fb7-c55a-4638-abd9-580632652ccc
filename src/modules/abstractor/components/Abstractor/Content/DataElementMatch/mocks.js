import { DATA_ELEMENT_MATCH } from "modules/abstractor/graphql/query";

const irrDataMocks = [
  {
    request: {
      query: DATA_ELEMENT_MATCH
    },
    result: {
      data: {
        dataElementMatchForCurrentUser: {
          average: { amount: 97.13, thresholdMet: true },
          history: [
            { date: "2021-06-01", amount: 98.24, thresholdMet: true },
            { date: "2021-07-01", amount: 98.13, thresholdMet: true },
            { date: "2021-08-01", amount: 99.1, thresholdMet: true },
            { date: "2021-09-01", amount: 97.57, thresholdMet: true },
            { date: "2021-10-01", amount: 98.01, thresholdMet: true },
            { date: "2021-11-01", amount: 96.97, thresholdMet: false },
            { date: "2021-12-01", amount: 99.54, thresholdMet: true },
            { date: "2022-01-01", amount: 98, thresholdMet: true },
            { date: "2022-02-01", amount: 98, thresholdMet: true },
            { date: "2022-03-01", amount: 98, thresholdMet: true },
            { date: "2022-04-01", amount: 98, thresholdMet: true },
            { date: "2022-05-01", amount: 98, thresholdMet: true }
          ]
        }
      }
    }
  }
];

export default irrDataMocks;
