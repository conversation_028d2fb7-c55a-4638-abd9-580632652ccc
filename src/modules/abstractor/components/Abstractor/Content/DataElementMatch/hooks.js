import { useState } from "react";
import { useQuery } from "@apollo/client";
import { DATA_ELEMENT_MATCH } from "modules/abstractor/graphql/query";
import { path, pipe, propEq, reject } from "ramda";

export const useComponentLogic = ({ client }) => {
  const [filteredZeroPercentHistoryData, setFilteredZeroPercentHistoryData] =
    useState([]);

  const { data, loading, called, error } = useQuery(DATA_ELEMENT_MATCH, {
    client,
    onCompleted: () => {
      const filteredHistoryData = pipe(
        path(["dataElementMatchForCurrentUser", "history"]),
        reject(propEq("amount", 0))
      )(data);

      setFilteredZeroPercentHistoryData(filteredHistoryData);
    }
  });

  return {
    data,
    filteredZeroPercentHistoryData,
    loading,
    called,
    error
  };
};
