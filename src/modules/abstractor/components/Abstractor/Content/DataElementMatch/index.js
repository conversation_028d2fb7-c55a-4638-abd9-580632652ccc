import { useComponentLogic } from "./hooks";
import { DataCard, Spinner } from "@q-centrix/q-components-react";
import Month from "./Month";
import { formatToPercentageNumber } from "utils/formatPercentage";
import classNames from "classnames";

// eslint-disable-next-line complexity
const DataElementMatch = props => {
  const { data, filteredZeroPercentHistoryData, loading, called, error } =
    useComponentLogic(props);

  const containerClass = classNames("data-element-match", {
    loading,
    loaded: !loading && called
  });

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (loading)
    return (
      <div className="data-element-match">
        <Spinner />
      </div>
    );

  return (
    <DataCard
      title="Data element match"
      subTitle="Past 12 months"
      value={formatToPercentageNumber(
        data.dataElementMatchForCurrentUser.average.amount
      )}
      threshold={data.dataElementMatchForCurrentUser.average.thresholdMet}
      type="percent"
    >
      <div className={containerClass}>
        {filteredZeroPercentHistoryData.map(month => (
          <Month key={month.date} month={month} />
        ))}
      </div>
    </DataCard>
  );
};

export default DataElementMatch;
