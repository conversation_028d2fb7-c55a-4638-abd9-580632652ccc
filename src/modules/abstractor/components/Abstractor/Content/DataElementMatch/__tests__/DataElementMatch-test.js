import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import DataElementMatch from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  DataCard: "DataCard",
  Spinner: "Spinner"
}));

describe("DataElementMatch", () => {
  function render() {
    return create(
      decoratedApollo({
        component: DataElementMatch,
        props: {},
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("renders DataElementMatch cmp Loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
  test("renders DataElementMatch cmp Loaded", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
