// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DataElementMatch renders DataElementMatch cmp Loaded 1`] = `
<DataCard
  subTitle="Past 12 months"
  threshold={true}
  title="Data element match"
  type="percent"
  value="9,713.00"
>
  <div
    className="data-element-match loaded"
  >
    <div
      className="data-element"
      data-tooltip-id="06-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="07-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="08-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="09-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="10-01-2021-tooltip"
    />
    <div
      className="data-element-negative"
      data-tooltip-id="11-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="12-01-2021-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="01-01-2022-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="02-01-2022-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="03-01-2022-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="04-01-2022-tooltip"
    />
    <div
      className="data-element"
      data-tooltip-id="05-01-2022-tooltip"
    />
  </div>
</DataCard>
`;

exports[`DataElementMatch renders DataElementMatch cmp Loading 1`] = `
<div
  className="data-element-match"
>
  <Spinner />
</div>
`;
