import classnames from "classnames";
import { Tooltip } from "react-tooltip";
import { useComponentLogic } from "./hooks";
import "react-tooltip/dist/react-tooltip.css";
import { formatToPercentageNumber } from "utils/formatPercentage";

const Month = props => {
  const {
    month: { date, amount, thresholdMet }
  } = props;

  const { tickerMonth, tooltipId } = useComponentLogic(date);

  const dataElement = classnames({
    "data-element": thresholdMet,
    "data-element-negative": !thresholdMet
  });
  const tooltipPercentage = classnames({
    "percentage-positive": thresholdMet,
    "percentage-negative": !thresholdMet
  });
  const tooltipArrow = classnames({
    "date-tooltip-arrow-positive": thresholdMet,
    "date-tooltip-arrow-negative": !thresholdMet
  });

  return (
    <>
      <Tooltip
        id={`${tooltipId}-tooltip`}
        place={thresholdMet ? "top" : "bottom"}
        className="date-tooltip"
        classNameArrow={tooltipArrow}
      >
        <div className="date-tooltip-content">
          <span className="date-month">{tickerMonth}</span>
          <span className={tooltipPercentage}>
            {formatToPercentageNumber(amount)}%
          </span>
        </div>
      </Tooltip>
      <div data-tooltip-id={`${tooltipId}-tooltip`} className={dataElement} />
    </>
  );
};

export default Month;
