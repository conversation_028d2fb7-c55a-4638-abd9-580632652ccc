import Content from "./Content";
import LastLogInTile from "shared/widgets/LastLogInTile";
import "styles/abstractor.scss";
import { ApolloProvider } from "@apollo/client";
import { apolloClient } from "../../../../base";
import Layout from "shared/components/Layout";
import QPoints from "shared/widgets/QPoints";
import Earnings from "shared/widgets/Earnings";
import Hours from "shared/widgets/Hours";
import redirectToRoot from "utils/redirectToRoot";

const client = apolloClient("/graphql");

const tbChildren = (
  <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
    <h1 className="tw-text-2xl tw-font-semibold">Workflow</h1>
    <div className="tw-flex tw-items-center tw-gap-5">
      <Hours />
      <QPoints />
      <Earnings />
      <LastLogInTile />
    </div>
  </div>
);

const Abstractor = () => (
  <ApolloProvider client={client}>
    <Layout tbChildren={tbChildren} onLogoClick={redirectToRoot}>
      <div className="abstractor-container">
        <div className="abstractor-content">
          <Content />
        </div>
      </div>
    </Layout>
  </ApolloProvider>
);

export default Abstractor;
