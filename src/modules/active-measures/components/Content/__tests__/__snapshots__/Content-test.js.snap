// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content it renders component with error 1`] = `
<p
  className="error"
>
  Error: Could not retrieve the data.
</p>
`;

exports[`Content renders component 1`] = `
<div
  className="active-measures tw-grid tw-min-h-full tw-min-w-[calc(100vw-350px)] tw-max-w-[calc(100vw-70px)] tw-grid-rows-[60px_60px_100%] tw-overflow-hidden"
  id="active-measures-main"
>
  <UtilityBar />
  <div
    role="grid"
  >
    <div
      className="tw-flex tw-max-h-[calc(100vh-216px)] tw-min-h-[calc(100vh-276px)] tw-max-w-[calc(100vw-70px)] tw-flex-wrap tw-overflow-auto"
    >
      <TableHeader />
      <TableBody />
    </div>
  </div>
</div>
`;

exports[`Content renders component while loading 1`] = `<Spinner />`;
