import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import Content from "..";
import mocks from "modules/active-measures/graphql/mocks";
import { GET_MEASURES_SETS_BY_FACILITY_GROUP } from "modules/active-measures/graphql/query";

jest.mock("../Table/TableHeader", () => "TableHeader");
jest.mock("../Table/TableBody", () => "TableBody");
jest.mock("../Table/UtilityBar", () => "UtilityBar");
jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner"
}));
jest.mock("react-router-dom", () => ({
  useParams: () => ({ id: 1 })
}));

describe("Content", () => {
  function render(mock = mocks) {
    return create(
      decoratedApollo({
        component: Content,
        props: {},
        initialAppValues: {
          activeMeasures: []
        },
        apolloMocks: mock
      })
    );
  }

  test("renders component while loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const errorMock = [
      {
        request: {
          query: GET_MEASURES_SETS_BY_FACILITY_GROUP,
          variables: { facilityGroupId: 2 }
        },
        error: new Error("an error occurred")
      }
    ];

    const component = render(errorMock);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("renders component", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
