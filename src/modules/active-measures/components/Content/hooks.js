import { useQuery } from "@apollo/client";
import { useParams } from "react-router-dom";
import { GET_MEASURES_SETS_BY_FACILITY_GROUP } from "modules/active-measures/graphql/query";
import { useDispatch } from "react-redux";
import { setActiveMeasures } from "modules/active-measures/redux/slice";
import { complement, evolve, filter, map, pipe, prop, propOr } from "ramda";
import { isNullOrEmpty } from "utils/fp";

export const useComponentLogic = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { loading, error } = useQuery(GET_MEASURES_SETS_BY_FACILITY_GROUP, {
    variables: {
      facilityGroupId: Number(id)
    },
    onCompleted: (data = {}) => {
      pipe(
        propOr([], "measuresSetsByFacilityGroup"),
        map(
          evolve({
            measures: filter(
              pipe(
                prop("regulatoryGroupMeasureConfigurations"),
                complement(isNullOrEmpty)
              )
            )
          })
        ),
        filter(pipe(prop("measures"), complement(isNullOrEmpty))),
        filteredData => dispatch(setActiveMeasures(filteredData))
      )(data);
    },
    // eslint-disable-next-line no-empty-function
    onError: () => {}
  });

  return {
    loading,
    error
  };
};
