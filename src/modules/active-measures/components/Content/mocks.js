import { GET_MEASURES_SETS_BY_FACILITY_GROUP } from "modules/active-measures/graphql/query";

const defaultEndDate = {
  quarter: 4,
  year: 2023
};

const configurationData = (id, endDate = defaultEndDate) => [
  {
    id,
    active: true,
    collectionStartDate: {
      quarter: 1,
      year: 2022
    },
    collectionEndDate: endDate
  }
];

const activeMeasuresDataMock = [
  {
    request: {
      query: GET_MEASURES_SETS_BY_FACILITY_GROUP,
      variables: {
        facilityGroupId: 1
      }
    },
    result: {
      data: {
        measuresSetsByFacilityGroup: [
          {
            id: 1,
            shortName: "ASR-IP",
            name: "Acute Stroke Ready Inpatient",
            measures: [
              {
                id: 1,
                shortName: "ASR-IP-1",
                name: "Thrombolytic Therapy: Inpatient Admission",
                regulatoryGroupMeasureConfigurations: [
                  {
                    id: 1,
                    active: true,
                    collectionStartDate: null,
                    collectionEndDate: null
                  }
                ]
              },
              {
                id: 2,
                shortName: "ASR-IP-2",
                name: "Antithrombotic Therapy By End of Hospital Day 2",
                regulatoryGroupMeasureConfigurations: configurationData(1, null)
              },
              {
                id: 3,
                shortName: "ASR-IP-3",
                name: "Discharged on Antithrombotic Therapy",
                regulatoryGroupMeasureConfigurations: configurationData(1, null)
              }
            ]
          },
          {
            id: 4,
            shortName: "ASR-OP",
            name: "Acute Stroke Ready Outpatient",
            measures: [
              {
                id: 5,
                shortName: "ASR-OP-1",
                name: "Thrombolytic Therapy: Drip and Ship",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 4,
                shortName: "ASR-OP",
                name: "Acute Stroke Ready Outpatient",
                regulatoryGroupMeasureConfigurations: []
              }
            ]
          },
          {
            id: 5,
            shortName: "CSTK",
            name: "Comprehensive Stroke",
            measures: [
              {
                id: 6,
                shortName: "CSTK-01",
                name: "National Institutes of Health Stroke Scale (NIHSS Score Performed for Ischemic Stroke Patients)",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 7,
                shortName: "CSTK-02",
                name: "Modified Rankin Score (mRS) at 90 Days",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 8,
                shortName: "CSTK-03",
                name: "Severity Measurement Performed for SAH and ICH Patients (Overall Rate)",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 9,
                shortName: "CSTK-04",
                name: "Procoagulant Reversal Agent Initiation for Intracerebral Hemorrhage (ICH )",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 10,
                shortName: "CSTK-05",
                name: "Hemorrhagic Transformation (Overall Rate)",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 11,
                shortName: "CSTK-06",
                name: "Nimodipine Treatment Administered",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 12,
                shortName: "CSTK-08",
                name: "Thrombolysis in Cerebral Infarction (TICI Post-Treatment Reperfusion Grade)",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 13,
                shortName: "CSTK-09",
                name: "Arrival Time to Skin Puncture",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 14,
                shortName: "CSTK-10",
                name: "Modified Rankin Score (mRS) at 90 Days: Favorable Outcome",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 15,
                shortName: "CSTK-11",
                name: "Timeliness of Reperfusion: Arrival Time to TICI 2B or Higher",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 16,
                shortName: "CSTK-12",
                name: "Timeliness of Reperfusion: Skin Puncture to TICI 2B or Higher",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 6,
            shortName: "ED",
            name: "Emergency Department",
            measures: [
              {
                id: 17,
                shortName: "ED-1",
                name: "Median Time from ED Arrival to ED Departure for Admitted ED Patients – Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 18,
                shortName: "ED-2",
                name: "Admit Decision Time to ED Departure Time for Admitted Patients – Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 7,
            shortName: "HBIPS-Discharge",
            name: "Hospital Based Inpatient Psychiatric Services Discharge",
            measures: [
              {
                id: 19,
                shortName: "HBIPS-5",
                name: "Multiple Antipsychotic Medications at Discharge with Appropriate Justification- Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 8,
            shortName: "HBIPS-Events",
            name: "Hospital Based Inpatient Psychiatric Services Events",
            measures: [
              {
                id: 21,
                shortName: "HBIPS-2",
                name: "Physical Restraint- Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 22,
                shortName: "HBIPS-3",
                name: "Seclusion- Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 20,
                shortName: "HBIPS-Events",
                name: "Hospital Based Inpatient Psychiatric Services  Events",
                regulatoryGroupMeasureConfigurations: []
              }
            ]
          },
          {
            id: 9,
            shortName: "IMM",
            name: "Immunization",
            measures: [
              {
                id: 23,
                shortName: "IMM-2",
                name: "Influenza Immunization",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 10,
            shortName: "OP-TPT",
            name: "Outpatient ED-Throughput",
            measures: [
              {
                id: 24,
                shortName: "OP-18",
                name: "Median Time from ED Arrival to ED Departure for Discharged ED Patients - Overall Measure",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 11,
            shortName: "OP-STK",
            name: "Outpatient Stroke",
            measures: [
              {
                id: 25,
                shortName: "OP-STK",
                name: "Outpatient Stroke",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 26,
                shortName: "OP-23",
                name: "Head CT or MRI Scan Results for Acute Ischemic Stroke or Hemorrhagic Stroke Patients who Received Head CT or MRI Scan Interpretation Within 45 minutes of ED Arrival",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 12,
            shortName: "OP-29",
            name: "Colonoscopy",
            measures: [
              {
                id: 27,
                shortName: "OP-29",
                name: "Follow -up interval for normal COL",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 13,
            shortName: "OP-31",
            name: "Cataracts",
            measures: [
              {
                id: 28,
                shortName: "OP-31",
                name: "Cataracts – Improvement in Patient’s Visual Function within 90 Days Following Cataract Surgery",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 14,
            shortName: "PC-Mother",
            name: "Perinatal Care Mother",
            measures: [
              {
                id: 29,
                shortName: "PC-01",
                name: "Elective Delivery",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 30,
                shortName: "PC-02",
                name: "Cesarean Section - Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 15,
            shortName: "PC-Newborn",
            name: "Perinatal Care Newborn",
            measures: [
              {
                id: 31,
                shortName: "PC-05",
                name: "Exclusive Breast Milk Feeding",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 32,
                shortName: "PC-06",
                name: "Unexpected Complications in Term Newborns - Overall Rate",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 16,
            shortName: "SMD",
            name: "Screening for Metabolic Disorders",
            measures: [
              {
                id: 33,
                shortName: "SMD-1",
                name: "Screening for Metabolic Disorder",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 17,
            shortName: "SEP",
            name: "Sepsis",
            measures: [
              {
                id: 34,
                shortName: "SEP-1",
                name: "Early Management Bundle, Severe Sepsis/Septic Shock",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 18,
            shortName: "STK",
            name: "Stroke",
            measures: [
              {
                id: 35,
                shortName: "STK-1",
                name: "Venous Thromboembolism (VTE) Prophylaxis",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 36,
                shortName: "STK-10",
                name: "Assessed for Rehabilitation",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 37,
                shortName: "STK-2",
                name: "Discharged on Antithrombotic Therapy",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 38,
                shortName: "STK-3",
                name: "Anticoagulation Therapy for Atrial Fibrillation/Flutter",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 39,
                shortName: "STK-4",
                name: "Thrombolytic Therapy",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 40,
                shortName: "STK-5",
                name: "Antithrombotic Therapy By End of Hospital Day Two",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 41,
                shortName: "STK-6",
                name: "Discharged on Statin Medication",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 42,
                shortName: "STK-8",
                name: "Stroke Education",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 19,
            shortName: "STK-OP",
            name: "Stroke Outpatient",
            measures: [
              {
                id: 53,
                shortName: "STK-OP-1",
                name: "Door to Transfer to Another Hospital",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 20,
            shortName: "SUB",
            name: "Substance Use",
            measures: [
              {
                id: 55,
                shortName: "SUB-2",
                name: "Alcohol Use- Brief Intervention Provided or offered",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 56,
                shortName: "SUB-3",
                name: "Alcohol and Other Drug Use Disorder Treatment Provided or Offered at Discharge",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 21,
            shortName: "TOB",
            name: "Tobacco Treatment",
            measures: [
              {
                id: 57,
                shortName: "TOB-2",
                name: "Tobacco Use Treatment Provided or Offered",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              },
              {
                id: 58,
                shortName: "TOB-3",
                name: "Tobacco Use Treatment Provided or Offered at Discharge",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 22,
            shortName: "TR",
            name: "Transition Record",
            measures: [
              {
                id: 59,
                shortName: "TR-1",
                name: "Transition Record with Specified Elements Received by Discharged Patients",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          },
          {
            id: 23,
            shortName: "VTE",
            name: "Venous Thromboembolism",
            measures: [
              {
                id: 60,
                shortName: "VTE-6",
                name: "Incidence of Potentially Preventable VTE",
                regulatoryGroupMeasureConfigurations: configurationData(1)
              }
            ]
          }
        ]
      }
    }
  }
];

const activeMeasuresMocks = [...activeMeasuresDataMock];

export default activeMeasuresMocks;
