import { Spinner } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import UtilityBar from "./Table/UtilityBar";
import TableHeader from "./Table/TableHeader";
import TableBody from "./Table/TableBody";
// eslint-disable-next-line complexity
const Content = () => {
  const { loading, error } = useComponentLogic();

  if (error) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }

  if (loading) return <Spinner />;

  return (
    <div
      id="active-measures-main"
      className="active-measures tw-grid tw-min-h-full tw-min-w-[calc(100vw-350px)] tw-max-w-[calc(100vw-70px)] tw-grid-rows-[60px_60px_100%] tw-overflow-hidden"
    >
      <UtilityBar />
      <div role="grid">
        <div className="tw-flex tw-max-h-[calc(100vh-216px)] tw-min-h-[calc(100vh-276px)] tw-max-w-[calc(100vw-70px)] tw-flex-wrap tw-overflow-auto">
          <TableHeader />
          <TableBody />
        </div>
      </div>
    </div>
  );
};

export default Content;
