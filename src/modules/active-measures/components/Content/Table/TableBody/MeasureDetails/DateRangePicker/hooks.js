import { getQuarter, getYear, startOfQuarter } from "date-fns";
import { useMemo, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setMeasureData,
  setIsModified
} from "modules/active-measures/redux/slice";
import activeMeasuresSelectors from "modules/active-measures/redux/selectors";
import { always, ifElse } from "ramda";
import { isNullOrEmpty } from "utils/fp";

const monthOffset = quarter => (quarter - 1) * 3;
const startOfQuarterDate = (year, quarter) =>
  startOfQuarter(new Date(year, monthOffset(quarter)));
const getQuarterAndYear = date => ({
  quarter: getQuarter(new Date(date)),
  year: getYear(new Date(date))
});

const calculateDate = ifElse(isNullOrEmpty, always(null), ({ year, quarter }) =>
  startOfQuarterDate(year, quarter)
);

export const useComponentLogic = props => {
  const { measureIndex, measureSetIndex, dates } = props;
  const dispatch = useDispatch();
  const getEndDate = useSelector(state =>
    activeMeasuresSelectors.getMeasuresData(
      measureSetIndex,
      measureIndex,
      dates.end,
      state
    )
  );

  const getStartDate = useSelector(state =>
    activeMeasuresSelectors.getMeasuresData(
      measureSetIndex,
      measureIndex,
      dates.start,
      state
    )
  );

  const startDate = useMemo(() => calculateDate(getStartDate), [getStartDate]);

  const endDate = useMemo(() => calculateDate(getEndDate), [getEndDate]);

  const handleDate = useCallback(
    (name, value) => {
      dispatch(
        setMeasureData({
          measureSetIndex,
          measureIndex,
          name,
          value: value ? getQuarterAndYear(value) : null
        })
      );
      dispatch(setIsModified(true));
    },
    [dispatch, setMeasureData]
  );

  return {
    startDate,
    endDate,
    handleDate
  };
};
