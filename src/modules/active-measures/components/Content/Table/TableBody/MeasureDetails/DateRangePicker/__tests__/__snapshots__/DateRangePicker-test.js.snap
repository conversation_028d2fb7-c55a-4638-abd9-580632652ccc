// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateRangePicker it renders component correctly 1`] = `
<div
  className="tw-grid tw-grid-cols-2 tw-gap-10"
>
  <div>
    <DateInput
      className="tw-min-w-[100px] tw-max-w-[100px]"
      dateFormat="quarter"
      endDate={2023-10-01T07:00:00.000Z}
      maxDate={2022-01-01T08:00:00.000Z}
      name="collectionStartDate"
      onChange={[Function]}
      placeholder="Q/YYYY"
      selectsStart={true}
      showQuarterYearPicker={true}
      value={2022-01-01T08:00:00.000Z}
    />
  </div>
  <div>
    <DateInput
      className="tw-min-w-[100px] tw-max-w-[100px]"
      dateFormat="quarter"
      minDate={2022-01-01T08:00:00.000Z}
      name="collectionEndDate"
      onChange={[Function]}
      placeholder="Q/YYYY"
      selectsEnd={true}
      showQuarterYearPicker={true}
      startDate={2022-01-01T08:00:00.000Z}
      value={2023-10-01T07:00:00.000Z}
    />
  </div>
</div>
`;
