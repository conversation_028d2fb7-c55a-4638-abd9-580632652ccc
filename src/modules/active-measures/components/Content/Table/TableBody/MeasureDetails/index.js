import MeasureConfiguration from "./MeasureConfiguration";

const MeasureDetails = props => {
  const {
    name,
    shortName,
    measureConfiguration,
    measureIndex,
    measureSetIndex
  } = props;

  return (
    <div className="tw-non-italic tw-flex tw-min-h-[59px] tw-w-full tw-flex-row tw-items-center tw-border-b tw-border-black-29 tw-text-sm tw-font-normal odd:tw-bg-gray-25">
      <div className="tw-flex tw-h-full tw-w-[370px] tw-flex-row tw-items-center tw-px-5 tw-py-[11px] tw-shadow-[rgba(0,0,0,0.15)_2px_0px_6px_-1px]">
        <div className="tw-w-[75px]">{shortName}</div>
        <div className="tw-w-[50px]">{measureConfiguration.measureType}</div>
        <div className="tw-w-[200px]">{name}</div>
      </div>
      <MeasureConfiguration
        measureConfiguration={measureConfiguration}
        measureIndex={measureIndex}
        measureSetIndex={measureSetIndex}
      />
    </div>
  );
};

export default MeasureDetails;
