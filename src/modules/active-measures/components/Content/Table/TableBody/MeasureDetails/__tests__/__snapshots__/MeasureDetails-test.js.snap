// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MeasureDetails it renders component correctly 1`] = `
<div
  className="tw-non-italic tw-flex tw-min-h-[59px] tw-w-full tw-flex-row tw-items-center tw-border-b tw-border-black-29 tw-text-sm tw-font-normal odd:tw-bg-gray-25"
>
  <div
    className="tw-flex tw-h-full tw-w-[370px] tw-flex-row tw-items-center tw-px-5 tw-py-[11px] tw-shadow-[rgba(0,0,0,0.15)_2px_0px_6px_-1px]"
  >
    <div
      className="tw-w-[75px]"
    >
      ASR-IP-1
    </div>
    <div
      className="tw-w-[50px]"
    >
      IP
    </div>
    <div
      className="tw-w-[200px]"
    >
      Thrombolytic Therapy: Inpatient Admission
    </div>
  </div>
  <MeasureConfiguration
    measureConfiguration={
      Object {
        "active": true,
        "collectionEndDate": Object {
          "quarter": 4,
          "year": 2023,
        },
        "collectionStartDate": Object {
          "quarter": 1,
          "year": 2022,
        },
        "id": 1,
        "measureType": "IP",
      }
    }
    measureIndex={0}
  />
</div>
`;
