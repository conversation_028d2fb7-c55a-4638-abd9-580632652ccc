import { DateInput } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const DateRangePicker = props => {
  const { dates } = props;
  const { startDate, endDate, handleDate } = useComponentLogic(props);

  return (
    <div className="tw-grid tw-grid-cols-2 tw-gap-10">
      <div>
        <DateInput
          name={dates.start}
          showQuarterYearPicker
          dateFormat="quarter"
          placeholder="Q/YYYY"
          className="tw-min-w-[100px] tw-max-w-[100px]"
          value={startDate}
          selectsStart
          endDate={endDate}
          maxDate={startDate}
          onChange={newDate => handleDate(dates.start, newDate)}
          disabled
        />
      </div>
      <div>
        <DateInput
          name={dates.end}
          showQuarterYearPicker
          dateFormat="quarter"
          placeholder="Q/YYYY"
          className="tw-min-w-[100px] tw-max-w-[100px]"
          value={endDate}
          selectsEnd
          startDate={startDate}
          minDate={startDate}
          onChange={newDate => handleDate(dates.end, newDate)}
          disabled
        />
      </div>
    </div>
  );
};

export default DateRangePicker;
