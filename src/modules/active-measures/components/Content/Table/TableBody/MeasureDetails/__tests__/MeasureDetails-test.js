import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks from "modules/active-measures/graphql/mocks";
import MeasureDetails from "..";

jest.mock("../MeasureConfiguration", () => "MeasureConfiguration");

const props = {
  shortName: "ASR-IP-1",
  name: "Thrombolytic Therapy: Inpatient Admission",
  measureConfiguration: {
    id: 1,
    active: true,
    measureType: "IP",
    collectionStartDate: {
      quarter: 1,
      year: 2022
    },
    collectionEndDate: {
      quarter: 4,
      year: 2023
    }
  },
  measuresSetIndex: 0,
  measureIndex: 0
};

describe("MeasureDetails", () => {
  function render() {
    return create(
      decoratedApollo({
        component: MeasureDetails,
        props,
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
