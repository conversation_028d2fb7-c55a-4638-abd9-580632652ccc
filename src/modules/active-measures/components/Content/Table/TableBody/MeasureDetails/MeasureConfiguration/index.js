import { Checkbox } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import DateRangePicker from "../DateRangePicker";
import columns from "modules/active-measures/columns.json";

const MeasureConfiguration = props => {
  const { measureConfiguration, measureIndex, measureSetIndex } = props;
  const { id } = measureConfiguration;
  const { isActive, handleCheckbox } = useComponentLogic(props);

  return (
    <div className="tw-ml-[35px] tw-flex tw-flex-row tw-gap-10">
      <div className="tw-flex tw-w-[50px] tw-justify-center">
        <Checkbox
          name={`checkbox-${measureSetIndex}-${measureIndex}`}
          checked={isActive}
          onChange={handleCheckbox}
          disabled
        />
      </div>
      {columns.map(column => (
        <DateRangePicker
          key={column.key}
          id={id}
          measureIndex={measureIndex}
          measureSetIndex={measureSetIndex}
          dates={{
            start: column.startDateName,
            end: column.endDateName
          }}
        />
      ))}
    </div>
  );
};

export default MeasureConfiguration;
