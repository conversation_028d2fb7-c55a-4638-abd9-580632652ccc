import {
  setMeasureData,
  setIsModified
} from "modules/active-measures/redux/slice";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import activeMeasuresSelectors from "modules/active-measures/redux/selectors";

export const useComponentLogic = props => {
  const { measureIndex, measureSetIndex } = props;
  const dispatch = useDispatch();

  const isActive = useSelector(state =>
    activeMeasuresSelectors.getMeasuresData(
      measureSetIndex,
      measureIndex,
      "active",
      state
    )
  );

  const handleCheckbox = useCallback(
    e => {
      dispatch(
        setMeasureData({
          measureSetIndex,
          measureIndex,
          name: "active",
          value: e.target.checked
        })
      );
      dispatch(setIsModified(true));
    },
    [dispatch, setMeasureData]
  );

  return {
    isActive,
    handleCheckbox
  };
};
