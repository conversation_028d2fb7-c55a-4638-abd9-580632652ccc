// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MeasureConfiguration it renders component correctly 1`] = `
<div
  className="tw-ml-[35px] tw-flex tw-flex-row tw-gap-10"
>
  <div
    className="tw-flex tw-w-[50px] tw-justify-center"
  >
    <Checkbox
      disabled={true}
      name="checkbox-undefined-0"
      onChange={[Function]}
    />
  </div>
  <DateRangePicker
    dates={
      Object {
        "end": "collectionEndDate",
        "start": "collectionStartDate",
      }
    }
    id={1}
    measureIndex={0}
  />
</div>
`;
