import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks from "modules/active-measures/graphql/mocks";
import DateRangePicker from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));
jest.mock("react-router-dom", () => ({
  useParams: () => ({ id: 1 })
}));

const props = {
  id: 1,
  dates: {
    start: "collectionStartDate",
    end: "collectionEndDate"
  },
  measureSetIndex: 0,
  measureIndex: 0
};

const appValues = {
  activeMeasures: {
    measuresSetsByFacilityGroup: [
      {
        id: 1,
        shortName: "ASR-IP",
        name: "Acute Stroke Ready Inpatient",
        measures: [
          {
            id: 1,
            shortName: "ASR-IP-1",
            name: "Thrombolytic Therapy: Inpatient Admission",
            regulatoryGroupMeasureConfigurations: [
              {
                id: 1,
                active: true,
                collectionStartDate: {
                  quarter: 1,
                  year: 2022
                },
                collectionEndDate: {
                  quarter: 4,
                  year: 2023
                }
              }
            ]
          }
        ]
      }
    ]
  }
};

describe("DateRangePicker", () => {
  function render() {
    return create(
      decoratedApollo({
        component: DateRangePicker,
        props,
        initialValues: appValues,
        initialAppValues: appValues,
        apolloMocks: mocks
      })
    );
  }

  test.skip("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
