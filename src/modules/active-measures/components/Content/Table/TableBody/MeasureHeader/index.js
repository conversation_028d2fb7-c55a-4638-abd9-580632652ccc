import MeasureDetails from "../MeasureDetails";
import { findIndex, propEq } from "ramda";

const getIndex = id => findIndex(propEq("id", id));

const MeasureHeader = props => {
  const { shortName, name, measures, measureSetIndex } = props;

  return (
    <div className="tw-order-2 tw-flex tw-flex-col">
      <div className="tw-non-italic tw-flex tw-h-[59px] tw-w-full tw-flex-row tw-items-center tw-gap-x-2.5 tw-border-b tw-border-black-29 tw-bg-gray-300 tw-px-5 tw-text-xs tw-font-semibold tw-text-black">
        <div className="tw-w-[75px]">({shortName})</div>
        <div className="tw-w-[150px]">{name}</div>
      </div>
      {measures.map(measure => {
        const { id, regulatoryGroupMeasureConfigurations } = measure;

        return (
          <MeasureDetails
            key={id}
            measureSetIndex={measureSetIndex}
            measureIndex={getIndex(id)(measures)}
            name={measure.name}
            shortName={measure.shortName}
            measureConfiguration={regulatoryGroupMeasureConfigurations[0]}
          />
        );
      })}
    </div>
  );
};

export default MeasureHeader;
