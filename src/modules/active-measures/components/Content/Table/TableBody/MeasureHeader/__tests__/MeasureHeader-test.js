import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import mocks from "modules/active-measures/graphql/mocks";
import MeasureHeader from "..";

jest.mock("../../MeasureDetails", () => "MeasureDetails");

const props = {
  name: "Acute Stroke Ready Inpatient",
  shortName: "ASR-IP",
  measures: [
    {
      id: 1,
      shortName: "ASR-IP-1",
      name: "Thrombolytic Therapy: Inpatient Admission",
      regulatoryGroupMeasureConfigurations: {
        id: 1,
        active: true,
        measureType: "IP",
        collectionStartDate: {
          quarter: 1,
          year: 2022
        },
        collectionEndDate: {
          quarter: 4,
          year: 2023
        }
      }
    }
  ],
  measuresSetIndex: 0
};

describe("MeasureHeader", () => {
  function render() {
    return create(
      decoratedApollo({
        component: MeasureHeader,
        props,
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
