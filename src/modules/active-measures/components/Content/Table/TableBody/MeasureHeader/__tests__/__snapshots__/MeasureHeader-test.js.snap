// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MeasureHeader it renders component correctly 1`] = `
<div
  className="tw-order-2 tw-flex tw-flex-col"
>
  <div
    className="tw-non-italic tw-flex tw-h-[59px] tw-w-full tw-flex-row tw-items-center tw-gap-x-2.5 tw-border-b tw-border-black-29 tw-bg-gray-300 tw-px-5 tw-text-xs tw-font-semibold tw-text-black"
  >
    <div
      className="tw-w-[75px]"
    >
      (
      ASR-IP
      )
    </div>
    <div
      className="tw-w-[150px]"
    >
      Acute Stroke Ready Inpatient
    </div>
  </div>
  <MeasureDetails
    measureIndex={0}
    name="Thrombolytic Therapy: Inpatient Admission"
    shortName="ASR-IP-1"
  />
</div>
`;
