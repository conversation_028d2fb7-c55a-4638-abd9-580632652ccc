// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableBody it renders component correctly 1`] = `
<div
  className="tw-left-0 tw-order-2 tw-mt-[60px] tw-grid tw-w-full"
>
  <div
    className="tw-flex tw-w-full tw-flex-col"
  >
    <MeasureHeader
      measureSetIndex={0}
      measures={
        Array [
          Object {
            "id": 1,
            "name": "Thrombolytic Therapy: Inpatient Admission",
            "regulatoryGroupMeasureConfigurations": Object {
              "active": true,
              "collectionEndDate": Object {
                "quarter": 4,
                "year": 2023,
              },
              "collectionStartDate": Object {
                "quarter": 1,
                "year": 2022,
              },
              "id": 1,
            },
            "shortName": "ASR-IP-1",
          },
        ]
      }
      name="Acute Stroke Ready Inpatient"
      shortName="ASR-IP"
    />
  </div>
</div>
`;
