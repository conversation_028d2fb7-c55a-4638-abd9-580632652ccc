import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import mocks from "modules/active-measures/graphql/mocks";
import TableBody from "..";

jest.mock("../MeasureHeader", () => "MeasureHeader");

const appValues = {
  activeMeasures: {
    measuresSetsByFacilityGroup: [
      {
        id: 1,
        shortName: "ASR-IP",
        name: "Acute Stroke Ready Inpatient",
        measures: [
          {
            id: 1,
            shortName: "ASR-IP-1",
            name: "Thrombolytic Therapy: Inpatient Admission",
            regulatoryGroupMeasureConfigurations: {
              id: 1,
              active: true,
              collectionStartDate: {
                quarter: 1,
                year: 2022
              },
              collectionEndDate: {
                quarter: 4,
                year: 2023
              }
            }
          }
        ]
      }
    ]
  }
};

describe("TableBody", () => {
  function render() {
    return create(
      decoratedApollo({
        component: TableBody,
        props: {},
        initialAppValues: appValues,
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
