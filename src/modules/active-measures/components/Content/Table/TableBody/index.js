import MeasureHeader from "./MeasureHeader";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const TableBody = () => {
  const { activeMeasures, getIndex } = useComponentLogic();

  return (
    <div className="tw-left-0 tw-order-2 tw-mt-[60px] tw-grid tw-w-full">
      <div className="tw-flex tw-w-full tw-flex-col">
        {activeMeasures.map(measure => {
          const { id, shortName, name, measures } = measure;

          return (
            <MeasureHeader
              key={id}
              measureSetIndex={getIndex(id)(activeMeasures)}
              shortName={shortName}
              name={name}
              measures={measures}
            />
          );
        })}
      </div>
    </div>
  );
};

export default TableBody;
