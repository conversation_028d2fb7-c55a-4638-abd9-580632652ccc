import { useSelector } from "react-redux";
import activeMeasuresSelectors from "modules/active-measures/redux/selectors";
import { findIndex, propEq } from "ramda";

const getIndex = id => findIndex(propEq("id", id));

export const useComponentLogic = () => {
  const activeMeasures = useSelector(state =>
    activeMeasuresSelectors.getActiveMeasures(state)
  );

  return {
    activeMeasures,
    getIndex
  };
};
