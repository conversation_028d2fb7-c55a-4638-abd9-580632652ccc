import { create } from "react-test-renderer";
import { decoratedApolloWithDispatch } from "utils/tests/decorated";
import TableHeader from "..";
import mocks from "modules/active-measures/graphql/mocks";

const appValues = {
  activeMeasures: {
    measuresSetsByFacilityGroup: [
      {
        id: 1,
        shortName: "ASR-IP",
        name: "Acute Stroke Ready Inpatient",
        measures: [
          {
            id: 1,
            shortName: "ASR-IP-1",
            name: "Thrombolytic Therapy: Inpatient Admission",
            regulatoryGroupMeasureConfigurations: {
              id: 1,
              active: true,
              measureType: "IP",
              collectionStartDate: {
                quarter: 1,
                year: 2022
              },
              collectionEndDate: {
                quarter: 4,
                year: 2023
              }
            }
          }
        ]
      }
    ]
  }
};

describe("TableHeader", () => {
  test("renders TableHeader", () => {
    const { component } = decoratedApolloWithDispatch({
      component: TableHeader,
      initialAppValues: appValues,
      apolloMocks: mocks
    });

    const TableHeaderComponent = create(component);

    expect(TableHeaderComponent).toMatchSnapshot();
  });
});
