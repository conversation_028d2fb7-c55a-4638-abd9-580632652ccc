// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableHeader renders TableHeader 1`] = `
<div
  className="tw-non-italic tw-sticky tw-top-0 tw-z-20 tw-mb-[calc((100vh-443px)*-1)] tw-flex tw-h-[60px] tw-w-full tw-flex-row tw-border-b tw-border-[rgba(0,0,0,0.12)] tw-bg-gray-200 tw-text-[13px] tw-font-bold tw-text-[#015F86]"
>
  <div
    className="tw-order-0 tw-sticky tw-left-0 tw-top-0 tw-z-20 tw-flex tw-h-full tw-flex-row tw-items-center tw-border-r tw-border-r-gray-400 tw-bg-gray-200 tw-px-5 tw-py-[11px] tw-shadow-[rgba(0,0,0,0.15)_2px_0px_6px_-1px]"
  >
    <div
      className="tw-w-[75px]"
    >
      Measure
    </div>
    <div
      className="tw-w-[50px]"
    >
      Type
    </div>
    <div
      className="tw-w-[200px]"
    >
      Name
    </div>
  </div>
  <div
    className="tw-flex-rows tw-sticky tw-top-0 tw-z-10 tw-order-1 tw-flex tw-h-full tw-items-center tw-gap-10"
  >
    <div
      className="tw-ml-[35px] tw-flex tw-w-[50px] tw-content-end"
    >
      <p>
        Active Measure
      </p>
    </div>
    <div
      className="tw-grid tw-grid-cols-2 tw-gap-10"
    >
      <div
        className="tw-w-[100px]"
      >
        <p>
          Collection Start Date
        </p>
      </div>
      <div
        className="tw-ml-[35px] tw-w-[100px]"
      >
        <p>
          Collection End Date
        </p>
      </div>
    </div>
  </div>
</div>
`;
