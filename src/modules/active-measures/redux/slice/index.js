import { createSlice } from "@reduxjs/toolkit";
import { assoc, assocPath } from "ramda";

const initialState = {
  isModified: false,
  measuresSetsByFacilityGroup: []
};

export const activeMeasures = createSlice({
  name: "activeMeasures",
  initialState,
  reducers: {
    setIsModified: (state, { payload }) => assoc("isModified", payload, state),
    setMeasureData: (state, { payload }) => {
      const { measureSetIndex, measureIndex, name, value } = payload;

      return assocPath(
        [
          "measuresSetsByFacilityGroup",
          measureSetIndex,
          "measures",
          measureIndex,
          "regulatoryGroupMeasureConfigurations",
          0,
          name
        ],
        value
      )(state);
    },
    setActiveMeasures: (state, { payload }) =>
      assoc("measuresSetsByFacilityGroup", payload, state)
  }
});

export const { setIsModified, setMeasureData, setActiveMeasures } =
  activeMeasures.actions;

export default activeMeasures.reducer;
