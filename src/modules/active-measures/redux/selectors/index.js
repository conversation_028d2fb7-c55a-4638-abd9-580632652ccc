import { path } from "ramda";
import localizeSelectors from "utils/localizeSelectors";

const localState = path(["app", "activeMeasures"]);

export function getIsModified(state) {
  return state.isModified;
}

// eslint-disable-next-line max-params
export function getMeasuresData(measureSetIndex, measureIndex, name, state) {
  return path([
    "measuresSetsByFacilityGroup",
    measureSetIndex,
    "measures",
    measureIndex,
    "regulatoryGroupMeasureConfigurations",
    0,
    name
  ])(state);
}

export function getActiveMeasures(state) {
  return state.measuresSetsByFacilityGroup;
}

export default localizeSelectors(localState, {
  getIsModified,
  getMeasuresData,
  getActiveMeasures
});
