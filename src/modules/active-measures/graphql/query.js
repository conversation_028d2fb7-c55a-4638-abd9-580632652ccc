import { gql } from "@apollo/client";

export const GET_MEASURES_SETS_BY_FACILITY_GROUP = gql`
  query measuresSetsByFacilityGroup($facilityGroupId: Int!) {
    measuresSetsByFacilityGroup(facilityGroupId: $facilityGroupId) {
      id
      shortName
      name
      measures {
        id
        shortName
        name
        regulatoryGroupMeasureConfigurations {
          id
          active
          measureType
          collectionStartDate {
            quarter
            year
          }
          collectionEndDate {
            quarter
            year
          }
        }
      }
    }
  }
`;
