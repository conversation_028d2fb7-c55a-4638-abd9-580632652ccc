import stepHourEntryMocks from "../components/Activity/Content/StepsSection/mocks";
import { USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT } from "shared/components/CaseReassignment/graphql/queries";
import {
  GET_ACTIVITY_DETAILS,
  GET_ACTIVITY_PERMISSIONS,
  GET_STEP_TYPES,
  GET_ACTIVITY_ASSIGNEES
} from "./query";
import {
  DELETE_ACTIVITY,
  REASSIGN_ACTIVITY
} from "modules/activity/graphql/mutation";
import { CREATE_OR_UPDATE_ACTIVITY_COMMENT } from "../components/Activity/Content/CommentsAndNotesSection/graphql/mutations";

const hourEntryMock = [
  {
    id: "1",
    totalTime: {
      hours: 2,
      minutes: 120,
      seconds: 7200
    },
    startedAt: "2025-01-17 11:00:00 -0500",
    finishedAt: "2025-01-17 13:00:00 -0500"
  }
];

const activityMockSuccessResponse = (id, hourEntries = []) => ({
  data: {
    activityDetails: {
      id,
      facility: {
        id: "6",
        name: "Hospital Questionnaire",
        __typename: "Facility"
      },
      activityType: {
        id: "5",
        name: "Case Communication/Partner Collaboration",
        __typename: "ActivityType"
      },
      owner: {
        id: "1",
        fullName: "Russell Reas",
        __typename: "User"
      },
      assignee: {
        id: "9",
        fullName: "Julia White",
        __typename: "User"
      },
      status: {
        id: "1",
        name: "Open",
        __typename: "ActivityStatus"
      },
      createdAt: "2025-01-17",
      deadlineDate: "2025-02-01",
      steps: [
        {
          id: "1",
          assignee: {
            id: "9",
            fullName: "Julia White",
            __typename: "User"
          },
          stepType: {
            id: "23",
            name: "Set up Client Meeting",
            __typename: "StepTypeDetails"
          },
          deadlineDate: "2025-02-01",
          status: {
            key: "open",
            name: "Open",
            __typename: "ActivityStatus"
          },
          createdAt: "2025-01-17 19:40:04 -0500",
          permissions: {
            edit: true,
            delete: true,
            __typename: "StepPermissions"
          },
          hourGroup: {
            id: "1",
            hourEntries,
            __typename: "Step"
          }
        }
      ],
      __typename: "Activity"
    }
  }
});

const permissionsMock = (action, activityId = "", authorized = true) => ({
  request: {
    query: GET_ACTIVITY_PERMISSIONS,
    variables: {
      ...(activityId && { activityId }),
      action
    }
  },
  result: {
    data: {
      activityPermissions: {
        authorized
      }
    }
  }
});

const stepTypes = {
  data: {
    stepTypes: {
      count: 4,
      page: 1,
      perPage: 25,
      stepTypes: [
        {
          id: 1,
          name: "Greenlight Orientation",
          isDisabled: false
        },
        {
          id: 2,
          name: "Assign Initial Greenlighting Case",
          isDisabled: false
        },
        {
          id: 3,
          name: "Complete First Greenlighting Case",
          isDisabled: false
        },
        {
          id: 4,
          name: "IRR Review and Scoring",
          isDisabled: true
        }
      ]
    }
  }
};

const activityDetailsMocks = [
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 1
      }
    },
    result: activityMockSuccessResponse(1)
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 2
      }
    },
    result: activityMockSuccessResponse(2, hourEntryMock)
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 3
      }
    },
    result: activityMockSuccessResponse(3, hourEntryMock)
  },
  permissionsMock("create"),
  permissionsMock("view", 1),
  permissionsMock("reassign", 1),
  permissionsMock("delete", 1),
  permissionsMock("view", 2, true),
  permissionsMock("reassign", 2, false),
  permissionsMock("delete", 2, false),
  permissionsMock("view", 3),
  permissionsMock("reassign", 3),
  permissionsMock("delete", 3),
  {
    request: {
      query: USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT,
      variables: {
        perPage: 25,
        page: 1
      }
    },
    result: {
      data: {
        activityUsers: {
          users: [
            {
              id: "1",
              fullName: "Russell Reas",
              __typename: "UserBasic"
            },
            {
              id: "3",
              fullName: "Sam Lee",
              __typename: "UserBasic"
            }
          ],
          __typename: "Users",
          count: 2
        }
      }
    }
  },
  {
    request: {
      query: DELETE_ACTIVITY,
      variables: {
        activityId: 1
      }
    },
    result: {
      data: {
        deleteActivity: {
          response: true
        }
      }
    }
  },
  {
    request: {
      query: REASSIGN_ACTIVITY,
      variables: {
        activityId: 1,
        assigneeId: "1",
        deadline: null
      }
    },
    result: {
      data: {
        reassignActivity: {
          successful: {
            successfulIds: ["1"],
            messages: ["Completed activity reassignment"]
          },
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: GET_ACTIVITY_ASSIGNEES,
      variables: {
        facilityIds: [1],
        search: "",
        page: 1,
        perPage: 25
      }
    },
    result: {
      data: {
        activeInternalUsersByNameAndFacility: {
          page: 1,
          count: 2,
          perPage: 25,
          users: [
            {
              id: 1,
              fullName: "John Doe"
            },
            {
              id: 2,
              fullName: "Jane Smith"
            }
          ]
        }
      }
    }
  },
  {
    request: {
      query: CREATE_OR_UPDATE_ACTIVITY_COMMENT,
      variables: {
        activityId: 1,
        body: "Test comment"
      }
    },
    result: {
      data: {
        createOrUpdateActivityComment: {
          comment: {
            id: 1,
            createdAt: "2024-10-02T09:13:36-07:00",
            updatedAt: "2024-10-02T09:13:36-07:00",
            editable: true,
            body: "Test comment"
          },
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: GET_STEP_TYPES,
      variables: {
        activityId: 1
      }
    },
    result: stepTypes
  },
  {
    request: {
      query: GET_STEP_TYPES,
      variables: {
        activityId: 2
      }
    },
    error: new Error("an error occurred")
  },
  {
    request: {
      query: GET_STEP_TYPES,
      variables: {
        activityId: 3
      }
    },
    result: stepTypes
  }
];

export default [...activityDetailsMocks, ...stepHourEntryMocks];
