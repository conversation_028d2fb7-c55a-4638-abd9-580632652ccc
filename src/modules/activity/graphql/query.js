import { gql } from "@apollo/client";

export const GET_ACTIVITY_DETAILS = gql`
  query activityDetails($activityId: ID!) {
    activityDetails(activityId: $activityId) {
      id
      facility {
        id
        name
      }
      activityType {
        id
        name
      }
      owner {
        id
        fullName
      }
      assignee {
        id
        fullName
      }
      status {
        id
        name
      }
      createdAt
      deadlineDate
      productCategory
      steps {
        id
        assignee {
          id
          fullName
        }
        stepType {
          id
          name
        }
        deadlineDate
        status {
          key
          name
        }
        createdAt
        permissions {
          edit
          delete
          createHourEntry
        }
        hourGroup {
          id
          hourEntries {
            id
            totalTime {
              hours
              minutes
              seconds
            }
            startedAt
            finishedAt
            stepPermissions {
              updateHourEntry
            }
          }
        }
      }
    }
  }
`;

export const GET_ACTIVITY_ASSIGNEES = gql`
  query activeInternalUsersByNameAndFacility(
    $facilityIds: [ID!]
    $search: String
    $page: Int
    $perPage: Int
  ) {
    activeInternalUsersByNameAndFacility(
      facilityIds: $facilityIds
      search: $search
      page: $page
      perPage: $perPage
    ) {
      page
      count
      perPage
      users {
        id
        fullName
      }
    }
  }
`;

export const GET_ACTIVITY_TYPES = gql`
  query activityTypes {
    activityTypes {
      id
      name
    }
  }
`;

export const GET_ACTIVITY_PERMISSIONS = gql`
  query activityPermissions($action: String!, $activityId: ID) {
    activityPermissions(action: $action, activityId: $activityId) {
      authorized
    }
  }
`;

export const GET_STEP_TYPES = gql`
  query stepTypes(
    $activityId: ID
    $activityTypeId: ID
    $page: Int
    $perPage: Int
    $search: String
  ) {
    stepTypes(
      activityId: $activityId
      activityTypeId: $activityTypeId
      page: $page
      perPage: $perPage
      search: $search
    ) {
      count
      page
      perPage
      stepTypes {
        id
        name
      }
    }
  }
`;
