import { gql } from "@apollo/client";

export const CREATE_ACTIVITY = gql`
  mutation createActivity($activity: ActivityInput!, $startTimer: Boolean) {
    createActivity(activity: $activity, startTimer: $startTimer) {
      activity {
        id
        deadlineDate
        facility {
          id
        }
        status {
          name
        }
        activityType {
          id
          name
        }
        assignee {
          fullName
        }
        owner {
          fullName
        }
        createdAt
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_ACTIVITY = gql`
  mutation updateActivity($activityId: ID!, $activityInput: ActivityInput!) {
    updateActivity(activityId: $activityId, activityInput: $activityInput) {
      activity {
        id
        deadlineDate
        facility {
          id
        }
        status {
          name
        }
        activityType {
          id
          name
        }
        assignee {
          fullName
        }
        owner {
          fullName
        }
        createdAt
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const DELETE_ACTIVITY = gql`
  mutation deleteActivity($activityId: ID!) {
    deleteActivity(activityId: $activityId) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const REASSIGN_ACTIVITY = gql`
  mutation reassignActivity(
    $activityId: ID!
    $assigneeId: ID
    $comment: String
    $ownerId: ID
    $deadlineDate: String
  ) {
    reassignActivity(
      activityId: $activityId
      assigneeId: $assigneeId
      ownerId: $ownerId
      deadlineDate: $deadlineDate
      comment: $comment
    ) {
      success
      errors {
        messages {
          attribute
          errors
        }
        fullMessages
      }
    }
  }
`;

export const UPDATE_ACTIVITY_STATUS = gql`
  mutation updateActivityStatus($activityId: ID!, $status: String!) {
    updateActivityStatus(activityId: $activityId, status: $status) {
      response
      activity {
        id
        status {
          key
          name
        }
      }
      errors {
        messages {
          errors
        }
      }
    }
  }
`;

export const CREATE_STEP = gql`
  mutation createStep($step: StepInput!) {
    createStep(step: $step) {
      step {
        id
        activity {
          id
          activityType {
            name
          }
        }
        assignee {
          fullName
        }
        status {
          id
        }
        stepType {
          name
          activityType {
            name
          }
        }
        deadlineDate
        updatedAt
        createdAt
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_STEP = gql`
  mutation updateStep($stepId: ID!, $stepInput: StepInput!) {
    updateStep(stepId: $stepId, stepInput: $stepInput) {
      step {
        id
        activity {
          id
          activityType {
            name
          }
        }
        assignee {
          fullName
        }
        status {
          id
        }
        stepType {
          name
          activityType {
            name
          }
        }
        deadlineDate
        updatedAt
        createdAt
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_STEP_STATUS = gql`
  mutation updateStepStatus($stepId: ID!, $status: String!) {
    updateStepStatus(stepId: $stepId, status: $status) {
      response
      step {
        id
        status {
          id
          name
        }
      }
      errors {
        messages {
          errors
        }
      }
    }
  }
`;

export const DELETE_STEP = gql`
  mutation deleteStep($stepId: ID!) {
    deleteStep(stepId: $stepId) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const CREATE_STEP_HOUR_ENTRY = gql`
  mutation createStepHourEntry($stepId: ID!, $hourEntryInput: HourEntryInput!) {
    createStepHourEntry(stepId: $stepId, hourEntryInput: $hourEntryInput) {
      hourEntry {
        id
        startedAt
        finishedAt
        hours {
          id
          workStartDate
          hoursWorked
        }
        hourGroup {
          id
          totalTime
        }
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_STEP_HOUR_ENTRY = gql`
  mutation updateStepHourEntry(
    $hourEntryId: ID!
    $hourEntryInput: HourEntryInput!
  ) {
    updateStepHourEntry(
      hourEntryId: $hourEntryId
      hourEntryInput: $hourEntryInput
    ) {
      hourEntry {
        id
        startedAt
        finishedAt
        hours {
          id
          workStartDate
          hoursWorked
        }
        hourGroup {
          id
          totalTime
        }
      }
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const DELETE_STEP_HOUR_ENTRY = gql`
  mutation deleteStepHourEntry($hourEntryId: ID!) {
    deleteStepHourEntry(hourEntryId: $hourEntryId) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;
