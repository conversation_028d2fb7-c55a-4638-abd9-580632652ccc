import ReadOnlyField from "shared/components/ReadOnlyField";
import ControlledSelect from "modules/cases/components/CaseDetails/Content/CaseDetailsSection/CaseDetailsForm/ControlledSelect";
import { FACILITIES } from "modules/cases/components/CaseCreate/graphql/query";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const ActivityDetailsFormFields = ({
  activityDetails,
  editMode,
  form,
  toggleEditMode,
  isPhase4Enabled
}) => {
  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = form;
  const { onSubmit, formattedDate } = useComponentLogic({
    activityDetails,
    form,
    toggleEditMode
  });

  return (
    <form
      id="activity-details-form"
      autoComplete="off"
      onSubmit={handleSubmit(onSubmit)}
      className="tw-w-full"
      disabled={isSubmitting}
    >
      <div className="tw-grid tw-max-w-full tw-grid-cols-2 tw-gap-x-5 tw-gap-y-5 tw-border-r tw-border-black/[0.12] tw-p-5">
        <ReadOnlyField
          field="Date Created"
          value={formattedDate(activityDetails?.createdAt)}
        />
        <ReadOnlyField
          field="Deadline"
          value={formattedDate(activityDetails?.deadlineDate)}
        />
        <ReadOnlyField
          field="Activity Type"
          value={activityDetails?.activityType?.name}
        />
        {isPhase4Enabled && (
          <ReadOnlyField
            field="Product Category"
            value={activityDetails?.productCategory}
          />
        )}
        <ReadOnlyField
          field="Activity Owner"
          value={activityDetails?.owner?.fullName}
        />
        <ReadOnlyField
          field="Activity Assignee"
          value={activityDetails?.assignee?.fullName}
        />
        {editMode ? (
          <ControlledSelect
            id="facilityId"
            name="facilityId"
            label="Facility Name"
            control={control}
            query={FACILITIES}
            variables={{ perPage: 25 }}
            queryObjectNamePath={[
              "currentUserAvailableFacilities",
              "userAvailableFacilities"
            ]}
            error={Boolean(errors.facilityName)}
            errorText={errors.facilityName?.message}
            isSearchable
            isPageable
            smallPagination
            {...register("facilityId")}
          />
        ) : (
          <ReadOnlyField
            field="Facility Name"
            value={activityDetails?.facility?.name}
          />
        )}
      </div>
    </form>
  );
};

export default ActivityDetailsFormFields;
