// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActivityDetailsFormFields it renders component correctly in Edit Mode 1`] = `
<form
  autoComplete="off"
  className="tw-w-full"
  disabled={false}
  id="activity-details-form"
>
  <div
    className="tw-grid tw-max-w-full tw-grid-cols-2 tw-gap-x-5 tw-gap-y-5 tw-border-r tw-border-black/[0.12] tw-p-5"
  >
    <ReadOnlyField
      field="Date Created"
      value="08/01/2024"
    />
    <ReadOnlyField
      field="Deadline"
      value="10/01/2024"
    />
    <ReadOnlyField
      field="Activity Type"
      value="Activity Type 1"
    />
    <ReadOnlyField
      field="Product Category"
      value="Test Product Category"
    />
    <ReadOnlyField
      field="Activity Owner"
      value="Russell Reas"
    />
    <ReadOnlyField
      field="Activity Assignee"
      value="<PERSON> Smith"
    />
    <ControlledDateInput
      control={[MockFunction]}
      error={false}
      id="facilityId"
      isPageable={true}
      isSearchable={true}
      label="Facility Name"
      name="facilityId"
      query={
        Object {
          "definitions": Array [
            Object {
              "directives": Array [],
              "kind": "OperationDefinition",
              "name": Object {
                "kind": "Name",
                "value": "currentUserAvailableFacilities",
              },
              "operation": "query",
              "selectionSet": Object {
                "kind": "SelectionSet",
                "selections": Array [
                  Object {
                    "alias": undefined,
                    "arguments": Array [
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "perPage",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "perPage",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "page",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "page",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "search",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "search",
                          },
                        },
                      },
                    ],
                    "directives": Array [],
                    "kind": "Field",
                    "name": Object {
                      "kind": "Name",
                      "value": "currentUserAvailableFacilities",
                    },
                    "selectionSet": Object {
                      "kind": "SelectionSet",
                      "selections": Array [
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "userAvailableFacilities",
                          },
                          "selectionSet": Object {
                            "kind": "SelectionSet",
                            "selections": Array [
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "id",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "name",
                                },
                                "selectionSet": undefined,
                              },
                            ],
                          },
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "count",
                          },
                          "selectionSet": undefined,
                        },
                      ],
                    },
                  },
                ],
              },
              "variableDefinitions": Array [
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "perPage",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "page",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "String",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "search",
                    },
                  },
                },
              ],
            },
          ],
          "kind": "Document",
          "loc": Object {
            "end": 294,
            "start": 0,
          },
        }
      }
      queryObjectNamePath={
        Array [
          "currentUserAvailableFacilities",
          "userAvailableFacilities",
        ]
      }
      smallPagination={true}
      variables={
        Object {
          "perPage": 25,
        }
      }
    />
  </div>
</form>
`;

exports[`ActivityDetailsFormFields it renders component correctly in Read Only Mode 1`] = `
<form
  autoComplete="off"
  className="tw-w-full"
  disabled={false}
  id="activity-details-form"
>
  <div
    className="tw-grid tw-max-w-full tw-grid-cols-2 tw-gap-x-5 tw-gap-y-5 tw-border-r tw-border-black/[0.12] tw-p-5"
  >
    <ReadOnlyField
      field="Date Created"
      value="08/01/2024"
    />
    <ReadOnlyField
      field="Deadline"
      value="10/01/2024"
    />
    <ReadOnlyField
      field="Activity Type"
      value="Activity Type 1"
    />
    <ReadOnlyField
      field="Product Category"
      value="Test Product Category"
    />
    <ReadOnlyField
      field="Activity Owner"
      value="Russell Reas"
    />
    <ReadOnlyField
      field="Activity Assignee"
      value="Jane Smith"
    />
    <ReadOnlyField
      field="Facility Name"
      value="Large Medical Facility"
    />
  </div>
</form>
`;
