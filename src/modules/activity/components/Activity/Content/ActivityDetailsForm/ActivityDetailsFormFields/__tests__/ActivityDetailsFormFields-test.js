import ActivityDetailsFormFields from "..";
import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";

jest.mock("shared/components/ReadOnlyField", () => "ReadOnlyField");
jest.mock(
  "modules/cases/components/CaseDetails/Content/CaseDetailsSection/CaseDetailsForm/ControlledSelect",
  () => "ControlledDateInput"
);
jest.mock("shared/components/ControlledDateInput", () => "ControlledSelect");
jest.mock("../hooks", () => ({
  useComponentLogic: props => ({
    ...jest.requireActual("../hooks").useComponentLogic(props),
    today: new Date("2024-11-07T00:00:00Z")
  })
}));

const mockedProps = (editMode = false) => ({
  activityDetails: {
    activityType: {
      id: 1,
      name: "Activity Type 1"
    },
    assignee: {
      fullName: "<PERSON>",
      id: 2
    },
    productCategory: "Test Product Category",
    createdAt: "2024-08-01T19:00:00-04:00",
    deadlineDate: "2024-10-01T19:00:00-04:00",
    facility: {
      id: 1,
      name: "Large Medical Facility"
    },
    id: 1,
    owner: {
      fullName: "Russell Reas",
      id: 1
    },
    status: {
      id: "2",
      name: "In Progress"
    }
  },
  editMode,
  form: {
    register: jest.fn(),
    watch: jest.fn(),
    control: jest.fn(),
    handleSubmit: jest.fn(),
    formState: { errors: {}, isSubmitting: false }
  },
  toggleEditMode: jest.fn(),
  isPhase4Enabled: true
});

describe("ActivityDetailsFormFields", () => {
  const render = editMode =>
    create(
      decoratedApollo({
        component: ActivityDetailsFormFields,
        props: mockedProps(editMode),
        initialValues: {},
        initialAppValues: {},
        apolloMocks: []
      })
    );

  test("it renders component correctly in Read Only Mode", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly in Edit Mode", () => {
    const component = render(true);

    expect(component).toMatchSnapshot();
  });
});
