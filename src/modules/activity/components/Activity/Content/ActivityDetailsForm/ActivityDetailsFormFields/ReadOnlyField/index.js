const ReadOnlyField = ({ field, value }) => (
  <div className="tw-flex tw-min-w-[240px] tw-flex-col tw-gap-y-1">
    <h3 className="tw-text-[14px] tw-font-semibold tw-text-black">{field}</h3>
    <p className="tw-text-wrap tw-min-h-[35px] tw-rounded-[5px] tw-bg-qc-ice tw-px-4 tw-py-[8.5px] tw-text-[14px] tw-font-normal tw-text-black">
      {value}
    </p>
  </div>
);

export default ReadOnlyField;
