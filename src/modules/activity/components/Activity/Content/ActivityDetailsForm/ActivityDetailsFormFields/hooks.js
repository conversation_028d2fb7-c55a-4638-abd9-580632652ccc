import { always, applySpec, ifElse } from "ramda";
import { useMutation } from "@apollo/client";
import { UPDATE_ACTIVITY } from "modules/activity/graphql/mutation";
import { useToast } from "@q-centrix/q-components-react";
import { isNullOrEmpty } from "utils/fp";
import { format } from "date-fns";
import { formatISODateString } from "utils/formatISODateString";

const translateDate = ifElse(isNullOrEmpty, always(null), val =>
  format(new Date(val), "yyyy-MM-dd")
);

const formattedDate = ifElse(isNullOrEmpty, always(""), value =>
  formatISODateString(value, "MM/dd/yyyy")
);

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  activityDetails,
  form: { watch },
  toggleEditMode
}) => {
  const deadlineDate = watch("deadlineDate");
  const facilityIdSelectedOption = watch("facilityId");
  const activityTypeSelectedOption = watch("activityType");
  const activityOwnerSelectedOption = watch("activityOwner");
  const activityAssigneeSelectedOption = watch("activityAssignee");

  const { toast } = useToast();
  const [updateActivity] = useMutation(UPDATE_ACTIVITY);

  const onSubmit = () => {
    const loadingToast = toast({
      variant: "loading",
      description: "Updating Activity..."
    });

    updateActivity({
      variables: applySpec({
        activityId: () => Number(activityDetails?.id),
        activityInput: {
          facilityId: always(facilityIdSelectedOption?.value),
          activityTypeId: always(activityTypeSelectedOption?.value),
          ownerId: always(activityOwnerSelectedOption?.value),
          assigneeId: always(activityAssigneeSelectedOption?.value),
          deadlineDate: () => translateDate(deadlineDate)
        }
      })(),
      // eslint-disable-next-line no-shadow, complexity, max-statements
      onCompleted: ({ updateActivity: updateActivityResults }) => {
        if (updateActivityResults?.errors) {
          updateActivityResults.errors?.fullMessages.forEach(
            (errorMessage, index) => {
              if (index === 0) {
                loadingToast.update({
                  description: errorMessage,
                  variant: "error"
                });
              } else {
                toast({
                  variant: "error",
                  description: errorMessage
                });
              }
            }
          );
        } else {
          toggleEditMode(false);
          loadingToast.update({
            variant: "success",
            description: "Activity details updated successfully"
          });
        }
      },
      onError: error => {
        loadingToast.update({
          variant: "error",
          description: error.message
        });
      },
      // invalidate list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            activityList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
  };

  return {
    onSubmit,
    formattedDate
  };
};
