import wait from "waait";
import ActivityDetailsForm from "..";
import { create } from "react-test-renderer";
import { decoratedApollo } from "utils/tests/decorated";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  TagWithDropdown: "TagWithDropdown",
  Spinner: "Spinner",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("../ActivityDetailsFormFields", () => "ActivityDetailsFormFields");
jest.mock(
  "../ActivityDetailsHeaderContent",
  () => "ActivityDetailsHeaderContent"
);

const mockedProps = {
  activityDetails: {
    activityType: {
      id: 1,
      name: "Activity Type 1"
    },
    assignee: {
      fullName: "<PERSON>",
      id: 2
    },
    createdAt: "2024-08-01T19:00:00-04:00",
    deadlineDate: "2024-10-01T19:00:00-04:00",
    facility: {
      id: 1,
      name: "Large Medical Facility"
    },
    id: 1,
    owner: {
      fullName: "<PERSON>",
      id: 1
    },
    status: {
      id: "2",
      name: "In Progress"
    }
  },
  form: {},
  toggleEditMode: jest.fn()
};

describe("ActivityDetailsForm", () => {
  const render = (props = mockedProps) =>
    create(
      decoratedApollo({
        component: ActivityDetailsForm,
        props,
        initialValues: {},
        initialAppValues: {},
        apolloMocks: []
      })
    );

  test("it renders component correctly", async () => {
    const component = render();

    await wait(100);

    expect(component).toMatchSnapshot();
  });

  test("it renders Spinner when loading", () => {
    const component = render({ loading: true });

    expect(component).toMatchSnapshot();
  });

  test("it renders Error message", () => {
    const component = render({ error: { message: "Test error message" } });

    expect(component).toMatchSnapshot();
  });
});
