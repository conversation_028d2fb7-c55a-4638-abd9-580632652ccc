// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ActivityDetailsForm it renders Error message 1`] = `
<p
  className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500"
>
  Error: 
  Test error message
</p>
`;

exports[`ActivityDetailsForm it renders Spinner when loading 1`] = `
<div
  className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10"
>
  <Spinner />
</div>
`;

exports[`ActivityDetailsForm it renders component correctly 1`] = `
<CardWithHeader
  bodyClasses="!tw-rounded-b-[5px]"
  cardClasses="tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-px-2.5 tw-py-2 !tw-rounded-t-[5px]"
  headerContent={
    <ActivityDetailsHeaderContent
      onCancelClick={[Function]}
      onEditClick={[Function]}
    />
  }
  isExpandable={true}
  onExpandFunction={[Function]}
>
  <div
    className="tw-flex tw-w-full"
  >
    <ActivityDetailsFormFields
      activityDetails={
        Object {
          "activityType": Object {
            "id": 1,
            "name": "Activity Type 1",
          },
          "assignee": Object {
            "fullName": "Jane Smith",
            "id": 2,
          },
          "createdAt": "2024-08-01T19:00:00-04:00",
          "deadlineDate": "2024-10-01T19:00:00-04:00",
          "facility": Object {
            "id": 1,
            "name": "Large Medical Facility",
          },
          "id": 1,
          "owner": Object {
            "fullName": "Russell Reas",
            "id": 1,
          },
          "status": Object {
            "id": "2",
            "name": "In Progress",
          },
        }
      }
      form={Object {}}
      toggleEditMode={[MockFunction]}
    />
    <div
      className="tw-flex tw-justify-center tw-py-5 lg:tw-basis-1/4 2xl:tw-basis-2/12"
    >
      <div>
        <TagWithDropdown
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Complete",
                "tagStatus": "success",
                "value": "complete",
              },
              Object {
                "label": "Blocked",
                "tagStatus": "danger",
                "value": "blocked",
              },
              Object {
                "label": "In Progress",
                "tagStatus": "main",
                "value": "in_progress",
              },
              Object {
                "label": "Open",
                "tagStatus": "default",
                "value": "open",
              },
            ]
          }
          status="main"
          value={
            Object {
              "label": "In Progress",
              "tagStatus": "main",
              "value": "in_progress",
            }
          }
        />
      </div>
    </div>
  </div>
</CardWithHeader>
`;
