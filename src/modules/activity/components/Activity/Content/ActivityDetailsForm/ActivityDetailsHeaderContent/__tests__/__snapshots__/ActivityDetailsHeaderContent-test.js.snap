// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DetailItem it renders component correctly 1`] = `
<div
  className="tw-flex tw-justify-between"
>
  <h3
    className="tw-flex tw-items-center"
  >
    Activity Details
  </h3>
  <button
    className="main button tw-justify-center tw-text-sm tw-transition-all tw-ease-in-out tw-delay-[0.2s] tw-py-2.5 tw-px-[15px] tw-font-sans hover:tw-shadow-card tw-bg-white hover:tw-bg-white tw-border-2 tw-border-solid tw-text-qc-blue-800 tw-border-qc-blue-800 hover:tw-text-qc-blue-900 hover:tw-border-qc-blue-900 hover:tw-cursor-pointer button-outline tw-flex tw-gap-2.5 tw-items-center tw-self-end tw-h-[30px] tw-rounded-[4px] tw-font-semibold"
    disabled={false}
    onClick={[Function]}
  >
    <i
      className="fa-regular fa-pen tw-opacity-85"
    />
    Edit Activity Details
  </button>
</div>
`;
