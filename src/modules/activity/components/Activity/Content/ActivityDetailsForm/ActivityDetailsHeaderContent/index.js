import { Button } from "@q-centrix/q-components-react";

const ActivityDetailsHeaderContent = ({
  editMode,
  onEditClick,
  onCancelClick,
  loading
}) => (
  <div className="tw-flex tw-justify-between">
    <h3 className="tw-flex tw-items-center">Activity Details</h3>
    {editMode ? (
      <div className="tw-flex tw-gap-5">
        <Button
          bg="neutral"
          outline
          customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[30px] tw-rounded-[4px] tw-font-semibold tw-border-gray-900"
          onClick={onCancelClick}
        >
          <i className="fa-light fa-xmark" />
          Cancel
        </Button>
        <Button
          bg="success"
          outline
          customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[30px] tw-rounded-[4px] tw-font-semibold"
          type="submit"
          form="activity-details-form"
        >
          <i className="fa-light fa-check" />
          Save
        </Button>
      </div>
    ) : (
      <Button
        outline
        customStyle="tw-flex tw-gap-2.5 tw-items-center tw-self-end tw-h-[30px] tw-rounded-[4px] tw-font-semibold"
        onClick={onEditClick}
        disabled={loading}
      >
        <i className="fa-regular fa-pen tw-opacity-85" />
        Edit Activity Details
      </Button>
    )}
  </div>
);

export default ActivityDetailsHeaderContent;
