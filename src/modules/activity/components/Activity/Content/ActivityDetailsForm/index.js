import ActivityDetailsFormFields from "./ActivityDetailsFormFields";
import ActivityDetailsHeaderContent from "./ActivityDetailsHeaderContent";
import {
  CardWithHeader,
  Spinner,
  TagWithDropdown
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const ActivityDetailsForm = props => {
  const {
    activityDetails,
    loading,
    error,
    form,
    editMode,
    toggleEditMode,
    isPhase4Enabled
  } = props;
  const { status, statusOptions, handleStatusChange, handleCancelEdit } =
    useComponentLogic(props);

  if (loading) {
    return (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-p-10">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <p className="tw-px-4 tw-py-10 tw-text-sm tw-text-error-500">
        Error: {error.message}
      </p>
    );
  }

  return (
    <CardWithHeader
      headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-px-2.5 tw-py-2 !tw-rounded-t-[5px]"
      isExpandable
      cardClasses="tw-overflow-visible"
      bodyClasses="!tw-rounded-b-[5px]"
      onExpandFunction={handleCancelEdit}
      headerContent={
        <ActivityDetailsHeaderContent
          editMode={editMode}
          onEditClick={() => toggleEditMode(true)}
          onCancelClick={handleCancelEdit}
          loading={loading}
        />
      }
    >
      <div className="tw-flex tw-w-full">
        <ActivityDetailsFormFields
          activityDetails={activityDetails}
          editMode={editMode}
          toggleEditMode={toggleEditMode}
          form={form}
          isPhase4Enabled={isPhase4Enabled}
        />
        <div className="tw-flex tw-justify-center tw-py-5 lg:tw-basis-1/4 2xl:tw-basis-2/12">
          <div>
            <TagWithDropdown
              status={status.tagStatus}
              value={status}
              options={statusOptions}
              onChange={handleStatusChange}
            />
          </div>
        </div>
      </div>
    </CardWithHeader>
  );
};

export default ActivityDetailsForm;
