import { useMutation } from "@apollo/client";
import { UPDATE_ACTIVITY_STATUS } from "modules/activity/graphql/mutation";
import { find, propEq } from "ramda";
import { useCallback, useState } from "react";
import { useToast } from "@q-centrix/q-components-react";

const statusOptions = [
  { value: "complete", label: "Complete", tagStatus: "success" },
  { value: "blocked", label: "Blocked", tagStatus: "danger" },
  { value: "in_progress", label: "In Progress", tagStatus: "main" },
  { value: "open", label: "Open", tagStatus: "default" }
];

const getStatusByLabel = labelValue =>
  find(propEq("label", labelValue), statusOptions);

export const useComponentLogic = ({
  activityDetails,
  form,
  toggleEditMode
}) => {
  const { toast } = useToast();
  const [status, setStatus] = useState(
    getStatusByLabel(activityDetails?.status?.name)
  );

  const [updateActivityStatus] = useMutation(UPDATE_ACTIVITY_STATUS);

  const handleStatusChange = useCallback(event => {
    updateActivityStatus({
      variables: {
        activityId: Number(activityDetails?.id),
        status: event.value
      },
      onCompleted: ({ updateActivityStatus: updateActivityStatusResults }) => {
        if (updateActivityStatusResults?.errors) {
          updateActivityStatusResults.errors?.messages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        } else {
          setStatus(
            getStatusByLabel(updateActivityStatusResults.activity.status.name)
          );
          toast({
            variant: "success",
            description: "Status updated!"
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  }, []);

  const handleCancelEdit = () => {
    toggleEditMode(false);
    form.reset();
  };

  return {
    status,
    statusOptions,
    handleStatusChange,
    handleCancelEdit
  };
};
