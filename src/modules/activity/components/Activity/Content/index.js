import ActivityDetailsForm from "./ActivityDetailsForm";
import { Spin<PERSON>, Text<PERSON>rea } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import redirectToRoot from "utils/redirectToRoot";
import StepsSection from "./StepsSection";
import DeleteModal from "shared/components/DeleteModal";
import CaseReassignment from "shared/components/CaseReassignment";
import { GET_ACTIVITY_DETAILS } from "modules/activity/graphql/query";
import ExpandablePanel from "shared/components/ExpandablePanel";
import classnames from "classnames";
import CommentsAndNotesSection from "./CommentsAndNotesSection";

// eslint-disable-next-line complexity
const Content = ({
  deleteModalIsOpen,
  toggleDeleteModal,
  shouldShowActivityReassignment,
  toggleActivityReassignment
}) => {
  const {
    isRightPanelOpen,
    setIsRightPanelOpen,
    editMode,
    toggleEditMode,
    form,
    activityDetails,
    loading,
    detailsErrors,
    viewPermissionLoading,
    authorized,
    handleDeleteActivity,
    handleActivityReassignmentSuccess,
    activityReassignmentCommentValue,
    handleActivityReassignmentCommentChange,
    activityReassignmentCommentIsSubmitting,
    activityId,
    steps,
    loggedInUser,
    isPhase4Enabled
  } = useComponentLogic({
    toggleDeleteModal,
    toggleActivityReassignment,
    shouldShowActivityReassignment
  });

  if (viewPermissionLoading || loading)
    return (
      <div className="tw-flex tw-h-full tw-items-center tw-justify-center">
        <Spinner />
      </div>
    );

  if (!authorized) return redirectToRoot();

  const activityContainerClasses = classnames(
    "tw-absolute tw-left-0 tw-top-0 tw-flex tw-h-full tw-flex-col tw-gap-y-2.5 tw-overflow-y-auto tw-bg-gray-25 tw-p-2.5 tw-transition-all tw-ease-in-out",
    {
      "tw-w-[calc(100%-429px)]": isRightPanelOpen,
      "tw-w-[calc(100%-15px)]": !isRightPanelOpen
    }
  );

  return (
    <div
      className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
      id="activity-details"
    >
      <div className={activityContainerClasses}>
        <ActivityDetailsForm
          form={form}
          activityDetails={activityDetails}
          loading={loading}
          error={detailsErrors}
          editMode={editMode}
          toggleEditMode={toggleEditMode}
          isPhase4Enabled={isPhase4Enabled}
        />

        <StepsSection
          steps={steps}
          activityId={activityId}
          isPhase4Enabled={isPhase4Enabled}
        />
      </div>
      <ExpandablePanel
        showButton
        isPanelOpen={isRightPanelOpen}
        handlePanelToggle={setIsRightPanelOpen}
      >
        {shouldShowActivityReassignment ? (
          <CaseReassignment
            caseIds={activityDetails?.id}
            onCancel={toggleActivityReassignment}
            onSuccess={handleActivityReassignmentSuccess}
            refetchQueries={[GET_ACTIVITY_DETAILS]}
            isActivityDetails
            activityReassignmentCommentValue={activityReassignmentCommentValue}
            reassignmentType="activity"
          >
            <TextArea
              id="activity-reassignment-comment"
              value={activityReassignmentCommentValue}
              onChange={handleActivityReassignmentCommentChange}
              label="Comment"
              placeholder="Enter comments here..."
              textareaClassName="tw-min-h-[120px]"
              disabled={activityReassignmentCommentIsSubmitting}
            />
          </CaseReassignment>
        ) : (
          <CommentsAndNotesSection
            activityId={activityId}
            loggedInUser={loggedInUser}
          />
        )}
      </ExpandablePanel>
      <DeleteModal
        title="Delete Activity"
        body="Are you sure you want to permanently delete this activity?"
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteActivity}
      />
    </div>
  );
};

export default Content;
