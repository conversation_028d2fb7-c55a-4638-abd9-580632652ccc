// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommentsAndNotesSection it renders component with data 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-col tw-gap-y-2.5"
>
  <AnimatePresence>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Comments
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-down"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedComments
          comments={
            Array [
              Object {
                "__typename": "Comment",
                "body": "Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.

Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.",
                "createdAt": "2023-10-24T11:57:24-04:00",
                "editable": true,
                "id": "142",
                "updatedAt": "2023-12-19T11:45:51-05:00",
                "user": Object {
                  "__typename": "User",
                  "email": "<EMAIL>",
                  "fullName": "Russell Reas",
                  "id": "1",
                },
              },
              Object {
                "__typename": "Comment",
                "body": "Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.",
                "createdAt": "2023-10-24T11:57:21-04:00",
                "editable": true,
                "id": "141",
                "updatedAt": "2023-10-24T11:57:21-04:00",
                "user": Object {
                  "__typename": "User",
                  "email": "<EMAIL>",
                  "fullName": "Russell Reas",
                  "id": "1",
                },
              },
              Object {
                "__typename": "Comment",
                "body": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. 

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?",
                "createdAt": "2023-10-18T13:04:51-04:00",
                "editable": true,
                "id": "52",
                "updatedAt": "2023-10-24T11:57:39-04:00",
                "user": Object {
                  "__typename": "User",
                  "email": "<EMAIL>",
                  "fullName": "Russell Reas",
                  "id": "1",
                },
              },
              Object {
                "__typename": "Comment",
                "body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. 

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
                "createdAt": "2023-10-18T13:04:11-04:00",
                "editable": true,
                "id": "51",
                "updatedAt": "2023-10-24T11:57:55-04:00",
                "user": Object {
                  "__typename": "User",
                  "email": "<EMAIL>",
                  "fullName": "Russell Reas",
                  "id": "1",
                },
              },
            ]
          }
          deleteLoading={false}
          isExpanded={null}
          loading={false}
          loggedInUser={
            Object {
              "email": "<EMAIL>",
              "error": null,
              "id": "1",
              "isFetching": false,
              "isLoaded": true,
              "menuLinks": Array [],
            }
          }
          newCommentValue=""
          onDeleteExistingComment={[Function]}
          onNewCommentChange={[Function]}
          onSubmitNewComment={[Function]}
          onUpdateExistingComment={[Function]}
        />
      </motion.div>
    </CardWithHeader>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Notes
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-up"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedNotes
          activityId="1"
          isExpanded={null}
          mutation={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "createOrUpdateActivityNote",
                  },
                  "operation": "mutation",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "text",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "createOrUpdateActivityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "note",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "id",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "text",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "user",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "fullName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "errors",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "fullMessages",
                                    },
                                    "selectionSet": undefined,
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "String",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "text",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 289,
                "start": 0,
              },
            }
          }
          query={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "activityNote",
                  },
                  "operation": "query",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "id",
                              },
                              "selectionSet": undefined,
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                              "selectionSet": undefined,
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 116,
                "start": 0,
              },
            }
          }
        />
      </motion.div>
    </CardWithHeader>
  </AnimatePresence>
</div>
`;

exports[`CommentsAndNotesSection it renders component with error 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-col tw-gap-y-2.5"
>
  <AnimatePresence>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Comments
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-down"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedComments
          comments={Array []}
          deleteLoading={false}
          error={[ApolloError: an error occurred]}
          isExpanded={null}
          loading={false}
          loggedInUser={
            Object {
              "email": "<EMAIL>",
              "error": null,
              "id": "1",
              "isFetching": false,
              "isLoaded": true,
              "menuLinks": Array [],
            }
          }
          newCommentValue=""
          onDeleteExistingComment={[Function]}
          onNewCommentChange={[Function]}
          onSubmitNewComment={[Function]}
          onUpdateExistingComment={[Function]}
        />
      </motion.div>
    </CardWithHeader>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Notes
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-up"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedNotes
          activityId="1"
          isExpanded={null}
          mutation={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "createOrUpdateActivityNote",
                  },
                  "operation": "mutation",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "text",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "createOrUpdateActivityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "note",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "id",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "text",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "user",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "fullName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "errors",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "fullMessages",
                                    },
                                    "selectionSet": undefined,
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "String",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "text",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 289,
                "start": 0,
              },
            }
          }
          query={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "activityNote",
                  },
                  "operation": "query",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "id",
                              },
                              "selectionSet": undefined,
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                              "selectionSet": undefined,
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 116,
                "start": 0,
              },
            }
          }
        />
      </motion.div>
    </CardWithHeader>
  </AnimatePresence>
</div>
`;

exports[`CommentsAndNotesSection it renders component with loading state 1`] = `
<div
  className="tw-flex tw-h-full tw-flex-col tw-gap-y-2.5"
>
  <AnimatePresence>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Comments
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-down"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedComments
          comments={Array []}
          deleteLoading={false}
          isExpanded={null}
          loading={true}
          loggedInUser={
            Object {
              "email": "<EMAIL>",
              "error": null,
              "id": "1",
              "isFetching": false,
              "isLoaded": true,
              "menuLinks": Array [],
            }
          }
          newCommentValue=""
          onDeleteExistingComment={[Function]}
          onNewCommentChange={[Function]}
          onSubmitNewComment={[Function]}
          onUpdateExistingComment={[Function]}
        />
      </motion.div>
    </CardWithHeader>
    <CardWithHeader
      bodyClasses="tw-overlfow-scroll tw-max-h-full"
      cardClasses="tw-max-h-full"
      headerClasses="main title-white tw-p-2.5"
      headerContent={
        <div
          className="tw-flex tw-flex-row tw-justify-between"
        >
          <h3>
            Notes
          </h3>
          <button
            className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
            onClick={[Function]}
            type="button"
          >
            <i
              className="fa-light fa-chevron-up"
            />
          </button>
        </div>
      }
    >
      <motion.div
        animate={
          Object {
            "height": "50%",
          }
        }
        initial={
          Object {
            "height": "calc(100% - 46px)",
          }
        }
        transition={
          Object {
            "duration": 0.6,
            "ease": "easeIn",
          }
        }
      >
        <UpdatedNotes
          activityId="1"
          isExpanded={null}
          mutation={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "createOrUpdateActivityNote",
                  },
                  "operation": "mutation",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "text",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "createOrUpdateActivityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "note",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "id",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "text",
                                    },
                                    "selectionSet": undefined,
                                  },
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "user",
                                    },
                                    "selectionSet": Object {
                                      "kind": "SelectionSet",
                                      "selections": Array [
                                        Object {
                                          "alias": undefined,
                                          "arguments": Array [],
                                          "directives": Array [],
                                          "kind": "Field",
                                          "name": Object {
                                            "kind": "Name",
                                            "value": "fullName",
                                          },
                                          "selectionSet": undefined,
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "errors",
                              },
                              "selectionSet": Object {
                                "kind": "SelectionSet",
                                "selections": Array [
                                  Object {
                                    "alias": undefined,
                                    "arguments": Array [],
                                    "directives": Array [],
                                    "kind": "Field",
                                    "name": Object {
                                      "kind": "Name",
                                      "value": "fullMessages",
                                    },
                                    "selectionSet": undefined,
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "String",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "text",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 289,
                "start": 0,
              },
            }
          }
          query={
            Object {
              "definitions": Array [
                Object {
                  "directives": Array [],
                  "kind": "OperationDefinition",
                  "name": Object {
                    "kind": "Name",
                    "value": "activityNote",
                  },
                  "operation": "query",
                  "selectionSet": Object {
                    "kind": "SelectionSet",
                    "selections": Array [
                      Object {
                        "alias": undefined,
                        "arguments": Array [
                          Object {
                            "kind": "Argument",
                            "name": Object {
                              "kind": "Name",
                              "value": "activityId",
                            },
                            "value": Object {
                              "kind": "Variable",
                              "name": Object {
                                "kind": "Name",
                                "value": "activityId",
                              },
                            },
                          },
                        ],
                        "directives": Array [],
                        "kind": "Field",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityNote",
                        },
                        "selectionSet": Object {
                          "kind": "SelectionSet",
                          "selections": Array [
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "id",
                              },
                              "selectionSet": undefined,
                            },
                            Object {
                              "alias": undefined,
                              "arguments": Array [],
                              "directives": Array [],
                              "kind": "Field",
                              "name": Object {
                                "kind": "Name",
                                "value": "text",
                              },
                              "selectionSet": undefined,
                            },
                          ],
                        },
                      },
                    ],
                  },
                  "variableDefinitions": Array [
                    Object {
                      "defaultValue": undefined,
                      "directives": Array [],
                      "kind": "VariableDefinition",
                      "type": Object {
                        "kind": "NonNullType",
                        "type": Object {
                          "kind": "NamedType",
                          "name": Object {
                            "kind": "Name",
                            "value": "ID",
                          },
                        },
                      },
                      "variable": Object {
                        "kind": "Variable",
                        "name": Object {
                          "kind": "Name",
                          "value": "activityId",
                        },
                      },
                    },
                  ],
                },
              ],
              "kind": "Document",
              "loc": Object {
                "end": 116,
                "start": 0,
              },
            }
          }
        />
      </motion.div>
    </CardWithHeader>
  </AnimatePresence>
</div>
`;
