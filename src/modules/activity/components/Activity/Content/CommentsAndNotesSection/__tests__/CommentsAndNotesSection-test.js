import { act, create } from "react-test-renderer";
import wait from "waait";
import { decoratedApollo } from "utils/tests/decorated";
import {
  activityCommentsSuccessMock,
  activityCommentsErrorMock
} from "../graphql/mocks";
import CommentsAndNotesSection from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));
jest.mock("shared/components/UpdatedNotes", () => "UpdatedNotes");
jest.mock("shared/components/UpdatedComments", () => "UpdatedComments");

function render(apolloMocks = [activityCommentsSuccessMock]) {
  return create(
    decoratedApollo({
      component: CommentsAndNotesSection,
      props: {
        activityId: "1",
        loggedInUser: {
          menuLinks: [],
          email: "<EMAIL>",
          id: "1",
          isFetching: false,
          isLoaded: true,
          error: null
        }
      },
      initialValues: {},
      initialAppValues: {},
      apolloMocks
    })
  );
}

describe("CommentsAndNotesSection", () => {
  test("it renders component with loading state", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const component = render([activityCommentsErrorMock]);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
