import { gql } from "@apollo/client";

export const CREATE_OR_UPDATE_ACTIVITY_COMMENT = gql`
  mutation createOrUpdateActivityComment(
    $activityId: ID!
    $commentId: ID
    $body: String!
  ) {
    createOrUpdateActivityComment(
      activityId: $activityId
      commentId: $commentId
      body: $body
    ) {
      comment {
        id
        createdAt
        updatedAt
        editable
        body
      }
      errors {
        messages {
          errors
          attribute
        }
        fullMessages
      }
    }
  }
`;

export const CREATE_OR_UPDATE_ACTIVITY_NOTE = gql`
  mutation createOrUpdateActivityNote($activityId: ID!, $text: String!) {
    createOrUpdateActivityNote(activityId: $activityId, text: $text) {
      note {
        id
        text
        user {
          fullName
        }
      }
      errors {
        fullMessages
      }
    }
  }
`;
