import { gql } from "@apollo/client";

export const ACTIVITY_COMMENTS = gql`
  query activityComments($activityId: ID!) {
    activityComments(activityId: $activityId) {
      id
      editable
      body
      createdAt
      updatedAt
      user {
        id
        fullName
        email
      }
    }
  }
`;

export const ACTIVITY_NOTE = gql`
  query activityNote($activityId: ID!) {
    activityNote(activityId: $activityId) {
      id
      text
    }
  }
`;
