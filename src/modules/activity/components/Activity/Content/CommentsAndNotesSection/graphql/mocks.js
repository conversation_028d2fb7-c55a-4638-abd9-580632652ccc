import { ACTIVITY_COMMENTS, ACTIVITY_NOTE } from "./queries";

export const mockActivityId = "1";

export const activityCommentsSuccessMock = {
  request: {
    query: ACTIVITY_COMMENTS,
    variables: { activityId: mockActivityId }
  },
  result: {
    data: {
      activityComments: [
        {
          __typename: "Comment",
          id: "142",
          user: {
            __typename: "User",
            id: "1",
            fullName: "<PERSON> Re<PERSON>",
            email: "<EMAIL>"
          },
          editable: true,
          body: "Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.\n\nItaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.",
          createdAt: "2023-10-24T11:57:24-04:00",
          updatedAt: "2023-12-19T11:45:51-05:00"
        },
        {
          __typename: "Comment",
          id: "141",
          user: {
            __typename: "User",
            id: "1",
            fullName: "<PERSON>as",
            email: "<EMAIL>"
          },
          editable: true,
          body: "Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.",
          createdAt: "2023-10-24T11:57:21-04:00",
          updatedAt: "2023-10-24T11:57:21-04:00"
        },
        {
          __typename: "Comment",
          id: "52",
          user: {
            __typename: "User",
            id: "1",
            fullName: "Russell Reas",
            email: "<EMAIL>"
          },
          editable: true,
          body: "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. \n\nNeque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?",
          createdAt: "2023-10-18T13:04:51-04:00",
          updatedAt: "2023-10-24T11:57:39-04:00"
        },
        {
          __typename: "Comment",
          id: "51",
          user: {
            __typename: "User",
            id: "1",
            fullName: "Russell Reas",
            email: "<EMAIL>"
          },
          editable: true,
          body: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. \n\nDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
          createdAt: "2023-10-18T13:04:11-04:00",
          updatedAt: "2023-10-24T11:57:55-04:00"
        }
      ]
    }
  }
};

export const activityCommentsErrorMock = {
  request: {
    query: ACTIVITY_COMMENTS,
    variables: { activityId: mockActivityId }
  },
  error: new Error("an error occurred")
};

export const activityNoteSuccessMock = {
  request: {
    query: ACTIVITY_NOTE,
    variables: { activityId: Number(mockActivityId) }
  },
  result: {
    data: {
      activityNote: {
        __typename: "Note",
        id: "1",
        text: "Mock note text"
      }
    }
  }
};

export const activityNoteErrorMock = {
  request: {
    query: ACTIVITY_NOTE,
    variables: { activityId: Number(mockActivityId) }
  },
  error: new Error("an error occurred")
};
