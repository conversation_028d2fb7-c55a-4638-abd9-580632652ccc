import { CardWithHeader } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import { ACTIVITY_NOTE } from "./graphql/queries";
import { CREATE_OR_UPDATE_ACTIVITY_NOTE } from "./graphql/mutations";
import UpdatedComments from "shared/components/UpdatedComments";
import UpdatedNotes from "shared/components/UpdatedNotes";
import { AnimatePresence, motion } from "framer-motion";
import classNames from "classnames";

const expandIconClass = componentState =>
  classNames("fa-light", {
    "fa-chevron-down": !componentState,
    "fa-chevron-up": componentState
  });

const CommentsAndNotesSection = ({ activityId, loggedInUser }) => {
  const {
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    createOrUpdateCaseCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    activityCommentsLoading,
    activityCommentsError,
    componentsState,
    toggleExpand,
    getCommentsHeight,
    getNotesHeight
  } = useComponentLogic({ activityId });

  return (
    <div className="tw-flex tw-h-full tw-flex-col tw-gap-y-2.5">
      <AnimatePresence>
        <CardWithHeader
          headerClasses="main title-white tw-p-2.5"
          headerContent={
            <div className="tw-flex tw-flex-row tw-justify-between">
              <h3>Comments</h3>
              <button
                type="button"
                onClick={() => toggleExpand("comments")}
                className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
              >
                <i
                  className={expandIconClass(
                    componentsState.expandState.comments
                  )}
                />
              </button>
            </div>
          }
          cardClasses="tw-max-h-full"
          bodyClasses="tw-overlfow-scroll tw-max-h-full"
        >
          <motion.div
            {...getCommentsHeight}
            transition={{
              duration: 0.6,
              ease: componentsState.expandState.comments ? "easeOut" : "easeIn"
            }}
          >
            <UpdatedComments
              isExpanded={componentsState.expandState.comments}
              loggedInUser={loggedInUser}
              newCommentValue={newCommentValue}
              onNewCommentChange={handleNewCommentChange}
              onSubmitNewComment={handleSubmitNewComment}
              createLoading={createOrUpdateCaseCommentLoading}
              onUpdateExistingComment={handleUpdateExistingComment}
              updateLoading={createOrUpdateCaseCommentLoading}
              onDeleteExistingComment={handleDeleteExistingComment}
              deleteLoading={deleteCommentLoading}
              comments={sortedComments}
              loading={activityCommentsLoading}
              error={activityCommentsError}
            />
          </motion.div>
        </CardWithHeader>
        <CardWithHeader
          headerClasses="main title-white tw-p-2.5"
          headerContent={
            <div className="tw-flex tw-flex-row tw-justify-between">
              <h3>Notes</h3>
              <button
                type="button"
                onClick={() => toggleExpand("notes")}
                className="tw-ml-2.5 tw-flex tw-h-[25px] tw-w-[25px] tw-items-center tw-justify-center tw-rounded tw-border tw-px-2.5 tw-py-[5px]"
              >
                <i
                  className={expandIconClass(
                    !componentsState.expandState.notes
                  )}
                />
              </button>
            </div>
          }
          cardClasses="tw-max-h-full"
          bodyClasses="tw-overlfow-scroll tw-max-h-full"
        >
          <motion.div
            {...getNotesHeight}
            transition={{
              duration: 0.6,
              ease: componentsState.expandState.notes ? "easeOut" : "easeIn"
            }}
          >
            <UpdatedNotes
              isExpanded={componentsState.expandState.notes}
              activityId={activityId}
              query={ACTIVITY_NOTE}
              mutation={CREATE_OR_UPDATE_ACTIVITY_NOTE}
            />
          </motion.div>
        </CardWithHeader>
      </AnimatePresence>
    </div>
  );
};

export default CommentsAndNotesSection;
