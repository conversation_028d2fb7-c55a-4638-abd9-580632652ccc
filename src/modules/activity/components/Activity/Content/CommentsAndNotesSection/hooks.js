/* eslint-disable max-statements */
import { useState, useMemo, useCallback } from "react";
import { useQuery, useMutation } from "@apollo/client";
import { always, cond, equals, isEmpty, T } from "ramda";
import { ACTIVITY_COMMENTS } from "./graphql/queries";
import { CREATE_OR_UPDATE_ACTIVITY_COMMENT } from "./graphql/mutations";
import { DELETE_COMMENT } from "shared/graphql/mutation";
import { sortByCreatedAtDescending } from "utils/fp/sortByCreatedAtDescending";
import { useToast } from "@q-centrix/q-components-react";

const calculateHeights = (
  component,
  otherComponent,
  { expandState, prevState }
) => {
  const getInitialHeight = cond([
    [
      state =>
        state[component] === true ||
        prevState[component] === "calc(100% - 46px)",
      always("50%")
    ],
    [
      state => state[component] === false && prevState[component] === "0px",
      always("50%")
    ],
    [T, always("calc(100% - 46px)")]
  ]);

  const getAnimateHeight = cond([
    [
      state => state[component] === null || state[otherComponent] === null,
      always("50%")
    ],
    [state => state[component] === true, always("calc(100% - 46px)")],
    [T, always("0px")]
  ]);

  return {
    initial: { height: getInitialHeight(expandState) },
    animate: { height: getAnimateHeight(expandState) }
  };
};

export const useComponentLogic = ({ activityId }) => {
  const [newCommentValue, setNewCommentValue] = useState("");
  const [componentsState, setComponentsState] = useState({
    expandState: { comments: null, notes: null },
    prevState: { comments: "50%", notes: "50%" }
  });
  const { toast } = useToast();

  const toggleExpand = useCallback(component => {
    const otherComponent = component === "comments" ? "notes" : "comments";

    setComponentsState(prevState => {
      const newExpandState = cond([
        [equals(null), () => ({ [component]: true, [otherComponent]: false })],
        [T, () => ({ comments: null, notes: null })]
      ])(prevState.expandState[component]);

      const newPrevState = {
        ...prevState.prevState,
        [component]:
          prevState.expandState[component] === null
            ? "calc(100% - 46px)"
            : "50%",
        [otherComponent]: "50%"
      };

      return {
        expandState: newExpandState,
        prevState: newPrevState
      };
    });
  }, []);

  const getCommentsHeight = useMemo(
    () => calculateHeights("comments", "notes", componentsState),
    [componentsState]
  );
  const getNotesHeight = useMemo(
    () => calculateHeights("notes", "comments", componentsState),
    [componentsState]
  );

  const {
    data: activityCommentsData = {},
    loading: activityCommentsLoading,
    error: activityCommentsError
  } = useQuery(ACTIVITY_COMMENTS, {
    variables: { activityId }
  });

  const { activityComments = [] } = activityCommentsData;

  const sortedComments = useMemo(
    () => sortByCreatedAtDescending(activityComments),
    [activityComments]
  );

  const [
    createOrUpdateActivityComment,
    { loading: createOrUpdateActivityCommentLoading }
  ] = useMutation(CREATE_OR_UPDATE_ACTIVITY_COMMENT, {
    refetchQueries: [ACTIVITY_COMMENTS]
  });

  const handleNewCommentChange = e => setNewCommentValue(e.target.value);

  const handleSubmitNewComment = e => {
    e.preventDefault();

    if (isEmpty(newCommentValue)) return;

    createOrUpdateActivityComment({
      variables: {
        activityId,
        body: newCommentValue.trim()
      },
      onCompleted: ({
        createOrUpdateActivityComment: createOrUpdateActivityCommentResult
      }) => {
        if (createOrUpdateActivityCommentResult?.errors) {
          createOrUpdateActivityCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          setNewCommentValue("");
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleUpdateExistingComment = (
    commentId,
    updatedComment,
    { onCompleted }
  ) => {
    createOrUpdateActivityComment({
      variables: {
        activityId,
        commentId,
        body: updatedComment.trim()
      },
      onCompleted: ({
        createOrUpdateActivityComment: createOrUpdateActivityCommentResult
      }) => {
        if (createOrUpdateActivityCommentResult?.errors) {
          createOrUpdateActivityCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          onCompleted();
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const [deleteComment, { loading: deleteCommentLoading }] = useMutation(
    DELETE_COMMENT,
    { refetchQueries: [ACTIVITY_COMMENTS] }
  );

  const handleDeleteExistingComment = commentId => {
    deleteComment({
      variables: { commentId },
      onCompleted: ({ deleteComment: deleteCommentResult }) => {
        if (deleteCommentResult?.errors) {
          deleteCommentResult.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  return {
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    createOrUpdateActivityCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    activityCommentsLoading,
    activityCommentsError,
    componentsState,
    toggleExpand,
    getCommentsHeight,
    getNotesHeight
  };
};
