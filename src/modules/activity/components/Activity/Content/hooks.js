import { useMutation, useQuery } from "@apollo/client";
import { useNavigate, useParams } from "react-router-dom";
import {
  GET_ACTIVITY_DETAILS,
  GET_ACTIVITY_PERMISSIONS
} from "modules/activity/graphql/query";
import { DELETE_ACTIVITY } from "modules/activity/graphql/mutation";
import { useSelector } from "react-redux";
import accountSettingsSelectors from "modules/app/redux/selectors/accountSettings";
import { useToast } from "@q-centrix/q-components-react";
import { useEffect, useState } from "react";
import {
  always,
  and,
  applySpec,
  head,
  ifElse,
  isEmpty,
  path,
  pipe,
  propOr,
  reduce,
  split,
  toPairs
} from "ramda";
import { useForm } from "react-hook-form";
import { isNullOrEmpty } from "utils/fp";
import { parse } from "date-fns";
import { yupResolver } from "@hookform/resolvers/yup";
import formSchema from "../validationSchema";
import { CREATE_OR_UPDATE_ACTIVITY_COMMENT } from "./CommentsAndNotesSection/graphql/mutations";
import { userFeatureToggles } from "modules/facility-groups/utils/userFeatureToggles";
// eslint-disable-next-line max-statements

const translateOption = (labelPath, valuePath) =>
  ifElse(
    and(path(labelPath), path(valuePath)),
    applySpec({
      label: path(labelPath),
      value: path(valuePath)
    }),
    always(null)
  );

const translateDate = datePath =>
  pipe(
    path(datePath),
    ifElse(
      isNullOrEmpty,
      always(null),
      pipe(split("T"), head, val => parse(val, "yyyy-MM-dd", new Date()))
    )
  );

const translateOptionKeepingExtraData = objectPath =>
  pipe(
    path(objectPath),
    toPairs,
    // eslint-disable-next-line complexity
    reduce((acc, [key, value]) => {
      if (key === "name" || key === "fullName") acc.label = value;
      else if (key === "id") acc.value = value;
      else acc[key] = value;
      return acc;
    }, {})
  );

// eslint-disable-next-line max-statements
export const useComponentLogic = ({
  toggleDeleteModal,
  toggleActivityReassignment,
  shouldShowActivityReassignment
}) => {
  const { isFeatureEnabled } = userFeatureToggles();
  const isPhase4Enabled = isFeatureEnabled("QFlow Phase 4");
  const loggedInUser = useSelector(accountSettingsSelectors.accountSettings);
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [
    activityReassignmentCommentValue,
    setActivityReassignmentCommentValue
  ] = useState("");
  const [editMode, setEditMode] = useState(false);
  const toggleEditMode = value => {
    setEditMode(value);
  };
  const [isRightPanelOpen, setIsRightPanelOpen] = useState(true);
  const {
    data: { activityPermissions: { authorized } } = {
      activityPermissions: { authorized: false }
    },
    loading: viewPermissionLoading
  } = useQuery(GET_ACTIVITY_PERMISSIONS, {
    variables: {
      action: "view",
      activityId: Number(id)
    }
  });

  const [deleteActivity] = useMutation(DELETE_ACTIVITY);

  const handleDeleteActivity = () => {
    deleteActivity({
      variables: {
        activityId: Number(id)
      },
      onCompleted: () => {
        navigate("/cases");
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      // invalidate activitiy list queries after mutation
      update: cache => {
        cache.modify({
          fields: {
            activityList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
    toggleDeleteModal();
  };

  const {
    data: { activityDetails } = {},
    loading,
    error: detailsErrors
  } = useQuery(GET_ACTIVITY_DETAILS, {
    variables: { activityId: Number(id) }
  });

  const form = useForm({
    resolver: yupResolver(formSchema),
    values: applySpec({
      createdAt: translateDate(["createdAt"]),
      deadlineDate: translateDate(["deadlineDate"]),
      facilityId: translateOption(["facility", "name"], ["facility", "id"]),
      activityType: translateOptionKeepingExtraData(["activityType"]),
      activityOwner: translateOptionKeepingExtraData(["owner"]),
      activityAssignee: translateOptionKeepingExtraData(["assignee"]),
      productCategory: propOr(null, "productCategory")
    })(activityDetails)
  });

  const steps = activityDetails?.steps;

  const handleActivityReassignmentCommentChange = e =>
    setActivityReassignmentCommentValue(e.target.value);

  const [
    createOrUpdateActivityComment,
    { loading: activityReassignmentCommentIsSubmitting }
  ] = useMutation(CREATE_OR_UPDATE_ACTIVITY_COMMENT);

  const handleSubmitActivityReassignmentComment = () => {
    if (isEmpty(activityReassignmentCommentValue)) return;

    createOrUpdateActivityComment({
      variables: {
        activityId: Number(id),
        body: activityReassignmentCommentValue.trim()
      },
      onCompleted: ({
        createOrUpdateActivityComment: createOrUpdateActivityCommentResult
      }) => {
        if (createOrUpdateActivityCommentResult?.errors) {
          createOrUpdateActivityCommentResult.errors?.fullMessages?.forEach(
            message => {
              toast({
                variant: "error",
                description: message
              });
            }
          );
        } else {
          setActivityReassignmentCommentValue("");
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  const handleActivityReassignmentSuccess = () => {
    handleSubmitActivityReassignmentComment();
    toggleActivityReassignment();
  };

  useEffect(() => {
    if (shouldShowActivityReassignment && !isRightPanelOpen) {
      setIsRightPanelOpen(true);
    }
  }, [shouldShowActivityReassignment, isRightPanelOpen]);

  return {
    isRightPanelOpen,
    setIsRightPanelOpen,
    editMode,
    toggleEditMode,
    activityDetails,
    form,
    loading,
    detailsErrors,
    viewPermissionLoading,
    authorized,
    handleDeleteActivity,
    handleActivityReassignmentSuccess,
    activityReassignmentCommentValue,
    handleActivityReassignmentCommentChange,
    activityReassignmentCommentIsSubmitting,
    activityId: id,
    steps,
    loggedInUser,
    isPhase4Enabled
  };
};
