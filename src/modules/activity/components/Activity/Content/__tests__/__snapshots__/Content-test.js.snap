// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Content it renders Spinner component 1`] = `
<div
  className="tw-flex tw-h-full tw-items-center tw-justify-center"
>
  <Spinner />
</div>
`;

exports[`Content it renders component correctly 1`] = `
<div
  className="tw-relative tw-flex tw-h-full tw-overflow-hidden tw-bg-gray-25"
  id="activity-details"
>
  <div
    className="tw-absolute tw-left-0 tw-top-0 tw-flex tw-h-full tw-flex-col tw-gap-y-2.5 tw-overflow-y-auto tw-bg-gray-25 tw-p-2.5 tw-transition-all tw-ease-in-out tw-w-[calc(100%-429px)]"
  >
    <ActivityDetailsForm
      activityDetails={
        Object {
          "__typename": "Activity",
          "activityType": Object {
            "__typename": "ActivityType",
            "id": "5",
            "name": "Case Communication/Partner Collaboration",
          },
          "assignee": Object {
            "__typename": "User",
            "fullName": "<PERSON> White",
            "id": "9",
          },
          "createdAt": "2025-01-17",
          "deadlineDate": "2025-02-01",
          "facility": Object {
            "__typename": "Facility",
            "id": "6",
            "name": "Hospital Questionnaire",
          },
          "id": 1,
          "owner": Object {
            "__typename": "User",
            "fullName": "Russell Reas",
            "id": "1",
          },
          "status": Object {
            "__typename": "ActivityStatus",
            "id": "1",
            "name": "Open",
          },
          "steps": Array [
            Object {
              "assignee": Object {
                "__typename": "User",
                "fullName": "Julia White",
                "id": "9",
              },
              "createdAt": "2025-01-17 19:40:04 -0500",
              "deadlineDate": "2025-02-01",
              "hourGroup": Object {
                "__typename": "Step",
                "hourEntries": Array [],
                "id": "1",
              },
              "id": "1",
              "permissions": Object {
                "__typename": "StepPermissions",
                "delete": true,
                "edit": true,
              },
              "status": Object {
                "__typename": "ActivityStatus",
                "key": "open",
                "name": "Open",
              },
              "stepType": Object {
                "__typename": "StepTypeDetails",
                "id": "23",
                "name": "Set up Client Meeting",
              },
            },
          ],
        }
      }
      editMode={false}
      form={
        Object {
          "clearErrors": [Function],
          "control": Object {
            "_defaultValues": Object {
              "activityAssignee": Object {
                "__typename": "User",
                "label": "Julia White",
                "value": "9",
              },
              "activityOwner": Object {
                "__typename": "User",
                "label": "Russell Reas",
                "value": "1",
              },
              "activityType": Object {
                "__typename": "ActivityType",
                "label": "Case Communication/Partner Collaboration",
                "value": "5",
              },
              "createdAt": 2025-01-17T00:00:00.000Z,
              "deadlineDate": 2025-02-01T00:00:00.000Z,
              "facilityId": Object {
                "label": "Hospital Questionnaire",
                "value": "6",
              },
              "productCategory": null,
            },
            "_executeSchema": [Function],
            "_fields": Object {},
            "_formState": Object {
              "dirtyFields": Object {},
              "errors": Object {},
              "isDirty": false,
              "isLoading": false,
              "isSubmitSuccessful": false,
              "isSubmitted": false,
              "isSubmitting": false,
              "isValid": false,
              "isValidating": false,
              "submitCount": 0,
              "touchedFields": Object {},
            },
            "_formValues": Object {
              "activityAssignee": Object {
                "__typename": "User",
                "label": "Julia White",
                "value": "9",
              },
              "activityOwner": Object {
                "__typename": "User",
                "label": "Russell Reas",
                "value": "1",
              },
              "activityType": Object {
                "__typename": "ActivityType",
                "label": "Case Communication/Partner Collaboration",
                "value": "5",
              },
              "createdAt": 2025-01-17T00:00:00.000Z,
              "deadlineDate": 2025-02-01T00:00:00.000Z,
              "facilityId": Object {
                "label": "Hospital Questionnaire",
                "value": "6",
              },
              "productCategory": null,
            },
            "_getDirty": [Function],
            "_getFieldArray": [Function],
            "_getWatch": [Function],
            "_names": Object {
              "array": Set {},
              "focus": "",
              "mount": Set {},
              "unMount": Set {},
              "watch": Set {},
              "watchAll": false,
            },
            "_options": Object {
              "mode": "onSubmit",
              "reValidateMode": "onChange",
              "resolver": [Function],
              "shouldFocusError": true,
              "values": Object {
                "activityAssignee": Object {
                  "__typename": "User",
                  "label": "Julia White",
                  "value": "9",
                },
                "activityOwner": Object {
                  "__typename": "User",
                  "label": "Russell Reas",
                  "value": "1",
                },
                "activityType": Object {
                  "__typename": "ActivityType",
                  "label": "Case Communication/Partner Collaboration",
                  "value": "5",
                },
                "createdAt": 2025-01-17T00:00:00.000Z,
                "deadlineDate": 2025-02-01T00:00:00.000Z,
                "facilityId": Object {
                  "label": "Hospital Questionnaire",
                  "value": "6",
                },
                "productCategory": null,
              },
            },
            "_proxyFormState": Object {
              "dirtyFields": false,
              "errors": false,
              "isDirty": false,
              "isValid": false,
              "isValidating": false,
              "touchedFields": false,
            },
            "_removeUnmounted": [Function],
            "_reset": [Function],
            "_resetDefaultValues": [Function],
            "_state": Object {
              "action": false,
              "mount": true,
              "watch": false,
            },
            "_subjects": Object {
              "array": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "state": Object {
                "next": [Function],
                "observers": Array [
                  Object {
                    "next": [Function],
                  },
                ],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
              "values": Object {
                "next": [Function],
                "observers": Array [],
                "subscribe": [Function],
                "unsubscribe": [Function],
              },
            },
            "_updateDisabledField": [Function],
            "_updateFieldArray": [Function],
            "_updateFormState": [Function],
            "_updateValid": [Function],
            "getFieldState": [Function],
            "handleSubmit": [Function],
            "register": [Function],
            "setError": [Function],
            "unregister": [Function],
          },
          "formState": Object {
            "defaultValues": Object {
              "activityAssignee": Object {
                "__typename": "User",
                "label": "Julia White",
                "value": "9",
              },
              "activityOwner": Object {
                "__typename": "User",
                "label": "Russell Reas",
                "value": "1",
              },
              "activityType": Object {
                "__typename": "ActivityType",
                "label": "Case Communication/Partner Collaboration",
                "value": "5",
              },
              "createdAt": 2025-01-17T00:00:00.000Z,
              "deadlineDate": 2025-02-01T00:00:00.000Z,
              "facilityId": Object {
                "label": "Hospital Questionnaire",
                "value": "6",
              },
              "productCategory": null,
            },
          },
          "getFieldState": [Function],
          "getValues": [Function],
          "handleSubmit": [Function],
          "register": [Function],
          "reset": [Function],
          "resetField": [Function],
          "setError": [Function],
          "setFocus": [Function],
          "setValue": [Function],
          "trigger": [Function],
          "unregister": [Function],
          "watch": [Function],
        }
      }
      isPhase4Enabled={true}
      loading={false}
      toggleEditMode={[Function]}
    />
    <StepsSection
      activityId={1}
      isPhase4Enabled={true}
      steps={
        Array [
          Object {
            "assignee": Object {
              "__typename": "User",
              "fullName": "Julia White",
              "id": "9",
            },
            "createdAt": "2025-01-17 19:40:04 -0500",
            "deadlineDate": "2025-02-01",
            "hourGroup": Object {
              "__typename": "Step",
              "hourEntries": Array [],
              "id": "1",
            },
            "id": "1",
            "permissions": Object {
              "__typename": "StepPermissions",
              "delete": true,
              "edit": true,
            },
            "status": Object {
              "__typename": "ActivityStatus",
              "key": "open",
              "name": "Open",
            },
            "stepType": Object {
              "__typename": "StepTypeDetails",
              "id": "23",
              "name": "Set up Client Meeting",
            },
          },
        ]
      }
    />
  </div>
  <button
    className="tw-z-10 tw-absolute tw-top-1/2 tw-right-0 tw-transform tw--translate-y-1/2 tw-transition-all tw-ease-in-out tw-rounded-l-[7px] tw-bg-qc-orange-800 tw-text-white tw-transition-all tw-ease-in-out hover:tw-cursor-pointer hover:tw-bg-qc-orange-900 tw--translate-x-[414px]"
    onClick={[Function]}
    type="button"
  >
    <i
      className="tw-leading tw-px-[3px] tw-py-[30px] tw-text-sm tw-leading-normal tw-transition-all tw-ease-in-out fa-solid fa-chevron-right"
    />
  </button>
  <div
    className="tw-absolute tw-top-0 tw-right-0 tw-flex tw-h-full tw-transition-all tw-ease-in-out"
  >
    <div
      className="tw-flex tw-h-full tw-w-[414px] tw-flex-col tw-gap-2.5 tw-overflow-auto tw-bg-qc-blue-50 tw-p-2.5 tw-pl-5 tw-shadow-qc-sm"
    >
      <CommentsAndNotesSection
        activityId={1}
      />
    </div>
  </div>
  <DeleteModal
    body="Are you sure you want to permanently delete this activity?"
    onConfirm={[Function]}
    title="Delete Activity"
  />
</div>
`;
