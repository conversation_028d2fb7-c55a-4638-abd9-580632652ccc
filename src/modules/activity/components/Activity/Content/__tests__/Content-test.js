import wait from "waait";
import { create } from "react-test-renderer";
import { decoratedApollo } from "utils/tests/decorated";
import mocks from "modules/activity/graphql/mocks";
import Content from "modules/activity/components/Activity/Content";
import { useParams } from "react-router-dom";

jest.mock("../ActivityDetailsForm", () => "ActivityDetailsForm");
jest.mock("../CommentsAndNotesSection", () => "CommentsAndNotesSection");
jest.mock("shared/components/DeleteModal", () => "DeleteModal");
jest.mock("shared/components/CaseReassignment", () => "CaseReassignment");
jest.mock("../StepsSection", () => "StepsSection");
jest.mock("react-router-dom", () => {
  const mockUseParams = jest.fn();
  const mockUseNavigate = jest.fn();

  return {
    useParams: mockUseParams,
    useNavigate: mockUseNavigate
  };
});
jest.mock("@q-centrix/q-components-react", () => ({
  Spinner: "Spinner",
  TextArea: "TextArea",
  useToast: () => ({ toast: jest.fn() })
}));

describe("Content", () => {
  beforeEach(() => {
    useParams.mockReset();
  });
  const render = (mock = mocks) =>
    create(
      decoratedApollo({
        component: Content,
        props: {},
        initialValues: {},
        initialAppValues: {
          enabledFeatureToggles: {
            enabledFeatureToggles: ["QFlow Phase 4"]
          }
        },
        apolloMocks: mock
      })
    );

  test("it renders component correctly", async () => {
    useParams.mockReturnValue({ id: 1 });
    const component = render();

    await wait(100);

    expect(component).toMatchSnapshot();
  });

  test("it renders Spinner component", () => {
    useParams.mockReturnValue({ id: 2 });
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
