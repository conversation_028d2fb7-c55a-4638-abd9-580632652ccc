import { ascend, indexOf, path, sort } from "ramda";
import { useMemo } from "react";

const statusOrder = ["In Progress", "Open", "Blocked", "Complete"];
const sortStepsList = sort(
  ascend(status => indexOf(path(["status", "name"], status), statusOrder))
);

export const useComponentLogic = ({ steps }) => {
  const sortedSteps = useMemo(() => sortStepsList(steps), [steps]);

  return {
    sortedSteps
  };
};
