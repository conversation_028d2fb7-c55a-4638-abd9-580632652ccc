// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StepsHeader it renders Dropdown component in loading state 1`] = `
<div
  className="tw-flex tw-justify-between"
>
  <h3
    className="tw-flex tw-items-center"
  >
    Steps
  </h3>
  <Dropdown
    iconClass="fa-solid fa-chevron-down"
    loading={true}
    menuPlacement="auto"
    onChange={[Function]}
    options={Array []}
    placeholder="Select New Step to Add"
  />
</div>
`;

exports[`StepsHeader it renders an error 1`] = `
<div
  className="tw-flex tw-justify-between"
>
  <h3
    className="tw-flex tw-items-center"
  >
    Steps
  </h3>
  <p
    className="tw-text-sm tw-text-error-500"
  >
    Error: an error occurred
  </p>
</div>
`;

exports[`StepsHeader it renders component correctly 1`] = `
<div
  className="tw-flex tw-justify-between"
>
  <h3
    className="tw-flex tw-items-center"
  >
    Steps
  </h3>
  <Dropdown
    iconClass="fa-solid fa-chevron-down"
    loading={false}
    menuPlacement="auto"
    onChange={[Function]}
    options={
      Array [
        Object {
          "label": "Greenlight Orientation",
          "value": 1,
        },
        Object {
          "label": "Assign Initial Greenlighting Case",
          "value": 2,
        },
        Object {
          "label": "Complete First Greenlighting Case",
          "value": 3,
        },
        Object {
          "label": "IRR Review and Scoring",
          "value": 4,
        },
      ]
    }
    placeholder="Select New Step to Add"
  />
</div>
`;
