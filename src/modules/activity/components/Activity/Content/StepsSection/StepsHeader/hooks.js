import { useToast } from "@q-centrix/q-components-react";
import { useMutation, useQuery } from "@apollo/client";
import { useMemo } from "react";
import { map } from "ramda";
import {
  GET_ACTIVITY_DETAILS,
  GET_STEP_TYPES
} from "modules/activity/graphql/query";
import { CREATE_STEP } from "modules/activity/graphql/mutation";

export const useComponentLogic = ({ activityId }) => {
  const { toast } = useToast();
  const {
    data: { stepTypes = { stepTypes: [] } } = {},
    loading,
    error
  } = useQuery(GET_STEP_TYPES, {
    variables: { activityId: Number(activityId) }
  });

  const [createStep] = useMutation(CREATE_STEP);

  const handleCreateStep = selection => {
    const stepTypeId = selection.value;

    createStep({
      variables: {
        step: {
          stepTypeId,
          activityId
        }
      },
      onCompleted: ({ createStep: createStepResults }) => {
        if (createStepResults?.errors) {
          createStepResults.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: stepError => {
        toast({
          variant: "error",
          description: stepError.message
        });
      },
      refetchQueries: [GET_ACTIVITY_DETAILS, GET_STEP_TYPES]
    });
  };

  const stepTypeOptions = useMemo(
    () =>
      map(stepType => ({ label: stepType.name, value: stepType.id }))(
        stepTypes?.stepTypes
      ),
    [stepTypes]
  );

  return {
    stepTypeOptions,
    loading,
    error,
    handleCreateStep
  };
};
