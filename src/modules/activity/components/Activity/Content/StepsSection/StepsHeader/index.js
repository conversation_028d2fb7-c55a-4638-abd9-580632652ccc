import { Dropdown } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const StepsHeader = ({ activityId }) => {
  const { stepTypeOptions, loading, error, handleCreateStep } =
    useComponentLogic({
      activityId
    });

  return (
    <div className="tw-flex tw-justify-between">
      <h3 className="tw-flex tw-items-center">Steps</h3>
      {error ? (
        <p className="tw-text-sm tw-text-error-500">
          {`Error: ${error.message}`}
        </p>
      ) : (
        <Dropdown
          loading={loading}
          options={stepTypeOptions}
          onChange={handleCreateStep}
          iconClass="fa-solid fa-chevron-down"
          placeholder="Select New Step to Add"
          menuPlacement="auto"
        />
      )}
    </div>
  );
};

export default StepsHeader;
