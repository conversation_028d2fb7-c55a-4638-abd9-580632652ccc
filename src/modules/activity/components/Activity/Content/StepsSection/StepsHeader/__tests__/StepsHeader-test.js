import wait from "waait";
import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks from "modules/activity/graphql/mocks";
import StepsHeader from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Dropdown: "Dropdown",
  useToast: () => ({ toast: jest.fn() })
}));

describe("StepsHeader", () => {
  const render = activityId =>
    create(
      decoratedApollo({
        component: StepsHeader,
        props: { activityId },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );

  test("it renders component correctly", async () => {
    const component = render(1);

    await wait(100);

    expect(component).toMatchSnapshot();
  });

  test("it renders an error", async () => {
    const component = render(2);

    await wait(100);

    expect(component).toMatchSnapshot();
  });

  test("it renders Dropdown component in loading state", () => {
    const component = render(3);

    expect(component).toMatchSnapshot();
  });
});
