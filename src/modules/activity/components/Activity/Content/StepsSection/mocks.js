import { GET_ACTIVITY_DETAILS } from "modules/activity/graphql/query";
import {
  CREATE_STEP_HOUR_ENTRY,
  DELETE_STEP_HOUR_ENTRY,
  UPDATE_STEP_HOUR_ENTRY
} from "modules/activity/graphql/mutation";

const activityMockSuccessResponse = (id, hourGroup = {}) => ({
  data: {
    activityDetails: {
      id,
      facility: {
        id: "6",
        name: "Hospital Questionnaire",
        __typename: "Facility"
      },
      activityType: {
        id: "5",
        name: "Case Communication/Partner Collaboration",
        __typename: "ActivityType"
      },
      owner: {
        id: "1",
        fullName: "<PERSON> Reas",
        __typename: "User"
      },
      assignee: {
        id: "9",
        fullName: "<PERSON> White",
        __typename: "User"
      },
      status: {
        id: "1",
        name: "Open",
        __typename: "ActivityStatus"
      },
      createdAt: "2025-01-17",
      deadlineDate: "2025-02-01",
      steps: [
        {
          id: "1",
          assignee: {
            id: "9",
            fullName: "<PERSON>",
            __typename: "User"
          },
          stepType: {
            id: "23",
            name: "Set up Client Meeting",
            __typename: "StepTypeDetails"
          },
          deadlineDate: "2025-02-01",
          status: {
            key: "open",
            name: "Open",
            __typename: "ActivityStatus"
          },
          createdAt: "2025-01-17 11:00:00 -0500",
          permissions: {
            edit: true,
            delete: true,
            __typename: "StepPermissions"
          },
          hourGroup
        }
      ],
      __typename: "Activity"
    }
  }
});

const addHourEntryMock = {
  id: "1",
  hourEntries: [
    {
      id: "1",
      totalTime: {
        hours: null,
        minutes: null,
        seconds: null
      },
      startedAt: new Date(),
      finishedAt: null
    }
  ],
  __typename: "Step"
};

const updateHourEntryMock = {
  id: "1",
  hourEntries: [
    {
      id: "1",
      totalTime: {
        hours: 3,
        minutes: 180,
        seconds: 10800
      },
      startedAt: "2025-01-17 11:00:00 -0500",
      finishedAt: "2025-01-17 14:00:00 -0500"
    }
  ],
  __typename: "Step"
};

const stepHourEntryMocks = [
  {
    request: {
      query: CREATE_STEP_HOUR_ENTRY,
      variables: {
        stepId: "1",
        hourEntryInput: {
          id: null,
          hourGroupId: null,
          startedAt: "2025-01-16T22:00:19.936Z",
          finishedAt: null
        }
      }
    },
    result: {
      data: {
        createStepHourEntry: {
          hourEntry: {
            id: "1",
            hourGroupId: 1,
            startedAt: "2025-01-16T22:00:19.936Z",
            finishedAt: null
          },
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 1
      }
    },
    result: activityMockSuccessResponse(1, addHourEntryMock)
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 1
      }
    },
    result: activityMockSuccessResponse(1, addHourEntryMock)
  },
  {
    request: {
      query: DELETE_STEP_HOUR_ENTRY,
      variables: {
        hourEntryId: "1"
      }
    },
    result: {
      data: {
        deleteStepHourEntry: {
          response: true,
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 2
      }
    },
    result: activityMockSuccessResponse(2)
  },
  {
    request: {
      query: UPDATE_STEP_HOUR_ENTRY,
      variables: {
        hourEntryId: "1",
        hourEntryInput: {
          startedAt: "2025-01-17 11:00:00 -0500",
          finishedAt: "2025-01-17 14:00:00 -0500"
        }
      }
    },
    result: {
      data: {
        updateStepHourEntry: {
          hourEntry: {
            id: "1",
            hourGroupId: 1,
            startedAt: "2025-01-17T08:00:00.936Z",
            finishedAt: "2025-01-17T11:00:00.936Z"
          },
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: GET_ACTIVITY_DETAILS,
      variables: {
        activityId: 3
      }
    },
    result: activityMockSuccessResponse(3, updateHourEntryMock)
  }
];

export default stepHourEntryMocks;
