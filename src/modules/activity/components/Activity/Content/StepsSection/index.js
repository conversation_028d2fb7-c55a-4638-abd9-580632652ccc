import { Card<PERSON>ithHeader } from "@q-centrix/q-components-react";
import StepsHeader from "./StepsHeader";
import Step from "./Step";
import { motion, AnimatePresence } from "framer-motion";
import { useComponentLogic } from "./hooks";

const StepsSection = props => {
  const { steps, activityId, isPhase4Enabled } = props;
  const { sortedSteps } = useComponentLogic({ steps });

  return (
    <CardWithHeader
      headerContent={<StepsHeader activityId={activityId} />}
      headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-py-2.5 tw-px-2.5 !tw-rounded-t-[5px] "
      isExpandable
      cardClasses="tw-overflow-visible"
    >
      <div className="tw-flex tw-flex-col tw-gap-2.5 tw-p-2.5">
        <AnimatePresence>
          {sortedSteps?.map(step => (
            <motion.div key={step.id} layout transition={{ duration: 0.5 }}>
              <Step
                key={step.id}
                step={step}
                activityId={activityId}
                isPhase4Enabled={isPhase4Enabled}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </CardWithHeader>
  );
};

export default StepsSection;
