import { create } from "react-test-renderer";
import StepsSection from "..";

jest.mock("../StepsHeader", () => "StepsHeader");
jest.mock("../Step", () => "Step");
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));
jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader"
}));

const steps = [
  {
    id: 3,
    assignee: {
      fullName: "<PERSON> Kapoor"
    },
    deadlineDate: "2024-08-01T19:00:00-04:00",
    status: {
      key: 3,
      name: "complete"
    },
    createdAt: "2024-08-01T19:00:00-04:00",
    stepType: {
      name: "IRR Review and Scoring"
    }
  }
];

describe("Content", () => {
  const render = activityId =>
    create(
      <StepsSection activityId={activityId} steps={steps} isPhase4Enabled />
    );

  test("it renders component correctly", () => {
    const component = render(1);

    expect(component).toMatchSnapshot();
  });
});
