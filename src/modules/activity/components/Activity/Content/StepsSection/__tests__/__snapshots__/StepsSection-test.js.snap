// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Content it renders component correctly 1`] = `
<CardWithHeader
  cardClasses="tw-overflow-visible"
  headerClasses="!tw-bg-qc-blue-800 tw-text-sm !tw-text-white tw-py-2.5 tw-px-2.5 !tw-rounded-t-[5px] "
  headerContent={
    <StepsHeader
      activityId={1}
    />
  }
  isExpandable={true}
>
  <div
    className="tw-flex tw-flex-col tw-gap-2.5 tw-p-2.5"
  >
    <AnimatePresence>
      <motion.div
        layout={true}
        transition={
          Object {
            "duration": 0.5,
          }
        }
      >
        <Step
          activityId={1}
          isPhase4Enabled={true}
          step={
            Object {
              "assignee": Object {
                "fullName": "<PERSON> Kapoor",
              },
              "createdAt": "2024-08-01T19:00:00-04:00",
              "deadlineDate": "2024-08-01T19:00:00-04:00",
              "id": 3,
              "status": Object {
                "key": 3,
                "name": "complete",
              },
              "stepType": Object {
                "name": "IRR Review and Scoring",
              },
            }
          }
        />
      </motion.div>
    </AnimatePresence>
  </div>
</CardWithHeader>
`;
