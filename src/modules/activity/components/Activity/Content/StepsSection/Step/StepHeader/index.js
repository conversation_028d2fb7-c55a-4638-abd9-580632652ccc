import {
  TagWithDropdown,
  Button,
  CustomTooltip
} from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import EditStepHeader from "./EditStepHeader";
import classNames from "classnames";

const hoursRedirectButton = (
  <div className="tw-flex tw-min-w-[79px] tw-flex-row tw-items-center tw-justify-center tw-gap-2.5">
    <p>Hours Added</p>
    <i className="fa fas fa-chevron-right tw-text-sm" />
  </div>
);

// eslint-disable-next-line complexity
const StepHeader = props => {
  const {
    deadline,
    stepType,
    permissions,
    handleDeleteStep,
    isPhase4Enabled,
    hasHourEntries
  } = props;
  const { edit, delete: canDeleteStep, createHourEntry } = permissions;
  const {
    editMode,
    toggleEditMode,
    handleUpdateStep,
    handleCancelEdit,
    enableSave,
    newAssignee,
    setNewAssignee,
    newDeadlineDate,
    setNewDeadlineDate,
    showAssigneeValue,
    isComplete,
    createStepHourEntryLoading,
    handleCreateStepHourEntry,
    redirectToHours,
    disableToHoursButton,
    currentAssignee
  } = useComponentLogic(props);
  const { stepStatus, tagOptions, handleStatusChange } =
    useComponentLogic(props);

  const hoursPillClasses = classNames("!tw-border-2", {
    "tw-opacity-25 hover:!tw-cursor-default": disableToHoursButton
  });

  return (
    <div className="tw-flex tw-w-full tw-flex-col tw-gap-y-2.5">
      <div className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between">
        <div className="tw-flex tw-flex-col">
          <div className="tw-flex tw-gap-2.5 tw-text-sm tw-font-semibold">
            {stepType.name}
            {!editMode && (
              <span className="tw-text-sm tw-font-normal tw-text-black-70">
                ({showAssigneeValue})
              </span>
            )}
            {edit && !editMode && (
              <span
                className="tw-text-qc-blue-800 hover:tw-cursor-pointer"
                onClick={toggleEditMode}
              >
                <i className="fa-regular fa-pen" />
              </span>
            )}
          </div>
          {!editMode && (
            <div className="tw-text-xs tw-font-semibold">
              Deadline:
              <span className="tw-text-xs tw-font-normal"> {deadline}</span>
            </div>
          )}
        </div>
        <div className="tw-flex tw-flex-row tw-items-center tw-gap-2.5">
          {!editMode && (
            <>
              <div className="tw-flex tw-flex-row tw-items-baseline tw-justify-center tw-gap-5 tw-pr-5 tw-font-normal">
                <TagWithDropdown
                  status={stepStatus.tagStatus}
                  value={stepStatus}
                  options={tagOptions}
                  isBorderless={!edit}
                  isDisabled={!edit}
                  onChange={e => {
                    handleStatusChange(e?.value);
                  }}
                />
                {isPhase4Enabled && (
                  <TagWithDropdown
                    className={hoursPillClasses}
                    isTagOnly
                    onClick={() => redirectToHours()}
                    status="success"
                    isDisabled={disableToHoursButton}
                    text={hoursRedirectButton}
                  />
                )}
              </div>
              <div className="tw-flex tw-gap-2.5">
                <CustomTooltip
                  content="Mark complete"
                  dark="true"
                  position="top"
                  id="check-tooltip"
                >
                  <Button
                    onClick={() => handleStatusChange("complete")}
                    disabled={!edit || isComplete}
                  >
                    <i className="fa-solid fa-check" />
                  </Button>
                </CustomTooltip>

                {/* Will be implemented Phase 4 */}
                {/*
                  <Button customStyle="tw-bg-purple-700	">
                    <i className="fa-solid fa-reply-clock" />
                  </Button>
                */}
                <CustomTooltip
                  content="Delete step"
                  dark="true"
                  position="top"
                  id="delete-tooltip"
                >
                  <Button
                    bg="danger"
                    onClick={() => handleDeleteStep()}
                    disabled={!canDeleteStep}
                  >
                    <i className="fa-solid fa-trash" />
                  </Button>
                </CustomTooltip>
                {isPhase4Enabled && (
                  <CustomTooltip
                    content="Add new time entry"
                    dark="true"
                    position="top"
                    id="add-tooltip"
                  >
                    <Button
                      bg="success"
                      onClick={handleCreateStepHourEntry}
                      disabled={!createHourEntry || createStepHourEntryLoading}
                    >
                      <i className="fa-solid fa-plus" />
                    </Button>
                  </CustomTooltip>
                )}
              </div>
            </>
          )}
          {editMode && (
            <>
              <Button
                customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[35px] tw-rounded-[4px] tw-font-semibold tw-bg-qcNeutrals-700 hover:tw-border-qcNeutrals-700 hover:tw-bg-qcNeutrals-700"
                onClick={handleCancelEdit}
              >
                <i className="fa-light fa-xmark" />
                Cancel
              </Button>
              <Button
                customStyle="tw-flex tw-gap-2.5 tw-items-center tw-h-[35px] tw-rounded-[4px] tw-font-semibold tw-bg-qcGreen-700 hover:tw-border-qcGreen-700 hover:tw-bg-qcGreen-700"
                onClick={handleUpdateStep}
                disabled={!enableSave}
              >
                <i className="fa-light fa-check" />
                Save
              </Button>
            </>
          )}
        </div>
      </div>
      {edit && editMode && (
        <EditStepHeader
          newAssignee={newAssignee}
          newDeadlineDate={newDeadlineDate}
          handleAssigneeChange={setNewAssignee}
          handleDeadlineChange={setNewDeadlineDate}
          hasHourEntries={hasHourEntries}
          currentAssignee={currentAssignee}
        />
      )}
    </div>
  );
};

export default StepHeader;
