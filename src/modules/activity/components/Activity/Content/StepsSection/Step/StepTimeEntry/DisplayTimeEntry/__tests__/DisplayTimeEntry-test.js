import { create } from "react-test-renderer";
import DisplayTimeEntry from "..";

jest.mock("shared/components/Field", () => "Field");

const mockedProps = {
  dateOfEntry: "Fri Jan 17, 2025",
  formattedStartTime: "08:00:00 am",
  formattedEndTime: "10:00:00 am",
  formattedTotalTime: "2 hrs 0 mins 0 secs"
};

const render = props => create(<DisplayTimeEntry {...props} />);

describe("DisplayTimeEntry", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });
});
