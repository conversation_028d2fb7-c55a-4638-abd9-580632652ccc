// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StepTimeEntry it renders component correctly 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-row tw-items-center tw-gap-5 tw-border-t tw-border-gray-200 tw-bg-white tw-px-2.5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0"
>
  <i
    className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
  />
  <DisplayTimeEntry
    dateOfEntry="Fri Jan 17, 2025"
    formattedEndTime="06:00:00 pm"
    formattedStartTime="04:00:00 pm"
    formattedTotalTime="2 hrs 0 mins 0 secs"
  />
  <UpdatedTimerButtonGroup
    disableCancel={false}
    disableSave={false}
    handleCancelButton={[Function]}
    handleDeleteButton={[Function]}
    handleEditButton={[Function]}
    handleSaveButton={[Function]}
    isEditing={false}
    showTimer={false}
    userCanDelete={true}
    userCanEdit={true}
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this hour entry?"
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete hour entry?"
  />
  <StepTimeEntryModal
    body=""
    isOpen={false}
    onConfirm={[Function]}
    onEdit={[Function]}
    showConfirmButton={true}
    title=""
  />
</div>
`;

exports[`StepTimeEntry it renders component correctly with permissions set to false 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-row tw-items-center tw-gap-5 tw-border-t tw-border-gray-200 tw-bg-white tw-px-2.5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0"
>
  <i
    className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 "
  />
  <DisplayTimeEntry
    dateOfEntry="Fri Jan 17, 2025"
    formattedEndTime="06:00:00 pm"
    formattedStartTime="04:00:00 pm"
    formattedTotalTime="2 hrs 0 mins 0 secs"
  />
  <UpdatedTimerButtonGroup
    disableCancel={false}
    disableSave={false}
    handleCancelButton={[Function]}
    handleDeleteButton={[Function]}
    handleEditButton={[Function]}
    handleSaveButton={[Function]}
    isEditing={false}
    showTimer={false}
    userCanDelete={true}
    userCanEdit={true}
  />
  <DeleteModal
    body="Are you sure you want to permanently delete this hour entry?"
    isOpen={false}
    onCancel={[Function]}
    onConfirm={[Function]}
    title="Delete hour entry?"
  />
  <StepTimeEntryModal
    body=""
    isOpen={false}
    onConfirm={[Function]}
    onEdit={[Function]}
    showConfirmButton={true}
    title=""
  />
</div>
`;
