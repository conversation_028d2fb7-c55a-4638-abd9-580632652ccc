import { create } from "react-test-renderer";
import EditStepHeader from "..";

jest.mock("shared/components/Select", () => "Select");
jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));
jest.mock("utils/frequentDateValues", () => ({
  today: new Date("2024-11-20T00:00:00Z") // Mocked date
}));

const mockedProps = {
  newAssignee: { value: "1", label: "Russell Reas" },
  newDeadlineDate: "Sat Nov 30 2024 00:00:00 GMT-0800",
  handleAssigneeChange: jest.fn(),
  handleDeadlineChange: jest.fn()
};

describe("EditStepHeader", () => {
  const render = (props = mockedProps) => create(<EditStepHeader {...props} />);

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
