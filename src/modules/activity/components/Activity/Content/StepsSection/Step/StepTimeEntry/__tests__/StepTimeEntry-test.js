import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import StepTimeEntry from "..";

jest.mock("shared/components/Timer", () => "Timer");
jest.mock("shared/components/DeleteModal", () => "DeleteModal");
jest.mock("../StepTimeEntryModal", () => "StepTimeEntryModal");
jest.mock("../EditTimeEntryForm", () => "EditTimeEntryForm");
jest.mock("../DisplayTimeEntry", () => "DisplayTimeEntry");
jest.mock(
  "shared/components/Timer/UpdatedTimerButtonGroup",
  () => "UpdatedTimerButtonGroup"
);

const mockedProps = {
  entry: {
    id: "1",
    totalTime: {
      hours: 2,
      minutes: 120,
      seconds: 7200
    },
    startedAt: "2025-01-17 11:00:00 -0500",
    finishedAt: "2025-01-17 13:00:00 -0500",
    stepPermissions: {
      updateHourEntry: true
    }
  },
  stepId: "1"
};

function render(props) {
  return create(
    decoratedApollo({
      component: StepTimeEntry,
      props,
      initialValues: {},
      initialAppValues: {}
    })
  );
}

describe("StepTimeEntry", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with permissions set to false", () => {
    const component = render({
      ...mockedProps,
      permissions: {
        ...mockedProps.permissions,
        edit: false,
        delete: false
      }
    });

    expect(component).toMatchSnapshot();
  });
});
