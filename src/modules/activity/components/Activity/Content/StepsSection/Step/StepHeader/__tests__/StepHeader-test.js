import { create } from "react-test-renderer";
import StepHeader from "..";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";

const mockedProps = (editOrDelete = true) => ({
  stepId: "1",
  deadline: "2024-08-01T19:00:00-04:00",
  status: "Complete",
  permissions: {
    edit: editOrDelete,
    delete: editOrDelete,
    createHourEntry: true
  },
  stepType: {
    name: "Assign Initial Greenlighting Case"
  },
  assignee: {
    id: "1",
    fullName: "Russell Reas"
  },
  isPhase4Enabled: true
});

jest.mock("@q-centrix/q-components-react", () => ({
  TagWithDropdown: "TagWithDropdown",
  Button: "Button",
  CustomTooltip: "CustomTooltip",
  useToast: () => ({ toast: jest.fn() })
}));
jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => jest.fn()
}));

describe("StepHeader", () => {
  const render = edit =>
    create(
      decoratedApollo({
        component: StepHeader,
        props: mockedProps(edit),
        initialAppValues: {
          accountSettings: {
            id: "1"
          }
        },
        initialValues: {},
        apolloMocks: []
      })
    );

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with editOrDelete false", () => {
    const component = render(false);

    expect(component).toMatchSnapshot();
  });
});
