// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Step it renders component correctly without editOrDelete permissions 1`] = `
<CardWithHeader
  bodyClasses="tw-rounded-b-[5px]"
  cardClasses="tw-border tw-border-gray-800 tw-shadow-none tw-overflow-visible"
  headerClasses="!tw-bg-gray-200 tw-py-2 tw-px-2.5 !tw-rounded-t-[5px] tw-min-h-[60px] tw-border-b tw-border-b-gray-600"
  headerContent={
    <StepHeader
      activityId="1"
      assignee={
        Object {
          "__typename": "User",
          "fullName": "<PERSON> White",
          "id": "9",
        }
      }
      deadline="02/01/2025"
      handleDeleteStep={[Function]}
      hasHourEntries={true}
      hourGroupId="1"
      isPhase4Enabled={true}
      permissions={
        Object {
          "__typename": "StepPermissions",
          "delete": true,
          "edit": true,
        }
      }
      status="Open"
      stepId="1"
      stepType={
        Object {
          "__typename": "StepTypeDetails",
          "id": "23",
          "name": "Set up Client Meeting",
        }
      }
    />
  }
>
  <StepTimeEntry
    entry={
      Object {
        "finishedAt": "2025-01-17 13:00:00 -0500",
        "id": "1",
        "startedAt": "2025-01-17 11:00:00 -0500",
        "totalTime": Object {
          "hours": 2,
          "minutes": 120,
          "seconds": 7200,
        },
      }
    }
    stepId="1"
  />
</CardWithHeader>
`;
