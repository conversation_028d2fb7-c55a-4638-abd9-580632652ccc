// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EditStepHeader it renders component correctly 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-row tw-gap-x-10 tw-px-2.5 tw-text-sm tw-font-normal"
>
  <div
    className="tw-flex tw-flex-row tw-items-center tw-gap-x-2.5"
  >
    Assignee
    <Select
      fields={
        Array [
          "id",
          "fullName",
        ]
      }
      id="step-assignee"
      initialValue={
        Object {
          "label": "Russell Reas",
          "value": "1",
        }
      }
      inputClassName="tw-min-w-[175px] tw-max-w-[175px]"
      isPageable={true}
      isSearchable={true}
      name="step-assignee"
      onChange={[MockFunction]}
      path={
        Array [
          "activityUsers",
          "users",
        ]
      }
      placeholder="Select Assignee"
      query={
        Object {
          "definitions": Array [
            Object {
              "directives": Array [],
              "kind": "OperationDefinition",
              "name": Object {
                "kind": "Name",
                "value": "activityUsers",
              },
              "operation": "query",
              "selectionSet": Object {
                "kind": "SelectionSet",
                "selections": Array [
                  Object {
                    "alias": undefined,
                    "arguments": Array [
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "perPage",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "perPage",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "page",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "page",
                          },
                        },
                      },
                      Object {
                        "kind": "Argument",
                        "name": Object {
                          "kind": "Name",
                          "value": "search",
                        },
                        "value": Object {
                          "kind": "Variable",
                          "name": Object {
                            "kind": "Name",
                            "value": "search",
                          },
                        },
                      },
                    ],
                    "directives": Array [],
                    "kind": "Field",
                    "name": Object {
                      "kind": "Name",
                      "value": "activityUsers",
                    },
                    "selectionSet": Object {
                      "kind": "SelectionSet",
                      "selections": Array [
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "page",
                          },
                          "selectionSet": undefined,
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "count",
                          },
                          "selectionSet": undefined,
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "perPage",
                          },
                          "selectionSet": undefined,
                        },
                        Object {
                          "alias": undefined,
                          "arguments": Array [],
                          "directives": Array [],
                          "kind": "Field",
                          "name": Object {
                            "kind": "Name",
                            "value": "users",
                          },
                          "selectionSet": Object {
                            "kind": "SelectionSet",
                            "selections": Array [
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "firstName",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "lastName",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "id",
                                },
                                "selectionSet": undefined,
                              },
                              Object {
                                "alias": undefined,
                                "arguments": Array [],
                                "directives": Array [],
                                "kind": "Field",
                                "name": Object {
                                  "kind": "Name",
                                  "value": "fullName",
                                },
                                "selectionSet": undefined,
                              },
                            ],
                          },
                        },
                      ],
                    },
                  },
                ],
              },
              "variableDefinitions": Array [
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "perPage",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "Int",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "page",
                    },
                  },
                },
                Object {
                  "defaultValue": undefined,
                  "directives": Array [],
                  "kind": "VariableDefinition",
                  "type": Object {
                    "kind": "NamedType",
                    "name": Object {
                      "kind": "Name",
                      "value": "String",
                    },
                  },
                  "variable": Object {
                    "kind": "Variable",
                    "name": Object {
                      "kind": "Name",
                      "value": "search",
                    },
                  },
                },
              ],
            },
          ],
          "kind": "Document",
          "loc": Object {
            "end": 270,
            "start": 0,
          },
        }
      }
      variables={
        Object {
          "perPage": 25,
        }
      }
    />
  </div>
  <div
    className="tw-flex tw-flex-row tw-items-center tw-gap-x-2.5"
  >
    Deadline
    <DateInput
      id="deadlineDate"
      inputContainerClassName="tw-min-w-[175px] tw-max-w-[175px]"
      minDate={2024-11-20T00:00:00.000Z}
      name="deadlineDate"
      onChange={[MockFunction]}
      value="Sat Nov 30 2024 00:00:00 GMT-0800"
    />
  </div>
</div>
`;
