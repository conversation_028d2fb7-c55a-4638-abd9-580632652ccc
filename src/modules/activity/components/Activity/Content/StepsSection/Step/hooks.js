import { always, ifElse, pathOr, pipe } from "ramda";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";
import { format, parse } from "date-fns";
import { useMutation } from "@apollo/client";
import { DELETE_STEP } from "modules/activity/graphql/mutation";
import { useToast } from "@q-centrix/q-components-react";
import { GET_ACTIVITY_DETAILS } from "modules/activity/graphql/query";
import { useMemo } from "react";

const formatDateEntry = (value, valueFormat) =>
  ifElse(
    isNullOrEmpty,
    always(null),
    pipe(
      val => parse(val, valueFormat, new Date()),
      val => format(val, "MM/dd/yyyy")
    )
  )(value);

const getHourGroupId = pathOr(null, ["hourGroup", "id"]);
const hourEntriesExist = pipe(
  pathOr([], ["hourGroup", "hourEntries"]),
  isNotNullOrEmpty
);

export const useComponentLogic = props => {
  const { step } = props;
  const hourGroupId = getHourGroupId(step);
  const { toast } = useToast();
  const { id, deadlineDate, permissions } = step;
  const deadline = formatDateEntry(deadlineDate, "yyyy-MM-dd");
  const hasHourEntries = useMemo(() => hourEntriesExist(step), [step]);

  const [deleteStep] = useMutation(DELETE_STEP, {
    refetchQueries: [GET_ACTIVITY_DETAILS]
  });

  const handleDeleteStep = () => {
    deleteStep({
      variables: {
        stepId: Number(id)
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      }
    });
  };

  return {
    deadline,
    permissions,
    handleDeleteStep,
    hourGroupId,
    hasHourEntries
  };
};
