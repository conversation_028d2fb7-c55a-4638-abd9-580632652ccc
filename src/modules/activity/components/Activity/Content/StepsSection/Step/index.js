import { CardWithHeader } from "@q-centrix/q-components-react";
import StepHeader from "./StepHeader";
import { useComponentLogic } from "./hooks";
import StepTimeEntry from "./StepTimeEntry";

const Step = props => {
  const { step, activityId, isPhase4Enabled } = props;
  const { id, stepType, assignee, status, hourGroup } = step;
  const {
    deadline,
    permissions,
    handleDeleteStep,
    hourGroupId,
    hasHourEntries
  } = useComponentLogic(props);

  return (
    <CardWithHeader
      cardClasses="tw-border tw-border-gray-800 tw-shadow-none tw-overflow-visible"
      headerContent={
        <StepHeader
          deadline={deadline}
          stepType={stepType}
          status={status.name}
          permissions={permissions}
          assignee={assignee}
          stepId={id}
          activityId={activityId}
          handleDeleteStep={handleDeleteStep}
          hourGroupId={hourGroupId}
          isPhase4Enabled={isPhase4Enabled}
          hasHourEntries={hasHourEntries}
        />
      }
      headerClasses="!tw-bg-gray-200 tw-py-2 tw-px-2.5 !tw-rounded-t-[5px] tw-min-h-[60px] tw-border-b tw-border-b-gray-600"
      bodyClasses="tw-rounded-b-[5px]"
    >
      {isPhase4Enabled &&
        hourGroup?.hourEntries?.map(entry => (
          <StepTimeEntry entry={entry} key={`${id}-${entry.id}`} stepId={id} />
        ))}
    </CardWithHeader>
  );
};

export default Step;
