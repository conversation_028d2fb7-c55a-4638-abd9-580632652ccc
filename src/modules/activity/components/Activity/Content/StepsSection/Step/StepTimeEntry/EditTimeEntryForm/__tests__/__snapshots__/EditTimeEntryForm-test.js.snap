// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EditTimeEntryForm it renders component correctly 1`] = `
<div
  className="tw-flex tw-flex-row tw-gap-5"
>
  <Field
    label="Date of Entry"
  >
    <DateInput
      inputContainerClassName="tw-min-w-[110px]"
      onChange={[MockFunction]}
      value="Fri Jan 17 2025 08:00:00 GMT-0800 (Pacific Standard Time)"
    />
  </Field>
  <Field
    label="Start Time"
  >
    <TimeInput
      handleTime={[MockFunction]}
      handleTimePeriod={[MockFunction]}
      name="start"
      value={
        Object {
          "dateTime": "2025-01-17T16:00:00.000Z",
          "period": "am",
          "time": "08:00:00",
        }
      }
    />
  </Field>
  <Field
    label="End Time"
  >
    <TimeInput
      handleTime={[MockFunction]}
      handleTimePeriod={[MockFunction]}
      name="end"
      value={
        Object {
          "dateTime": "2025-01-17T18:00:00.000Z",
          "period": "am",
          "time": "10:00:00",
        }
      }
    />
  </Field>
</div>
`;
