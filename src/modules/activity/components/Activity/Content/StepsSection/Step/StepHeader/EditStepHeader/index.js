import { DateInput } from "@q-centrix/q-components-react";
import { USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT } from "shared/components/CaseReassignment/graphql/queries";
import Select from "shared/components/Select";
import { today } from "utils/frequentDateValues";

const EditStepHeader = props => {
  const {
    newAssignee,
    newDeadlineDate,
    handleAssigneeChange,
    handleDeadlineChange,
    hasHourEntries,
    currentAssignee
  } = props;

  return (
    <div className="tw-flex tw-w-full tw-flex-row tw-gap-x-10 tw-px-2.5 tw-text-sm tw-font-normal">
      <div className="tw-flex tw-flex-row tw-items-center tw-gap-x-2.5">
        Assignee
        <Select
          id="step-assignee"
          name="step-assignee"
          fields={["id", "fullName"]}
          initialValue={hasHourEntries ? currentAssignee : newAssignee}
          onChange={handleAssigneeChange}
          query={USERS_AVAILABLE_FOR_ACTIVITY_REASSIGNMENT}
          variables={{ perPage: 25 }}
          path={["activityUsers", "users"]}
          placeholder="Select Assignee"
          isPageable
          isSearchable
          inputClassName="tw-min-w-[175px] tw-max-w-[175px]"
          disabled={hasHourEntries}
        />
      </div>
      <div className="tw-flex tw-flex-row tw-items-center tw-gap-x-2.5">
        Deadline
        <DateInput
          id="deadlineDate"
          name="deadlineDate"
          value={newDeadlineDate}
          onChange={handleDeadlineChange}
          minDate={today}
          inputContainerClassName="tw-min-w-[175px] tw-max-w-[175px]"
        />
      </div>
    </div>
  );
};

export default EditStepHeader;
