// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Save Modal renders component 1`] = `
<ConfirmationModal
  appElement="#activity-details"
  isDanger={false}
  isOpen={true}
  title="Your time is greater than 8 hours"
>
  <div
    className="modal-content"
  >
    <p
      className="modal-paragraph"
    >
      You've added a time entry longer than 8 hours. Confirm your time is accurate or edit your end time.
    </p>
    <div
      className="button-container center-container"
    >
      <Button
        bg="neutral"
        customStyle="modal-buttons"
        onClick={[MockFunction]}
        outline={true}
      >
        <i
          className="fa-regular fa-x"
        />
        Edit
      </Button>
    </div>
  </div>
</ConfirmationModal>
`;
