import { useMutation } from "@apollo/client";
import {
  CREATE_STEP_HOUR_ENTRY,
  UPDATE_STEP,
  UPDATE_STEP_STATUS
} from "modules/activity/graphql/mutation";
import { useCallback, useState } from "react";
import { useToast } from "@q-centrix/q-components-react";
import {
  always,
  any,
  applySpec,
  equals,
  find,
  identity,
  ifElse,
  not,
  prop,
  propEq,
  propOr,
  reject
} from "ramda";
import { isNotNullOrEmpty, isNullOrEmpty } from "utils/fp";
import { GET_ACTIVITY_DETAILS } from "modules/activity/graphql/query";
import { format } from "date-fns";
import { useSelector } from "react-redux";
import accountSettingsSelectors from "modules/app/redux/selectors/accountSettings";
import { useNavigate } from "react-router-dom";
import { toHourDetails } from "utils/toHourDetails";

const formatDate = ifElse(isNullOrEmpty, always(null), val =>
  format(val, "MM/dd/yyyy")
);

const createStepHourEntryInput = applySpec({
  dateOfEntry: val => format(val, "yyyy-MM-dd"),
  startTime: identity,
  endTime: always(null)
});

const statusOptions = [
  { value: "complete", label: "Complete", tagStatus: "success" },
  { value: "blocked", label: "Blocked", tagStatus: "danger" },
  { value: "in_progress", label: "In Progress", tagStatus: "main" },
  { value: "open", label: "Open", tagStatus: "default" }
];

const tagOptions = reject(propEq("value", "complete"))(statusOptions);

const getStatusByLabel = labelValue =>
  find(propEq("label", labelValue), statusOptions);

const getAssigneeValue = (currentUserId, assignee) =>
  ifElse(
    propEq("id", currentUserId),
    always("Self"),
    prop("fullName")
  )(assignee);

const formatAssignee = applySpec({
  label: propOr("", "fullName"),
  value: propOr("", "id")
});

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { stepId, activityId, stepType, assignee, status, hourGroupId } = props;
  const currentAssignee = formatAssignee(assignee || {});
  const [stepStatus, setStepStatus] = useState(getStatusByLabel(status));
  const { toast } = useToast();
  const navigate = useNavigate();
  const loggedInUser = useSelector(accountSettingsSelectors.accountSettings);
  const [newAssignee, setNewAssignee] = useState(null);
  const [newDeadlineDate, setNewDeadlineDate] = useState();
  const [editMode, setEditMode] = useState(false);
  const toggleEditMode = () => setEditMode(not);
  const redirectToHours = () =>
    hourGroupId && toHourDetails(hourGroupId, navigate);
  const disableToHoursButton = isNullOrEmpty(hourGroupId);

  const [updateStep] = useMutation(UPDATE_STEP);
  const isComplete = equals("Complete", status);

  const handleUpdateStep = () => {
    updateStep({
      variables: {
        stepId: Number(stepId),
        stepInput: {
          ...(newAssignee && { assigneeId: Number(newAssignee?.value) }),
          ...(newDeadlineDate && {
            deadlineDate: formatDate(newDeadlineDate, "MM/dd/yyyy")
          }),
          activityId: Number(activityId),
          stepTypeId: Number(stepType.id)
        }
      },
      onCompleted: () => {
        setNewAssignee(null);
        setNewDeadlineDate(null);
        toggleEditMode();
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      refetchQueries: [GET_ACTIVITY_DETAILS]
    });
  };

  const [createStepHourEntry, { loading: createStepHourEntryLoading }] =
    useMutation(CREATE_STEP_HOUR_ENTRY, {
      refetchQueries: [GET_ACTIVITY_DETAILS]
    });

  const handleCreateStepHourEntry = useCallback(() => {
    createStepHourEntry({
      variables: {
        stepId,
        hourEntryInput: createStepHourEntryInput(new Date())
      },
      onCompleted: ({ createStepHourEntry: createStepHourEntryResult }) => {
        if (createStepHourEntryResult?.errors) {
          createStepHourEntryResult.errors?.fullMessages?.forEach(message => {
            toast({
              variant: "error",
              description: message
            });
          });
        }
      },
      onError: error => {
        toast({
          variant: "error",
          description: error.message
        });
      },
      update: cache => {
        cache.modify({
          fields: {
            hourGroupList: (_value, { DELETE }) => DELETE
          }
        });
      }
    });
  }, [stepId, hourGroupId]);

  const handleCancelEdit = () => {
    toggleEditMode();
    setNewAssignee(null);
    setNewDeadlineDate(null);
  };

  const enableSave = any(isNotNullOrEmpty, [newAssignee, newDeadlineDate]);

  const showAssigneeValue = getAssigneeValue(loggedInUser?.id, assignee);

  const [updateStepStatus] = useMutation(UPDATE_STEP_STATUS);

  const handleStatusChange = useCallback(
    newStatus => {
      updateStepStatus({
        variables: {
          stepId: Number(stepId),
          status: newStatus
        },
        onCompleted: ({ updateStepStatus: { errors, step } }) => {
          if (errors) {
            errors?.messages?.forEach(message => {
              toast({
                variant: "error",
                description: message
              });
            });
          } else {
            setStepStatus(getStatusByLabel(step.status.name));
          }
        },
        onError: error => {
          toast({
            variant: "error",
            description: error.message
          });
        },
        refetchQueries: [GET_ACTIVITY_DETAILS]
      });
    },
    [stepId]
  );

  return {
    editMode,
    toggleEditMode,
    handleUpdateStep,
    handleCancelEdit,
    enableSave,
    newAssignee,
    setNewAssignee,
    newDeadlineDate,
    setNewDeadlineDate,
    showAssigneeValue,
    stepStatus,
    tagOptions,
    handleStatusChange,
    isComplete,
    createStepHourEntryLoading,
    handleCreateStepHourEntry,
    redirectToHours,
    disableToHoursButton,
    currentAssignee
  };
};
