import { create } from "react-test-renderer";
import StepTimeEntryModal from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

describe("Save Modal", () => {
  const render = props => create(<StepTimeEntryModal {...props} />);

  test("renders component", () => {
    const component = render({
      title: "Your time is greater than 8 hours",
      body: "You've added a time entry longer than 8 hours. Confirm your time is accurate or edit your end time.",
      isOpen: true,
      onEdit: jest.fn(),
      onConfirm: jest.fn(),
      showConfirmButton: false
    });

    expect(component).toMatchSnapshot();
  });
});
