import { create } from "react-test-renderer";
import EditTimeEntryForm from "..";

jest.mock("shared/components/Field", () => "Field");
jest.mock("shared/components/TimeInput", () => "TimeInput");
jest.mock("@q-centrix/q-components-react", () => ({
  DateInput: "DateInput"
}));

const mockedProps = {
  date: "Fri Jan 17 2025 08:00:00 GMT-0800 (Pacific Standard Time)",
  handleDate: jest.fn(),
  times: {
    start: {
      time: "08:00:00",
      period: "am",
      dateTime: "2025-01-17T16:00:00.000Z"
    },
    end: {
      time: "10:00:00",
      period: "am",
      dateTime: "2025-01-17T18:00:00.000Z"
    }
  },
  handleTime: jest.fn(),
  handleTimePeriod: jest.fn()
};

const render = props => create(<EditTimeEntryForm {...props} />);

describe("EditTimeEntryForm", () => {
  test("it renders component correctly", () => {
    const component = render(mockedProps);

    expect(component).toMatchSnapshot();
  });
});
