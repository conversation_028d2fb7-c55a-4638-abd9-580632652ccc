import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import Step from "..";

const mockedProps = {
  activityId: "1",
  isPhase4Enabled: true,
  step: {
    id: "1",
    assignee: {
      __typename: "User",
      id: "9",
      fullName: "Julia White"
    },
    stepType: {
      __typename: "StepTypeDetails",
      id: "23",
      name: "Set up Client Meeting"
    },
    deadlineDate: "2025-02-01",
    status: {
      __typename: "ActivityStatus",
      key: "open",
      name: "Open"
    },
    createdAt: "2025-01-17 19:40:04 -0500",
    permissions: {
      __typename: "StepPermissions",
      edit: true,
      delete: true
    },
    hourGroup: {
      __typename: "Step",
      id: "1",
      hourEntries: [
        {
          id: "1",
          totalTime: {
            hours: 2,
            minutes: 120,
            seconds: 7200
          },
          startedAt: "2025-01-17 11:00:00 -0500",
          finishedAt: "2025-01-17 13:00:00 -0500"
        }
      ]
    }
  }
};

jest.mock("../StepHeader", () => "StepHeader");
jest.mock("../StepTimeEntry", () => "StepTimeEntry");
jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  useToast: () => ({ toast: jest.fn() })
}));

describe("Step", () => {
  function render(props = mockedProps) {
    return create(
      decoratedApollo({
        component: Step,
        props,
        initialAppValues: {},
        initialValues: {},
        apolloMocks: []
      })
    );
  }

  test("it renders component correctly without editOrDelete permissions", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
