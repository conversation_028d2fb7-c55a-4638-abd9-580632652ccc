// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DisplayTimeEntry it renders component correctly 1`] = `
<div
  className="tw-grid tw-w-full lg:tw-grid-cols-2 lg:tw-grid-rows-2 xl:tw-grid-cols-4 xl:tw-grid-rows-1"
>
  <Field
    className="tw-order-1 lg:tw-col-start-1 lg:tw-row-start-1 xl:tw-col-start-1 xl:tw-row-start-1"
    label="Date of Entry"
  >
    <p
      className="tw-font-normal"
    >
      Fri Jan 17, 2025
    </p>
  </Field>
  <div
    className="tw-flex tw-flex-col tw-gap-2 tw-justify-self-start lg:tw-grow lg:!tw-flex-row lg:!tw-justify-evenly"
  >
    <Field
      className="tw-order-2 lg:tw-col-start-2 lg:tw-row-start-1 xl:tw-col-start-2 xl:tw-row-start-1"
      label="Start Time"
    >
      <p
        className="tw-font-normal"
      >
        08:00:00 am
      </p>
    </Field>
  </div>
  <div
    className="tw-flex tw-flex-col tw-gap-2 tw-justify-self-start lg:tw-grow lg:!tw-flex-row lg:!tw-justify-evenly"
  >
    <Field
      className="tw-order-3 lg:tw-col-start-2 lg:tw-row-start-2 xl:tw-col-start-3 xl:tw-row-start-1"
      label="End Time"
    >
      <p
        className="tw-font-normal"
      >
        10:00:00 am
      </p>
    </Field>
  </div>
  <Field
    className="tw-order-4 lg:tw-col-start-1 lg:tw-row-start-2 xl:tw-col-start-4 xl:tw-row-start-1"
    label="Total Time"
  >
    <p
      className="tw-font-normal"
    >
      2 hrs 0 mins 0 secs
    </p>
  </Field>
</div>
`;
