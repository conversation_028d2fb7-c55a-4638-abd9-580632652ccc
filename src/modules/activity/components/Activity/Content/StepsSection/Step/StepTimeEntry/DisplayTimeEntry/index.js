import Field from "shared/components/Field";

const DisplayTimeEntry = ({
  dateOfEntry,
  formattedStartTime,
  formattedEndTime,
  formattedTotalTime
}) => (
  <div className="tw-grid tw-w-full lg:tw-grid-cols-2 lg:tw-grid-rows-2 xl:tw-grid-cols-4 xl:tw-grid-rows-1">
    <Field
      label="Date of Entry"
      className="tw-order-1 lg:tw-col-start-1 lg:tw-row-start-1 xl:tw-col-start-1 xl:tw-row-start-1"
    >
      <p className="tw-font-normal">{dateOfEntry}</p>
    </Field>
    <div className="tw-flex tw-flex-col tw-gap-2 tw-justify-self-start lg:tw-grow lg:!tw-flex-row lg:!tw-justify-evenly">
      <Field
        label="Start Time"
        className="tw-order-2 lg:tw-col-start-2 lg:tw-row-start-1 xl:tw-col-start-2 xl:tw-row-start-1"
      >
        <p className="tw-font-normal">{formattedStartTime}</p>
      </Field>
    </div>
    <div className="tw-flex tw-flex-col tw-gap-2 tw-justify-self-start lg:tw-grow lg:!tw-flex-row lg:!tw-justify-evenly">
      <Field
        label="End Time"
        className="tw-order-3 lg:tw-col-start-2 lg:tw-row-start-2 xl:tw-col-start-3 xl:tw-row-start-1"
      >
        <p className="tw-font-normal">{formattedEndTime}</p>
      </Field>
    </div>
    <Field
      label="Total Time"
      className="tw-order-4 lg:tw-col-start-1 lg:tw-row-start-2 xl:tw-col-start-4 xl:tw-row-start-1"
    >
      <p className="tw-font-normal">{formattedTotalTime}</p>
    </Field>
  </div>
);

export default DisplayTimeEntry;
