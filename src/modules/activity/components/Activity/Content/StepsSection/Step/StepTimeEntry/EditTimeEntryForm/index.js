import Field from "shared/components/Field";
import { DateInput } from "@q-centrix/q-components-react";
import TimeInput from "shared/components/TimeInput";

const EditTimeEntryForm = ({
  date,
  handleDate,
  times,
  handleTime,
  handleTimePeriod
}) => (
  <div className="tw-flex tw-flex-row tw-gap-5">
    <Field label="Date of Entry">
      <DateInput
        value={date}
        onChange={handleDate}
        inputContainerClassName="tw-min-w-[110px]"
      />
    </Field>
    <Field label="Start Time">
      <TimeInput
        name="start"
        value={times.start}
        handleTime={handleTime}
        handleTimePeriod={handleTimePeriod}
      />
    </Field>
    <Field label="End Time">
      <TimeInput
        name="end"
        value={times.end}
        handleTime={handleTime}
        handleTimePeriod={handleTimePeriod}
      />
    </Field>
  </div>
);

export default EditTimeEntryForm;
