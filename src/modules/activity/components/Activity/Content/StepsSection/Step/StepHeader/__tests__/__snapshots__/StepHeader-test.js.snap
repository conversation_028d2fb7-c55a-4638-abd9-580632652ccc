// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StepHeader it renders component correctly 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-gap-y-2.5"
>
  <div
    className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between"
  >
    <div
      className="tw-flex tw-flex-col"
    >
      <div
        className="tw-flex tw-gap-2.5 tw-text-sm tw-font-semibold"
      >
        Assign Initial Greenlighting Case
        <span
          className="tw-text-sm tw-font-normal tw-text-black-70"
        >
          (
          Self
          )
        </span>
        <span
          className="tw-text-qc-blue-800 hover:tw-cursor-pointer"
          onClick={[Function]}
        >
          <i
            className="fa-regular fa-pen"
          />
        </span>
      </div>
      <div
        className="tw-text-xs tw-font-semibold"
      >
        Deadline:
        <span
          className="tw-text-xs tw-font-normal"
        >
           
          2024-08-01T19:00:00-04:00
        </span>
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-items-center tw-gap-2.5"
    >
      <div
        className="tw-flex tw-flex-row tw-items-baseline tw-justify-center tw-gap-5 tw-pr-5 tw-font-normal"
      >
        <TagWithDropdown
          isBorderless={false}
          isDisabled={false}
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Blocked",
                "tagStatus": "danger",
                "value": "blocked",
              },
              Object {
                "label": "In Progress",
                "tagStatus": "main",
                "value": "in_progress",
              },
              Object {
                "label": "Open",
                "tagStatus": "default",
                "value": "open",
              },
            ]
          }
          status="success"
          value={
            Object {
              "label": "Complete",
              "tagStatus": "success",
              "value": "complete",
            }
          }
        />
        <TagWithDropdown
          className="!tw-border-2 tw-opacity-25 hover:!tw-cursor-default"
          isDisabled={true}
          isTagOnly={true}
          onClick={[Function]}
          status="success"
          text={
            <div
              className="tw-flex tw-min-w-[79px] tw-flex-row tw-items-center tw-justify-center tw-gap-2.5"
            >
              <p>
                Hours Added
              </p>
              <i
                className="fa fas fa-chevron-right tw-text-sm"
              />
            </div>
          }
        />
      </div>
      <div
        className="tw-flex tw-gap-2.5"
      >
        <CustomTooltip
          content="Mark complete"
          dark="true"
          id="check-tooltip"
          position="top"
        >
          <Button
            disabled={true}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-check"
            />
          </Button>
        </CustomTooltip>
        <CustomTooltip
          content="Delete step"
          dark="true"
          id="delete-tooltip"
          position="top"
        >
          <Button
            bg="danger"
            disabled={false}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-trash"
            />
          </Button>
        </CustomTooltip>
        <CustomTooltip
          content="Add new time entry"
          dark="true"
          id="add-tooltip"
          position="top"
        >
          <Button
            bg="success"
            disabled={false}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-plus"
            />
          </Button>
        </CustomTooltip>
      </div>
    </div>
  </div>
</div>
`;

exports[`StepHeader it renders component correctly with editOrDelete false 1`] = `
<div
  className="tw-flex tw-w-full tw-flex-col tw-gap-y-2.5"
>
  <div
    className="tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-between"
  >
    <div
      className="tw-flex tw-flex-col"
    >
      <div
        className="tw-flex tw-gap-2.5 tw-text-sm tw-font-semibold"
      >
        Assign Initial Greenlighting Case
        <span
          className="tw-text-sm tw-font-normal tw-text-black-70"
        >
          (
          Self
          )
        </span>
      </div>
      <div
        className="tw-text-xs tw-font-semibold"
      >
        Deadline:
        <span
          className="tw-text-xs tw-font-normal"
        >
           
          2024-08-01T19:00:00-04:00
        </span>
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-items-center tw-gap-2.5"
    >
      <div
        className="tw-flex tw-flex-row tw-items-baseline tw-justify-center tw-gap-5 tw-pr-5 tw-font-normal"
      >
        <TagWithDropdown
          isBorderless={true}
          isDisabled={true}
          onChange={[Function]}
          options={
            Array [
              Object {
                "label": "Blocked",
                "tagStatus": "danger",
                "value": "blocked",
              },
              Object {
                "label": "In Progress",
                "tagStatus": "main",
                "value": "in_progress",
              },
              Object {
                "label": "Open",
                "tagStatus": "default",
                "value": "open",
              },
            ]
          }
          status="success"
          value={
            Object {
              "label": "Complete",
              "tagStatus": "success",
              "value": "complete",
            }
          }
        />
        <TagWithDropdown
          className="!tw-border-2 tw-opacity-25 hover:!tw-cursor-default"
          isDisabled={true}
          isTagOnly={true}
          onClick={[Function]}
          status="success"
          text={
            <div
              className="tw-flex tw-min-w-[79px] tw-flex-row tw-items-center tw-justify-center tw-gap-2.5"
            >
              <p>
                Hours Added
              </p>
              <i
                className="fa fas fa-chevron-right tw-text-sm"
              />
            </div>
          }
        />
      </div>
      <div
        className="tw-flex tw-gap-2.5"
      >
        <CustomTooltip
          content="Mark complete"
          dark="true"
          id="check-tooltip"
          position="top"
        >
          <Button
            disabled={true}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-check"
            />
          </Button>
        </CustomTooltip>
        <CustomTooltip
          content="Delete step"
          dark="true"
          id="delete-tooltip"
          position="top"
        >
          <Button
            bg="danger"
            disabled={true}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-trash"
            />
          </Button>
        </CustomTooltip>
        <CustomTooltip
          content="Add new time entry"
          dark="true"
          id="add-tooltip"
          position="top"
        >
          <Button
            bg="success"
            disabled={false}
            onClick={[Function]}
          >
            <i
              className="fa-solid fa-plus"
            />
          </Button>
        </CustomTooltip>
      </div>
    </div>
  </div>
</div>
`;
