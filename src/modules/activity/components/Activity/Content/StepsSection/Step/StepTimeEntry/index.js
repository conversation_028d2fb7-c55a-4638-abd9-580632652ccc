import { useComponentLogic } from "./hooks";
import Timer from "shared/components/Timer";
import UpdatedTimerButtonGroup from "shared/components/Timer/UpdatedTimerButtonGroup";
import DeleteModal from "shared/components/DeleteModal";
import StepTimeEntryModal from "./StepTimeEntryModal";
import EditTimeEntryForm from "./EditTimeEntryForm";
import DisplayTimeEntry from "./DisplayTimeEntry";

// eslint-disable-next-line complexity
const StepTimeEntry = props => {
  const {
    formattedStartTime,
    formattedEndTime,
    dateOfEntry,
    date,
    handleDate,
    handleEditButton,
    handleSaveButton,
    handleCancelButton,
    handleDeleteButton,
    deleteModalIsOpen,
    toggleDeleteModal,
    hourEntryModalContent,
    hourEntryWarningModalIsOpen,
    showConfirmButton,
    handleEditHourEntry,
    handleConfirmHourEntry,
    handleDeleteHourEntry,
    isEditing,
    formattedTotalTime,
    times,
    handleTime,
    handleTimePeriod,
    isRunning,
    setIsRunning,
    timerStartTime,
    elapsedTime,
    setElapsedTime,
    showTimer,
    setShowTimer,
    handleTimerClick,
    handleStartTimer,
    saveIsDisabled,
    cancelIsDisabled,
    canUpdateHourEntry
  } = useComponentLogic(props);

  return (
    <div className="tw-flex tw-w-full tw-flex-row tw-items-center tw-gap-5 tw-border-t tw-border-gray-200 tw-bg-white tw-px-2.5 tw-py-2.5 tw-text-sm tw-text-black-70 last:tw-border-b-0">
      <i className="fa-regular fa-arrow-turn-down-right  tw-text-black-29 " />
      {isEditing ? (
        <EditTimeEntryForm
          date={date}
          handleDate={handleDate}
          times={times}
          handleTime={handleTime}
          handleTimePeriod={handleTimePeriod}
        />
      ) : (
        <DisplayTimeEntry
          dateOfEntry={dateOfEntry}
          formattedStartTime={formattedStartTime}
          formattedEndTime={formattedEndTime}
          formattedTotalTime={formattedTotalTime}
        />
      )}
      {showTimer && (
        <Timer
          isRunning={isRunning}
          setIsRunning={setIsRunning}
          startTime={timerStartTime}
          elapsedTime={elapsedTime}
          setElapsedTime={setElapsedTime}
          setShowTimer={setShowTimer}
          handleTimerClick={handleTimerClick}
          handleStartTimer={handleStartTimer}
        />
      )}
      <UpdatedTimerButtonGroup
        showTimer={showTimer}
        userCanEdit={canUpdateHourEntry}
        userCanDelete={canUpdateHourEntry}
        isEditing={isEditing}
        handleEditButton={handleEditButton}
        handleSaveButton={handleSaveButton}
        disableSave={saveIsDisabled}
        disableCancel={cancelIsDisabled}
        handleDeleteButton={handleDeleteButton}
        handleCancelButton={handleCancelButton}
      />
      <DeleteModal
        title="Delete hour entry?"
        body="Are you sure you want to permanently delete this hour entry?"
        isOpen={deleteModalIsOpen}
        onCancel={toggleDeleteModal}
        onConfirm={handleDeleteHourEntry}
      />
      <StepTimeEntryModal
        title={hourEntryModalContent.title}
        body={hourEntryModalContent.body}
        isOpen={hourEntryWarningModalIsOpen}
        showConfirmButton={showConfirmButton}
        onEdit={handleEditHourEntry}
        onConfirm={handleConfirmHourEntry}
      />
    </div>
  );
};

export default StepTimeEntry;
