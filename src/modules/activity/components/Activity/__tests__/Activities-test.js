import Activity from "modules/activity/components/Activity";
import { create } from "react-test-renderer";

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => jest.fn()
}));
jest.mock("../Content", () => "Content");
jest.mock("shared/components/Layout", () => "Layout");

describe("Activity", () => {
  const render = () => create(<Activity />);

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
