// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Activity it renders component correctly 1`] = `
<Layout
  onBackArrowClick={[Function]}
  stChildren={
    <SecondaryToolbarContent
      activityReassignmentDisabled={false}
      onCreateNew={[Function]}
      onDeleteActivity={[Function]}
      onReassignActivity={[Function]}
    />
  }
  stChildrenClassName="tw-gap-5"
  tbChildren={
    <TopBarContent
      title="Activity Details"
    />
  }
  tbLabel="Activity Details"
>
  <Content
    deleteModalIsOpen={false}
    shouldShowActivityReassignment={false}
    toggleActivityReassignment={[Function]}
    toggleDeleteModal={[Function]}
  />
</Layout>
`;
