import Layout from "shared/components/Layout";
import { useComponentLogic } from "./hooks";
import Content from "./Content";
import TopBarContent from "modules/cases/components/CaseDetails/TopBarContent";
import SecondaryToolbarContent from "./SecondaryToolbarContent";
import { ApolloProvider } from "@apollo/client";
import { apolloClient } from "../../../../base";

const client = apolloClient("/qapps/graphql");
const Activity = () => {
  const {
    handleBackArrowClick,
    handleCreateNew,
    deleteModalIsOpen,
    toggleDeleteModal,
    shouldShowActivityReassignment,
    toggleActivityReassignment
  } = useComponentLogic();

  const tbChildren = <TopBarContent title="Activity Details" />;

  const stChildren = (
    <SecondaryToolbarContent
      onCreateNew={handleCreateNew}
      onDeleteActivity={toggleDeleteModal}
      activityReassignmentDisabled={shouldShowActivityReassignment}
      onReassignActivity={toggleActivityReassignment}
    />
  );

  return (
    <ApolloProvider client={client}>
      <Layout
        tbLabel="Activity Details"
        tbChildren={tbChildren}
        stChildren={stChildren}
        stChildrenClassName="tw-gap-5"
        onBackArrowClick={handleBackArrowClick}
      >
        <Content
          toggleDeleteModal={toggleDeleteModal}
          deleteModalIsOpen={deleteModalIsOpen}
          shouldShowActivityReassignment={shouldShowActivityReassignment}
          toggleActivityReassignment={toggleActivityReassignment}
        />
      </Layout>
    </ApolloProvider>
  );
};

export default Activity;
