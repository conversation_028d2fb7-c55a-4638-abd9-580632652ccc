import { useState } from "react";
import { not } from "ramda";
import { useNavigate } from "react-router-dom";

export const useComponentLogic = () => {
  const navigate = useNavigate();
  const [shouldShowActivityReassignment, setShouldShowActivityReassignment] =
    useState(false);
  const toggleActivityReassignment = () =>
    setShouldShowActivityReassignment(not);
  const handleBackArrowClick = () => navigate("/cases");
  const [deleteModalIsOpen, setDeleteModalIsOpen] = useState(false);
  const toggleDeleteModal = () => setDeleteModalIsOpen(not);

  const handleCreateNew = () => navigate("/activities/new");

  return {
    handleBackArrowClick,
    handleCreateNew,
    deleteModalIsOpen,
    toggleDeleteModal,
    shouldShowActivityReassignment,
    toggleActivityReassignment
  };
};
