import * as yup from "yup";

const formSchema = yup
  .object({
    facilityId: yup.object().notRequired(),
    activityType: yup
      .object({
        value: yup.string().required("Activity Type is required")
      })
      .required("Activity Type is required"),
    activityOwner: yup
      .object({
        value: yup.string().required("Activity Owner is required")
      })
      .required("Activity Owner is required"),
    activityAssignee: yup
      .object({
        value: yup.string().required("Activity Assignee is required")
      })
      .required("Activity Assignee is required"),
    deadlineDate: yup.date().required("Deadline is required")
  })
  .required();

export default formSchema;
