import { useQuery } from "@apollo/client";
import { GET_ACTIVITY_PERMISSIONS } from "modules/activity/graphql/query";
import { any, equals } from "ramda";
import { useMemo } from "react";
import { useParams } from "react-router-dom";

export const useComponentLogic = () => {
  const { id } = useParams();
  const {
    data: { activityPermissions: { authorized: canCreate } } = {
      activityPermissions: {
        authorized: false
      }
    },
    loading: createPermissionLoading
  } = useQuery(GET_ACTIVITY_PERMISSIONS, {
    variables: {
      action: "create"
    }
  });

  const {
    data: { activityPermissions: { authorized: canDelete } } = {
      activityPermissions: {
        authorized: false
      }
    },
    loading: deletePermissionLoading
  } = useQuery(GET_ACTIVITY_PERMISSIONS, {
    variables: {
      action: "delete",
      activityId: Number(id)
    }
  });

  const {
    data: { activityPermissions: { authorized: canReassign } } = {
      activityPermissions: {
        authorized: false,
        activityId: Number(id)
      }
    },
    loading: reassignPermissionLoading
  } = useQuery(GET_ACTIVITY_PERMISSIONS, {
    variables: {
      action: "reassign",
      activityId: Number(id)
    }
  });

  const permissionsLoading = useMemo(
    () =>
      any(equals(true))([
        createPermissionLoading,
        deletePermissionLoading,
        reassignPermissionLoading
      ]),
    [
      createPermissionLoading,
      deletePermissionLoading,
      reassignPermissionLoading
    ]
  );

  return {
    permissionsLoading,
    canCreate,
    canDelete,
    canReassign
  };
};
