import { act, create } from "react-test-renderer";
import SecondaryToolbarContent from "..";
import { decoratedApollo } from "utils/tests/decorated";
import wait from "waait";
import { InMemoryCache } from "@apollo/client";
import activityDetailsMocks from "modules/activity/graphql/mocks";
import { useParams } from "react-router-dom";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));

jest.mock("react-router-dom", () => {
  const mockUseParams = jest.fn();

  return {
    useParams: mockUseParams
  };
});

const cache = new InMemoryCache({});

describe("SecondaryToolbarContent", () => {
  beforeEach(() => {
    useParams.mockReset();
  });
  const render = (apolloMocks = activityDetailsMocks) =>
    create(
      decoratedApollo({
        component: SecondaryToolbarContent,
        props: {},
        initialAppValues: {},
        initialValues: {},
        apolloMocks,
        apolloCache: cache
      })
    );

  test("it renders component with Create New, Delete Activity, and Reassign Activity buttons", async () => {
    useParams.mockReturnValue({ id: 1 });
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component without any action buttons", async () => {
    useParams.mockReturnValue({ id: 2 });
    const component = () =>
      create(
        decoratedApollo({
          component: SecondaryToolbarContent,
          props: {},
          initialAppValues: {},
          initialValues: {},
          apolloMocks: activityDetailsMocks
        })
      );

    await act(() => wait(100));

    expect(component()).toMatchSnapshot();
  });
});
