// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SecondaryToolbarContent it renders component with Create New, Delete Activity, and Reassign Activity buttons 1`] = `
Array [
  <Button
    bg="danger"
    customStyle="tw-flex tw-gap-2.5 tw-items-center"
    outline={true}
  >
    <i
      className="fa-regular fa-trash"
    />
    Delete Activity
  </Button>,
  <Button
    bg="main"
    customStyle="tw-flex tw-gap-2.5 tw-items-center"
    outline={true}
  >
    <i
      className="fa-regular fa-arrow-right-arrow-left"
    />
    Reassign Activity
  </Button>,
  <Button
    customStyle="tw-flex tw-gap-2.5 tw-items-center"
  >
    <i
      className="fa-regular fa-plus"
    />
    Create New
  </Button>,
]
`;

exports[`SecondaryToolbarContent it renders component without any action buttons 1`] = `null`;
