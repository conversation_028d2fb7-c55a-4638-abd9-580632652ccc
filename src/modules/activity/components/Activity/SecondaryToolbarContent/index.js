import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

// Uncomment Reassign props and button once mutation is connected

// eslint-disable-next-line complexity
const SecondaryToolbarContent = ({
  onCreateNew,
  onDeleteActivity,
  onReassignActivity,
  activityReassignmentDisabled
}) => {
  // eslint-disable-next-line no-unused-vars
  const { permissionsLoading, canCreate, canDelete, canReassign } =
    useComponentLogic();

  if (permissionsLoading) return null;

  return (
    <>
      {canDelete && (
        <Button
          outline
          bg="danger"
          customStyle="tw-flex tw-gap-2.5 tw-items-center"
          onClick={onDeleteActivity}
        >
          <i className="fa-regular fa-trash" />
          Delete Activity
        </Button>
      )}
      {canReassign && (
        <Button
          outline
          bg="main"
          customStyle="tw-flex tw-gap-2.5 tw-items-center"
          onClick={onReassignActivity}
          disabled={activityReassignmentDisabled}
        >
          <i className="fa-regular fa-arrow-right-arrow-left" />
          Reassign Activity
        </Button>
      )}
      {canCreate && (
        <Button
          customStyle="tw-flex tw-gap-2.5 tw-items-center"
          onClick={onCreateNew}
        >
          <i className="fa-regular fa-plus" />
          Create New
        </Button>
      )}
    </>
  );
};

export default SecondaryToolbarContent;
