import { useSelector } from "react-redux";
import caseReview from "modules/case-review/redux/selectors";
import { useCallback } from "react";
import { useNavigate } from "react-router-dom";

export const useComponentLogic = () => {
  const caseId = useSelector(state => caseReview.getCaseId(state));
  const reviewType = useSelector(state => caseReview.getReviewType(state));
  const navigate = useNavigate();

  const backButtonClick = useCallback(
    e => {
      if (e) e.preventDefault();

      localStorage.removeItem("persist:caseReview");
      navigate(caseId ? `/cases/${caseId}` : "/cases");
    },
    [caseId, navigate]
  );

  return {
    backButtonClick,
    reviewType
  };
};
