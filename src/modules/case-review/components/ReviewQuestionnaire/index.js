import { ApolloProvider } from "@apollo/client";
import { apolloClient } from "../../../../base";
import Content from "./Content";
import Layout from "shared/components/Layout";
import "styles/partial-review.scss";
import { useComponentLogic } from "./hooks";
import Hours from "shared/widgets/Hours";
import LastLogInTile from "shared/widgets/LastLogInTile";

const client = apolloClient("/qapps/graphql");

const ReviewQuestionnaire = () => {
  const { reviewType, backButtonClick } = useComponentLogic();

  const tbChildren = (
    <div className="tw-flex tw-w-full tw-items-center tw-justify-between">
      <h1 className="tw-text-2xl tw-font-semibold">{reviewType} Review</h1>
      <div className="tw-flex tw-items-center tw-gap-5">
        {/* <QPoints />
        <Earnings /> */}
        <Hours />
        <LastLogInTile />
      </div>
    </div>
  );

  return (
    <ApolloProvider client={client}>
      <Layout
        tbOnClick={backButtonClick}
        tbChildren={tbChildren}
        client={client}
        onBackArrowClick={backButtonClick}
      >
        <Content />
      </Layout>
    </ApolloProvider>
  );
};

export default ReviewQuestionnaire;
