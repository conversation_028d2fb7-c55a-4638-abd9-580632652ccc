import { Spinner } from "@q-centrix/q-components-react";
import Header from "./Header";
import General from "./General";
import MismatchedElements from "./MismatchedElements";
import ElementsReviewed from "./ElementsReviewed";
import { useComponentLogic } from "./hooks";
import "styles/partial-review.scss";
import CommentsSection from "./CommentsSection";

const Content = () => {
  const { reviewId, error, loading, mutationLoading, loggedInUser } =
    useComponentLogic();

  if (error) return <p className="error">Error: No data.</p>;

  if (loading) return <Spinner />;

  return (
    <div className="tw-flex tw-h-full">
      <div className="tw-basis-3/4">
        <div className="partial-review-container">
          <div className="partial-review-header">
            <Header />
          </div>
          <div className="partial-review-main">
            <General />
            <MismatchedElements mutationLoading={mutationLoading} />
            <ElementsReviewed mutationLoading={mutationLoading} />
          </div>
        </div>
      </div>
      <div className="tw-basis-1/4 tw-border-l tw-border-gray-200 tw-bg-white">
        <CommentsSection reviewId={reviewId} loggedInUser={loggedInUser} />
      </div>
    </div>
  );
};

export default Content;
