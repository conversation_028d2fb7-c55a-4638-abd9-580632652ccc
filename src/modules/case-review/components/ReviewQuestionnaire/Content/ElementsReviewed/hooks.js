import { useCallback, useMemo, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  updateElementsReviewed,
  addError
} from "modules/case-review/redux/slice";
import CaseReview from "modules/case-review/redux/selectors";
import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA } from "modules/case-review/graphql/query";
import {
  any,
  always,
  applySpec,
  both,
  complement,
  equals,
  find,
  identity,
  ifElse,
  map,
  mergeRight,
  not,
  path,
  pathEq,
  pipe,
  prop,
  reject,
  T,
  sort,
  descend,
  filter,
  concat
} from "ramda";
import { isNullOrEmpty } from "utils/fp";

const isSelectedElement = selectedElements => element =>
  any(
    both(
      pathEq(["irrElement", "id"], element.value),
      complement(prop("delete"))
    ),
    selectedElements
  );

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const [caseReviewElements, setCaseReviewElements] = useState([]);
  const [elementOptions, setElementOptions] = useState([]);
  const review = useSelector(state => CaseReview.getGeneral(state));
  const {
    id,
    questionnaireType,
    addonGroups = [],
    elements,
    readOnly
  } = review;
  const reviewType = useSelector(state => CaseReview.getReviewType(state));

  const isTargetedReview = useMemo(
    () => equals(reviewType, "Targeted"),
    [reviewType]
  );

  const savedAddonGroupIds = useMemo(
    () =>
      pipe(
        reject(prop("delete")),
        map(path(["irrAddonGroup", "id"]))
      )(addonGroups),
    [addonGroups]
  );

  const {
    data,
    loading: loadingElements,
    error: errorElements
  } = useQuery(GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA, {
    variables: {
      questionnaireId: id,
      addonGroupIds: savedAddonGroupIds
    }
  });

  useEffect(() => {
    if (data) {
      const { caseReviewElementOptions = [] } = data;

      setCaseReviewElements(caseReviewElementOptions);

      const options = map(
        applySpec({
          label: prop("name"),
          value: prop("id")
        }),
        caseReviewElementOptions
      );

      setElementOptions(options);
    }
  }, [data]);

  const elementsReviewed = useMemo(
    () =>
      map(
        element =>
          pipe(
            find(
              both(
                pathEq(["irrElement", "id"], element.id),
                complement(prop("delete"))
              )
            ),
            ifElse(
              identity,
              applySpec({
                checked: T,
                elementsId: prop("id"),
                mismatch: prop("mismatch"),
                multiplier: prop("multiplier"),
                corrected: prop("corrected"),
                comment: prop("comment"),
                caseReviewCategory: prop("caseReviewCategory"),
                irrElement: prop("irrElement"),
                delete: prop("delete"),
                irrElementId: element.id,
                caseReviewCategoryId: path(["caseReviewCategory", "id"]),
                error: prop("error")
              }),
              always({
                checked: false,
                irrElement: element,
                irrElementId: element.id
              })
            ),
            mergeRight(element)
          )(elements),
        caseReviewElements
      ),
    [caseReviewElements, elements]
  );

  const filteredOptions = useMemo(() => {
    if (!isNullOrEmpty(elementOptions)) {
      return reject(isSelectedElement(elements), elementOptions);
    }
    return elementOptions;
  }, [elements, elementOptions]);

  const dispatch = useDispatch();

  const handleAddElementsReviewed = useCallback(
    value => {
      dispatch(
        updateElementsReviewed({
          irrElementId: value.value,
          name: "irrElement",
          checked: true,
          value: { id: value.value, name: value.label },
          irrElementName: value.label,
          isTargetedReview
        })
      );
      dispatch(
        updateElementsReviewed({
          irrElementId: value.value,
          name: "delete",
          checked: true,
          value: false,
          irrElementName: value.label
        })
      );
      dispatch(
        updateElementsReviewed({
          irrElementId: value.value,
          name: "checked",
          value: true,
          irrElementName: value.label
        })
      );
      if (not(isTargetedReview)) {
        dispatch(
          updateElementsReviewed({
            irrElementId: value.value,
            name: "mismatch",
            value: !isTargetedReview,
            irrElementName: value.label
          })
        );
        dispatch(addError({ id: value.value, error: true }));
      }
    },
    [dispatch]
  );

  const withElementId = useMemo(
    () =>
      pipe(
        filter(prop("elementsId")),
        sort(descend(prop("elementsId")))
      )(elementsReviewed),
    [elementsReviewed]
  );

  const noElementId = useMemo(
    () => reject(prop("elementsId"))(elementsReviewed),
    [elementsReviewed]
  );

  const sortedElementsReviewed = concat(noElementId, withElementId);

  const borderTop = filteredOptions.length !== sortedElementsReviewed.length;

  return {
    loadingElements,
    errorElements,
    questionnaireType,
    handleAddElementsReviewed,
    filteredOptions,
    readOnly,
    isTargetedReview,
    sortedElementsReviewed,
    borderTop
  };
};
