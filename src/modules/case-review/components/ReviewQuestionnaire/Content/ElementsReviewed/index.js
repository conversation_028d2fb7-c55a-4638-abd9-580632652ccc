import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  InputDropdown
} from "@q-centrix/q-components-react";
import { AnimatePresence, motion } from "framer-motion";
import Element from "./Element";
import classnames from "classnames";
import { useComponentLogic } from "./hooks";
import { and } from "ramda";

// eslint-disable-next-line complexity
export const ElementsReviewed = ({ mutationLoading }) => {
  const {
    loadingElements,
    errorElements,
    questionnaireType,
    handleAddElementsReviewed,
    filteredOptions,
    readOnly,
    isTargetedReview,
    sortedElementsReviewed,
    borderTop
  } = useComponentLogic();

  if (and(loadingElements || mutationLoading, questionnaireType === "specific"))
    return <Spinner />;
  if (errorElements) {
    return <p className="error">Error: Could not retrieve the data.</p>;
  }
  if (questionnaireType === "critical") return null;

  const inputDropdownContainer = classnames("tw-m-5", {
    "tw-border-t tw-border-black-29 tw-p-5": borderTop
  });

  return (
    <CardWithHeader
      headerContent={
        <p className="card-header-title tw-my-5">
          {isTargetedReview ? "Elements reviewed" : "Mismatched Elements"}
        </p>
      }
      headerClasses="tw-sticky tw-top-0"
      cardClasses="border-10px"
      bodyClasses="tw-max-h-[56vh] tw-mb-5 tw-overflow-y-auto"
    >
      <AnimatePresence>
        {sortedElementsReviewed &&
          sortedElementsReviewed.map(element => (
            <motion.div
              exit={{ height: 0, opacity: 0, margin: 0, padding: 0 }}
              transition={{
                opacity: { duration: 0 },
                type: "spring",
                bounce: 0.3
              }}
              key={element.id}
            >
              <Element
                element={element}
                isTargetedReview={isTargetedReview}
                readOnly={readOnly}
              />
            </motion.div>
          ))}
        <div className={inputDropdownContainer}>
          <InputDropdown
            options={filteredOptions}
            placeholder="Select the name of an element you reviewed"
            onChange={handleAddElementsReviewed}
            iconClass="fa-solid fa-chevron-down"
            disabled={readOnly}
            menuPortalTarget={document.body}
          />
        </div>
      </AnimatePresence>
    </CardWithHeader>
  );
};

export default ElementsReviewed;
