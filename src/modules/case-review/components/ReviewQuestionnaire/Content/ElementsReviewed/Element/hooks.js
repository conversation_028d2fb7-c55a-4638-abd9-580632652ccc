import { useState, useCallback, useMemo } from "react";
import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_CATEGORIES_DATA } from "modules/case-review/graphql/query";
import { useDispatch, useSelector } from "react-redux";
import {
  updateElementsReviewed,
  addError,
  removeError,
  deleteElementContent
} from "modules/case-review/redux/slice";
import CaseReview from "modules/case-review/redux/selectors";
import {
  any,
  applySpec,
  equals,
  find,
  isEmpty,
  map,
  not,
  prop,
  propEq,
  whereEq
} from "ramda";
import determineInputValueByType from "utils/fp/determineInputValueByType";

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { element } = props;
  const { caseReviewCategory, elementsId, irrElement, corrected, mismatch } =
    element;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [selCategory, setSelCategory] = useState({});
  const reviewType = useSelector(state => CaseReview.getReviewType(state));
  const errors = useSelector(state => CaseReview.getErrors(state));
  const checkForError = useMemo(
    () => any(whereEq({ id: element.id, error: true }))(errors),
    [errors, element]
  );
  const [errorRequired, setErrorRequired] = useState(checkForError);
  const { loading, error } = useQuery(GET_CASE_REVIEW_CATEGORIES_DATA, {
    onCompleted: (data = []) => {
      const { caseReviewCategories = [] } = data;

      const options = map(
        applySpec({
          label: prop("name"),
          value: prop("id")
        }),
        caseReviewCategories
      );

      setCategoryOptions(options);

      if (caseReviewCategory) {
        const initialCategory = find(
          propEq("value", caseReviewCategory.id),
          options
        );

        setSelCategory(initialCategory);
      }
    },
    // eslint-disable-next-line no-empty-function
    onError: () => {}
  });

  const dispatch = useDispatch();

  const handleMismatchToggle = useCallback(() => {
    if (isEmpty(selCategory) || isEmpty(element.caseReviewCategory)) {
      setErrorRequired(true);
      dispatch(addError({ id: element.irrElement.id, error: !mismatch }));
    }
    dispatch(
      updateElementsReviewed({
        elementsId,
        irrElementId: irrElement.id,
        name: "mismatch",
        value: !mismatch
      })
    );
    dispatch(
      updateElementsReviewed({
        elementsId,
        irrElementId: irrElement.id,
        name: "irrElementId",
        value: irrElement.id
      })
    );
    setSelCategory({});
  }, [dispatch, elementsId, irrElement, mismatch]);

  const mismatchText = () => (mismatch ? "Mismatch: Yes" : "Mismatch: No");

  const handleIsCorrected = useCallback(() => {
    dispatch(
      updateElementsReviewed({
        elementsId,
        irrElementId: irrElement.id,
        name: "corrected",
        value: !corrected
      })
    );
    dispatch(
      updateElementsReviewed({
        elementsId,
        irrElementId: irrElement.id,
        name: "irrElementId",
        value: irrElement.id
      })
    );
  }, [dispatch, elementsId, irrElement, corrected]);
  // will need to refactor to use one dispatch
  const handleMismatchedCategory = useCallback(
    value => {
      setSelCategory({
        label: value.label,
        value: value.value
      });
      setErrorRequired(false);
      dispatch(removeError({ id: element.irrElement.id }));
      dispatch(
        updateElementsReviewed({
          elementsId: element.elementsId,
          irrElementId: element.irrElement.id,
          name: "caseReviewCategory",
          value: { id: value.value, name: value.label }
        })
      );
      dispatch(
        updateElementsReviewed({
          elementsId,
          irrElementId: irrElement.id,
          name: "caseReviewCategoryId",
          value: value.value
        })
      );
      dispatch(
        updateElementsReviewed({
          elementsId,
          irrElementId: irrElement.id,
          name: "irrElementId",
          value: irrElement.id
        })
      );
    },
    [dispatch, element]
  );

  const handleMultiplierChange = useCallback(
    e => {
      dispatch(
        updateElementsReviewed({
          elementsId: element.elementsId,
          irrElementId: element.irrElement.id,
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
      dispatch(
        updateElementsReviewed({
          elementsId,
          irrElementId: irrElement.id,
          name: "irrElementId",
          value: irrElement.id
        })
      );
    },
    [dispatch, elementsId, irrElement]
  );

  const handleChange = useCallback(
    e => {
      dispatch(
        updateElementsReviewed({
          elementsId,
          irrElementId: irrElement.id,
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
      dispatch(
        updateElementsReviewed({
          elementsId,
          irrElementId: irrElement.id,
          name: "irrElementId",
          value: irrElement.id
        })
      );
    },
    [dispatch, elementsId, irrElement]
  );

  const handleModal = useCallback(() => setIsModalOpen(not), [setIsModalOpen]);

  const handleDeleteElementReviewed = useCallback(() => {
    dispatch(
      updateElementsReviewed({
        elementsId,
        irrElementId: irrElement.id,
        name: "delete",
        checked: false,
        value: true
      })
    );
    setSelCategory({});
    dispatch(deleteElementContent({ irrElementId: irrElement.id }));
    dispatch(removeError({ id: element.irrElement.id }));
    handleModal();
  }, [dispatch, elementsId, irrElement]);

  return {
    loading,
    error,
    handleMismatchToggle,
    mismatchText,
    handleIsCorrected,
    handleMismatchedCategory,
    handleModal,
    isModalOpen,
    handleDeleteElementReviewed,
    selCategory,
    categoryOptions,
    handleChange,
    handleMultiplierChange,
    errorRequired: equals("Full", reviewType) ? checkForError : errorRequired
  };
};
