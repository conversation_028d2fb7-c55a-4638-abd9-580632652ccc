import { AnimatePresence, motion } from "framer-motion";
import { and } from "ramda";
import {
  InputDropdown,
  Switch,
  Checkbox,
  Button,
  CardWithHeader
} from "@q-centrix/q-components-react";
import DeleteEntryModal from "./DeleteEntryModal";
import { useComponentLogic } from "./hooks";
import classNames from "classnames";

const variants = {
  unmount: {
    opacity: 0,
    height: 0,
    transition: { opacity: { duration: 0 } }
  },
  mount: {
    opacity: 1,
    height: "40px"
  }
};

// eslint-disable-next-line complexity
const Element = props => {
  const { element, isTargetedReview, readOnly } = props;
  const elementMismatch = classNames(
    "element-reviewed-mismatch tw-flex tw-justify-between tw-items-center",
    {
      full: !isTargetedReview
    }
  );
  const {
    error,
    handleMismatchToggle,
    mismatchText,
    handleIsCorrected,
    handleMismatchedCategory,
    handleModal,
    isModalOpen,
    handleDeleteElementReviewed,
    selCategory,
    categoryOptions,
    handleChange,
    handleMultiplierChange,
    errorRequired
  } = useComponentLogic(props);

  if (error) return <p className="error">Error: No data.</p>;
  return (
    <>
      {and(element.checked, !element.delete) && (
        <motion.div>
          <DeleteEntryModal
            isOpen={isModalOpen}
            handleModal={handleModal}
            handleDelete={handleDeleteElementReviewed}
          />
          <CardWithHeader
            cardClasses="border-10px tw-m-5"
            headerClasses="main title-white tw-px-5 tw-py-2.5"
            bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
            headerContent={
              <>
                <div className="tw-flex tw-items-center tw-justify-between">
                  <p>{element.irrElement.name}</p>
                  <Button
                    outline
                    bg="danger"
                    onClick={handleModal}
                    customStyle="tw-flex tw-gap-2.5 tw-items-center"
                    disabled={readOnly}
                  >
                    <i className="fa fa-xmark" />
                    Delete Entry
                  </Button>
                </div>
              </>
            }
          >
            <div className={elementMismatch}>
              {isTargetedReview && (
                <div>
                  <Switch
                    fieldName={element.id}
                    label={mismatchText()}
                    defaultToggle={element.mismatch}
                    onChange={handleMismatchToggle}
                    disabled={readOnly}
                  />
                </div>
              )}
              {and(element.mismatch, element.allowMultiplier) && (
                <div>
                  <div className="tw-flex tw-items-center tw-gap-2.5">
                    <input
                      type="number"
                      name="multiplier"
                      value={element.multiplier || ""}
                      onChange={handleMultiplierChange}
                      disabled={readOnly}
                      min={0}
                      className={
                        element.error
                          ? "tw-w-20 tw-rounded tw-border tw-border-error-600 tw-px-4 tw-py-2.5"
                          : "tw-w-20 tw-rounded tw-border tw-border-black-29 tw-px-4 tw-py-2.5"
                      }
                    />
                    <p> times</p>
                  </div>
                </div>
              )}
            </div>
            <div className="tw-flex tw-flex-row tw-justify-end">
              {element.error && (
                <span className="tw-w-[300px] tw-pr-5 tw-text-xs tw-font-normal tw-italic tw-text-error-700">
                  {element.error}
                </span>
              )}
            </div>

            <AnimatePresence>
              {element.mismatch && (
                <motion.div
                  initial="unmount"
                  exit="unmount"
                  animate="mount"
                  variants={variants}
                  className="tw-mb-5"
                >
                  <div>
                    <InputDropdown
                      value={selCategory}
                      options={categoryOptions}
                      placeholder="Mismatched category"
                      onChange={handleMismatchedCategory}
                      iconClass="fa-solid fa-chevron-down"
                      disabled={readOnly}
                      error={errorRequired}
                      errorText="this is required"
                      menuPortalTarget={document.body}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            {element.mismatch && (
              <textarea
                name="comment"
                className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
                placeholder="Comment or reason for mismatch"
                value={element.comment}
                onChange={handleChange}
                disabled={readOnly}
              />
            )}
            <AnimatePresence>
              {element.mismatch && (
                <motion.div
                  initial="unmount"
                  exit="unmount"
                  animate="mount"
                  variants={variants}
                  className="tw-flex"
                >
                  <Checkbox
                    name={`isCorrected-${element.id}`}
                    label="Element Corrected"
                    checked={element.corrected}
                    onChange={handleIsCorrected}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </CardWithHeader>
          <div className="elements-separator" />
        </motion.div>
      )}
    </>
  );
};

export default Element;
