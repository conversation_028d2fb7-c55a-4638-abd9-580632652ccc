// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DeleteEntryModal components renders correctly 1`] = `
<ConfirmationModal
  appElement=".main"
  isDanger={true}
  title="Confirm Action"
>
  <div
    className="modal-content"
  >
    <p
      className="modal-paragraph"
    >
      Are you sure you want to delete ALL the information contained in this entry?
    </p>
  </div>
  <div
    className="button-container"
  >
    <Button
      bg="neutral"
      customStyle="modal-buttons"
      outline={true}
    >
      <i
        className="fa fa-xmark"
      />
      Cancel
    </Button>
    <Button
      bg="danger"
      customStyle="modal-buttons"
    >
      <i
        className="fa-regular fa-trash-can"
      />
      Confirm
    </Button>
  </div>
</ConfirmationModal>
`;
