import { act, create } from "react-test-renderer";
import DeleteEntryModal from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  ConfirmationModal: "ConfirmationModal"
}));

describe("DeleteEntryModal", () => {
  function render(props = {}) {
    return create(<DeleteEntryModal {...props} />);
  }

  test("components renders correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("closes the modal on button click", () => {
    const handleModal = jest.fn();
    const component = render({ handleModal });
    const instance = component.root;
    const [btn] = instance.findAllByType("Button");

    act(() => btn.props.onClick());
    expect(handleModal).toHaveBeenCalled();
  });
});
