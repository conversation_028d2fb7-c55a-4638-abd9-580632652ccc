import { Button, ConfirmationModal } from "@q-centrix/q-components-react";
import "styles/confirmation-modal.scss";

const DeleteEntryModal = ({ isOpen, handleModal, handleDelete }) => (
  <ConfirmationModal
    title="Confirm Action"
    isDanger
    isOpen={isOpen}
    appElement=".main"
  >
    <div className="modal-content">
      <p className="modal-paragraph">
        Are you sure you want to delete ALL the information contained in this
        entry?
      </p>
    </div>
    <div className="button-container">
      <Button
        onClick={handleModal}
        customStyle="modal-buttons"
        outline
        bg="neutral"
      >
        <i className="fa fa-xmark" />
        Cancel
      </Button>
      <Button onClick={handleDelete} customStyle="modal-buttons" bg="danger">
        <i className="fa-regular fa-trash-can" />
        Confirm
      </Button>
    </div>
  </ConfirmationModal>
);

export default DeleteEntryModal;
