// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Element it renders component disabled 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={true}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center"
    >
      <div>
        <Switch
          defaultToggle={true}
          disabled={true}
          fieldName="7"
          label="Mismatch: Yes"
          onChange={[Function]}
        />
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-mb-5"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <div>
          <InputDropdown
            disabled={true}
            error={false}
            errorText="this is required"
            iconClass="fa-solid fa-chevron-down"
            menuPortalTarget={<body />}
            onChange={[Function]}
            options={Array []}
            placeholder="Mismatched category"
            value={Object {}}
          />
        </div>
      </motion.div>
    </AnimatePresence>
    <textarea
      className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
      disabled={true}
      name="comment"
      onChange={[Function]}
      placeholder="Comment or reason for mismatch"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-flex"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <Checkbox
          label="Element Corrected"
          name="isCorrected-7"
          onChange={[Function]}
        />
      </motion.div>
    </AnimatePresence>
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;

exports[`Element it renders component for Full Review 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={false}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center full"
    >
      <div>
        <div
          className="tw-flex tw-items-center tw-gap-2.5"
        >
          <input
            className="tw-w-20 tw-rounded tw-border tw-border-black-29 tw-px-4 tw-py-2.5"
            disabled={false}
            min={0}
            name="multiplier"
            onChange={[Function]}
            type="number"
            value=""
          />
          <p>
             times
          </p>
        </div>
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-mb-5"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <div>
          <InputDropdown
            disabled={false}
            error={false}
            errorText="this is required"
            iconClass="fa-solid fa-chevron-down"
            menuPortalTarget={<body />}
            onChange={[Function]}
            options={Array []}
            placeholder="Mismatched category"
            value={Object {}}
          />
        </div>
      </motion.div>
    </AnimatePresence>
    <textarea
      className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
      disabled={false}
      name="comment"
      onChange={[Function]}
      placeholder="Comment or reason for mismatch"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-flex"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <Checkbox
          label="Element Corrected"
          name="isCorrected-7"
          onChange={[Function]}
        />
      </motion.div>
    </AnimatePresence>
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;

exports[`Element it renders component for Targeted Review 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={false}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center"
    >
      <div>
        <Switch
          defaultToggle={true}
          disabled={false}
          fieldName="7"
          label="Mismatch: Yes"
          onChange={[Function]}
        />
      </div>
      <div>
        <div
          className="tw-flex tw-items-center tw-gap-2.5"
        >
          <input
            className="tw-w-20 tw-rounded tw-border tw-border-black-29 tw-px-4 tw-py-2.5"
            disabled={false}
            min={0}
            name="multiplier"
            onChange={[Function]}
            type="number"
            value=""
          />
          <p>
             times
          </p>
        </div>
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-mb-5"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <div>
          <InputDropdown
            disabled={false}
            error={false}
            errorText="this is required"
            iconClass="fa-solid fa-chevron-down"
            menuPortalTarget={<body />}
            onChange={[Function]}
            options={Array []}
            placeholder="Mismatched category"
            value={Object {}}
          />
        </div>
      </motion.div>
    </AnimatePresence>
    <textarea
      className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
      disabled={false}
      name="comment"
      onChange={[Function]}
      placeholder="Comment or reason for mismatch"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-flex"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <Checkbox
          label="Element Corrected"
          name="isCorrected-7"
          onChange={[Function]}
        />
      </motion.div>
    </AnimatePresence>
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;

exports[`Element it renders component with multiplier error 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={false}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center"
    >
      <div>
        <Switch
          defaultToggle={true}
          disabled={false}
          fieldName="7"
          label="Mismatch: Yes"
          onChange={[Function]}
        />
      </div>
      <div>
        <div
          className="tw-flex tw-items-center tw-gap-2.5"
        >
          <input
            className="tw-w-20 tw-rounded tw-border tw-border-error-600 tw-px-4 tw-py-2.5"
            disabled={false}
            min={0}
            name="multiplier"
            onChange={[Function]}
            type="number"
            value=""
          />
          <p>
             times
          </p>
        </div>
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    >
      <span
        className="tw-w-[300px] tw-pr-5 tw-text-xs tw-font-normal tw-italic tw-text-error-700"
      >
        Elements multiplier must be greater than or equal to 1
      </span>
    </div>
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-mb-5"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <div>
          <InputDropdown
            disabled={false}
            error={false}
            errorText="this is required"
            iconClass="fa-solid fa-chevron-down"
            menuPortalTarget={<body />}
            onChange={[Function]}
            options={Array []}
            placeholder="Mismatched category"
            value={Object {}}
          />
        </div>
      </motion.div>
    </AnimatePresence>
    <textarea
      className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
      disabled={false}
      name="comment"
      onChange={[Function]}
      placeholder="Comment or reason for mismatch"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-flex"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <Checkbox
          label="Element Corrected"
          name="isCorrected-7"
          onChange={[Function]}
        />
      </motion.div>
    </AnimatePresence>
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;

exports[`Element it renders component with one entry with mismatch 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={false}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center"
    >
      <div>
        <Switch
          defaultToggle={true}
          disabled={false}
          fieldName="7"
          label="Mismatch: Yes"
          onChange={[Function]}
        />
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-mb-5"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <div>
          <InputDropdown
            disabled={false}
            error={false}
            errorText="this is required"
            iconClass="fa-solid fa-chevron-down"
            menuPortalTarget={<body />}
            onChange={[Function]}
            options={Array []}
            placeholder="Mismatched category"
            value={Object {}}
          />
        </div>
      </motion.div>
    </AnimatePresence>
    <textarea
      className="tw-rounded tw-border tw-border-gray-700 tw-px-[15px] tw-py-2.5 placeholder:tw-text-gray-700"
      disabled={false}
      name="comment"
      onChange={[Function]}
      placeholder="Comment or reason for mismatch"
    />
    <AnimatePresence>
      <motion.div
        animate="mount"
        className="tw-flex"
        exit="unmount"
        initial="unmount"
        variants={
          Object {
            "mount": Object {
              "height": "40px",
              "opacity": 1,
            },
            "unmount": Object {
              "height": 0,
              "opacity": 0,
              "transition": Object {
                "opacity": Object {
                  "duration": 0,
                },
              },
            },
          }
        }
      >
        <Checkbox
          label="Element Corrected"
          name="isCorrected-7"
          onChange={[Function]}
        />
      </motion.div>
    </AnimatePresence>
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;

exports[`Element it renders component with one entry without mismatch 1`] = `
<motion.div>
  <DeleteEntryModal
    handleDelete={[Function]}
    handleModal={[Function]}
    isOpen={false}
  />
  <CardWithHeader
    bodyClasses="!tw-bg-gray-50 tw-p-5 tw-flex tw-flex-col tw-gap-2.5"
    cardClasses="border-10px tw-m-5"
    headerClasses="main title-white tw-px-5 tw-py-2.5"
    headerContent={
      <React.Fragment>
        <div
          className="tw-flex tw-items-center tw-justify-between"
        >
          <p>
            Test Element
          </p>
          <Button
            bg="danger"
            customStyle="tw-flex tw-gap-2.5 tw-items-center"
            disabled={false}
            onClick={[Function]}
            outline={true}
          >
            <i
              className="fa fa-xmark"
            />
            Delete Entry
          </Button>
        </div>
      </React.Fragment>
    }
  >
    <div
      className="element-reviewed-mismatch tw-flex tw-justify-between tw-items-center"
    >
      <div>
        <Switch
          defaultToggle={false}
          disabled={false}
          fieldName="7"
          label="Mismatch: No"
          onChange={[Function]}
        />
      </div>
    </div>
    <div
      className="tw-flex tw-flex-row tw-justify-end"
    />
    <AnimatePresence />
    <AnimatePresence />
  </CardWithHeader>
  <div
    className="elements-separator"
  />
</motion.div>
`;
