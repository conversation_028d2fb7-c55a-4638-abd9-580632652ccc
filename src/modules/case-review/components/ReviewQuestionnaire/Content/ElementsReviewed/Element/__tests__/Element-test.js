import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks from "../../mocks";
import Element from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  InputDropdown: "InputDropdown",
  Switch: "Switch",
  Checkbox: "Checkbox",
  CardWithHeader: "CardWithHeader"
}));
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));
jest.mock("react-modal", () => "Modal");
jest.mock("../DeleteEntryModal", () => "DeleteEntryModal");

describe("Element", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: Element,
        props,
        initialValues: {},
        initialAppValues: {
          caseReview: {
            elements: [
              {
                elementsId: "1",
                checked: true,
                id: "7",
                irrAddonGroups: [{ id: "3", allowMultipliers: true }],
                irrElement: { id: "9", name: "Test Element" },
                name: "Element 2"
              }
            ],
            reviewType: "Targeted",
            errors: []
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component for Targeted Review", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "9", name: "Test Element" },
        name: "Element 2",
        mismatch: true,
        allowMultiplier: true
      },
      isTargetedReview: true,
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component for Full Review", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "9", name: "Test Element" },
        name: "Element 2",
        mismatch: true,
        allowMultiplier: true
      },
      isTargetedReview: false,
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with one entry with mismatch", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "9", name: "Test Element" },
        name: "Element 2",
        mismatch: true
      },
      isTargetedReview: true,
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with one entry without mismatch", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "1", name: "Test Element" },
        name: "Element 2",
        mismatch: false
      },
      isTargetedReview: true,
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component disabled", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "9", name: "Test Element" },
        name: "Element 2",
        mismatch: true
      },
      isTargetedReview: true,
      readOnly: true
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with multiplier error", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        irrElement: { id: "1", name: "Test Element" },
        name: "Element 2",
        mismatch: true,
        allowMultiplier: true,
        error: "Elements multiplier must be greater than or equal to 1"
      },
      isTargetedReview: true,
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });
});
