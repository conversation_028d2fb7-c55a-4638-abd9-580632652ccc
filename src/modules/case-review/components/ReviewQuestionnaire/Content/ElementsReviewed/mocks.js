import {
  GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA,
  GET_CASE_REVIEW_CATEGORIES_DATA
} from "modules/case-review/graphql/query";

export const createQueryMocks = id => ({
  request: {
    query: GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA,
    variables: { questionnaireId: id, addonGroupIds: [] }
  },
  result: {
    data: {
      caseReviewElementOptions: [
        {
          id: "1",
          name: "Legend of Hungry Demon",
          allowMultiplier: false,
          irrAddonGroups: [],
          __typename: "IrrElement"
        },
        {
          id: "2",
          name: "Planet of the Ultra Blow",
          allowMultiplier: false,
          irrAddonGroups: [],
          __typename: "IrrElement"
        },
        {
          id: "3",
          name: "Test Element 1 with Multiplier",
          allowMultiplier: true,
          irrAddonGroups: [
            {
              allowMultipliers: true,
              id: "1",
              __typename: "IrrAddonGroup"
            }
          ],
          __typename: "IrrElement"
        },
        {
          id: "4",
          name: "Test Element 1 no Multiplier",
          allowMultiplier: false,
          irrAddonGroups: [
            {
              allowMultipliers: false,
              id: "1",
              __typename: "IrrAddonGroup"
            }
          ],
          __typename: "IrrElement"
        },
        {
          id: "5",
          name: "Saved to BE Element",
          allowMultiplier: false,
          irrAddonGroups: [],
          __typename: "IrrElement"
        }
      ]
    }
  }
});

const elementOptionsDataMock = [
  createQueryMocks("1"),
  createQueryMocks("2"),
  createQueryMocks("3"),
  createQueryMocks("4"),
  createQueryMocks("5"),
  createQueryMocks("6")
];

const caseReviewCategoryMocks = [
  {
    request: {
      query: GET_CASE_REVIEW_CATEGORIES_DATA
    },
    result: {
      data: {
        caseReviewCategories: [
          {
            id: 1,
            name: "Location Error",
            __typename: "CaseReviewCategory"
          },
          {
            id: 2,
            name: "Data Abstraction Error",
            __typename: "CaseReviewCategory"
          },
          {
            id: 3,
            name: "Data Entry Error",
            __typename: "CaseReviewCategory"
          },
          {
            id: 4,
            name: "Incomplete Documentation Error",
            __typename: "CaseReviewCategory"
          },
          {
            id: 5,
            name: "Conflicting Information Error",
            __typename: "CaseReviewCategory"
          }
        ]
      }
    }
  }
];

const elementsReviewedMocks = [
  ...elementOptionsDataMock,
  ...caseReviewCategoryMocks
];

export default elementsReviewedMocks;
