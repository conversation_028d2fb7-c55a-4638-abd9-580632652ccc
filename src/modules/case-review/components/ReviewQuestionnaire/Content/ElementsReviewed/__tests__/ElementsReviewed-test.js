import { act, create } from "react-test-renderer";
import wait from "waait";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import { GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA } from "modules/case-review/graphql/query";
import mocks from "../mocks";
import ElementsReviewed from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  Spinner: "Spinner",
  InputDropdown: "InputDropdown"
}));
jest.mock("framer-motion", () => ({
  AnimatePresence: "AnimatePresence",
  motion: {
    div: "motion.div"
  }
}));
jest.mock("../Element", () => "Element");

describe("ElementsReviewed", () => {
  function render(
    mock = mocks,
    questionnaireType = "specific",
    reviewType = "Targeted"
  ) {
    return create(
      decoratedApollo({
        component: ElementsReviewed,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: "2",
              questionnaireType,
              score: 0,
              mismatchCount: 1,
              criticalElementCount: 1,
              matchedCount: 1,
              updateAt: "2022-12-02 00:00:01",
              completedAt: null,
              availableIrrAddonGroups: [
                {
                  __typename: "IrrAddonGroup",
                  id: "1",
                  name: "Test",
                  allowMultipliers: true
                },
                {
                  __typename: "IrrAddonGroup",
                  id: "2",
                  name: "Test Group 2",
                  allowMultipliers: false
                }
              ],
              facility: {
                __typename: "Facility",
                id: "1",
                name: "Large Medical Center"
              },
              readOnly: false,
              abstractor: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              initiator: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              owner: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              patient: {
                __typename: "Patient",
                firstName: "Madelyn Effertz",
                lastName: "Grady Walker",
                mrn: "**********"
              },
              visit: {
                __typename: "Visit",
                number: "73943259"
              },
              task: {
                __typename: "Task",
                case: {
                  __typename: "Cases",
                  id: "49"
                }
              },
              caseType: {
                __typename: "Case",
                id: "22",
                name: "CDiff"
              },
              correctCaseType: false,
              originalCaseType: {
                __typename: "Case",
                id: "22",
                name: "CDiff"
              },
              addonGroups: [],
              elements: [
                {
                  id: 1,
                  irrElement: {
                    id: "5",
                    name: "Saved to BE Element"
                  },
                  irrElementId: "5",
                  mismatch: true,
                  corrected: true,
                  comment: "Test comment for specific review",
                  caseReviewCategory: {
                    id: 1,
                    name: "Location Error"
                  },
                  delete: false,
                  checked: true
                }
              ]
            },
            isOwner: true,
            reviewType
          }
        },
        apolloMocks: mock
      })
    );
  }

  test("it renders component with Loading Spinner", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const errorMock = [
      {
        request: { query: GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA },
        error: new Error("an error occurred")
      }
    ];
    const component = render(errorMock);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data for Targeted Review", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data for Full Review", async () => {
    const component = render(mocks, "ongoing", "Full");

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
