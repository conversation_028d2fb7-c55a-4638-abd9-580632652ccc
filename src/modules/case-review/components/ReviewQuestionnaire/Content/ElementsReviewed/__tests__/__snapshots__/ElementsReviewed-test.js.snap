// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ElementsReviewed it renders component with Loading Spinner 1`] = `<Spinner />`;

exports[`ElementsReviewed it renders component with data for Full Review 1`] = `
<CardWithHeader
  bodyClasses="tw-max-h-[56vh] tw-mb-5 tw-overflow-y-auto"
  cardClasses="border-10px"
  headerClasses="tw-sticky tw-top-0"
  headerContent={
    <p
      className="card-header-title tw-my-5"
    >
      Mismatched Elements
    </p>
  }
>
  <AnimatePresence>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "1",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "1",
              "irrAddonGroups": Array [],
              "name": "Legend of Hungry Demon",
            },
            "irrElementId": "1",
            "name": "Legend of Hungry Demon",
          }
        }
        isTargetedReview={false}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "2",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "2",
              "irrAddonGroups": Array [],
              "name": "Planet of the Ultra Blow",
            },
            "irrElementId": "2",
            "name": "Planet of the Ultra Blow",
          }
        }
        isTargetedReview={false}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": true,
            "checked": false,
            "id": "3",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": true,
              "id": "3",
              "irrAddonGroups": Array [
                Object {
                  "__typename": "IrrAddonGroup",
                  "allowMultipliers": true,
                  "id": "1",
                },
              ],
              "name": "Test Element 1 with Multiplier",
            },
            "irrElementId": "3",
            "name": "Test Element 1 with Multiplier",
          }
        }
        isTargetedReview={false}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "4",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "4",
              "irrAddonGroups": Array [
                Object {
                  "__typename": "IrrAddonGroup",
                  "allowMultipliers": true,
                  "id": "1",
                },
              ],
              "name": "Test Element 1 no Multiplier",
            },
            "irrElementId": "4",
            "name": "Test Element 1 no Multiplier",
          }
        }
        isTargetedReview={false}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "caseReviewCategory": Object {
              "id": 1,
              "name": "Location Error",
            },
            "caseReviewCategoryId": 1,
            "checked": true,
            "comment": "Test comment for specific review",
            "corrected": true,
            "delete": false,
            "elementsId": 1,
            "error": undefined,
            "id": "5",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "id": "5",
              "name": "Saved to BE Element",
            },
            "irrElementId": Object {},
            "mismatch": true,
            "multiplier": undefined,
            "name": "Saved to BE Element",
          }
        }
        isTargetedReview={false}
        readOnly={false}
      />
    </motion.div>
    <div
      className="tw-m-5 tw-border-t tw-border-black-29 tw-p-5"
    >
      <InputDropdown
        disabled={false}
        iconClass="fa-solid fa-chevron-down"
        menuPortalTarget={<body />}
        onChange={[Function]}
        options={
          Array [
            Object {
              "label": "Legend of Hungry Demon",
              "value": "1",
            },
            Object {
              "label": "Planet of the Ultra Blow",
              "value": "2",
            },
            Object {
              "label": "Test Element 1 with Multiplier",
              "value": "3",
            },
            Object {
              "label": "Test Element 1 no Multiplier",
              "value": "4",
            },
          ]
        }
        placeholder="Select the name of an element you reviewed"
      />
    </div>
  </AnimatePresence>
</CardWithHeader>
`;

exports[`ElementsReviewed it renders component with data for Targeted Review 1`] = `
<CardWithHeader
  bodyClasses="tw-max-h-[56vh] tw-mb-5 tw-overflow-y-auto"
  cardClasses="border-10px"
  headerClasses="tw-sticky tw-top-0"
  headerContent={
    <p
      className="card-header-title tw-my-5"
    >
      Elements reviewed
    </p>
  }
>
  <AnimatePresence>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "1",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "1",
              "irrAddonGroups": Array [],
              "name": "Legend of Hungry Demon",
            },
            "irrElementId": "1",
            "name": "Legend of Hungry Demon",
          }
        }
        isTargetedReview={true}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "2",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "2",
              "irrAddonGroups": Array [],
              "name": "Planet of the Ultra Blow",
            },
            "irrElementId": "2",
            "name": "Planet of the Ultra Blow",
          }
        }
        isTargetedReview={true}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": true,
            "checked": false,
            "id": "3",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": true,
              "id": "3",
              "irrAddonGroups": Array [
                Object {
                  "__typename": "IrrAddonGroup",
                  "allowMultipliers": true,
                  "id": "1",
                },
              ],
              "name": "Test Element 1 with Multiplier",
            },
            "irrElementId": "3",
            "name": "Test Element 1 with Multiplier",
          }
        }
        isTargetedReview={true}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "checked": false,
            "id": "4",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "irrElement": Object {
              "__typename": "IrrElement",
              "allowMultiplier": false,
              "id": "4",
              "irrAddonGroups": Array [
                Object {
                  "__typename": "IrrAddonGroup",
                  "allowMultipliers": true,
                  "id": "1",
                },
              ],
              "name": "Test Element 1 no Multiplier",
            },
            "irrElementId": "4",
            "name": "Test Element 1 no Multiplier",
          }
        }
        isTargetedReview={true}
        readOnly={false}
      />
    </motion.div>
    <motion.div
      exit={
        Object {
          "height": 0,
          "margin": 0,
          "opacity": 0,
          "padding": 0,
        }
      }
      transition={
        Object {
          "bounce": 0.3,
          "opacity": Object {
            "duration": 0,
          },
          "type": "spring",
        }
      }
    >
      <Element
        element={
          Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "caseReviewCategory": Object {
              "id": 1,
              "name": "Location Error",
            },
            "caseReviewCategoryId": 1,
            "checked": true,
            "comment": "Test comment for specific review",
            "corrected": true,
            "delete": false,
            "elementsId": 1,
            "error": undefined,
            "id": "5",
            "irrAddonGroups": Array [],
            "irrElement": Object {
              "id": "5",
              "name": "Saved to BE Element",
            },
            "irrElementId": Object {},
            "mismatch": true,
            "multiplier": undefined,
            "name": "Saved to BE Element",
          }
        }
        isTargetedReview={true}
        readOnly={false}
      />
    </motion.div>
    <div
      className="tw-m-5 tw-border-t tw-border-black-29 tw-p-5"
    >
      <InputDropdown
        disabled={false}
        iconClass="fa-solid fa-chevron-down"
        menuPortalTarget={<body />}
        onChange={[Function]}
        options={
          Array [
            Object {
              "label": "Legend of Hungry Demon",
              "value": "1",
            },
            Object {
              "label": "Planet of the Ultra Blow",
              "value": "2",
            },
            Object {
              "label": "Test Element 1 with Multiplier",
              "value": "3",
            },
            Object {
              "label": "Test Element 1 no Multiplier",
              "value": "4",
            },
          ]
        }
        placeholder="Select the name of an element you reviewed"
      />
    </div>
  </AnimatePresence>
</CardWithHeader>
`;

exports[`ElementsReviewed it renders component with error 1`] = `
<p
  className="error"
>
  Error: Could not retrieve the data.
</p>
`;
