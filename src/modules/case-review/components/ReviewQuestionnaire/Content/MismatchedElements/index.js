import { <PERSON><PERSON><PERSON><PERSON>ead<PERSON>, Spinner } from "@q-centrix/q-components-react";
import { and } from "ramda";
import Element from "./Element";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const MismatchedElements = ({ mutationLoading }) => {
  const { mismatchedElements, questionnaireType, loading, error, readOnly } =
    useComponentLogic();

  if (and(loading || mutationLoading, questionnaireType === "critical"))
    return <Spinner />;
  if (error) return <p className="error">Error: No data.</p>;
  if (questionnaireType === "specific") return null;

  return (
    <CardWithHeader
      headerContent={<p className="tw-m-5">Mismatched elements</p>}
      cardClasses="border-10px full-height"
    >
      <div>
        {mismatchedElements.map(element => (
          <Element
            key={element.id}
            element={element}
            loading={loading}
            readOnly={readOnly}
          />
        ))}
      </div>
    </CardWithHeader>
  );
};

export default MismatchedElements;
