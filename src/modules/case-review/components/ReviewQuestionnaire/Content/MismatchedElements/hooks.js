import { useMemo, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import CaseReview from "modules/case-review/redux/selectors";
import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA } from "modules/case-review/graphql/query";
import {
  find,
  prop,
  map,
  pathEq,
  pipe,
  ifElse,
  applySpec,
  T,
  identity,
  always,
  mergeRight,
  reject,
  path
} from "ramda";

export const useComponentLogic = () => {
  const [caseReviewElements, setCaseReviewElements] = useState([]);
  const review = useSelector(state => CaseReview.getGeneral(state));
  const {
    id,
    addonGroups = [],
    questionnaireType,
    elements,
    readOnly
  } = review;

  const savedAddonGroupIds = useMemo(
    () =>
      pipe(
        reject(prop("delete")),
        map(path(["irrAddonGroup", "id"]))
      )(addonGroups),
    [addonGroups]
  );

  const { data, loading, error } = useQuery(
    GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA,
    {
      variables: { questionnaireId: id, addonGroupIds: savedAddonGroupIds },
      // eslint-disable-next-line no-empty-function
      onError: () => {}
    }
  );

  useEffect(() => {
    if (data) {
      const { caseReviewElementOptions = [] } = data;

      setCaseReviewElements(caseReviewElementOptions);
    }
  }, [data]);
  // eslint-disable-next-line complexity
  const mismatchedElements = useMemo(
    () =>
      map(
        element =>
          pipe(
            find(pathEq(["irrElement", "id"], element.id)),
            ifElse(
              identity,
              applySpec({
                checked: T,
                elementsId: prop("id"),
                mismatch: prop("mismatch"),
                multiplier: prop("multiplier"),
                corrected: prop("corrected"),
                comment: prop("comment"),
                caseReviewCategory: prop("caseReviewCategory"),
                irrElement: prop("irrElement"),
                delete: prop("delete"),
                irrElementId: element.id,
                caseReviewCategoryId: path(["caseReviewCategory", "id"]),
                error: prop("error")
              }),
              always({
                checked: false,
                irrElement: element,
                irrElementId: element.id
              })
            ),
            mergeRight(element)
          )(elements),
        caseReviewElements
      ),
    [caseReviewElements, elements]
  );

  return {
    loading,
    error,
    mismatchedElements,
    questionnaireType,
    readOnly
  };
};
