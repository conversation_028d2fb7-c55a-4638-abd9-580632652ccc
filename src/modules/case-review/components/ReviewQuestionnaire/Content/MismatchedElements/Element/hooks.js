import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { whereEq, any, and } from "ramda";
import {
  addError,
  updateElements,
  removeError,
  deleteMismatchedElementContent
} from "modules/case-review/redux/slice";
import CaseReview from "modules/case-review/redux/selectors";
import determineInputValueByType from "utils/fp/determineInputValueByType";
import { isNullOrEmpty } from "utils/fp";

// eslint-disable-next-line max-statements
export const useComponentLogic = props => {
  const { element } = props;
  const errors = useSelector(state => CaseReview.getErrors(state));
  const checkForError = useMemo(
    () => any(whereEq({ id: element.id, error: true }))(errors),
    [errors, element]
  );

  const dispatch = useDispatch();

  const onCheckboxChange = useCallback(
    e => {
      if (and(isNullOrEmpty(element.caseReviewCategory), e.target.checked)) {
        dispatch(addError({ id: element.id, error: e.target.checked }));
      }
      if (!e.target.checked) {
        dispatch(deleteMismatchedElementContent({ irrElementId: element.id }));
        dispatch(removeError({ id: element.id }));
      }
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "delete",
          checked: e.target.checked,
          value: !determineInputValueByType(e)
        })
      );
    },
    [dispatch, element]
  );

  const onElementCorrectedChange = useCallback(
    e => {
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "corrected",
          value: determineInputValueByType(e)
        })
      );
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "irrElementId",
          value: element.id
        })
      );
    },
    [dispatch, element]
  );

  const onChange = useCallback(
    e => {
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "irrElementId",
          value: element.id
        })
      );
    },
    [dispatch, element]
  );

  return {
    onCheckboxChange,
    onChange,
    onElementCorrectedChange,
    errorRequired: checkForError
  };
};
