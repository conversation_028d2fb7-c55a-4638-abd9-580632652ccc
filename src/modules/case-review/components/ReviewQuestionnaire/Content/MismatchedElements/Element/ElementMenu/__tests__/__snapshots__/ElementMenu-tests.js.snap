// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ElementMenu it renders component 1`] = `
<div
  className="tw-ml-6 tw-mt-2.5 tw-flex tw-flex-col tw-gap-5 tw-rounded-[5px] tw-bg-gray-50 tw-p-2.5"
>
  <InputDropdown
    components={
      Object {
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    errorText="this is required"
    onChange={[Function]}
    options={Array []}
    placeholder="Mismatched category"
    value={Object {}}
  />
  <textarea
    className="tw-rounded tw-border tw-border-gray-700 tw-px-4 tw-py-[9px] tw-text-sm tw-font-normal placeholder:tw-text-black-29"
    name="comment"
    onChange={[Function]}
    placeholder="Comment or reason for mismatch"
    value="Testing comment"
  />
</div>
`;
