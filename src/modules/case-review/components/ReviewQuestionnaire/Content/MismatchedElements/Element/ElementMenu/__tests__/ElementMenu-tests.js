import { act, create } from "react-test-renderer";
import {
  decorated<PERSON><PERSON><PERSON>,
  decoratedApolloWithDispatch
} from "utils/tests/decorated";
import { updateElements } from "modules/case-review/redux/slice";
import mocks from "../../../../mocks";
import ElementMenu from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  InputDropdown: "InputDropdown"
}));

describe("ElementMenu", () => {
  function render() {
    return create(
      decoratedApollo({
        component: ElementMenu,
        props: {
          element: {
            elementsId: "1",
            checked: true,
            id: "7",
            irrAddonGroups: [{ id: "3", allowMultipliers: true }],
            name: "Element 2",
            comment: "Testing comment"
          }
        },
        initialValues: {},
        initialAppValues: { caseReview: {} },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        name: "Element 2",
        comment: "Testing comment"
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("it tests dispatch for Mismatched Reason", () => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: ElementMenu,
      props: {
        element: {
          elementsId: "1",
          checked: true,
          id: "7",
          irrAddonGroups: [{ id: "3", allowMultipliers: true }],
          name: "Element 2",
          comment: "Testing comment"
        }
      },
      initialAppValues: { caseReview: {} },
      apolloMocks: mocks
    });

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const mismatchedReasonInput = instance.findByType("InputDropdown");

    act(() =>
      mismatchedReasonInput.props.onChange({
        value: "Test",
        label: "Test label"
      })
    );

    expect(dispatch).toHaveBeenCalledWith(
      updateElements({
        elementsId: "1",
        irrElementId: "7",
        name: "caseReviewCategory",
        value: { id: "Test", name: "Test label" }
      })
    );
  });

  test("it tests dispatch for Comments", () => {
    const { component, dispatch } = decoratedApolloWithDispatch({
      component: ElementMenu,
      props: {
        element: {
          elementsId: "1",
          checked: true,
          id: "7",
          irrAddonGroups: [{ id: "3", allowMultipliers: true }],
          name: "Element 2",
          comment: "Testing comment"
        }
      },
      initialAppValues: { caseReview: {} },
      apolloMocks: mocks
    });

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const commentsInput = instance.findByType("textarea");

    act(() =>
      commentsInput.props.onChange({
        target: { name: "comment", value: "Testing comment" }
      })
    );

    expect(dispatch).toHaveBeenCalledWith(
      updateElements({
        elementsId: "1",
        irrElementId: "7",
        name: "comment",
        value: "Testing comment"
      })
    );
  });
});
