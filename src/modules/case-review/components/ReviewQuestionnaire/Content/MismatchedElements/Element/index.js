import { Checkbox } from "@q-centrix/q-components-react";
import { and } from "ramda";
import ElementMenu from "./ElementMenu";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
const Element = props => {
  const { element, readOnly } = props;
  const {
    onCheckboxChange,
    onChange,
    onElementCorrectedChange,
    errorRequired
  } = useComponentLogic(props);

  return (
    <>
      <div className="tw-mx-8 tw-my-4 tw-flex tw-flex-col">
        <div className="tw-flex tw-justify-between">
          <Checkbox
            name={element.id}
            label={element.name}
            onChange={onCheckboxChange}
            checked={element.checked && !element.delete}
            disabled={readOnly}
          />
          {and(
            and(element.allowMultiplier, element.checked),
            !element.delete
          ) && (
            <div className="tw-ml-5 tw-flex tw-flex-col">
              <div className="tw-justify-middle tw-flex tw-flex-row">
                <input
                  type="number"
                  name="multiplier"
                  value={element.multiplier || ""}
                  onChange={onChange}
                  disabled={readOnly}
                  min={0}
                  className={
                    element.error
                      ? "tw-flex tw-h-10 tw-w-[88px] tw-flex-row tw-rounded tw-border tw-border-error-600 tw-px-4 tw-text-sm"
                      : "tw-flex tw-h-10 tw-w-[88px] tw-flex-row tw-rounded tw-border tw-border-black-29 tw-px-4 tw-text-sm"
                  }
                />
                <p className="tw-mt-2.5 tw-pl-2.5 tw-text-xs tw-font-semibold tw-text-black-70">
                  times
                </p>
              </div>
            </div>
          )}

          {and(element.checked, !element.delete) && (
            <Checkbox
              name={element.name}
              label="Element corrected"
              onChange={onElementCorrectedChange}
              checked={element.corrected}
            />
          )}
        </div>

        <div className="tw-flex tw-flex-row tw-justify-end">
          {element.error && (
            <span className="tw-mt-2.5 tw-w-[300px] tw-pr-5 tw-text-xs tw-font-normal tw-italic tw-text-error-700">
              {element.error}
            </span>
          )}
        </div>

        {and(!element.delete, element.checked) && (
          <ElementMenu
            element={element}
            isOpen={element.mismatch}
            disabled={readOnly}
            errorRequired={errorRequired}
          />
        )}
      </div>
    </>
  );
};

export default Element;
