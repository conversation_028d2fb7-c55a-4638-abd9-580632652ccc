import { act, create } from "react-test-renderer";
import { decorated, decoratedWithDispatch } from "utils/tests/decorated";
import { updateElements } from "modules/case-review/redux/slice";
import { nth } from "ramda";
import Element from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Checkbox: "Checkbox"
}));
jest.mock("../ElementMenu", () => "ElementMenu");

describe("Element", () => {
  function render(props) {
    return create(
      decorated(
        Element,
        { ...props },
        {},
        {
          caseReview: {
            addonGroups: [
              {
                id: "1",
                multiplier: 44,
                irrAddonGroup: {
                  id: "1",
                  name: "Testing",
                  allowMultipliers: false
                }
              },
              {
                id: "2",
                multiplier: 1,
                irrAddonGroup: {
                  id: "3",
                  name: "Testing Addon Group",
                  allowMultipliers: true
                }
              }
            ],
            errors: []
          }
        }
      )
    );
  }

  test("it renders component", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        name: "Element 2",
        allowMultiplier: true
      },
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it tests dispatch for Checked checkbox", () => {
    const { component, dispatch } = decoratedWithDispatch(
      Element,
      {
        element: {
          elementsId: "1",
          checked: true,
          id: "7",
          irrAddonGroups: [{ id: "3", allowMultipliers: true }],
          name: "Element 2"
        },
        readOnly: false
      },
      {
        caseReview: {
          errors: []
        }
      }
    );

    const mountedComponent = create(component);
    const instance = mountedComponent.root;

    const [checkedButton] = instance.findAllByType("Checkbox");

    act(() =>
      checkedButton.props.onChange({
        target: { checked: true, value: true }
      })
    );

    expect(dispatch).toHaveBeenCalledWith(
      updateElements({
        elementsId: "1",
        irrElementId: "7",
        name: "delete",
        checked: true,
        value: false
      })
    );
  });

  test("it tests dispatch for onChange function", () => {
    const { component, dispatch } = decoratedWithDispatch(
      Element,
      {
        element: {
          elementsId: "1",
          checked: true,
          id: "7",
          irrAddonGroups: [{ id: "3", allowMultipliers: true }],
          name: "Element 2"
        },
        readOnly: false
      },
      {
        caseReview: {
          errors: []
        }
      }
    );

    const mountedComponent = create(component);
    const instance = mountedComponent.root;
    const checkedButton = nth(1, instance.findAllByType("Checkbox"));

    act(() =>
      checkedButton.props.onChange({
        target: { name: "corrected", value: true }
      })
    );

    expect(dispatch).toHaveBeenCalledWith(
      updateElements({
        elementsId: "1",
        irrElementId: "7",
        name: "corrected",
        value: true
      })
    );
  });

  test("it renders component disabled", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        name: "Element 2",
        allowMultiplier: true
      },
      readOnly: true
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component without multiplier", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: false }],
        name: "Element 2"
      },
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with multiplier error", () => {
    const component = render({
      element: {
        elementsId: "1",
        checked: true,
        id: "7",
        irrAddonGroups: [{ id: "3", allowMultipliers: true }],
        name: "Element 2",
        allowMultiplier: true,
        error: "Elements multiplier must be greater than or equal to 1"
      },
      readOnly: false
    });

    expect(component).toMatchSnapshot();
  });
});
