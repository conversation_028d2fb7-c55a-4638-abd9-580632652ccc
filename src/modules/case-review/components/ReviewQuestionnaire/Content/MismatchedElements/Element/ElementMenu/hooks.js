import { useCallback, useState } from "react";
import { useDispatch } from "react-redux";
import { updateElements, removeError } from "modules/case-review/redux/slice";
import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_CATEGORIES_DATA } from "modules/case-review/graphql/query";
import { applySpec, map, prop, propEq, find } from "ramda";
import determineInputValueByType from "utils/fp/determineInputValueByType";

export const useComponentLogic = props => {
  const { element } = props;
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [selCategory, setSelCategory] = useState({});

  const { loading, error } = useQuery(GET_CASE_REVIEW_CATEGORIES_DATA, {
    onCompleted: (data = []) => {
      const { caseReviewCategories = [] } = data;

      const options = map(
        applySpec({
          label: prop("name"),
          value: prop("id")
        }),
        caseReviewCategories
      );

      setCategoryOptions(options);

      if (element.caseReviewCategory) {
        const initialCategory = find(
          propEq("value", element.caseReviewCategory.id),
          options
        );

        setSelCategory(initialCategory);
      }
    },
    // eslint-disable-next-line no-empty-function
    onError: () => {}
  });

  const dispatch = useDispatch();

  // will need to refactor to use one dispatch
  const handleInput = useCallback(
    value => {
      setSelCategory({
        label: value.label,
        value: value.value
      });
      dispatch(removeError({ id: element.id }));
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "caseReviewCategory",
          value: { id: value.value, name: value.label }
        })
      );
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "caseReviewCategoryId",
          value: value.value
        })
      );
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "irrElementId",
          value: element.id
        })
      );
    },
    [dispatch, element]
  );

  const handleComments = useCallback(
    e => {
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
      dispatch(
        updateElements({
          elementsId: element.elementsId,
          irrElementId: element.id,
          name: "irrElementId",
          value: element.id
        })
      );
    },
    [dispatch, element]
  );

  return {
    loading,
    error,
    selCategory,
    categoryOptions,
    handleInput,
    handleComments
  };
};
