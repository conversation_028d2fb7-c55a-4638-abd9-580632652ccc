// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Element it renders component 1`] = `
<div
  className="tw-mx-8 tw-my-4 tw-flex tw-flex-col"
>
  <div
    className="tw-flex tw-justify-between"
  >
    <Checkbox
      checked={true}
      disabled={false}
      label="Element 2"
      name="7"
      onChange={[Function]}
    />
    <div
      className="tw-ml-5 tw-flex tw-flex-col"
    >
      <div
        className="tw-justify-middle tw-flex tw-flex-row"
      >
        <input
          className="tw-flex tw-h-10 tw-w-[88px] tw-flex-row tw-rounded tw-border tw-border-black-29 tw-px-4 tw-text-sm"
          disabled={false}
          min={0}
          name="multiplier"
          onChange={[Function]}
          type="number"
          value=""
        />
        <p
          className="tw-mt-2.5 tw-pl-2.5 tw-text-xs tw-font-semibold tw-text-black-70"
        >
          times
        </p>
      </div>
    </div>
    <Checkbox
      label="Element corrected"
      name="Element 2"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-end"
  />
  <ElementMenu
    disabled={false}
    element={
      Object {
        "allowMultiplier": true,
        "checked": true,
        "elementsId": "1",
        "id": "7",
        "irrAddonGroups": Array [
          Object {
            "allowMultipliers": true,
            "id": "3",
          },
        ],
        "name": "Element 2",
      }
    }
    errorRequired={false}
  />
</div>
`;

exports[`Element it renders component disabled 1`] = `
<div
  className="tw-mx-8 tw-my-4 tw-flex tw-flex-col"
>
  <div
    className="tw-flex tw-justify-between"
  >
    <Checkbox
      checked={true}
      disabled={true}
      label="Element 2"
      name="7"
      onChange={[Function]}
    />
    <div
      className="tw-ml-5 tw-flex tw-flex-col"
    >
      <div
        className="tw-justify-middle tw-flex tw-flex-row"
      >
        <input
          className="tw-flex tw-h-10 tw-w-[88px] tw-flex-row tw-rounded tw-border tw-border-black-29 tw-px-4 tw-text-sm"
          disabled={true}
          min={0}
          name="multiplier"
          onChange={[Function]}
          type="number"
          value=""
        />
        <p
          className="tw-mt-2.5 tw-pl-2.5 tw-text-xs tw-font-semibold tw-text-black-70"
        >
          times
        </p>
      </div>
    </div>
    <Checkbox
      label="Element corrected"
      name="Element 2"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-end"
  />
  <ElementMenu
    disabled={true}
    element={
      Object {
        "allowMultiplier": true,
        "checked": true,
        "elementsId": "1",
        "id": "7",
        "irrAddonGroups": Array [
          Object {
            "allowMultipliers": true,
            "id": "3",
          },
        ],
        "name": "Element 2",
      }
    }
    errorRequired={false}
  />
</div>
`;

exports[`Element it renders component with multiplier error 1`] = `
<div
  className="tw-mx-8 tw-my-4 tw-flex tw-flex-col"
>
  <div
    className="tw-flex tw-justify-between"
  >
    <Checkbox
      checked={true}
      disabled={false}
      label="Element 2"
      name="7"
      onChange={[Function]}
    />
    <div
      className="tw-ml-5 tw-flex tw-flex-col"
    >
      <div
        className="tw-justify-middle tw-flex tw-flex-row"
      >
        <input
          className="tw-flex tw-h-10 tw-w-[88px] tw-flex-row tw-rounded tw-border tw-border-error-600 tw-px-4 tw-text-sm"
          disabled={false}
          min={0}
          name="multiplier"
          onChange={[Function]}
          type="number"
          value=""
        />
        <p
          className="tw-mt-2.5 tw-pl-2.5 tw-text-xs tw-font-semibold tw-text-black-70"
        >
          times
        </p>
      </div>
    </div>
    <Checkbox
      label="Element corrected"
      name="Element 2"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-end"
  >
    <span
      className="tw-mt-2.5 tw-w-[300px] tw-pr-5 tw-text-xs tw-font-normal tw-italic tw-text-error-700"
    >
      Elements multiplier must be greater than or equal to 1
    </span>
  </div>
  <ElementMenu
    disabled={false}
    element={
      Object {
        "allowMultiplier": true,
        "checked": true,
        "elementsId": "1",
        "error": "Elements multiplier must be greater than or equal to 1",
        "id": "7",
        "irrAddonGroups": Array [
          Object {
            "allowMultipliers": true,
            "id": "3",
          },
        ],
        "name": "Element 2",
      }
    }
    errorRequired={false}
  />
</div>
`;

exports[`Element it renders component without multiplier 1`] = `
<div
  className="tw-mx-8 tw-my-4 tw-flex tw-flex-col"
>
  <div
    className="tw-flex tw-justify-between"
  >
    <Checkbox
      checked={true}
      disabled={false}
      label="Element 2"
      name="7"
      onChange={[Function]}
    />
    <Checkbox
      label="Element corrected"
      name="Element 2"
      onChange={[Function]}
    />
  </div>
  <div
    className="tw-flex tw-flex-row tw-justify-end"
  />
  <ElementMenu
    disabled={false}
    element={
      Object {
        "checked": true,
        "elementsId": "1",
        "id": "7",
        "irrAddonGroups": Array [
          Object {
            "allowMultipliers": false,
            "id": "3",
          },
        ],
        "name": "Element 2",
      }
    }
    errorRequired={false}
  />
</div>
`;
