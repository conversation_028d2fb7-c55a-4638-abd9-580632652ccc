import { InputDropdown } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";

const CustomDropdownIndicator = () => (
  <div className="custom-dropdown-indicator">
    <i className="fa-solid fa-caret-down" />
  </div>
);

export const selectComponents = {
  DropdownIndicator: CustomDropdownIndicator,
  IndicatorSeparator: () => null
};

const ElementMenu = props => {
  const { element, disabled, errorRequired } = props;
  const { categoryOptions, selCategory, handleInput, handleComments } =
    useComponentLogic(props);

  return (
    <div className="tw-ml-6 tw-mt-2.5 tw-flex tw-flex-col tw-gap-5 tw-rounded-[5px] tw-bg-gray-50 tw-p-2.5">
      <InputDropdown
        options={categoryOptions}
        components={selectComponents}
        placeholder="Mismatched category"
        value={selCategory}
        onChange={handleInput}
        disabled={disabled}
        error={errorRequired}
        errorText="this is required"
      />
      <textarea
        name="comment"
        placeholder="Comment or reason for mismatch"
        className="tw-rounded tw-border tw-border-gray-700 tw-px-4 tw-py-[9px] tw-text-sm tw-font-normal placeholder:tw-text-black-29"
        onChange={handleComments}
        value={element.comment}
        disabled={disabled}
      />
    </div>
  );
};

export default ElementMenu;
