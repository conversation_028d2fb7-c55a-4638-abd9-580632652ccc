import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import MismatchedElements from "..";
import mocks from "../../ElementsReviewed/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  Spinner: "Spinner"
}));

jest.mock("../Element", () => "Element");

describe("MismatchedElements", () => {
  function render(questionnaireType = "critical") {
    return create(
      decoratedApollo({
        component: MismatchedElements,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: "1",
              questionnaireType,
              score: 0.67,
              mismatchCount: 1,
              criticalElementCount: 3,
              matchedCount: 2,
              updatedAt: "2022-12-02 00:00:01",
              completedAt: null,
              availableIrrAddonGroups: [
                {
                  __typename: "IrrAddonGroup",
                  id: "1",
                  name: "Test",
                  allowMultipliers: true
                },
                {
                  __typename: "IrrAddonGroup",
                  id: "2",
                  name: "Test Group 2",
                  allowMultipliers: false
                }
              ],
              facility: {
                __typename: "Facility",
                id: "1",
                name: "Large Medical Center"
              },
              readOnly: false,
              abstractor: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              initiator: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              owner: {
                __typename: "User",
                id: "1",
                fullName: "Russell Reas"
              },
              patient: {
                __typename: "Patient",
                firstName: "Madelyn Effertz",
                lastName: "Grady Walker",
                mrn: "**********"
              },
              visit: {
                __typename: "Visit",
                number: "73943259"
              },
              task: {
                __typename: "Task",
                case: {
                  __typename: "Cases",
                  id: "49"
                }
              },
              caseType: {
                __typename: "Case",
                id: "22",
                name: "CDiff"
              },
              correctCaseType: false,
              originalCaseType: {
                __typename: "Case",
                id: "22",
                name: "CDiff"
              },
              addonGroups: [],
              elements: [
                {
                  id: 1,
                  irrElement: {
                    id: "5",
                    name: "Saved to BE Element"
                  },
                  irrElementId: "5",
                  mismatch: true,
                  multiplier: 0,
                  corrected: true,
                  comment: "Test comment for critical review",
                  caseReviewCategory: {
                    id: 1,
                    name: "Location Error"
                  },
                  delete: false,
                  checked: true
                }
              ]
            }
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component while loading", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders null when reason review is specific", () => {
    const component = render("specific");

    expect(component).toMatchSnapshot();
  });
});
