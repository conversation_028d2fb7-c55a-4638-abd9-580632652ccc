// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`MismatchedElements it renders component correctly 1`] = `
<CardWithHeader
  cardClasses="border-10px full-height"
  headerContent={
    <p
      className="tw-m-5"
    >
      Mismatched elements
    </p>
  }
>
  <div>
    <Element
      element={
        Object {
          "__typename": "IrrElement",
          "allowMultiplier": false,
          "checked": false,
          "id": "1",
          "irrAddonGroups": Array [],
          "irrElement": Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "id": "1",
            "irrAddonGroups": Array [],
            "name": "Legend of Hungry Demon",
          },
          "irrElementId": "1",
          "name": "Legend of Hungry Demon",
        }
      }
      loading={false}
      readOnly={false}
    />
    <Element
      element={
        Object {
          "__typename": "IrrElement",
          "allowMultiplier": false,
          "checked": false,
          "id": "2",
          "irrAddonGroups": Array [],
          "irrElement": Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "id": "2",
            "irrAddonGroups": Array [],
            "name": "Planet of the Ultra Blow",
          },
          "irrElementId": "2",
          "name": "Planet of the Ultra Blow",
        }
      }
      loading={false}
      readOnly={false}
    />
    <Element
      element={
        Object {
          "__typename": "IrrElement",
          "allowMultiplier": true,
          "checked": false,
          "id": "3",
          "irrAddonGroups": Array [
            Object {
              "__typename": "IrrAddonGroup",
              "allowMultipliers": true,
              "id": "1",
            },
          ],
          "irrElement": Object {
            "__typename": "IrrElement",
            "allowMultiplier": true,
            "id": "3",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "name": "Test Element 1 with Multiplier",
          },
          "irrElementId": "3",
          "name": "Test Element 1 with Multiplier",
        }
      }
      loading={false}
      readOnly={false}
    />
    <Element
      element={
        Object {
          "__typename": "IrrElement",
          "allowMultiplier": false,
          "checked": false,
          "id": "4",
          "irrAddonGroups": Array [
            Object {
              "__typename": "IrrAddonGroup",
              "allowMultipliers": true,
              "id": "1",
            },
          ],
          "irrElement": Object {
            "__typename": "IrrElement",
            "allowMultiplier": false,
            "id": "4",
            "irrAddonGroups": Array [
              Object {
                "__typename": "IrrAddonGroup",
                "allowMultipliers": true,
                "id": "1",
              },
            ],
            "name": "Test Element 1 no Multiplier",
          },
          "irrElementId": "4",
          "name": "Test Element 1 no Multiplier",
        }
      }
      loading={false}
      readOnly={false}
    />
    <Element
      element={
        Object {
          "__typename": "IrrElement",
          "allowMultiplier": false,
          "caseReviewCategory": Object {
            "id": 1,
            "name": "Location Error",
          },
          "caseReviewCategoryId": 1,
          "checked": true,
          "comment": "Test comment for critical review",
          "corrected": true,
          "delete": false,
          "elementsId": 1,
          "error": undefined,
          "id": "5",
          "irrAddonGroups": Array [],
          "irrElement": Object {
            "id": "5",
            "name": "Saved to BE Element",
          },
          "irrElementId": Object {},
          "mismatch": true,
          "multiplier": 0,
          "name": "Saved to BE Element",
        }
      }
      loading={false}
      readOnly={false}
    />
  </div>
</CardWithHeader>
`;

exports[`MismatchedElements it renders component while loading 1`] = `<Spinner />`;

exports[`MismatchedElements it renders null when reason review is specific 1`] = `null`;
