import { InputDropdown, RadioButton } from "@q-centrix/q-components-react";
import { AnimatePresence, motion } from "framer-motion";
import { useCallback } from "react";
import CaseTypeWarningModal from "./CaseTypeWarningModal";
import { useComponentLogic } from "./hooks";

const InitialCaseTypeQuestion = props => {
  const {
    handleInput,
    disabled,
    selectComponents,
    selectedCaseType,
    correctCaseType
  } = props;

  const {
    modal,
    setModal,
    result,
    caseTypeValue,
    caseTypesLoading,
    caseTypeOptions,
    handleCaseTypeChange,
    handleSelectChange
  } = useComponentLogic(props);

  return (
    <>
      <CaseTypeWarningModal
        caseTypeValue={caseTypeValue}
        modal={modal}
        handleCaseTypeChange={handleCaseTypeChange}
        closeModal={useCallback(() => setModal(false), [])}
      />
      <div className="question-container">
        <label>
          Was the case type initially chosen by the abstractor correct?
        </label>
        <RadioButton
          name="abstractorCorrect"
          label="Yes"
          value="Yes"
          checked={result.abstractorCorrect === "Yes"}
          onChange={handleInput}
          disabled={disabled}
        />
        <RadioButton
          name="abstractorCorrect"
          label="No"
          value="No"
          checked={result.abstractorCorrect === "No"}
          onChange={handleInput}
          disabled={disabled}
        />
      </div>
      <AnimatePresence>
        {result.abstractorCorrect === "No" && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 104 }}
            exit={{
              height: 0,
              overflow: "hidden",
              transition: { duration: 0.2 }
            }}
            className="motion-question-container"
          >
            <div className="question-container indent-1">
              <label>Initial case type chosen by the abstractor</label>
              <span>{selectedCaseType?.name}</span>
            </div>
            <div className="question-container indent-1">
              <label>Correct case type</label>
              <InputDropdown
                name="correctCaseType"
                options={caseTypeOptions}
                components={selectComponents}
                placeholder="[Correct case type]"
                value={correctCaseType}
                onChange={handleSelectChange}
                loading={caseTypesLoading}
                iconClass="fa-solid fa-chevron-down"
                disabled={disabled}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default InitialCaseTypeQuestion;
