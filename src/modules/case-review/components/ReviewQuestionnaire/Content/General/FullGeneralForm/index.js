import { selectComponents } from "../../MismatchedElements/Element/ElementMenu";
import { useComponentLogic } from "./hooks";
import "modules/case-review/styles/general.scss";
import Addons from "../Addons";
import CompleteReviewQuestion from "./CompleteReviewQuestion";
import InitialCaseTypeQuestion from "./InitialCaseTypeQuestion";
import ReviewCategoryQuestion from "./ReviewCategoryQuestion";

export const FullGeneralForm = () => {
  const {
    result,
    caseTypesLoading,
    caseTypeOptions,
    correctCaseType,
    selectedCaseType,
    handleInput,
    handleSelectChange,
    setSelectedCaseType
  } = useComponentLogic();

  return (
    <div className="general-form">
      <CompleteReviewQuestion
        canCompleteReview={result?.canCompleteReview}
        handleInput={handleInput}
        incompleteReasonValue={result?.incompleteReasonValue}
        handleSelectChange={handleSelectChange}
        disabled={result.readOnly}
      />
      {result?.canCompleteReview === "Yes" && (
        <>
          <InitialCaseTypeQuestion
            result={result}
            disabled={result.readOnly}
            handleInput={handleInput}
            selectedCaseType={selectedCaseType}
            caseTypeOptions={caseTypeOptions}
            selectComponents={selectComponents}
            correctCaseType={correctCaseType}
            handleSelectChange={handleSelectChange}
            caseTypesLoading={caseTypesLoading}
            onSetSelectedCaseType={setSelectedCaseType}
          />
          <ReviewCategoryQuestion
            result={result}
            disabled={result.readOnly}
            handleInput={handleInput}
          />
          <Addons
            addons={selectedCaseType?.addons}
            disabled={result.readOnly}
          />
        </>
      )}
    </div>
  );
};

export default FullGeneralForm;
