// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ReviewCategoryQuestion it renders component with reasonReview set to Initial 1`] = `
<div
  className="question-container"
>
  <label>
    Full Review category
  </label>
  <RadioButton
    checked="Initial"
    disabled={true}
    label="Initial"
    name="reasonReview"
    onChange={[MockFunction]}
    value="Initial"
  />
</div>
`;

exports[`ReviewCategoryQuestion it renders component with reasonReview set to Selected 1`] = `
<div
  className="question-container"
>
  <label>
    Full Review category
  </label>
  <RadioButton
    checked="Selected"
    disabled={true}
    label="Selected"
    name="reasonReview"
    onChange={[MockFunction]}
    value="Selected"
  />
</div>
`;
