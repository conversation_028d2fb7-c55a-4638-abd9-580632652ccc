import { RadioButton } from "@q-centrix/q-components-react";

const ReviewCategoryQuestion = ({ result, handleInput, disabled }) => (
  <div className="question-container">
    <label>Full Review category</label>
    {result?.qCentrixEmployee &&
    result?.caseCompletedThru !== "monthlyIrrProcess" ? (
      <>
        <RadioButton
          name="reasonReview"
          label="Initial review"
          value="Initial"
          checked={result.reasonReview === "Initial"}
          onChange={handleInput}
          disabled={disabled}
        />
        <RadioButton
          name="reasonReview"
          label="Selected review"
          value="Selected"
          checked={result.reasonReview === "Selected"}
          onChange={handleInput}
          disabled={disabled}
        />
      </>
    ) : (
      <RadioButton
        name="reasonReview"
        label={result.reasonReview}
        value={result.reasonReview}
        checked={result.reasonReview}
        onChange={handleInput}
        disabled
      />
    )}
  </div>
);

export default ReviewCategoryQuestion;
