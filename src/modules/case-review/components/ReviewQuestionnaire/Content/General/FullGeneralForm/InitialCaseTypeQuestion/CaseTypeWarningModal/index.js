import { Button, ConfirmationModal } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import "styles/confirmation-modal.scss";

const CaseTypeWarningModal = ({
  modal,
  closeModal,
  handleCaseTypeChange,
  caseTypeValue
}) => {
  const { handleSave } = useComponentLogic({
    closeModal,
    handleCaseTypeChange,
    caseTypeValue
  });

  return (
    <ConfirmationModal
      title="Confirm Action"
      isDanger={false}
      isOpen={modal}
      appElement=".main"
    >
      <div className="modal-content">
        <p className="modal-paragraph">
          By changing the case type, all data will be removed from the
          questionnaire and emailed to you. Do you want to continue?
        </p>
        <div className="button-container">
          <Button outline customStyle="cancel-button" onClick={closeModal}>
            <i className="fa-regular fa-x" />
            Cancel
          </Button>
          <Button customStyle="success-button" onClick={() => handleSave()}>
            <i className="fa-regular fa-check" />
            Confirm
          </Button>
        </div>
      </div>
    </ConfirmationModal>
  );
};

export default CaseTypeWarningModal;
