// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FullGeneralForm it renders component correctly 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Can you complete the Full Review?
    </label>
    <RadioButton
      checked={true}
      name="canCompleteReview"
      onChange={[Function]}
      title="Yes"
      value="Yes"
    />
    <RadioButton
      checked={false}
      name="canCompleteReview"
      onChange={[Function]}
      title="No"
      value="No"
    />
  </div>
  <CaseTypeWarningModal
    closeModal={[Function]}
    handleCaseTypeChange={[Function]}
    modal={false}
  />
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="abstractorCorrect"
      onChange={[Function]}
      value="Yes"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="No"
      name="abstractorCorrect"
      onChange={[Function]}
      value="No"
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Full Review category
    </label>
    <RadioButton
      checked="Initial"
      disabled={true}
      label="Initial"
      name="reasonReview"
      onChange={[Function]}
      value="Initial"
    />
  </div>
  <Addons
    disabled={false}
  />
</div>
`;

exports[`FullGeneralForm it renders component correctly when disabled 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Can you complete the Full Review?
    </label>
    <RadioButton
      checked={true}
      name="canCompleteReview"
      onChange={[Function]}
      title="Yes"
      value="Yes"
    />
    <RadioButton
      checked={false}
      name="canCompleteReview"
      onChange={[Function]}
      title="No"
      value="No"
    />
  </div>
  <CaseTypeWarningModal
    closeModal={[Function]}
    handleCaseTypeChange={[Function]}
    modal={false}
  />
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={true}
      label="Yes"
      name="abstractorCorrect"
      onChange={[Function]}
      value="Yes"
    />
    <RadioButton
      checked={false}
      disabled={true}
      label="No"
      name="abstractorCorrect"
      onChange={[Function]}
      value="No"
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Full Review category
    </label>
    <RadioButton
      checked="Initial"
      disabled={true}
      label="Initial"
      name="reasonReview"
      onChange={[Function]}
      value="Initial"
    />
  </div>
  <Addons
    disabled={true}
  />
</div>
`;

exports[`FullGeneralForm it renders component with Loading in drop downs 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Can you complete the Full Review?
    </label>
    <RadioButton
      checked={true}
      name="canCompleteReview"
      onChange={[Function]}
      title="Yes"
      value="Yes"
    />
    <RadioButton
      checked={false}
      name="canCompleteReview"
      onChange={[Function]}
      title="No"
      value="No"
    />
  </div>
  <CaseTypeWarningModal
    closeModal={[Function]}
    handleCaseTypeChange={[Function]}
    modal={false}
  />
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="abstractorCorrect"
      onChange={[Function]}
      value="Yes"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="No"
      name="abstractorCorrect"
      onChange={[Function]}
      value="No"
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Full Review category
    </label>
    <RadioButton
      checked="Initial"
      disabled={true}
      label="Initial"
      name="reasonReview"
      onChange={[Function]}
      value="Initial"
    />
  </div>
  <Addons
    disabled={false}
  />
</div>
`;
