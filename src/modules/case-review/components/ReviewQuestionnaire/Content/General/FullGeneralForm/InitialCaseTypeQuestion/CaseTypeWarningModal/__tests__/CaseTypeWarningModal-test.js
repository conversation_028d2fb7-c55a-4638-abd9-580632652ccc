import { create } from "react-test-renderer";
import CaseTypeWarningModal from "..";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import mocks from "../../../../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  ConfirmationModal: "ConfirmationModal",
  Button: "Button"
}));

describe("FullGeneralForm", () => {
  function render(isOwner = true) {
    return create(
      decoratedApollo({
        component: CaseTypeWarningModal,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: 1,
              type: "targeted",
              ownerId: "1",
              reviewerId: "1",
              reviewDate: "2022-01-02",
              mrn: "12345",
              visit: "12",
              caseTypeId: "1",
              correctCaseTypeId: null,
              abstractorCorrect: "",
              reasonReview: "Initial",
              addons: [
                {
                  id: 1,
                  addonId: 1,
                  value: true,
                  multiplierValue: 2
                }
              ]
            },
            isOwner
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with Loading in drop downs", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
