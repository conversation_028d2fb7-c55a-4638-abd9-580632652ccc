import { create } from "react-test-renderer";
import InitialCaseTypeQuestion from "..";
import { decorated<PERSON>pollo } from "utils/tests/decorated";
import { selectComponents } from "modules/case-review/components/ReviewQuestionnaire/Content/MismatchedElements/Element/ElementMenu";
import mocks from "../../../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  RadioButton: "RadioButton",
  InputDropdown: "InputDropdown"
}));

jest.mock("../CaseTypeWarningModal", () => "CaseTypeWarningModal");

const properties = {
  handleInput: jest.fn(),
  disabled: false,
  selectedCaseType: { name: "Type 1" },
  selectComponents,
  correctCaseType: { label: "Type 1", value: "Type 1" },
  onSetSelectedCaseType: jest.fn()
};

const applicationValues = {
  caseReview: {
    review: {
      id: 1,
      type: "full",
      ownerId: "1",
      reviewerId: "1",
      reviewDate: "2022-01-02",
      mrn: "12345",
      visit: "12",
      caseTypeId: "1",
      correctCaseTypeId: null,
      canCompleteReview: "Yes",
      caseCompletedThru: "",
      abstractorCorrect: "Yes",
      reasonReview: "Initial",
      addons: [
        {
          id: 1,
          addonId: 1,
          value: true,
          multiplierValue: 2
        }
      ]
    },
    isOwner: true
  }
};

describe("InitialCaseTypeQuestion", () => {
  function render(appValues = applicationValues) {
    return create(
      decoratedApollo({
        component: InitialCaseTypeQuestion,
        props: properties,
        initialValues: {},
        initialAppValues: appValues,
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with abstractorCorrect yes", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with abstractorCorrect no", () => {
    const component = render({
      ...applicationValues,
      caseReview: { review: { abstractorCorrect: "No" } }
    });

    expect(component).toMatchSnapshot();
  });
});
