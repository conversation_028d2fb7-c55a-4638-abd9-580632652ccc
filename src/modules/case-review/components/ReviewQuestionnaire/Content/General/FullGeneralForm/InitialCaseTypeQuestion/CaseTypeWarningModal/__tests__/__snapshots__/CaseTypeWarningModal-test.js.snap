// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FullGeneralForm it renders component with Loading in drop downs 1`] = `
<ConfirmationModal
  appElement=".main"
  isDanger={false}
  title="Confirm Action"
>
  <div
    className="modal-content"
  >
    <p
      className="modal-paragraph"
    >
      By changing the case type, all data will be removed from the questionnaire and emailed to you. Do you want to continue?
    </p>
    <div
      className="button-container"
    >
      <Button
        customStyle="cancel-button"
        outline={true}
      >
        <i
          className="fa-regular fa-x"
        />
        Cancel
      </Button>
      <Button
        customStyle="success-button"
        onClick={[Function]}
      >
        <i
          className="fa-regular fa-check"
        />
        Confirm
      </Button>
    </div>
  </div>
</ConfirmationModal>
`;
