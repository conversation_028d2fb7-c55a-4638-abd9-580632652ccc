import { useDispatch, useSelector } from "react-redux";
import CaseReview from "modules/case-review/redux/selectors";
import { setGeneralValue } from "modules/case-review/redux/slice";
import { cond, equals } from "ramda";
import { useCallback, useState } from "react";
import determineInputValueByType from "utils/fp/determineInputValueByType";

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const [selectedCaseType, setSelectedCaseType] = useState(null);

  const result = useSelector(state => CaseReview.getGeneral(state));
  const handleSelectChange = useCallback(
    (value, name) => {
      cond([
        [
          equals("user"),
          () => {
            if (value) {
              dispatch(
                setGeneralValue({ name: "reviewerId", value: value.value })
              );
            }
          }
        ],
        [
          equals("incompleteReasonValue"),
          () => {
            if (value) {
              dispatch(
                setGeneralValue({
                  name: "incompleteReasonValue",
                  value
                })
              );
            }
          }
        ]
      ])(name);
    },
    [dispatch]
  );
  const handleInput = useCallback(
    e => {
      dispatch(
        setGeneralValue({
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
    },
    [dispatch]
  );

  return {
    result,
    selectedCaseType,
    handleInput,
    handleSelectChange,
    setSelectedCaseType
  };
};
