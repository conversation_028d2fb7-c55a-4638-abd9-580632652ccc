import { create, act } from "react-test-renderer";
import wait from "waait";
import { decoratedA<PERSON>lo } from "utils/tests/decorated";
import CompleteReviewQuestion from "..";
import mocks from "../../../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  RadioButton: "RadioButton",
  InputDropdown: "InputDropdown"
}));

describe("CompleteReviewQuestion", () => {
  function render(props) {
    return create(
      decoratedApollo({
        component: CompleteReviewQuestion,
        props,
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: 1,
              type: "full",
              ownerId: "1",
              reviewerId: "1",
              reviewDate: "2022-01-02",
              mrn: "12345",
              visit: "12",
              caseTypeId: "1",
              correctCaseTypeId: null,
              canCompleteReview: "Yes",
              caseCompletedThru: "",
              abstractorCorrect: "Yes",
              reasonReview: "Initial",
              addons: [
                {
                  id: 1,
                  addonId: 1,
                  value: true,
                  multiplierValue: 2
                }
              ]
            },
            isOwner: true
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with canCompleteReview yes", async () => {
    const component = render({
      handleInput: jest.fn(),
      canCompleteReview: "Yes",
      canCompleteReviewComment: ""
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with canCompleteReview no", async () => {
    const component = render({
      handleInput: jest.fn(),
      canCompleteReview: "No",
      canCompleteReviewComment: ""
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with canCompleteReview no and a comment", async () => {
    const component = render({
      handleInput: jest.fn(),
      canCompleteReview: "No",
      canCompleteReviewComment: "Testing this comment."
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
