import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES } from "modules/case-review/graphql/query";
import { useDispatch, useSelector } from "react-redux";
import CaseReview from "modules/case-review/redux/selectors";
import { setGeneralValue } from "modules/case-review/redux/slice";
import { applySpec, find, map, prop, propEq } from "ramda";
import { useCallback, useState } from "react";

export const useComponentLogic = ({ onSetSelectedCaseType }) => {
  const dispatch = useDispatch();
  const [caseTypeOptions, setCaseTypeOptions] = useState([]);
  const [correctCaseType, setCorrectCaseType] = useState(null);
  const [modal, setModal] = useState(false);
  const [caseTypeValue, setCaseTypeValue] = useState();

  const result = useSelector(state => CaseReview.getGeneral(state));
  const { data: { caseTypes } = { caseTypes: [] }, loading: caseTypesLoading } =
    useQuery(GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES, {
      variables: { questionnaireId: result.id },
      onCompleted: (data = {}) => {
        const { caseReviewQuestionnaireCaseTypes = [] } = data;
        const options = map(
          applySpec({
            label: prop("name"),
            value: prop("id")
          }),
          caseReviewQuestionnaireCaseTypes
        );
        const selCaseType = find(
          propEq("id", result.caseTypeId),
          caseReviewQuestionnaireCaseTypes
        );
        const selCorrectCaseType = find(
          propEq("value", result.correctCaseTypeId),
          options
        );

        setCaseTypeOptions(options);
        onSetSelectedCaseType(selCaseType);
        setCorrectCaseType(selCorrectCaseType || null);
      }
    });
  const handleCaseTypeChange = useCallback(
    value => {
      dispatch(setGeneralValue({ name: "caseTypeId", value: value.value }));
      dispatch(
        setGeneralValue({
          name: "correctCaseTypeId",
          value: value.value
        })
      );
      setCorrectCaseType(value);
      const selCaseType = find(propEq("id", value.value), caseTypes);

      onSetSelectedCaseType(selCaseType);
    },
    [caseTypes]
  );
  const handleSelectChange = useCallback(value => {
    if (value) {
      setModal(true);
      setCaseTypeValue(value);
    }
  }, []);

  return {
    result,
    caseTypesLoading,
    caseTypeOptions,
    correctCaseType,
    modal,
    caseTypeValue,
    setModal,
    handleCaseTypeChange,
    handleSelectChange
  };
};
