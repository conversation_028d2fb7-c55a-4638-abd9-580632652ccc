import { act, create } from "react-test-renderer";
import wait from "waait";
import FullGeneralForm from "..";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import mocks from "../../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  InputDropdown: "InputDropdown",
  RadioButton: "RadioButton"
}));

jest.mock("../../Addons", () => "Addons");
jest.mock(
  "../InitialCaseTypeQuestion/CaseTypeWarningModal",
  () => "CaseTypeWarningModal"
);

describe("FullGeneralForm", () => {
  function render(readOnly = false) {
    return create(
      decoratedApollo({
        component: FullGeneralForm,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: 1,
              type: "full",
              ownerId: "1",
              reviewerId: "1",
              reviewDate: "2022-01-02",
              mrn: "12345",
              visit: "12",
              caseTypeId: "1",
              correctCaseTypeId: null,
              canCompleteReview: "Yes",
              caseCompletedThru: "",
              abstractorCorrect: "",
              reasonReview: "Initial",
              addons: [
                {
                  id: 1,
                  addonId: 1,
                  value: true,
                  multiplierValue: 2
                }
              ],
              readOnly
            }
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with Loading in drop downs", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly when disabled", async () => {
    const component = render(true);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
