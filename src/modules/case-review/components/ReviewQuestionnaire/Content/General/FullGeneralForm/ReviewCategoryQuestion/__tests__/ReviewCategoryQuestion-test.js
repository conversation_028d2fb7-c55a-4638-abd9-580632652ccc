import { create } from "react-test-renderer";
import ReviewCategoryQuestion from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  RadioButton: "RadioButton"
}));

describe("ReviewCategoryQuestion", () => {
  function render(props) {
    return create(<ReviewCategoryQuestion {...props} />);
  }

  test("it renders component with reasonReview set to Initial", () => {
    const component = render({
      handleInput: jest.fn(),
      result: { reasonReview: "Initial" },
      disabled: false
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component with reasonReview set to Selected", () => {
    const component = render({
      handleInput: jest.fn(),
      result: { reasonReview: "Selected" },
      disabled: false
    });

    expect(component).toMatchSnapshot();
  });
});
