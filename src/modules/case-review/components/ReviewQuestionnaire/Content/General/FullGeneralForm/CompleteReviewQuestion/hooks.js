import { useQuery } from "@apollo/client";
import { GET_INCOMPLETE_REASONS } from "modules/case-review/graphql/query";
import { useState } from "react";

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const [incompleteReasonOptions, setIncompleteReasonOptions] = useState([]);

  const { loading: incompleteReasonsLoading } = useQuery(
    GET_INCOMPLETE_REASONS,
    {
      onCompleted: (data = {}) => {
        const { incompleteReasons: reasons = [] } = data;

        setIncompleteReasonOptions(reasons);
      }
    }
  );

  return {
    incompleteReasonOptions,
    incompleteReasonsLoading
  };
};
