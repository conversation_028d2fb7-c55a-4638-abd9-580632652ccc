import { useSelector } from "react-redux";
import { useMutation } from "@apollo/client";
import CaseReview from "modules/case-review/redux/selectors";
import { UPDATE_CASE_REVIEW_CASE_TYPE } from "modules/case-review/graphql/mutation";

export const useComponentLogic = ({
  closeModal,
  handleCaseTypeChange,
  caseTypeValue
}) => {
  const review = useSelector(state => CaseReview.getGeneral(state));

  const [saveReviewWithCaseType] = useMutation(UPDATE_CASE_REVIEW_CASE_TYPE, {
    variables: {
      questionnaireId: review?.id,
      caseTypeId: caseTypeValue,
      initiatorId: review?.initiator?.id,
      correctCaseType: review?.correctCaseType
    },
    onCompleted: () => {
      handleCaseTypeChange(caseTypeValue);
      closeModal();
    }
  });

  return {
    handleSave: saveReviewWithCaseType
  };
};
