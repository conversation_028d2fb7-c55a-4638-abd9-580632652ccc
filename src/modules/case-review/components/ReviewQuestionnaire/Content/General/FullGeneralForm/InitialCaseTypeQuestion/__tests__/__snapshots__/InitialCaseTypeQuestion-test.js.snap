// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InitialCaseTypeQuestion it renders component with abstractorCorrect no 1`] = `
Array [
  <CaseTypeWarningModal
    closeModal={[Function]}
    handleCaseTypeChange={[Function]}
    modal={false}
  />,
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="abstractorCorrect"
      onChange={[MockFunction]}
      value="Yes"
    />
    <RadioButton
      checked={true}
      disabled={false}
      label="No"
      name="abstractorCorrect"
      onChange={[MockFunction]}
      value="No"
    />
  </div>,
  <div
    className="motion-question-container"
    style={
      Object {
        "height": "0px",
      }
    }
  >
    <div
      className="question-container indent-1"
    >
      <label>
        Initial case type chosen by the abstractor
      </label>
      <span>
        Type 1
      </span>
    </div>
    <div
      className="question-container indent-1"
    >
      <label>
        Correct case type
      </label>
      <InputDropdown
        components={
          Object {
            "DropdownIndicator": [Function],
            "IndicatorSeparator": [Function],
          }
        }
        disabled={false}
        iconClass="fa-solid fa-chevron-down"
        loading={true}
        name="correctCaseType"
        onChange={[Function]}
        options={Array []}
        placeholder="[Correct case type]"
        value={
          Object {
            "label": "Type 1",
            "value": "Type 1",
          }
        }
      />
    </div>
  </div>,
]
`;

exports[`InitialCaseTypeQuestion it renders component with abstractorCorrect yes 1`] = `
Array [
  <CaseTypeWarningModal
    closeModal={[Function]}
    handleCaseTypeChange={[Function]}
    modal={false}
  />,
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={true}
      disabled={false}
      label="Yes"
      name="abstractorCorrect"
      onChange={[MockFunction]}
      value="Yes"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="No"
      name="abstractorCorrect"
      onChange={[MockFunction]}
      value="No"
    />
  </div>,
]
`;
