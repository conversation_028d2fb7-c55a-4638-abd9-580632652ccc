import { RadioButton, InputDropdown } from "@q-centrix/q-components-react";
import { AnimatePresence, motion } from "framer-motion";
import { selectComponents } from "../../../MismatchedElements/Element/ElementMenu";
import { useComponentLogic } from "./hooks";

const CompleteReviewQuestion = ({
  handleInput,
  canCompleteReview,
  incompleteReasonValue,
  handleSelectChange,
  disabled
}) => {
  const { incompleteReasonOptions, incompleteReasonsLoading } =
    useComponentLogic();

  return (
    <div className="question-container">
      <label>Can you complete the Full Review?</label>
      <RadioButton
        name="canCompleteReview"
        title="Yes"
        value="Yes"
        checked={canCompleteReview === "Yes"}
        onChange={handleInput}
      />
      <RadioButton
        name="canCompleteReview"
        title="No"
        value="No"
        checked={canCompleteReview === "No"}
        onChange={handleInput}
      />
      <AnimatePresence>
        {canCompleteReview === "No" && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 40 }}
            exit={{
              height: 0,
              overflow: "hidden",
              transition: { duration: 0.2 }
            }}
            className="question-container indent-1"
          >
            <InputDropdown
              name="incompleteReasonValue"
              options={incompleteReasonOptions}
              components={selectComponents}
              placeholder="[Reason for Incomplete Review]"
              value={incompleteReasonValue}
              onChange={handleSelectChange}
              loading={incompleteReasonsLoading}
              iconClass="fa-solid fa-chevron-down"
              disabled={disabled}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CompleteReviewQuestion;
