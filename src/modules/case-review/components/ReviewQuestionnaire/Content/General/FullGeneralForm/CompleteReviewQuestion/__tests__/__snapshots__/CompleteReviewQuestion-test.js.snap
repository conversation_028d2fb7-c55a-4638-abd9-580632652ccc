// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CompleteReviewQuestion it renders component with canCompleteReview no 1`] = `
<div
  className="question-container"
>
  <label>
    Can you complete the Full Review?
  </label>
  <RadioButton
    checked={false}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="Yes"
    value="Yes"
  />
  <RadioButton
    checked={true}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="No"
    value="No"
  />
  <div
    className="question-container indent-1"
    style={
      Object {
        "height": "0px",
      }
    }
  >
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      iconClass="fa-solid fa-chevron-down"
      loading={false}
      name="incompleteReasonValue"
      options={
        Array [
          Object {
            "label": "No abstractors qualified to complete",
            "value": "1",
          },
          Object {
            "label": "Cannot access record",
            "value": "2",
          },
          Object {
            "label": "Other",
            "value": "3",
          },
        ]
      }
      placeholder="[Reason for Incomplete Review]"
    />
  </div>
</div>
`;

exports[`CompleteReviewQuestion it renders component with canCompleteReview no and a comment 1`] = `
<div
  className="question-container"
>
  <label>
    Can you complete the Full Review?
  </label>
  <RadioButton
    checked={false}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="Yes"
    value="Yes"
  />
  <RadioButton
    checked={true}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="No"
    value="No"
  />
  <div
    className="question-container indent-1"
    style={
      Object {
        "height": "0px",
      }
    }
  >
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      iconClass="fa-solid fa-chevron-down"
      loading={false}
      name="incompleteReasonValue"
      options={
        Array [
          Object {
            "label": "No abstractors qualified to complete",
            "value": "1",
          },
          Object {
            "label": "Cannot access record",
            "value": "2",
          },
          Object {
            "label": "Other",
            "value": "3",
          },
        ]
      }
      placeholder="[Reason for Incomplete Review]"
    />
  </div>
</div>
`;

exports[`CompleteReviewQuestion it renders component with canCompleteReview yes 1`] = `
<div
  className="question-container"
>
  <label>
    Can you complete the Full Review?
  </label>
  <RadioButton
    checked={true}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="Yes"
    value="Yes"
  />
  <RadioButton
    checked={false}
    name="canCompleteReview"
    onChange={[MockFunction]}
    title="No"
    value="No"
  />
</div>
`;
