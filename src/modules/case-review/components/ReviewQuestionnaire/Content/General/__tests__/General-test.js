import { create } from "react-test-renderer";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import General from "..";
import mocks from "modules/case-review/graphql/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader"
}));
jest.mock("../GeneralForm", () => "GeneralForm");

describe("General", () => {
  function render() {
    return create(
      decoratedApollo({
        component: General,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id: 1,
              type: "targeted",
              ownerId: "1",
              reviewerId: "1",
              reviewDate: "2022-01-02",
              mrn: "12345",
              visit: "12",
              caseTypeId: "1",
              correctCaseTypeId: null,
              abstractorCorrect: "",
              reasonReview: null,
              addons: [
                {
                  id: 1,
                  addonId: 1,
                  value: true,
                  multiplierValue: 2
                }
              ]
            },
            isOwner: true
          }
        },
        apolloMocks: mocks
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
