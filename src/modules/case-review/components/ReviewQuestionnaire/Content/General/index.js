import { CardWithHeader } from "@q-centrix/q-components-react";
import { propOr, __ } from "ramda";
import "modules/case-review/styles/general.scss";
import GeneralForm from "./GeneralForm";
import FullGeneralForm from "./FullGeneralForm";
import { useComponentLogic } from "./hooks";

const formTypeComponents = {
  Targeted: GeneralForm,
  Full: FullGeneralForm
};

const formType = propOr(formTypeComponents.Targeted, __, formTypeComponents);

export const General = () => {
  const { reviewType } = useComponentLogic();

  const Form = formType(reviewType);

  return (
    <CardWithHeader
      headerContent={<p className="card-header-title tw-my-5">General</p>}
      headerClasses="tw-sticky tw-top-0"
      cardClasses="border-10px"
      bodyClasses="tw-max-h-[56vh] tw-mb-5 tw-overflow-y-auto"
    >
      <Form />
    </CardWithHeader>
  );
};

export default General;
