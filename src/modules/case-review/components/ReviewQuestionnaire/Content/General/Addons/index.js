import AddonContainer from "../AddonContainer";

export const Addons = props => {
  const { addons, disabled } = props;

  if (!addons?.length) return null;

  return (
    <div className="question-container">
      <label>Additional element groups reviewed</label>
      {addons.map(addon => (
        <AddonContainer key={addon.id} addon={addon} disabled={disabled} />
      ))}
    </div>
  );
};
export default Addons;
