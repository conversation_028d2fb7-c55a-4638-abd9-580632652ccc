import { create } from "react-test-renderer";
import Addons from "..";

jest.mock("../../AddonContainer", () => "AddonContainer");

describe("Addons", () => {
  function render(addons) {
    return create(<Addons addons={addons} />);
  }

  test("it renders null when null addons", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders null when no addons", () => {
    const component = render([]);

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", () => {
    const addons = [{ id: 1, allowMultipliers: true }];
    const component = render(addons);

    expect(component).toMatchSnapshot();
  });
});
