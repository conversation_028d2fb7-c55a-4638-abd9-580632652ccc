// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddonContainer it renders component correctly 1`] = `
<div
  className="multiplier-container"
>
  <Checkbox
    checked={true}
    label="Testing Addon Group"
    name="Testing Addon Group"
    onChange={[Function]}
  />
  <div
    className="multiplier"
  >
    <input
      min={0}
      name="multiplier"
      onChange={[Function]}
      type="number"
      value={1}
    />
    <p>
       times
    </p>
  </div>
</div>
`;

exports[`AddonContainer it renders component correctly with false value no multiplier allowed 1`] = `
<div
  className="multiplier-container"
>
  <Checkbox
    checked={false}
    label="Testing Addon Group"
    name="Testing Addon Group"
    onChange={[Function]}
  />
</div>
`;

exports[`AddonContainer it renders component correctly with true value no multiplier value 1`] = `
<div
  className="multiplier-container"
>
  <Checkbox
    checked={true}
    label="Testing Addon Group"
    name="Testing Addon Group"
    onChange={[Function]}
  />
  <div
    className="multiplier"
  >
    <input
      min={0}
      name="multiplier"
      onChange={[Function]}
      type="number"
      value=""
    />
    <p>
       times
    </p>
  </div>
</div>
`;
