import { useCallback } from "react";
import { useDispatch } from "react-redux";
import determineInputValueByType from "utils/fp/determineInputValueByType";
import { updateAddonValue } from "modules/case-review/redux/slice";

export const useComponentLogic = props => {
  const { addon } = props;
  const dispatch = useDispatch();

  const onChangeCheckbox = useCallback(
    e => {
      dispatch(
        updateAddonValue({
          addonGroupsId: addon.addonGroupsId,
          irrAddonGroupId: addon.id,
          name: "delete",
          checked: e.target.checked,
          value: !determineInputValueByType(e)
        })
      );
    },
    [dispatch, addon?.addonGroupsId, addon.id]
  );

  const onChangeMultiplier = useCallback(
    e => {
      dispatch(
        updateAddonValue({
          addonGroupsId: addon.addonGroupsId,
          irrAddonGroupId: addon.id,
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
    },
    [dispatch, addon?.addonGroupsId, addon.id]
  );

  return { onChangeMultiplier, onChangeCheckbox };
};
