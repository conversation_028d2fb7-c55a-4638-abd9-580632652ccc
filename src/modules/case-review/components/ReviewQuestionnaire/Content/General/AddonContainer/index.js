import { Checkbox } from "@q-centrix/q-components-react";
import { and } from "ramda";
import { useComponentLogic } from "./hooks";

// eslint-disable-next-line complexity
export const AddonContainer = props => {
  const { addon = {}, disabled } = props;
  const { onChangeMultiplier, onChangeCheckbox } = useComponentLogic(props);

  return (
    <div className="multiplier-container">
      <Checkbox
        name={addon.name}
        label={addon.name}
        checked={addon.checked && !addon.delete}
        onChange={onChangeCheckbox}
        disabled={disabled}
      />
      {and(and(addon.allowMultipliers, addon.checked), !addon.delete) && (
        <div className="multiplier">
          <input
            type="number"
            name="multiplier"
            value={addon.multiplier || ""}
            onChange={onChangeMultiplier}
            disabled={disabled}
            min={0}
          />
          <p> times</p>
        </div>
      )}
    </div>
  );
};
export default AddonContainer;
