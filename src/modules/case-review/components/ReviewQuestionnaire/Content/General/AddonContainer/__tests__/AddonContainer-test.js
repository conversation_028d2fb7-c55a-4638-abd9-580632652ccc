import { create } from "react-test-renderer";
import { decorated } from "utils/tests/decorated";
import AddonContainer from "..";

jest.mock("@q-centrix/q-components-react", () => ({
  Checkbox: "Checkbox"
}));

describe("AddonContainer", () => {
  function render(addon) {
    return create(
      decorated(AddonContainer, { addon }, null, {
        caseReview: {
          review: {
            addonGroups: [
              {
                id: "1",
                multiplier: 44,
                irrAddonGroup: {
                  id: "1",
                  name: "Testing",
                  allowMultipliers: false
                }
              },
              {
                id: "2",
                multiplier: 1,
                irrAddonGroup: {
                  id: "3",
                  name: "Testing Addon Group",
                  allowMultipliers: true
                }
              }
            ]
          }
        }
      })
    );
  }

  test("it renders component correctly", () => {
    const component = render({
      addonGroupsId: "2",
      allowMultipliers: true,
      checked: true,
      delete: false,
      id: "3",
      multiplier: 1,
      name: "Testing Addon Group"
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with false value no multiplier allowed", () => {
    const component = render({
      addonGroupsId: "2",
      allowMultipliers: false,
      checked: false,
      delete: false,
      id: "3",
      multiplier: 1,
      name: "Testing Addon Group"
    });

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with true value no multiplier value", () => {
    const component = render({
      addonGroupsId: "2",
      allowMultipliers: true,
      checked: true,
      delete: false,
      id: "3",
      name: "Testing Addon Group"
    });

    expect(component).toMatchSnapshot();
  });
});
