import {
  Label,
  InputDropdown,
  <PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  Switch
} from "@q-centrix/q-components-react";
import { selectComponents } from "../../MismatchedElements/Element/ElementMenu";
import { useComponentLogic } from "./hooks";
import "modules/case-review/styles/general.scss";
import Addons from "../Addons";
import GeneralFormModal from "./GeneralFormModal";
// eslint-disable-next-line complexity
export const GeneralForm = () => {
  const {
    losOverrideSwitchStatus,
    shouldShowLosOverride,
    handleSwitchChange,
    review,
    addons,
    usersLoading,
    caseTypesLoading,
    userOptions,
    caseTypeOptions,
    selectedUser,
    correctCaseType,
    modalEvent,
    mutationCallback,
    actionCallback,
    setModalEvent,
    handleSelectChange,
    handleInitiatorChange,
    handleCaseTypeInput,
    handleCaseTypeChange,
    handleCaseReviewChange,
    errorRequired,
    errorRequiredCaseType,
    caseTypeLoading,
    caseReviewTypeLoading,
    updateCaseTypeError
  } = useComponentLogic();

  return (
    <div className="general-form">
      <div className="question-container">
        <label>Person initiating the Targeted Review</label>
        <InputDropdown
          name="initiator"
          options={userOptions}
          components={selectComponents}
          placeholder="Type in a name to see options"
          value={selectedUser || ""}
          onChange={handleSelectChange}
          onInputChange={handleInitiatorChange}
          loading={usersLoading}
          disabled={review.readOnly}
          error={errorRequired}
          errorText="this is required"
          menuPortalTarget={document.body}
        />
      </div>
      <div className="question-container">
        <label>
          Was the case type initially chosen by the abstractor correct?
        </label>
        <RadioButton
          name="correctCaseType"
          label="Yes"
          value="true"
          checked={review.correctCaseType === true}
          onChange={handleCaseTypeInput}
          disabled={review.readOnly}
        />
        <RadioButton
          name="correctCaseType"
          label="No"
          value="false"
          checked={!review.correctCaseType && review.correctCaseType !== null}
          onChange={handleCaseTypeInput}
          disabled={review.readOnly}
        />
      </div>
      <GeneralFormModal
        events={modalEvent}
        setEvents={setModalEvent}
        callBackFunction={actionCallback}
        mutationCallback={mutationCallback}
      />
      {!review.correctCaseType && review.correctCaseType !== null && (
        <>
          <div className="question-container indent-1">
            <label>Initial case type chosen by the abstractor</label>
            <span>{review.originalCaseType?.name}</span>
          </div>
          <div className="question-container indent-1">
            <label>Correct case type</label>
            <InputDropdown
              name="correctCaseType"
              options={caseTypeOptions}
              components={selectComponents}
              placeholder="[Correct case type]"
              value={correctCaseType}
              onChange={handleCaseTypeChange}
              loading={caseTypesLoading}
              disabled={review.readOnly}
              menuPortalTarget={document.body}
              error={errorRequiredCaseType}
              errorText={updateCaseTypeError}
            />
            {shouldShowLosOverride && (
              <Label className="tw-flex tw-flex-col tw-gap-y-1">
                <span>Override LOS</span>
                <Switch
                  checked={losOverrideSwitchStatus}
                  onChange={handleSwitchChange}
                />
              </Label>
            )}
          </div>
        </>
      )}
      <div className="question-container">
        <label>Reason for completing the Targeted Review</label>
        <RadioButton
          name="questionnaireType"
          label="Specific element review"
          value="specific"
          checked={review.questionnaireType === "specific"}
          onChange={handleCaseReviewChange}
          disabled={review.readOnly}
        />
        <RadioButton
          name="questionnaireType"
          label="Critical element review"
          value="critical"
          checked={review.questionnaireType === "critical"}
          onChange={handleCaseReviewChange}
          disabled={review.readOnly}
        />
      </div>
      {caseTypeLoading || caseReviewTypeLoading ? (
        <Spinner />
      ) : (
        <Addons addons={addons} disabled={review.readOnly} />
      )}
    </div>
  );
};
export default GeneralForm;
