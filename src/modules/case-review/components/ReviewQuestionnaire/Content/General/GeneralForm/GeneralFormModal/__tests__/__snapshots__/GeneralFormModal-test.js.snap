// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GeneralFormModal components renders correctly 1`] = `
<ConfirmationModal
  appElement=".general-form"
  isDanger={true}
  isOpen={true}
  title="Confirm Action"
>
  <div
    className="modal-paragraph"
  >
    By changing the 
    , all data will be removed from the questionnaire. If you added any data to the questionnaire, it will be emailed to you. Do you want to continue?
  </div>
  <div
    className="button-container"
  >
    <Button
      bg="neutral"
      customStyle="modal-buttons"
      onClick={[Function]}
      outline={true}
    >
      <i
        className="fa fa-xmark"
      />
      Cancel
    </Button>
    <Button
      bg="danger"
      customStyle="modal-buttons"
      onClick={[Function]}
    >
      <i
        className="fa fa-check"
      />
      Confirm
    </Button>
  </div>
</ConfirmationModal>
`;
