import { act, create } from "react-test-renderer";
import wait from "waait";
import { Switch } from "@q-centrix/q-components-react";
import GeneralForm from "..";
import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import mocks from "../../mocks";
import { createQueryMocks as createReviewMocks } from "../../../mocks";
import { createQueryMocks as createElementsReviewMocks } from "../../../ElementsReviewed/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  CardWithHeader: "CardWithHeader",
  InputDropdown: "InputDropdown",
  Label: "Label",
  RadioButton: "RadioButton",
  Spinner: "Spinner",
  Switch: "Switch"
}));

jest.mock("../../Addons", () => "Addons");
jest.mock("../GeneralFormModal", () => "GeneralFormModal");

describe("General", () => {
  function render(readOnly = false, id = 1) {
    return create(
      decoratedApollo({
        component: GeneralForm,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              id,
              caseType: {
                id: "2"
              },
              readOnly,
              availableIrrAddonGroups: [
                {
                  id: "1",
                  name: "Testing",
                  allowMultipliers: false
                },
                {
                  id: "2",
                  name: "Testing addon",
                  allowMultipliers: true
                },
                {
                  id: "3",
                  name: "Testing Addon Group",
                  allowMultipliers: true
                }
              ],
              elements: [
                {
                  elementsId: "1",
                  checked: true,
                  id: "7",
                  irrAddonGroups: [{ id: "3", allowMultipliers: true }],
                  name: "Element 2"
                }
              ],
              addonGroups: [
                {
                  id: "1",
                  multiplier: 44,
                  irrAddonGroup: {
                    id: "1",
                    name: "Testing",
                    allowMultipliers: false
                  }
                },
                {
                  id: "2",
                  multiplier: 1,
                  irrAddonGroup: {
                    id: "3",
                    name: "Testing Addon Group",
                    allowMultipliers: true
                  }
                }
              ]
            },
            errors: []
          }
        },
        apolloMocks: [
          ...mocks,
          createReviewMocks(id, "critical", readOnly),
          createElementsReviewMocks(id)
        ]
      })
    );
  }

  test("it renders component with Loading in drop downs", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly when disabled", async () => {
    const component = render(true);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly with LOS type", async () => {
    const component = render(false, 5);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component correctly on error", async () => {
    const component = render(false, 5);

    await act(() => wait(100));

    const instance = component.root;
    const [sw] = instance.findAllByType(Switch);

    act(() => {
      sw.props.onChange();
    });

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
