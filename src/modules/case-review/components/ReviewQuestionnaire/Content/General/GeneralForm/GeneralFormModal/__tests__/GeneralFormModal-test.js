import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import GeneralFormModal from "..";
import mocks from "../../../../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  ConfirmationModal: "ConfirmationModal",
  Button: "Button"
}));

describe("GeneralFormModal", () => {
  function render(props = {}) {
    return create(
      decoratedApollo({
        component: GeneralFormModal,
        props,
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("components renders correctly", () => {
    const component = render({
      events: { showModal: true, event: {} },
      mutationCallback: { mutation: jest.fn() },
      callBackFunction: { callbackFn: jest.fn() }
    });

    expect(component).toMatchSnapshot();
  });

  test("closes the modal on button click", () => {
    const handleModal = jest.fn();
    const component = render({
      events: { showModal: true, event: {} },
      setEvents: handleModal,
      mutationCallback: { mutation: jest.fn() },
      callBackFunction: { callbackFn: jest.fn() }
    });
    const instance = component.root;
    const [btn] = instance.findAllByType("Button");

    act(() => btn.props.onClick());
    expect(handleModal).toHaveBeenCalled();
  });
});
