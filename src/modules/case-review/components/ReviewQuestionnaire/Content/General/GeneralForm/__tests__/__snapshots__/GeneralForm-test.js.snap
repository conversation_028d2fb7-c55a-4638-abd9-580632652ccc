// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`General it renders component correctly 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Person initiating the Targeted Review
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={true}
      errorText="this is required"
      loading={false}
      menuPortalTarget={<body />}
      name="initiator"
      onChange={[Function]}
      onInputChange={[Function]}
      options={Array []}
      placeholder="Type in a name to see options"
      value=""
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="correctCaseType"
      onChange={[Function]}
      value="true"
    />
    <RadioButton
      checked={true}
      disabled={false}
      label="No"
      name="correctCaseType"
      onChange={[Function]}
      value="false"
    />
  </div>
  <GeneralFormModal
    callBackFunction={Object {}}
    events={
      Object {
        "event": Object {},
        "modalFor": "",
        "showModal": false,
      }
    }
    mutationCallback={Object {}}
    setEvents={[Function]}
  />
  <div
    className="question-container indent-1"
  >
    <label>
      Initial case type chosen by the abstractor
    </label>
    <span />
  </div>
  <div
    className="question-container indent-1"
  >
    <label>
      Correct case type
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={false}
      errorText=""
      loading={false}
      menuPortalTarget={<body />}
      name="correctCaseType"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Type 1 with Addon Groups",
            "value": "1",
          },
          Object {
            "label": "Type 2 no Addon Groups",
            "value": "2",
          },
        ]
      }
      placeholder="[Correct case type]"
      value={
        Object {
          "label": "Type 2 no Addon Groups",
          "value": "2",
        }
      }
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Reason for completing the Targeted Review
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Specific element review"
      name="questionnaireType"
      onChange={[Function]}
      value="specific"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="Critical element review"
      name="questionnaireType"
      onChange={[Function]}
      value="critical"
    />
  </div>
  <Addons
    addons={
      Array [
        Object {
          "addonGroupsId": "1",
          "allowMultipliers": false,
          "checked": true,
          "delete": undefined,
          "id": "1",
          "irrAddonGroupId": Object {},
          "multiplier": 44,
          "name": "Testing",
        },
        Object {
          "allowMultipliers": true,
          "checked": false,
          "id": "2",
          "irrAddonGroupId": "2",
          "name": "Testing addon",
        },
        Object {
          "addonGroupsId": "2",
          "allowMultipliers": true,
          "checked": true,
          "delete": undefined,
          "id": "3",
          "irrAddonGroupId": Object {},
          "multiplier": 1,
          "name": "Testing Addon Group",
        },
      ]
    }
    disabled={false}
  />
</div>
`;

exports[`General it renders component correctly on error 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Person initiating the Targeted Review
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={true}
      errorText="this is required"
      loading={false}
      menuPortalTarget={<body />}
      name="initiator"
      onChange={[Function]}
      onInputChange={[Function]}
      options={Array []}
      placeholder="Type in a name to see options"
      value=""
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="correctCaseType"
      onChange={[Function]}
      value="true"
    />
    <RadioButton
      checked={true}
      disabled={false}
      label="No"
      name="correctCaseType"
      onChange={[Function]}
      value="false"
    />
  </div>
  <GeneralFormModal
    callBackFunction={Object {}}
    events={
      Object {
        "event": Object {},
        "modalFor": "",
        "showModal": false,
      }
    }
    mutationCallback={Object {}}
    setEvents={[Function]}
  />
  <div
    className="question-container indent-1"
  >
    <label>
      Initial case type chosen by the abstractor
    </label>
    <span />
  </div>
  <div
    className="question-container indent-1"
  >
    <label>
      Correct case type
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={true}
      errorText="The case type must match the LOS. Correct the case type or LOS and try again."
      loading={false}
      menuPortalTarget={<body />}
      name="correctCaseType"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Type 1 with Addon Groups",
            "value": "1",
          },
          Object {
            "label": "Type 2 no Addon Groups",
            "value": "2",
          },
        ]
      }
      placeholder="[Correct case type]"
      value={
        Object {
          "label": "Type 2 no Addon Groups",
          "value": "2",
        }
      }
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Reason for completing the Targeted Review
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Specific element review"
      name="questionnaireType"
      onChange={[Function]}
      value="specific"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="Critical element review"
      name="questionnaireType"
      onChange={[Function]}
      value="critical"
    />
  </div>
  <Addons
    addons={
      Array [
        Object {
          "addonGroupsId": "1",
          "allowMultipliers": false,
          "checked": true,
          "delete": undefined,
          "id": "1",
          "irrAddonGroupId": Object {},
          "multiplier": 44,
          "name": "Testing",
        },
        Object {
          "allowMultipliers": true,
          "checked": false,
          "id": "2",
          "irrAddonGroupId": "2",
          "name": "Testing addon",
        },
        Object {
          "addonGroupsId": "2",
          "allowMultipliers": true,
          "checked": true,
          "delete": undefined,
          "id": "3",
          "irrAddonGroupId": Object {},
          "multiplier": 1,
          "name": "Testing Addon Group",
        },
      ]
    }
    disabled={false}
  />
</div>
`;

exports[`General it renders component correctly when disabled 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Person initiating the Targeted Review
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={true}
      error={true}
      errorText="this is required"
      loading={false}
      menuPortalTarget={<body />}
      name="initiator"
      onChange={[Function]}
      onInputChange={[Function]}
      options={Array []}
      placeholder="Type in a name to see options"
      value=""
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={true}
      label="Yes"
      name="correctCaseType"
      onChange={[Function]}
      value="true"
    />
    <RadioButton
      checked={true}
      disabled={true}
      label="No"
      name="correctCaseType"
      onChange={[Function]}
      value="false"
    />
  </div>
  <GeneralFormModal
    callBackFunction={Object {}}
    events={
      Object {
        "event": Object {},
        "modalFor": "",
        "showModal": false,
      }
    }
    mutationCallback={Object {}}
    setEvents={[Function]}
  />
  <div
    className="question-container indent-1"
  >
    <label>
      Initial case type chosen by the abstractor
    </label>
    <span />
  </div>
  <div
    className="question-container indent-1"
  >
    <label>
      Correct case type
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={true}
      error={false}
      errorText=""
      loading={false}
      menuPortalTarget={<body />}
      name="correctCaseType"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Type 1 with Addon Groups",
            "value": "1",
          },
          Object {
            "label": "Type 2 no Addon Groups",
            "value": "2",
          },
        ]
      }
      placeholder="[Correct case type]"
      value={
        Object {
          "label": "Type 2 no Addon Groups",
          "value": "2",
        }
      }
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Reason for completing the Targeted Review
    </label>
    <RadioButton
      checked={false}
      disabled={true}
      label="Specific element review"
      name="questionnaireType"
      onChange={[Function]}
      value="specific"
    />
    <RadioButton
      checked={false}
      disabled={true}
      label="Critical element review"
      name="questionnaireType"
      onChange={[Function]}
      value="critical"
    />
  </div>
  <Addons
    addons={
      Array [
        Object {
          "addonGroupsId": "1",
          "allowMultipliers": false,
          "checked": true,
          "delete": undefined,
          "id": "1",
          "irrAddonGroupId": Object {},
          "multiplier": 44,
          "name": "Testing",
        },
        Object {
          "allowMultipliers": true,
          "checked": false,
          "id": "2",
          "irrAddonGroupId": "2",
          "name": "Testing addon",
        },
        Object {
          "addonGroupsId": "2",
          "allowMultipliers": true,
          "checked": true,
          "delete": undefined,
          "id": "3",
          "irrAddonGroupId": Object {},
          "multiplier": 1,
          "name": "Testing Addon Group",
        },
      ]
    }
    disabled={true}
  />
</div>
`;

exports[`General it renders component correctly with LOS type 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Person initiating the Targeted Review
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={true}
      errorText="this is required"
      loading={false}
      menuPortalTarget={<body />}
      name="initiator"
      onChange={[Function]}
      onInputChange={[Function]}
      options={Array []}
      placeholder="Type in a name to see options"
      value=""
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="correctCaseType"
      onChange={[Function]}
      value="true"
    />
    <RadioButton
      checked={true}
      disabled={false}
      label="No"
      name="correctCaseType"
      onChange={[Function]}
      value="false"
    />
  </div>
  <GeneralFormModal
    callBackFunction={Object {}}
    events={
      Object {
        "event": Object {},
        "modalFor": "",
        "showModal": false,
      }
    }
    mutationCallback={Object {}}
    setEvents={[Function]}
  />
  <div
    className="question-container indent-1"
  >
    <label>
      Initial case type chosen by the abstractor
    </label>
    <span />
  </div>
  <div
    className="question-container indent-1"
  >
    <label>
      Correct case type
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={false}
      errorText=""
      loading={false}
      menuPortalTarget={<body />}
      name="correctCaseType"
      onChange={[Function]}
      options={
        Array [
          Object {
            "label": "Type 1 with Addon Groups",
            "value": "1",
          },
          Object {
            "label": "Type 2 no Addon Groups",
            "value": "2",
          },
        ]
      }
      placeholder="[Correct case type]"
      value={
        Object {
          "label": "Type 2 no Addon Groups",
          "value": "2",
        }
      }
    />
    <Label
      className="tw-flex tw-flex-col tw-gap-y-1"
    >
      <span>
        Override LOS
      </span>
      <Switch
        onChange={[Function]}
      />
    </Label>
  </div>
  <div
    className="question-container"
  >
    <label>
      Reason for completing the Targeted Review
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Specific element review"
      name="questionnaireType"
      onChange={[Function]}
      value="specific"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="Critical element review"
      name="questionnaireType"
      onChange={[Function]}
      value="critical"
    />
  </div>
  <Addons
    addons={
      Array [
        Object {
          "addonGroupsId": "1",
          "allowMultipliers": false,
          "checked": true,
          "delete": undefined,
          "id": "1",
          "irrAddonGroupId": Object {},
          "multiplier": 44,
          "name": "Testing",
        },
        Object {
          "allowMultipliers": true,
          "checked": false,
          "id": "2",
          "irrAddonGroupId": "2",
          "name": "Testing addon",
        },
        Object {
          "addonGroupsId": "2",
          "allowMultipliers": true,
          "checked": true,
          "delete": undefined,
          "id": "3",
          "irrAddonGroupId": Object {},
          "multiplier": 1,
          "name": "Testing Addon Group",
        },
      ]
    }
    disabled={false}
  />
</div>
`;

exports[`General it renders component with Loading in drop downs 1`] = `
<div
  className="general-form"
>
  <div
    className="question-container"
  >
    <label>
      Person initiating the Targeted Review
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={false}
      errorText="this is required"
      loading={false}
      menuPortalTarget={<body />}
      name="initiator"
      onChange={[Function]}
      onInputChange={[Function]}
      options={Array []}
      placeholder="Type in a name to see options"
      value=""
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Was the case type initially chosen by the abstractor correct?
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Yes"
      name="correctCaseType"
      onChange={[Function]}
      value="true"
    />
    <RadioButton
      checked={true}
      disabled={false}
      label="No"
      name="correctCaseType"
      onChange={[Function]}
      value="false"
    />
  </div>
  <GeneralFormModal
    callBackFunction={Object {}}
    events={
      Object {
        "event": Object {},
        "modalFor": "",
        "showModal": false,
      }
    }
    mutationCallback={Object {}}
    setEvents={[Function]}
  />
  <div
    className="question-container indent-1"
  >
    <label>
      Initial case type chosen by the abstractor
    </label>
    <span />
  </div>
  <div
    className="question-container indent-1"
  >
    <label>
      Correct case type
    </label>
    <InputDropdown
      components={
        Object {
          "DropdownIndicator": [Function],
          "IndicatorSeparator": [Function],
        }
      }
      disabled={false}
      error={false}
      errorText=""
      loading={true}
      menuPortalTarget={<body />}
      name="correctCaseType"
      onChange={[Function]}
      options={Array []}
      placeholder="[Correct case type]"
      value={null}
    />
  </div>
  <div
    className="question-container"
  >
    <label>
      Reason for completing the Targeted Review
    </label>
    <RadioButton
      checked={false}
      disabled={false}
      label="Specific element review"
      name="questionnaireType"
      onChange={[Function]}
      value="specific"
    />
    <RadioButton
      checked={false}
      disabled={false}
      label="Critical element review"
      name="questionnaireType"
      onChange={[Function]}
      value="critical"
    />
  </div>
  <Addons
    addons={
      Array [
        Object {
          "addonGroupsId": "1",
          "allowMultipliers": false,
          "checked": true,
          "delete": undefined,
          "id": "1",
          "irrAddonGroupId": Object {},
          "multiplier": 44,
          "name": "Testing",
        },
        Object {
          "allowMultipliers": true,
          "checked": false,
          "id": "2",
          "irrAddonGroupId": "2",
          "name": "Testing addon",
        },
        Object {
          "addonGroupsId": "2",
          "allowMultipliers": true,
          "checked": true,
          "delete": undefined,
          "id": "3",
          "irrAddonGroupId": Object {},
          "multiplier": 1,
          "name": "Testing Addon Group",
        },
      ]
    }
    disabled={false}
  />
</div>
`;
