import { ConfirmationModal, Button } from "@q-centrix/q-components-react";
import "styles/confirmation-modal.scss";

const GeneralFormModal = ({
  events,
  setEvents,
  callBackFunction,
  mutationCallback
}) => {
  const { mutation, variables } = mutationCallback;
  const { callbackFn, parameters } = callBackFunction;

  return (
    <ConfirmationModal
      appElement=".general-form"
      title="Confirm Action"
      isOpen={events.showModal}
      isDanger
    >
      <div className="modal-paragraph">
        By changing the {events.modalFor}, all data will be removed from the
        questionnaire. If you added any data to the questionnaire, it will be
        emailed to you. Do you want to continue?
      </div>
      <div className="button-container">
        <Button
          onClick={() => setEvents(prev => ({ ...prev, showModal: false }))}
          customStyle="modal-buttons"
          outline
          bg="neutral"
        >
          <i className="fa fa-xmark" />
          Cancel
        </Button>
        <Button
          onClick={() => {
            window.localStorage.removeItem("persist:caseReview");
            callbackFn(...parameters);
            mutation({ variables });
            setEvents(prev => ({ ...prev, showModal: false }));
          }}
          customStyle="modal-buttons"
          bg="danger"
        >
          <i className="fa fa-check" />
          Confirm
        </Button>
      </div>
    </ConfirmationModal>
  );
};

export default GeneralFormModal;
