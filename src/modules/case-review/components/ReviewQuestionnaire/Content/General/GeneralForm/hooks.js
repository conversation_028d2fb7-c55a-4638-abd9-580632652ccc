import { useDispatch, useSelector } from "react-redux";
import { useCallback, useEffect, useMemo, useState } from "react";
import CaseReview from "modules/case-review/redux/selectors";
import {
  setGeneralValue,
  removeError,
  removeAllErrors,
  addError,
  setMutationLoading
} from "modules/case-review/redux/slice";
import { useQuery, useMutation } from "@apollo/client";
import {
  GET_CASE_REVIEW_INITIATORS,
  GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
  GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
  GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA
} from "modules/case-review/graphql/query";
import {
  UPDATE_CASE_REVIEW_CASE_TYPE,
  UPDATE_CASE_REVIEW_TYPE
} from "modules/case-review/graphql/mutation";
import determineInputValueByType from "utils/fp/determineInputValueByType";
import {
  not,
  always,
  applySpec,
  equals,
  find,
  ifElse,
  map,
  prop,
  propEq,
  when,
  pathEq,
  mergeLeft,
  T,
  pipe,
  identity,
  or,
  lt,
  length,
  filter,
  defaultTo,
  tap,
  F,
  head
} from "ramda";
import { isNotNullOrEmpty } from "utils/fp";

let debounce = null;
const debounceTimeInMilliseconds = 500;

const mutationConditionCheck = array =>
  or(
    pipe(defaultTo([]), filter(propEq("delete", false)), length, lt(0))(array),
    pipe(defaultTo([]), filter(pipe(prop("id"), lt(0))), length, lt(0))(array)
  );

const convertToBoolean = val => val === "true";

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const dispatch = useDispatch();
  const review = useSelector(state => CaseReview.getGeneral(state));

  const {
    id,
    addonGroups = [],
    availableIrrAddonGroups = [],
    elements = []
  } = review;

  const [
    caseReviewQuestionnaireCaseTypesState,
    setCaseReviewQuestionnaireCaseTypesState
  ] = useState([]);
  const [updateCaseTypeError, setUpdateCaseTypeError] = useState("");
  const [initiatorInput, setInitiatorInput] = useState("");
  const [userOptions, setUserOptions] = useState([]);
  const [caseTypeOptions, setCaseTypeOptions] = useState([]);
  const [correctCaseType, setCorrectCaseType] = useState(null);
  const [selectedUser, setSelectedUser] = useState();
  const [modalEvent, setModalEvent] = useState({
    showModal: false,
    event: {},
    modalFor: ""
  });
  const [errorRequired, setErrorRequired] = useState(false);
  const [errorRequiredCaseType, setErrorRequiredCaseType] = useState(false);
  const [mutationCallback, setMutationCallback] = useState({});
  const [actionCallback, setActionCallback] = useState({});
  const [shouldShowLosOverride, setShouldShowLosOverride] = useState(null);

  const [losOverrideSwitchStatus, setLosOverrideSwitchStatus] = useState(
    review.losOverride
  );

  useEffect(() => {
    if (review.initiator) {
      setSelectedUser({
        label: review.initiator?.fullName,
        value: review.initiator?.id
      });
      setErrorRequired(false);
    } else {
      setErrorRequired(true);
      setSelectedUser();
    }
  }, [review]);

  const addons = useMemo(
    () =>
      availableIrrAddonGroups &&
      map(
        addon =>
          pipe(
            find(pathEq(["irrAddonGroup", "id"], addon.id)),
            ifElse(
              identity,
              applySpec({
                checked: T,
                multiplier: prop("multiplier"),
                addonGroupsId: prop("id"),
                delete: prop("delete"),
                irrAddonGroupId: addon.id
              }),
              always({
                checked: false,
                irrAddonGroupId: addon.id
              })
            ),
            mergeLeft(addon)
          )(addonGroups),
        availableIrrAddonGroups
      ),
    [addonGroups, availableIrrAddonGroups]
  );

  const { loading: usersLoading } = useQuery(GET_CASE_REVIEW_INITIATORS, {
    variables: { questionnaireId: id, query: initiatorInput, limit: 20 },
    onCompleted: (data = {}) => {
      const { caseReviewInitiators = [] } = data;
      const options = map(
        applySpec({
          label: prop("fullName"),
          value: prop("id")
        }),
        caseReviewInitiators
      );

      setUserOptions(options);
    },
    skip: initiatorInput.length < 2
  });

  const handleInitiatorChange = useCallback(value => {
    clearTimeout(debounce);
    debounce = setTimeout(() => {
      setInitiatorInput(value);
    }, debounceTimeInMilliseconds);
  });
  const isLosOverrideAllowed = pipe(
    find(propEq("id", review?.caseType?.id)),
    prop("lengthOfStay"),
    ifElse(isNotNullOrEmpty, propEq("allowOverride", true), F)
  );

  const { loading: caseTypesLoading } = useQuery(
    GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
    {
      variables: { questionnaireId: Number(id) },
      onCompleted: (data = {}) => {
        const { caseReviewQuestionnaireCaseTypes = [] } = data;

        const options = map(
          applySpec({
            label: prop("name"),
            value: prop("id")
          }),
          caseReviewQuestionnaireCaseTypes
        );
        const selCaseType = find(
          propEq("value", review?.caseType?.id),
          options
        );
        const shouldShowLosOverrideInitialLoad = isLosOverrideAllowed(
          caseReviewQuestionnaireCaseTypes
        );

        setCaseReviewQuestionnaireCaseTypesState(
          caseReviewQuestionnaireCaseTypes
        );
        setShouldShowLosOverride(shouldShowLosOverrideInitialLoad);
        setCorrectCaseType(selCaseType || null);
        setCaseTypeOptions(options);
      }
    }
  );

  const [updateCaseType, { loading: caseTypeLoading }] = useMutation(
    UPDATE_CASE_REVIEW_CASE_TYPE,
    {
      refetchQueries: [
        {
          query: GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
          variables: { questionnaireId: id }
        },
        {
          query: GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA,
          variables: { questionnaireId: id, addonGroupIds: [] }
        }
      ],
      awaitRefetchQueries: true,
      onCompleted: (data = {}) => {
        const mutationError = data.updateCaseReviewCaseType.errors;

        if (mutationError) {
          const [firstErrorMessage] = mutationError.messages || [];

          setErrorRequiredCaseType(true);
          setUpdateCaseTypeError(head(firstErrorMessage.errors));
          dispatch(addError({ id: "case_type", error: true }));
          setShouldShowLosOverride(false);
          return;
        }

        const shouldShowLosOverrideOnChange = isLosOverrideAllowed(
          caseReviewQuestionnaireCaseTypesState
        );

        setShouldShowLosOverride(shouldShowLosOverrideOnChange);
      }
    }
  );

  const handleSwitchChange = () => {
    setLosOverrideSwitchStatus(
      pipe(
        not,
        tap(losOverride => {
          updateCaseType({
            variables: {
              questionnaireId: id,
              caseTypeId: review?.caseType?.id,
              initiatorId: review?.initiator?.id,
              correctCaseType: review?.correctCaseType,
              losOverride
            }
          });
        })
      )
    );
  };

  useEffect(() => {
    dispatch(setMutationLoading(caseTypeLoading));
  }, [caseTypeLoading]);

  const [updateCaseReviewType, { loading: caseReviewTypeLoading }] =
    useMutation(UPDATE_CASE_REVIEW_TYPE, {
      refetchQueries: [
        {
          query: GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
          variables: { questionnaireId: id }
        },
        {
          query: GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA,
          variables: { questionnaireId: id, addonGroupIds: [] }
        }
      ],
      awaitRefetchQueries: true
    });

  useEffect(() => {
    dispatch(setMutationLoading(caseReviewTypeLoading));
  }, [caseReviewTypeLoading]);

  const handleSelectChange = useCallback(
    (value, name) => {
      ifElse(
        equals("initiator"),
        () => {
          if (value) {
            dispatch(
              setGeneralValue({
                name: "initiator",
                value: { id: value.value, fullName: value.label }
              })
            );
            setSelectedUser(value);
            setErrorRequired(false);
            dispatch(removeError({ id: "initiator" }));
          }
        },
        when(equals("caseType"), () => {
          if (value) {
            dispatch(
              setGeneralValue({
                name: "caseType",
                value: { id: value.value, name: value.label }
              })
            );
            const selCaseType = find(
              propEq("value", value.value),
              caseTypeOptions
            );

            setErrorRequiredCaseType(false);
            dispatch(removeError({ id: "case_type" }));
            setCorrectCaseType(selCaseType);
          }
        })
      )(name);
    },
    [dispatch, caseTypeOptions]
  );
  const handleInput = useCallback(
    e => {
      dispatch(
        setGeneralValue({
          name: e.target.name,
          value: determineInputValueByType(e)
        })
      );
    },
    [dispatch]
  );

  const handleCaseTypeInput = useCallback(
    e => {
      dispatch(
        setGeneralValue({
          name: e.target.name,
          value: convertToBoolean(e.target.value)
        })
      );
    },
    [dispatch]
  );

  const checkElements = useMemo(
    () => mutationConditionCheck(elements),
    [elements]
  );

  const checkAddonGroups = useMemo(
    () => mutationConditionCheck(addonGroups),
    [addonGroups]
  );

  const handleCaseTypeChange = useCallback(
    event => {
      if (or(checkAddonGroups, checkElements)) {
        setActionCallback({
          callbackFn: handleSelectChange,
          parameters: [event, "caseType"]
        });
        setMutationCallback({
          mutation: updateCaseType,
          variables: {
            questionnaireId: id,
            caseTypeId: event.value,
            initiatorId: review?.initiator?.id,
            correctCaseType: review?.correctCaseType
          }
        });
        setModalEvent({ modalFor: "case type", showModal: true, event });
      } else {
        handleSelectChange(event, "caseType");
        updateCaseType({
          variables: {
            questionnaireId: id,
            caseTypeId: event.value,
            initiatorId: review?.initiator?.id,
            correctCaseType: review?.correctCaseType
          }
        });
      }
      localStorage.removeItem("persist:caseReview");
      dispatch(removeAllErrors());
    },
    [review, handleSelectChange, dispatch]
  );

  const handleCaseReviewChange = useCallback(
    event => {
      if (or(checkAddonGroups, checkElements)) {
        setActionCallback({ callbackFn: handleInput, parameters: [event] });
        setMutationCallback({
          mutation: updateCaseReviewType,
          variables: {
            questionnaireId: id,
            questionnaireType: event.target.value,
            initiatorId: review?.initiator?.id,
            correctCaseType: review?.correctCaseType
          }
        });
        setModalEvent({
          modalFor: "Targeted Review type",
          showModal: true,
          event
        });
      } else {
        handleInput(event);
        updateCaseReviewType({
          variables: {
            questionnaireId: id,
            questionnaireType: event.target.value,
            initiatorId: review?.initiator?.id,
            correctCaseType: review?.correctCaseType
          }
        });
      }
      localStorage.removeItem("persist:caseReview");
      dispatch(removeAllErrors());
    },
    [review, handleInput, dispatch]
  );

  return {
    review,
    addons,
    usersLoading,
    caseTypesLoading,
    userOptions,
    caseTypeOptions,
    correctCaseType,
    selectedUser,
    modalEvent,
    mutationCallback,
    actionCallback,
    setModalEvent,
    handleSelectChange,
    handleInitiatorChange,
    handleCaseTypeInput,
    handleCaseTypeChange,
    handleCaseReviewChange,
    errorRequired,
    errorRequiredCaseType,
    caseTypeLoading,
    caseReviewTypeLoading,
    losOverrideSwitchStatus,
    handleSwitchChange,
    shouldShowLosOverride,
    updateCaseTypeError
  };
};
