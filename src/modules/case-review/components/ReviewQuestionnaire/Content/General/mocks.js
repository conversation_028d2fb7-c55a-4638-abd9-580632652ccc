import {
  GET_CASE_REVIEW_INITIATORS,
  GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
  GET_INCOMPLETE_REASONS
} from "modules/case-review/graphql/query";
import { UPDATE_CASE_REVIEW_CASE_TYPE } from "modules/case-review/graphql/mutation";

const usersMocks = [
  {
    request: {
      query: GET_CASE_REVIEW_INITIATORS
    },
    result: {
      data: {
        caseReviewInitiators: [
          { fullName: "Jon Smith 1", id: "1", __typename: "User" },
          { fullName: "<PERSON> 2", id: "2", __typename: "User" },
          { fullName: "<PERSON> Smith 3", id: "3", __typename: "User" },
          { fullName: "Jon Smith 4", id: "4", __typename: "User" },
          { fullName: "Jon Smith 5", id: "5", __typename: "User" },
          { fullName: "<PERSON> 6", id: "6", __typename: "User" },
          { fullName: "<PERSON> 7", id: "7", __typename: "User" },
          { fullName: "<PERSON> Smith 8", id: "8", __typename: "User" },
          { fullName: "<PERSON> 9", id: "9", __typename: "User" },
          { fullName: "John Smith 10", id: "10", __typename: "User" }
        ]
      }
    }
  }
];

const caseTypesMocks = [
  {
    request: {
      query: GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
      variables: {
        questionnaireId: 1
      }
    },
    result: {
      data: {
        caseReviewQuestionnaireCaseTypes: [
          {
            id: "1",
            name: "Type 1 with Addon Groups",
            facilityIrrAddonGroups: [
              {
                id: 1,
                name: "Additional time tracker",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              },
              {
                id: 2,
                name: "Advanced stroke care",
                allowMultipliers: false,
                __typename: "IrrAddonGroup"
              },
              {
                id: 3,
                name: "Core measures",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              }
            ],
            lengthOfStay: {
              start: null,
              end: null,
              allowOverride: false
            },
            __typename: "Case"
          },
          {
            id: "2",
            name: "Type 2 no Addon Groups",
            facilityIrrAddonGroups: [],
            lengthOfStay: {
              start: null,
              end: null,
              allowOverride: false
            },
            __typename: "Case"
          }
        ]
      }
    }
  },
  {
    request: {
      query: GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
      variables: {
        questionnaireId: 3
      }
    },
    result: {
      data: {
        caseReviewQuestionnaireCaseTypes: [
          {
            id: "1",
            name: "Type 1 with Addon Groups",
            facilityIrrAddonGroups: [
              {
                id: 1,
                name: "Additional time tracker",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              },
              {
                id: 2,
                name: "Advanced stroke care",
                allowMultipliers: false,
                __typename: "IrrAddonGroup"
              },
              {
                id: 3,
                name: "Core measures",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              }
            ],
            __typename: "Case"
          },
          {
            id: "2",
            name: "Type 2 no Addon Groups",
            facilityIrrAddonGroups: [],
            __typename: "Case"
          }
        ]
      }
    }
  },
  {
    request: {
      query: GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES,
      variables: {
        questionnaireId: 5
      }
    },
    result: {
      data: {
        caseReviewQuestionnaireCaseTypes: [
          {
            id: "1",
            name: "Type 1 with Addon Groups",
            facilityIrrAddonGroups: [
              {
                id: 1,
                name: "Additional time tracker",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              },
              {
                id: 2,
                name: "Advanced stroke care",
                allowMultipliers: false,
                __typename: "IrrAddonGroup"
              },
              {
                id: 3,
                name: "Core measures",
                allowMultipliers: true,
                __typename: "IrrAddonGroup"
              }
            ],
            lengthOfStay: {
              start: null,
              end: null,
              allowOverride: false
            },
            __typename: "Case"
          },
          {
            id: "2",
            name: "Type 2 no Addon Groups",
            facilityIrrAddonGroups: [],
            lengthOfStay: {
              start: null,
              end: null,
              allowOverride: true
            },
            __typename: "Case"
          }
        ]
      }
    }
  }
];

const incompleteReasonsMocks = [
  {
    request: {
      query: GET_INCOMPLETE_REASONS
    },
    result: {
      data: {
        incompleteReasons: [
          { value: "1", label: "No abstractors qualified to complete" },
          { value: "2", label: "Cannot access record" },
          { value: "3", label: "Other" }
        ]
      }
    }
  }
];
const updateCaseReviewCaseTypeMocks = [
  {
    request: {
      query: UPDATE_CASE_REVIEW_CASE_TYPE,
      variables: {
        questionnaireId: "1",
        caseTypeId: "1",
        initiatorId: "1",
        correctCaseType: false
      }
    },
    result: {
      data: {
        updateCaseReviewCaseType: {
          errors: null,
          response: "true"
        }
      }
    }
  },
  {
    request: {
      query: UPDATE_CASE_REVIEW_CASE_TYPE,
      variables: {
        questionnaireId: 5,
        caseTypeId: "2",
        losOverride: true
      }
    },
    result: {
      data: {
        updateCaseReviewCaseType: {
          errors: {
            messages: [
              {
                attribute: "case_type",
                errors: [
                  "The case type must match the LOS. Correct the case type or LOS and try again."
                ],
                __typename: "MessageError"
              }
            ]
          },
          response: "false"
        }
      }
    }
  }
];

export default [
  ...usersMocks,
  ...caseTypesMocks,
  ...incompleteReasonsMocks,
  ...updateCaseReviewCaseTypeMocks
];
