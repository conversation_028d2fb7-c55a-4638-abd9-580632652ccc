import { useMemo } from "react";
import { useMutation } from "@apollo/client";
import { useDispatch, useSelector } from "react-redux";
import {
  __,
  any,
  assoc,
  both,
  dissoc,
  either,
  evolve,
  identity,
  ifElse,
  lt,
  map,
  not,
  pick,
  pipe,
  prop,
  propEq,
  propOr,
  reject
} from "ramda";
import { UPDATE_CASE_REVIEW_QUESTIONNAIRE } from "modules/case-review/graphql/mutation";
import { GET_CASE_REVIEW_QUESTIONNAIRE_DATA } from "modules/case-review/graphql/query";
import CaseReview from "modules/case-review/redux/selectors";
import capitalize from "utils/fp/capitalize";
import { isNullOrEmpty, removeProperties } from "utils/fp";
import { sendErrorToIrrElements } from "modules/case-review/redux/slice";

const isNegative = id => id < 1;
const checkForNegativeNumber = pipe(prop("id"), v => isNegative(v));
const formatElements = map(
  ifElse(checkForNegativeNumber, dissoc("id"), identity)
);

const elementsField = [
  "caseReviewCategoryId",
  "comment",
  "corrected",
  "delete",
  "id",
  "irrElementId",
  "mismatch",
  "multiplier"
];

const addonFields = ["delete", "id", "irrAddonGroupId", "multiplier"];

const clearItems = itemFields =>
  pipe(
    reject(both(pipe(prop("id"), lt(__, 0)), propEq("delete", true))),
    formatElements,
    map(pick(itemFields))
  );

const getInitiatorId = review =>
  assoc("initiatorId", review.initiator.id, review);

const reviewFields = [
  "addonGroups",
  "elements",
  "questionnaireType",
  "correctCaseType",
  "initiatorId"
];

const updateReview = pipe(
  evolve({
    elements: clearItems(elementsField),
    addonGroups: clearItems(addonFields)
  }),
  removeProperties(["__typename"]),
  getInitiatorId,
  pick(reviewFields)
);

const elementsMatchedName = reviewTypeMatch =>
  propOr(reviewTypeMatch.Targeted, __, reviewTypeMatch);

// eslint-disable-next-line max-statements
export const useComponentLogic = () => {
  const review = useSelector(state => CaseReview.getGeneral(state));
  const reviewType = useSelector(state => CaseReview.getReviewType(state));
  const { id, questionnaireType = "", updatedAt } = review;
  const errors = useSelector(state => CaseReview.getErrors(state));
  const checkErrors = useMemo(
    () => any(propEq("error", true))(errors),
    [errors]
  );
  const displaySavedAt = useMemo(
    () => not(isNullOrEmpty(updatedAt)),
    [updatedAt]
  );

  const dispatch = useDispatch();
  const [saveReview, { loading: saveMutationLoading }] = useMutation(
    UPDATE_CASE_REVIEW_QUESTIONNAIRE,
    {
      refetchQueries: [
        {
          query: GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
          variables: { questionnaireId: id }
        }
      ],
      awaitRefetchQueries: true
    }
  );

  const handleSave = () => {
    const questionnaire = updateReview(review);

    saveReview({
      variables: {
        questionnaireId: id,
        questionnaire
      },
      onCompleted: (data = {}) => {
        const {
          updateCaseReviewQuestionnaire: { errors: serverErrors }
        } = data;

        const sendErrorToElements = elements => {
          dispatch(sendErrorToIrrElements(elements));
        };

        ifElse(
          either(isNullOrEmpty, pipe(prop("elements"), isNullOrEmpty)),
          () => {
            localStorage.removeItem("persist:caseReview");
          },
          pipe(prop("elements"), sendErrorToElements)
        )(serverErrors);
      },
      // eslint-disable-next-line no-empty-function
      onError: () => {}
    });
  };

  const headerTitles = {
    Targeted: {
      completionDate: `${capitalize(
        questionnaireType
      )} Element Review Completion Date`,
      score: "Critical Elements Matched / Total Critical Elements = Score"
    },
    Full: {
      completionDate: "Full Review Completion Date",
      score: "Elements Matched / Total Elements = Score"
    }
  };

  const getHeaderTitles = useMemo(
    () => elementsMatchedName(headerTitles)(reviewType),
    [reviewType, headerTitles]
  );

  return {
    review,
    completionDateName: getHeaderTitles.completionDate,
    elementsPercentageName: getHeaderTitles.score,
    handleSave,
    isDisabled: checkErrors,
    displaySavedAt,
    saveMutationLoading
  };
};
