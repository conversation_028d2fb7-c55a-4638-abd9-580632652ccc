import { decorated<PERSON><PERSON><PERSON> } from "utils/tests/decorated";
import { act, create } from "react-test-renderer";
import wait from "waait";
import Header from "..";
import mocks from "../../General/mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button"
}));

jest.mock("../FieldContainer", () => "FieldContainer");

describe("Header", () => {
  function render(questionnaireType = "specific") {
    return create(
      decoratedApollo({
        component: Header,
        props: {},
        initialValues: {},
        initialAppValues: {
          caseReview: {
            review: {
              questionnaireType,
              score: 0.03,
              mismatchCount: 7,
              criticalElementCount: 9,
              matchedCount: 5,
              owner: {
                id: "1",
                fullName: "<PERSON>"
              },
              initiator: {
                id: "3",
                fullName: "<PERSON><PERSON>"
              },
              visit: {
                number: "73943259"
              },
              patient: {
                firstName: "<PERSON><PERSON>",
                lastName: "<PERSON>",
                mrn: "**********"
              },
              updatedAt: "2023-01-27T08:15:29-08:00",
              completedAt: null,
              caseType: {
                id: "86",
                name: "<PERSON>Q<PERSON>",
                facilityIrrAddonGroups: [
                  {
                    id: "1",
                    name: "Testing",
                    allowMultipliers: false
                  },
                  {
                    id: "2",
                    name: "Testing addon",
                    allowMultipliers: true
                  },
                  {
                    id: "3",
                    name: "Testing Addon Group",
                    allowMultipliers: true
                  }
                ]
              },
              facility: {
                id: "1",
                name: "Large Medical Center"
              },
              abstractor: {
                id: "1",
                fullName: "Russell Reas"
              },
              elements: [
                {
                  elementsId: "1",
                  checked: true,
                  id: "7",
                  irrAddonGroups: [{ id: "3", allowMultipliers: true }],
                  name: "Element 2"
                }
              ],
              addonGroups: [
                {
                  id: "1",
                  multiplier: 44,
                  irrAddonGroup: {
                    id: "1",
                    name: "Testing",
                    allowMultipliers: false
                  }
                },
                {
                  id: "2",
                  multiplier: 1,
                  irrAddonGroup: {
                    id: "3",
                    name: "Testing Addon Group",
                    allowMultipliers: true
                  }
                }
              ]
            },
            errors: [{ id: 1, error: true }]
          }
        },
        apolloMocks: mocks
      })
    );
  }
  test.skip("it renders component correctly specific", async () => {
    const component = render("specific");

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test.skip("it renders component correctly critical", async () => {
    const component = render("critical");

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
