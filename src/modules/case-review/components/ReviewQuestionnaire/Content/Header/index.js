import formatPercentage from "utils/formatPercentage";
import { formatDate } from "utils/formatDate";
import { formatDateTime } from "utils/formatDateTime";
import { Button } from "@q-centrix/q-components-react";
import { useComponentLogic } from "./hooks";
import FieldContainer from "./FieldContainer";
import classnames from "classnames";
// eslint-disable-next-line complexity
export const Header = () => {
  const {
    review,
    elementsPercentageName,
    completionDateName,
    handleSave,
    isDisabled,
    displaySavedAt,
    saveMutationLoading
  } = useComponentLogic();

  const {
    facility,
    abstractor,
    completedAt,
    visit,
    caseType,
    patient,
    owner,
    questionnaireType,
    score,
    mismatchCount,
    criticalElementCount,
    matchedCount,
    updatedAt
  } = review;

  const saveIconClass = classnames(
    { "fa fa-save": !saveMutationLoading },
    {
      "fa-solid fa-spinner-third fa-spin": saveMutationLoading
    }
  );

  return (
    <>
      <div className="header-title">
        <h2>{facility?.name}</h2>
        <div className="save-label-container">
          <div className="completion-date">
            <FieldContainer
              fieldName="Last saved:"
              fieldValue={
                displaySavedAt
                  ? formatDateTime(updatedAt, "MMMM dd, yyyy, h:mm aaa (z)")
                  : null
              }
            />
          </div>
          <Button onClick={handleSave} disabled={isDisabled} bg="success">
            <i className={saveIconClass} />
            Save
          </Button>
        </div>
      </div>
      <div className="separator" />
      <div className="header-fields">
        <FieldContainer
          fieldName="Abstractor"
          fieldValue={abstractor?.fullName}
        />
        <FieldContainer fieldName="Reviewer" fieldValue={owner?.fullName} />
        <div className="completion-date">
          <FieldContainer
            fieldName={completionDateName}
            fieldValue={
              completedAt ? formatDate(completedAt, "MM/dd/yyyy") : null
            }
          />
        </div>

        {questionnaireType === "specific" ? (
          <FieldContainer
            fieldName="Mismatched Elements"
            fieldValue={mismatchCount}
          />
        ) : (
          <FieldContainer
            fieldName={elementsPercentageName}
            fieldValue={
              score
                ? `${matchedCount} / ${criticalElementCount} = ${formatPercentage(
                    score
                  )}`
                : null
            }
          />
        )}
        <FieldContainer fieldName="MRN" fieldValue={patient?.mrn} />
        <FieldContainer fieldName="Visit Number" fieldValue={visit?.number} />
        <FieldContainer fieldName="Case Type" fieldValue={caseType?.name} />
      </div>
    </>
  );
};

export default Header;
