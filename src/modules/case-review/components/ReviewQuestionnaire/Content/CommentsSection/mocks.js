import { GET_CASE_REVIEW_COMMENTS } from "modules/case-review/graphql/query";
import {
  ADD_UPDATE_CASE_REVIEW_COMMENT,
  DELETE_COMMENT
} from "modules/case-review/graphql/mutation";

const commentsMock = [
  {
    request: {
      query: GET_CASE_REVIEW_COMMENTS,
      variables: { questionnaireId: 1 }
    },
    result: {
      data: {
        caseReviewComments: [
          {
            id: 1,
            user: {
              fullName: "<PERSON><PERSON> Reas",
              email: "<EMAIL>"
            },
            createdAt: "2014-06-02T12:30:00",
            updatedAt: "2014-06-02T12:30:00",
            body: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
          },
          {
            id: 2,
            user: {
              fullName: "Holden Spurrier",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-02T03:44:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
          },
          {
            id: 3,
            user: {
              fullName: "Peter Chanthasena",
              email: "<EMAIL>"
            },
            createdAt: "2017-06-04T09:36:00",
            updatedAt: "2017-06-04T09:36:00",
            body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
          },
          {
            id: 4,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2022-06-05T04:15:00",
            updatedAt: "2022-06-05T04:15:00",
            body: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
          }
        ]
      }
    }
  },
  {
    request: {
      query: GET_CASE_REVIEW_COMMENTS
    },
    result: {
      data: {
        caseReviewComments: [
          {
            id: 1,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
          },
          {
            id: 2,
            user: {
              fullName: "Holden Spurrier",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
          },
          {
            id: 3,
            user: {
              fullName: "Peter Chanthasena",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
          },
          {
            id: 4,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
          },
          {
            id: 5,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-02T03:44:00",
            body: "First added comment!"
          }
        ]
      }
    }
  },
  {
    request: {
      query: GET_CASE_REVIEW_COMMENTS
    },
    result: {
      data: {
        caseReviewComments: [
          {
            id: 1,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Updated comment!"
          },
          {
            id: 2,
            user: {
              fullName: "Holden Spurrier",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
          },
          {
            id: 3,
            user: {
              fullName: "Peter Chanthasena",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",

            body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
          },
          {
            id: 4,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
          },
          {
            id: 5,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "First added comment!"
          }
        ]
      }
    }
  },
  {
    request: {
      query: GET_CASE_REVIEW_COMMENTS
    },
    result: {
      data: {
        caseReviewComments: [
          {
            id: 1,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Updated comment!"
          },
          {
            id: 2,
            user: {
              fullName: "Holden Spurrier",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
          },
          {
            id: 3,
            user: {
              fullName: "Peter Chanthasena",
              email: "<EMAIL>"
            },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
          },
          {
            id: 5,
            user: { fullName: "R. Reas", email: "<EMAIL>" },
            createdAt: "2015-06-04T09:36:00",
            updatedAt: "2015-06-04T09:36:00",
            body: "First added comment!"
          }
        ]
      }
    }
  }
];

const updatedCommentMock = [
  {
    request: {
      query: ADD_UPDATE_CASE_REVIEW_COMMENT,
      variables: {
        questionnaireId: 1,
        comment: "First added comment!"
      }
    },
    result: {
      data: {
        addOrUpdateCaseReviewComment: {
          response: [
            {
              id: 1,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2014-06-02T12:30:00",
              updatedAt: "2014-06-02T12:30:00",
              body: "Updated comment!"
            },
            {
              id: 2,
              user: {
                fullName: "Holden Spurrier",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-02T03:44:00",
              updatedAt: "2015-06-02T03:44:00",
              body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
            },
            {
              id: 3,
              user: {
                fullName: "Peter Chanthasena",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
            },
            {
              id: 4,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2015-06-05T04:15:00",
              updatedAt: "2015-06-05T04:15:00",
              body: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
            },
            {
              id: 5,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2022-06-04T07:30:00",
              updatedAt: "2022-06-04T07:30:00",
              body: "First added comment!"
            }
          ],
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: ADD_UPDATE_CASE_REVIEW_COMMENT,
      variables: {
        questionnaireId: 1,
        commentId: 1,
        comment: "Updated comment!"
      }
    },
    result: {
      data: {
        addOrUpdateCaseReviewComment: {
          response: [
            {
              id: 1,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2014-06-02T12:30:00",
              updatedAt: "2014-06-02T12:30:00",
              body: "Updated comment!"
            },
            {
              id: 2,
              user: {
                fullName: "Holden Spurrier",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-02T03:44:00",
              updatedAt: "2015-06-02T03:44:00",
              body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
            },
            {
              id: 3,
              user: {
                fullName: "Peter Chanthasena",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
            },
            {
              id: 4,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              edited: false,
              body: "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
            }
          ],
          errors: null
        }
      }
    }
  },
  {
    request: {
      query: DELETE_COMMENT,
      variables: { commentId: 4 }
    },
    result: {
      data: {
        deleteComment: {
          updatedComments: [
            {
              id: 1,
              user: { fullName: "R. Reas", email: "<EMAIL>" },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              body: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
            },
            {
              id: 2,
              user: {
                fullName: "Holden Spurrier",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              body: "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
            },
            {
              id: 3,
              user: {
                fullName: "Peter Chanthasena",
                email: "<EMAIL>"
              },
              createdAt: "2015-06-04T09:36:00",
              updatedAt: "2015-06-04T09:36:00",
              body: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur."
            }
          ],
          errors: null
        }
      }
    }
  }
];

export default [...commentsMock, ...updatedCommentMock];
