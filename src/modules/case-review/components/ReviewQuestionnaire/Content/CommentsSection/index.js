import Comments from "shared/components/Comments";
import { useComponentLogic } from "./hooks";

const CommentsSection = ({ reviewId, loggedInUser }) => {
  const {
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    addOrUpdateCaseReviewCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    caseReviewCommentsLoading,
    caseReviewCommentsError
  } = useComponentLogic({ reviewId });

  return (
    <section className="tw-relative tw-h-full">
      <header className="tw-border-black-12 tw-border-b tw-bg-white">
        <h2 className="tw-p-5 tw-text-xl tw-font-semibold">Comments</h2>
      </header>
      <Comments
        loggedInUser={loggedInUser}
        newCommentValue={newCommentValue}
        onNewCommentChange={handleNewCommentChange}
        onSubmitNewComment={handleSubmitNewComment}
        createLoading={addOrUpdateCaseReviewCommentLoading}
        onUpdateExistingComment={handleUpdateExistingComment}
        updateLoading={addOrUpdateCaseReviewCommentLoading}
        onDeleteExistingComment={handleDeleteExistingComment}
        deleteLoading={deleteCommentLoading}
        comments={sortedComments}
        loading={caseReviewCommentsLoading}
        error={caseReviewCommentsError}
      />
    </section>
  );
};

export default CommentsSection;
