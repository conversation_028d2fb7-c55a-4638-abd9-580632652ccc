/* eslint-disable max-statements */
import { useMemo, useState } from "react";
import { useQuery, useMutation } from "@apollo/client";
import { GET_CASE_REVIEW_COMMENTS } from "modules/case-review/graphql/query";
import {
  ADD_UPDATE_CASE_REVIEW_COMMENT,
  DELETE_COMMENT
} from "modules/case-review/graphql/mutation";
import { isEmpty } from "ramda";
import { sortByCreatedAtDescending } from "utils/fp/sortByCreatedAtDescending";

export const useComponentLogic = ({ reviewId }) => {
  const [newCommentValue, setNewCommentValue] = useState("");

  const {
    data: caseReviewCommentsData = {},
    loading: caseReviewCommentsLoading,
    error: caseReviewCommentsError
  } = useQuery(GET_CASE_REVIEW_COMMENTS, {
    variables: { questionnaireId: reviewId }
  });

  const { caseReviewComments = [] } = caseReviewCommentsData;

  const sortedComments = useMemo(
    () => sortByCreatedAtDescending(caseReviewComments),
    [caseReviewComments]
  );

  const [
    addOrUpdateCaseReviewComment,
    { loading: addOrUpdateCaseReviewCommentLoading }
  ] = useMutation(ADD_UPDATE_CASE_REVIEW_COMMENT, {
    refetchQueries: [GET_CASE_REVIEW_COMMENTS]
  });

  const handleNewCommentChange = e => setNewCommentValue(e.target.value);

  const handleSubmitNewComment = e => {
    e.preventDefault();

    if (isEmpty(newCommentValue)) return;

    addOrUpdateCaseReviewComment({
      variables: {
        questionnaireId: reviewId,
        comment: newCommentValue.trim()
      },
      onCompleted: () => {
        setNewCommentValue("");
      }
    });
  };

  const handleUpdateExistingComment = (
    commentId,
    updatedComment,
    { onCompleted }
  ) => {
    addOrUpdateCaseReviewComment({
      variables: {
        questionnaireId: reviewId,
        commentId,
        comment: updatedComment.trim()
      },
      onCompleted
    });
  };

  const [deleteComment, { loading: deleteCommentLoading }] = useMutation(
    DELETE_COMMENT,
    { refetchQueries: [GET_CASE_REVIEW_COMMENTS] }
  );

  const handleDeleteExistingComment = commentId => {
    deleteComment({ variables: { commentId } });
  };

  return {
    newCommentValue,
    handleNewCommentChange,
    handleSubmitNewComment,
    handleUpdateExistingComment,
    addOrUpdateCaseReviewCommentLoading,
    handleDeleteExistingComment,
    deleteCommentLoading,
    sortedComments,
    caseReviewCommentsLoading,
    caseReviewCommentsError
  };
};
