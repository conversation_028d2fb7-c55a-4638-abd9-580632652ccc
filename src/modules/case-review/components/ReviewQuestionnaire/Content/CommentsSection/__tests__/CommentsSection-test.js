import { GET_CASE_REVIEW_COMMENTS } from "modules/case-review/graphql/query";
import { act, create } from "react-test-renderer";
import { decorated<PERSON><PERSON>lo } from "utils/tests/decorated";
import wait from "waait";
import CommentsSection from "..";
import mocks from "../mocks";

jest.mock("@q-centrix/q-components-react", () => ({
  Button: "Button",
  Spinner: "Spinner",
  TextArea: "TextArea"
}));
jest.mock("shared/components/Comments", () => "Comments");

describe("CommentsSection", () => {
  function render() {
    return create(
      decoratedApollo({
        component: CommentsSection,
        props: { loggedInUser: { id: "1", email: "<EMAIL>" } },
        initialValues: {},
        initialAppValues: {},
        apolloMocks: mocks
      })
    );
  }

  test("it renders component with Loading Spinner", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("it renders component with error", async () => {
    const errorMock = [
      {
        request: { query: GET_CASE_REVIEW_COMMENTS },
        error: new Error("an error occurred")
      }
    ];

    const component = render(errorMock);

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });

  test("it renders component with data", async () => {
    const component = render();

    await act(() => wait(100));

    expect(component).toMatchSnapshot();
  });
});
