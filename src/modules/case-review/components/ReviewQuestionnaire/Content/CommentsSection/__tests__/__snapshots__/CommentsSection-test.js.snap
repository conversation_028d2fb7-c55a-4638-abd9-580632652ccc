// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommentsSection it renders component with Loading Spinner 1`] = `
<section
  className="tw-relative tw-h-full"
>
  <header
    className="tw-border-black-12 tw-border-b tw-bg-white"
  >
    <h2
      className="tw-p-5 tw-text-xl tw-font-semibold"
    >
      Comments
    </h2>
  </header>
  <Comments
    comments={Array []}
    createLoading={false}
    deleteLoading={false}
    loading={true}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "id": "1",
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />
</section>
`;

exports[`CommentsSection it renders component with data 1`] = `
<section
  className="tw-relative tw-h-full"
>
  <header
    className="tw-border-black-12 tw-border-b tw-bg-white"
  >
    <h2
      className="tw-p-5 tw-text-xl tw-font-semibold"
    >
      Comments
    </h2>
  </header>
  <Comments
    comments={
      Array [
        Object {
          "body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 1,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
        Object {
          "body": "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 2,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "Holden Spurrier",
          },
        },
        Object {
          "body": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 3,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "Peter Chanthasena",
          },
        },
        Object {
          "body": "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 4,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
        Object {
          "body": "First added comment!",
          "createdAt": "2015-06-04T09:36:00",
          "id": 5,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
      ]
    }
    createLoading={false}
    deleteLoading={false}
    loading={false}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "id": "1",
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />
</section>
`;

exports[`CommentsSection it renders component with error 1`] = `
<section
  className="tw-relative tw-h-full"
>
  <header
    className="tw-border-black-12 tw-border-b tw-bg-white"
  >
    <h2
      className="tw-p-5 tw-text-xl tw-font-semibold"
    >
      Comments
    </h2>
  </header>
  <Comments
    comments={
      Array [
        Object {
          "body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 1,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
        Object {
          "body": "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 2,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "Holden Spurrier",
          },
        },
        Object {
          "body": "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 3,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "Peter Chanthasena",
          },
        },
        Object {
          "body": "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
          "createdAt": "2015-06-04T09:36:00",
          "id": 4,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
        Object {
          "body": "First added comment!",
          "createdAt": "2015-06-04T09:36:00",
          "id": 5,
          "updatedAt": "2015-06-02T03:44:00",
          "user": Object {
            "email": "<EMAIL>",
            "fullName": "R. Reas",
          },
        },
      ]
    }
    createLoading={false}
    deleteLoading={false}
    loading={false}
    loggedInUser={
      Object {
        "email": "<EMAIL>",
        "id": "1",
      }
    }
    newCommentValue=""
    onDeleteExistingComment={[Function]}
    onNewCommentChange={[Function]}
    onSubmitNewComment={[Function]}
    onUpdateExistingComment={[Function]}
    updateLoading={false}
  />
</section>
`;
