/* eslint-disable max-statements */
import { useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import accountSettingsSelectors from "modules/app/redux/selectors/accountSettings";
import {
  setReviewData,
  setIsReviewOwner,
  addError,
  setReviewType
} from "modules/case-review/redux/slice";
import CaseReview from "modules/case-review/redux/selectors";
import { useQuery } from "@apollo/client";
import { GET_CASE_REVIEW_QUESTIONNAIRE_DATA } from "modules/case-review/graphql/query";
import { eqProps, whereEq, map, and, isEmpty } from "ramda";

export const useComponentLogic = () => {
  const { reviewId } = useParams();
  const dispatch = useDispatch();
  const loggedInUser = useSelector(accountSettingsSelectors.accountSettings);
  const errors = useSelector(state => CaseReview.getErrors(state));
  const mutationLoading = useSelector(state =>
    CaseReview.getMutationLoading(state)
  );
  const checkErrors = useMemo(
    () =>
      map(
        whereEq({
          id: "initiator",
          error: true
        })
      )(errors),
    [errors]
  );

  const { data, loading, error } = useQuery(
    GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
    {
      variables: { questionnaireId: reviewId },
      skip: !loggedInUser.id
    }
  );

  useEffect(() => {
    if (data) {
      const { caseReviewQuestionnaire = {} } = data;

      dispatch(setReviewData(caseReviewQuestionnaire));
      dispatch(setReviewType(caseReviewQuestionnaire.questionnaireType));

      dispatch(
        setIsReviewOwner(
          eqProps("id", loggedInUser, caseReviewQuestionnaire.owner)
        )
      );
      if (and(!caseReviewQuestionnaire.initiator, isEmpty(checkErrors))) {
        dispatch(addError({ id: "initiator", error: true }));
      }
    }
  }, [data]);

  return {
    reviewId,
    loading,
    error,
    mutationLoading,
    loggedInUser
  };
};
