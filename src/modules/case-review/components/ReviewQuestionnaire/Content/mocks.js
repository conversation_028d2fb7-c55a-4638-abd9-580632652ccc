import { GET_CASE_REVIEW_QUESTIONNAIRE_DATA } from "modules/case-review/graphql/query";
import { UPDATE_CASE_REVIEW_QUESTIONNAIRE } from "modules/case-review/graphql/mutation";

export const createQueryMocks = (id, questionnaireType, readOnly) => ({
  request: {
    query: GET_CASE_REVIEW_QUESTIONNAIRE_DATA,
    variables: { questionnaireId: id }
  },
  result: {
    data: {
      caseReviewQuestionnaire: {
        id,
        questionnaireType,
        score: 0.67,
        mismatchCount: 1,
        criticalElementCount: 3,
        matchedCount: 2,
        updatedAt: "2022-12-02 00:00:01",
        completedAt: null,
        availableIrrAddonGroups: [
          {
            __typename: "IrrAddonGroup",
            id: "1",
            name: "Test",
            allowMultipliers: true
          },
          {
            __typename: "IrrAddonGroup",
            id: "2",
            name: "Test Group 2",
            allowMultipliers: false
          }
        ],
        facility: {
          __typename: "Facility",
          id: "1",
          name: "Large Medical Center"
        },
        readOnly,
        abstractor: {
          __typename: "User",
          id: "1",
          fullName: "<PERSON>"
        },
        initiator: {
          __typename: "User",
          id: "1",
          fullName: "Russell Reas"
        },
        owner: {
          __typename: "User",
          id: "1",
          fullName: "Russell Reas"
        },
        patient: {
          __typename: "Patient",
          firstName: "Madelyn Effertz",
          lastName: "Grady Walker",
          mrn: "**********"
        },
        visit: {
          __typename: "Visit",
          number: "73943259"
        },
        task: {
          __typename: "Task",
          case: {
            __typename: "Cases",
            id: "49"
          }
        },
        caseType: {
          __typename: "Case",
          id: "22",
          name: "CDiff"
        },
        correctCaseType: false,
        originalCaseType: {
          __typename: "Case",
          id: "22",
          name: "CDiff"
        },
        addonGroups: [],
        elements: [
          {
            id: 1,
            irrElement: {
              id: "5",
              name: "Saved to BE Element"
            },
            irrElementId: "5",
            mismatch: true,
            multiplier: 0,
            corrected: true,
            comment: "Test comment for critical review",
            caseReviewCategory: {
              id: 1,
              name: "Location Error"
            },
            delete: false,
            checked: true
          }
        ]
      }
    }
  }
});

const reviewDataMocks = [
  createQueryMocks("1", "critical", false),
  createQueryMocks("2", "specific", false),
  createQueryMocks("3", "ongoing", false),
  createQueryMocks("4", "critical", true),
  createQueryMocks("5", "specific", true),
  createQueryMocks("6", "ongoing", true)
];

const caseReviewDataMocks = [
  {
    request: {
      query: UPDATE_CASE_REVIEW_QUESTIONNAIRE,
      variables: {
        review: {
          id: 6,
          facilityName: "Willow Creek Women’s Hospital",
          type: "full",
          ownerId: "1",
          reviewerId: "1",
          reviewDate: "2022-07-04",
          mrn: "12346",
          visit: "14",
          caseTypeId: "2",
          canCompleteReview: "Yes",
          caseCompletedThru: "",
          correctCaseTypeId: null,
          abstractorCorrect: "No",
          reasonReview: "Initial",
          lastUpdatedAt: 1674520258051,
          qCentrixEmployee: true,
          addons: [
            {
              id: 1,
              addonId: 1,
              value: true,
              multiplierValue: 2
            }
          ],
          elementsReviewed: [{ id: "a0lB_uRe5QDo_UWgdvdYn", new: true }],
          mismatchedElements: [
            {
              id: 1,
              name: "Critical Element",
              allowMultipliers: true,
              multipliers: 1,
              mismatchedReason: null,
              comments: "",
              isChecked: true,
              isCorrected: false
            },
            {
              id: 2,
              name: "Critical Element",
              allowMultipliers: false,
              multipliers: null,
              mismatchedReason: null,
              comments: "",
              isChecked: false,
              isCorrected: false
            },
            {
              id: 3,
              name: "Critical Element",
              allowMultipliers: true,
              multipliers: 1,
              mismatchedReason: null,
              comments: "Comments",
              isChecked: false,
              isCorrected: false
            }
          ],
          __typename: "FullReview"
        }
      }
    },
    result: {
      data: {
        review: {
          id: 6,
          facilityName: "Willow Creek Women’s Hospital",
          type: "full",
          ownerId: "1",
          reviewerId: "1",
          reviewDate: "2022-07-04",
          mrn: "12346",
          visit: "14",
          caseTypeId: "1",
          canCompleteReview: "Yes",
          caseCompletedThru: "",
          correctCaseTypeId: null,
          abstractorCorrect: "",
          reasonReview: "Initial",
          lastUpdatedAt: "2022-07-06 00:00:01",
          qCentrixEmployee: true,
          addons: [
            {
              id: 1,
              addonId: 1,
              value: true,
              multiplierValue: 2
            }
          ],
          elementsReviewed: [],
          mismatchedElements: [
            {
              id: 1,
              name: "Critical Element",
              allowMultipliers: true,
              multipliers: 1,
              mismatchedReason: null,
              comments: "",
              isChecked: true,
              isCorrected: false
            },
            {
              id: 2,
              name: "Critical Element",
              allowMultipliers: false,
              multipliers: null,
              mismatchedReason: null,
              comments: "",
              isChecked: false,
              isCorrected: false
            },
            {
              id: 3,
              name: "Critical Element",
              allowMultipliers: true,
              multipliers: 1,
              mismatchedReason: null,
              comments: "Comments",
              isChecked: false,
              isCorrected: false
            }
          ],
          __typename: "FullReview"
        }
      }
    }
  }
];

export default [...reviewDataMocks, ...caseReviewDataMocks];
