import { path, propOr } from "ramda";
import localizeSelectors from "utils/localizeSelectors";

const localState = path(["app", "caseReview"]);

export function getGeneral(state) {
  return state.review;
}

export function getReviewType(state) {
  return propOr("", "reviewType", state);
}

export function getErrors(state) {
  return state.errors;
}

export function getCaseId(state) {
  return path(["review", "task", "case", "id"], state);
}

export function getMutationLoading(state) {
  return state.mutationLoading;
}

export default localizeSelectors(localState, {
  getGeneral,
  getReviewType,
  getErrors,
  getCaseId,
  getMutationLoading
});
