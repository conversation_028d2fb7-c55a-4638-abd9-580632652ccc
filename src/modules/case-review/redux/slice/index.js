import { isAfter } from "date-fns";
import nanoid from "nanoid";
import {
  always,
  and,
  append,
  assoc,
  assocPath,
  dissoc,
  either,
  equals,
  evolve,
  findIndex,
  ifElse,
  includes,
  gt,
  gte,
  lensPath,
  lte,
  mergeRight,
  over,
  path,
  pipe,
  prop,
  propEq,
  reduce,
  remove,
  set,
  when,
  __,
  last,
  add,
  descend,
  sort,
  complement,
  both,
  pathEq,
  multiply,
  isEmpty,
  reject
} from "ramda";
import { createSlice } from "@reduxjs/toolkit";

const updateLastUpdatedAt = (date = Date.now()) =>
  assocPath(["review", "lastUpdatedAt"], date);

const getAddonGroupIndex = addonGroupsId => state =>
  pipe(
    path(["review", "addonGroups"]),
    findIndex(propEq("id", addonGroupsId))
  )(state);

const getElementIndex =
  (elementId, irrElementId, ignoreDeleted = true) =>
  state =>
    pipe(
      path(["review", "elements"]),
      findIndex(
        both(
          either(
            propEq("id", elementId),
            pathEq(["irrElement", "id"], irrElementId)
          ),
          either(
            pipe(prop("id"), parseInt, gt(__, 0)),
            either(always(!ignoreDeleted), complement(prop("delete")))
          )
        )
      )
    )(state);

const randomNegativeNumber = () => multiply(Math.random(), -100);

const getNegativeNumber = pipe(
  path(["review", "elements"]),
  ifElse(
    isEmpty,
    () => randomNegativeNumber(),
    pipe(
      sort(descend(prop("id"))),
      last,
      prop("id"),
      id => add(Number(id), randomNegativeNumber()),
      when(gt(__, 0), multiply(-1))
    )
  )
);

const addErrorToElement = (state, { irrElementId, message: { errors } = {} }) =>
  pipe(
    getElementIndex(null, irrElementId),
    index => lensPath(["review", "elements", index]),
    lens => over(lens, assoc("error", errors), state)
  )(state);

const targetedQuestionnaireTypes = ["critical", "specific"];

const initialState = {
  review: {},
  errors: [],
  reviewType: "",
  mutationLoading: false
};

export const reviewSlice = createSlice({
  name: "review",
  initialState,
  reducers: {
    removeAllErrors: state => evolve({ errors: always([]) }, state),
    addError: (state, { payload }) => {
      const { id, error } = payload;

      return pipe(
        path(["errors"]),
        findIndex(propEq("id", id)),
        ifElse(
          gt(0),
          () =>
            evolve({
              errors: append(payload)
            })(state),
          index => set(lensPath(["errors", index, "error"]), error, state)
        )
      )(state);
    },

    removeError: (state, { payload }) => {
      const { id } = payload;

      const errorIndex = pipe(
        prop("errors"),
        findIndex(propEq("id", id))
      )(state);

      return when(
        always(gte(errorIndex, 0)),
        evolve({ errors: remove(errorIndex, 1) })
      )(state);
    },
    updateElements: (state, { payload }) => {
      const {
        elementsId,
        irrElementId,
        irrElementName,
        checked,
        name,
        value,
        caseReviewCategoryId,
        isTargetedReview
      } = payload;

      return pipe(
        getElementIndex(elementsId, irrElementId),
        ifElse(
          lte(0),
          index =>
            over(
              lensPath(["review", "elements", index]),
              pipe(assoc(name, value), dissoc("error")),
              state
            ),
          when(always(checked), () =>
            over(
              lensPath(["review", "elements"]),
              append({
                id: getNegativeNumber(state),
                irrElement: { id: irrElementId, name: irrElementName },
                irrElementId,
                mismatch: !isTargetedReview,
                corrected: false,
                comment: "",
                caseReviewCategory: {},
                delete: false,
                caseReviewCategoryId
              }),
              state
            )
          )
        ),
        over(
          lensPath(["review", "elements"]),
          reject(both(prop("delete"), pipe(prop("id"), gte(0))))
        )
      )(state);
    },
    updateElementsReviewed: (state, { payload }) => {
      const {
        elementsId,
        irrElementId,
        irrElementName,
        checked,
        name,
        value,
        caseReviewCategoryId,
        isTargetedReview
      } = payload;

      return pipe(
        getElementIndex(elementsId, irrElementId),
        ifElse(
          lte(0),
          index =>
            over(
              lensPath(["review", "elements", index]),
              pipe(assoc(name, value), dissoc("error")),
              state
            ),
          when(always(checked), () =>
            over(
              lensPath(["review", "elements"]),
              append({
                id: getNegativeNumber(state),
                irrElement: { id: irrElementId, name: irrElementName },
                irrElementId,
                mismatch: !isTargetedReview,
                corrected: false,
                comment: "",
                caseReviewCategory: {},
                delete: false,
                caseReviewCategoryId
              }),
              state
            )
          )
        )
      )(state);
    },
    deleteElementContent: (state, { payload }) => {
      const { irrElementId } = payload;

      return pipe(getElementIndex(undefined, irrElementId, false), index =>
        over(
          lensPath(["review", "elements", index]),
          object =>
            mergeRight(object, {
              mismatch: false,
              corrected: false,
              comment: "",
              caseReviewCategory: {},
              caseReviewCategoryId: null,
              delete: true,
              checked: false,
              multiplier: null
            }),
          state
        )
      )(state);
    },
    deleteMismatchedElementContent: (state, { payload }) => {
      const { irrElementId } = payload;

      return pipe(getElementIndex(undefined, irrElementId), index =>
        over(
          lensPath(["review", "elements", index]),
          object =>
            mergeRight(object, {
              corrected: false,
              multiplier: null,
              comment: "",
              caseReviewCategory: {}
            }),
          state
        )
      )(state);
    },
    updateAddonValue: (state, { payload }) => {
      const { addonGroupsId, irrAddonGroupId, name, value, checked } = payload;

      return pipe(
        getAddonGroupIndex(addonGroupsId),
        ifElse(
          lte(0),
          index =>
            set(lensPath(["review", "addonGroups", index, name]), value, state),
          when(always(checked), () =>
            over(
              lensPath(["review", "addonGroups"]),
              append({
                id: getNegativeNumber(state),
                irrAddonGroup: { id: irrAddonGroupId },
                irrAddonGroupId,
                delete: false
              }),
              state
            )
          )
        )
      )(state);
    },
    setReviewData: (state, { payload }) => {
      const newReview = evolve(
        {
          elementsReviewed: append({ id: nanoid(), new: true })
        },
        payload
      );
      const getPayloadTimeStamp = ifElse(
        prop("updatedAt"),
        pipe(prop("updatedAt"), Date.parse),
        always(0)
      )(payload);
      const getStateTimeStamp = state.review.updatedAt;
      const checkReviewIds = equals(state.review.id, payload.id);
      const checkReviewTimes = isAfter(getStateTimeStamp, getPayloadTimeStamp);
      const selectData = and(checkReviewIds, checkReviewTimes)
        ? state.review
        : newReview;

      return assoc("review", selectData, state);
    },
    setGeneralValue: (state, { payload }) => {
      const { value, name } = payload;

      return pipe(
        evolve({ review: assoc(name, value) }),
        updateLastUpdatedAt()
      )(state);
    },
    setIsReviewOwner: (state, { payload }) => assoc("isOwner", payload, state),
    sendErrorToIrrElements: (state, { payload: elements }) =>
      reduce(addErrorToElement, state, elements),
    setReviewType: (state, { payload }) =>
      ifElse(
        () => includes(payload, targetedQuestionnaireTypes),
        assoc("reviewType", "Targeted"),
        assoc("reviewType", "Full")
      )(state),

    setMutationLoading: (state, { payload }) =>
      assoc("mutationLoading", payload, state)
  }
});

// Action creators are generated for each case reducer function
export const {
  updateElements,
  updateElementsReviewed,
  deleteElementContent,
  deleteMismatchedElementContent,
  setReviewData,
  setGeneralValue,
  setIsReviewOwner,
  updateAddonValue,
  addError,
  removeError,
  sendErrorToIrrElements,
  removeAllErrors,
  setReviewType,
  setMutationLoading
} = reviewSlice.actions;

export default reviewSlice.reducer;
