import { gql } from "@apollo/client";

// We need to add this type to caseReviewQuestionnaire for Full review. Commented out since it was not implemented on the backend.
// ... on FullReview {
//   canCompleteReview
//   caseCompletedThru
//   qCentrixEmployee
// }

export const GET_CASE_REVIEW_QUESTIONNAIRE_DATA = gql`
  query caseReviewQuestionnaire($questionnaireId: ID!) {
    caseReviewQuestionnaire(questionnaireId: $questionnaireId) {
      id
      losOverride
      questionnaireType
      score
      mismatchCount
      criticalElementCount
      matchedCount
      updatedAt
      completedAt
      availableIrrAddonGroups {
        id
        name
        allowMultipliers
      }
      facility {
        id
        name
      }
      readOnly
      abstractor {
        id
        fullName
      }
      initiator {
        id
        fullName
      }
      owner {
        id
        fullName
      }
      patient {
        firstName
        lastName
        mrn
      }
      visit {
        number
      }
      task {
        case {
          id
        }
      }
      caseType {
        id
        name
      }
      correctCaseType
      originalCaseType {
        id
        name
      }
      addonGroups {
        id
        multiplier
        irrAddonGroup {
          id
          name
          allowMultipliers
        }
      }
      elements {
        id
        mismatch
        multiplier
        corrected
        comment
        caseReviewCategory {
          id
          name
        }
        irrElement {
          id
          name
        }
      }
    }
  }
`;

export const GET_CASE_REVIEW_CATEGORIES_DATA = gql`
  query caseReviewCategories {
    caseReviewCategories {
      id
      name
    }
  }
`;

export const GET_CASE_REVIEW_ELEMENT_OPTIONS_DATA = gql`
  query caseReviewElementOptions(
    $questionnaireId: ID!
    $addonGroupIds: [ID!]!
  ) {
    caseReviewElementOptions(
      questionnaireId: $questionnaireId
      addonGroupIds: $addonGroupIds
    ) {
      id
      name
      allowMultiplier
      irrAddonGroups {
        id
        allowMultipliers
      }
    }
  }
`;

export const GET_CASE_REVIEW_INITIATORS = gql`
  query caseReviewInitiators(
    $questionnaireId: ID!
    $query: String!
    $limit: Int!
  ) {
    caseReviewInitiators(
      questionnaireId: $questionnaireId
      query: $query
      limit: $limit
    ) {
      id
      fullName
    }
  }
`;

export const GET_CASE_REVIEW_QUESTIONNAIRE_CASE_TYPES = gql`
  query caseReviewQuestionnaireCaseTypes($questionnaireId: ID!) {
    caseReviewQuestionnaireCaseTypes(questionnaireId: $questionnaireId) {
      id
      name
      facilityIrrAddonGroups {
        id
        name
        allowMultipliers
      }
      lengthOfStay {
        start
        end
        allowOverride
      }
    }
  }
`;

export const GET_CASE_REVIEW_COMMENTS = gql`
  query caseReviewComments($questionnaireId: ID!) {
    caseReviewComments(questionnaireId: $questionnaireId) {
      id
      user {
        id
        fullName
        email
      }
      body
      createdAt
      updatedAt
    }
  }
`;

export const GET_INCOMPLETE_REASONS = gql`
  query incompleteReasons {
    incompleteReasons {
      value
      label
    }
  }
`;
