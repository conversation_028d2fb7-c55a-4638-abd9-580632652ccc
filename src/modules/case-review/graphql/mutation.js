import { gql } from "@apollo/client";

export const ADD_UPDATE_CASE_REVIEW_COMMENT = gql`
  mutation addOrUpdateCaseReviewComment(
    $questionnaireId: ID!
    $commentId: ID
    $comment: String!
  ) {
    addOrUpdateCaseReviewComment(
      questionnaireId: $questionnaireId
      commentId: $commentId
      comment: $comment
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const DELETE_COMMENT = gql`
  mutation deleteComment($commentId: ID!) {
    deleteComment(commentId: $commentId) {
      response
      errors {
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_CASE_REVIEW_QUESTIONNAIRE = gql`
  mutation updateCaseReviewQuestionnaire(
    $questionnaireId: ID!
    $questionnaire: CaseReviewQuestionnaireInput!
  ) {
    updateCaseReviewQuestionnaire(
      questionnaireId: $questionnaireId
      questionnaire: $questionnaire
    ) {
      response
      errors {
        elements {
          irrElementId
          message {
            errors
          }
        }
        messages {
          attribute
        }
      }
    }
  }
`;

export const UPDATE_CASE_REVIEW_CASE_TYPE = gql`
  mutation updateCaseReviewCaseType(
    $questionnaireId: ID!
    $caseTypeId: ID!
    $initiatorId: ID
    $correctCaseType: Boolean
    $losOverride: Boolean
  ) {
    updateCaseReviewCaseType(
      questionnaireId: $questionnaireId
      caseTypeId: $caseTypeId
      initiatorId: $initiatorId
      correctCaseType: $correctCaseType
      losOverride: $losOverride
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;

export const UPDATE_CASE_REVIEW_TYPE = gql`
  mutation updateCaseReviewType(
    $questionnaireId: ID!
    $questionnaireType: String!
    $initiatorId: ID
    $correctCaseType: Boolean
  ) {
    updateCaseReviewType(
      questionnaireId: $questionnaireId
      questionnaireType: $questionnaireType
      initiatorId: $initiatorId
      correctCaseType: $correctCaseType
    ) {
      response
      errors {
        fullMessages
        messages {
          attribute
          errors
        }
      }
    }
  }
`;
