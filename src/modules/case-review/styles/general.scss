@use "src/styles/_variables";
.card-header-title {
  margin-left: 20px;
  font-size: 16px;
}

.general-form {
  margin: 1.25rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .motion-question-container {
    display: flex;
    flex-direction: column;
    gap: 11px;
    margin-bottom: 5px;
  }

  .question-container {
    display: flex;
    flex-direction: column;
    gap: 5px;

    label {
      font-family: variables.$font-family;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      color: variables.$text-color;
    }

    .radio-cmp-container,
    .checkbox-label-container {
      padding-left: 1rem;
    }

    .custom-dropdown {
      width: 320px;
    }

    > label {
      font-family: variables.$font-family;
      font-style: normal;
      font-weight: 600;
      font-size: 13px;
      line-height: 16px;
      color: rgba(0, 0, 0, 0.7);
    }

    > span {
      font-family: variables.$font-family;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      color: #000000;
    }

    > input {
      height: 40px;
      gap: 10px;
      border: 1px solid rgba(0, 0, 0, 0.29);
      border-radius: 4px;
      font-family: variables.$font-family;
      padding: 9px 16px;
      resize: none;
      line-height: 18px;
      font-weight: 400;
      font-size: 14px;
    }
  }
  .multiplier-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 60px;
    height: 40px;

    .multiplier {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;

      input {
        box-sizing: border-box;
        padding: 9px 16px;
        width: 88px;
        height: 40px;
        background: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.29);
        border-radius: 4px;

        font-family: variables.$font-family;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        text-align: right;
        color: variables.$text-color;
      }
    }
  }

  .indent-1 {
    padding-left: 4.5rem;
  }
}
