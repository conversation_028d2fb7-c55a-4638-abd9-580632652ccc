import React from "react";
import PropTypes from "prop-types";

/**
 * `SearchBar` is a UI Component designed to display a text input to search.
 *
 * @component
 * @param {object} props - Component props.
 * @param {Function} props.handleChange - The function to call when the input value changes.
 * @param {string} props.placeholder - The placeholder of the input.
 * @param {string} props.iconClass - Classes to customize icon placement.
 * @param {string} props.inputClass - Classes to customize icon placement.
 * @param {string} props.searchInput - Input value.
 * @param {Function} props.onKeyDown - The function to be called on Enter keypress.
 * @param {Function} props.handleClear - The funcion for the icon to clear the input.
 */

export const SearchBar = ({
  placeholder,
  handleChange,
  iconClass,
  searchInput,
  onKeyDown,
  handleClear,
  inputClass = "",
  ...props
}) => {
  return (
    <div className="tw-flex tw-flex-row tw-relative">
      <input
        type="text"
        name="Search"
        id="Search"
        placeholder={placeholder}
        onChange={handleChange}
        value={searchInput}
        onKeyDown={onKeyDown}
        className={`${inputClass} tw-rounded-md tw-border tw-min-w-[300px] tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-600 focus:tw-border hover:tw-border-qc-blue-600 tw-text-sm placeholder:tw-text-qcNeutrals-400`}
        {...props}
      />
      {searchInput ? (
        <div className={iconClass}>
          <i
            className="fa-solid fa-circle-xmark tw-text-black-70 tw-pl-5"
            onClick={handleClear}
          ></i>
        </div>
      ) : (
        <div className={iconClass}>
          <i className="fa-solid fa-magnifying-glass tw-text-qc-blue-700 tw-pl-5"></i>
        </div>
      )}
    </div>
  );
};

SearchBar.displayName = "SearchBar";

SearchBar.propTypes = {
  /**
   * The placeholder of the input.
   */
  placeholder: PropTypes.string,
  /**
   * The search value of the input.
   */
  searchInput: PropTypes.string,
  /**
   * Classes to customize the placement of the input icon.
   */
  iconClass: PropTypes.string,
  /**
   * Classes to customize the input tag.
   */
  inputClass: PropTypes.string,
  /**
   * The function to call when the input value changes.
   */
  handleChange: PropTypes.func,
  /**
   * The function to call when user presses enter.
   */
  onKeyDown: PropTypes.func,
  /**
   * The function to call when user needs to clear the input.
   */
  handleClear: PropTypes.func
};

export default SearchBar;
