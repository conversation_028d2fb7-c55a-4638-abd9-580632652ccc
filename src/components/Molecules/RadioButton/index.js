import React from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";
import cn from "../../../utils/cn";

const radioButtonVariants = cva(
  "tw-appearance-none before:tw-block before:tw-h-[16px] before:tw-w-[16px] before:tw-rounded-full before:tw-transition-shadow before:tw-ease-in-out before:tw-duration-200",
  {
    variants: {
      checked: {
        true: "before:tw-shadow-[inset_0px_0px_0px_5px_#005f86]",
        false: "before:tw-shadow-[inset_0px_0px_0px_2px_#005f86]"
      }
    },
    compoundVariants: [
      {
        checked: true,
        disabled: true,
        class: "before:tw-shadow-[inset_0px_0px_0px_5px_rgba(0,0,0,0.29)]"
      },
      {
        checked: false,
        disabled: true,
        class: "before:tw-shadow-[inset_0px_0px_0px_2px_rgba(0,0,0,0.29)]"
      },
      {
        checked: true,
        disabled: false,
        class: "before:tw-shadow-[inset_0px_0px_0px_5px_#005f86]"
      },
      {
        checked: false,
        disabled: false,
        class: "before:tw-shadow-[inset_0px_0px_0px_2px_#005f86]"
      }
    ],
    defaultVariants: {
      checked: false,
      disabled: false
    }
  }
);

const labelVariants = cva("tw-text-black-70 tw-text-sm", {
  variants: {
    checked: {
      true: "tw-text-qc-blue-800",
      false: ""
    },
    disabled: {
      true: "tw-text-black-29",
      false: ""
    }
  },
  defaultVariants: {
    checked: false,
    disabled: false
  }
});

/**
 *
 * `RadioButton` is an Atom Level component designed to replace the
 * default input element with the type of radio. This is a custom
 * radio button input element.
 *
 * @param {string} name - name of the radio button, necessary to link btn with label
 * @param {string} title - label of the radio button
 * @param {boolean} checked - whether the input is checked or not. True of False
 * @param {string} value - the value of the radio button
 * @param {function} onChange - Function passed in to check if the id was checked
 *
 **/

export const RadioButton = React.forwardRef((props, ref) => {
  const { name, label, checked, value, onChange, disabled, ...rest } = props;
  const uniqueProp = `${name}-${value}`;

  return (
    <div className="tw-inline-flex tw-items-center tw-gap-2.5">
      <input
        ref={ref}
        id={uniqueProp}
        name={name}
        value={value}
        checked={checked}
        onChange={onChange}
        type="radio"
        disabled={disabled}
        className={cn(
          radioButtonVariants({
            checked,
            disabled
          })
        )}
        {...rest}
      />
      {label && (
        <label
          htmlFor={uniqueProp}
          className={cn(
            labelVariants({
              checked,
              disabled
            })
          )}
        >
          {label}
        </label>
      )}
    </div>
  );
});

RadioButton.displayName = "RadioButton";

RadioButton.propTypes = {
  /**
   * The name of the radio button, necessary to link the button with the label.
   */
  name: PropTypes.string,
  /**
   * The label of the radio button.
   */
  label: PropTypes.string,
  /**
   * Whether the input is checked or not. True or False.
   */
  checked: PropTypes.bool,
  /**
   * The value of the radio button.
   */
  value: PropTypes.string,
  /**
   * Function passed in to check if the id was checked.
   */
  onChange: PropTypes.func,
  /**
   * A boolean to disable the radio button.
   */
  disabled: PropTypes.bool
};

RadioButton.defaultProps = {
  name: "RadioButton",
  label: "",
  checked: false,
  value: ""
};

export default RadioButton;
