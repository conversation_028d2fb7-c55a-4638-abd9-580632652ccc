import React from "react";
import PropTypes from "prop-types";
import UserTab from "./UserTab";
import ManageTab from "./ManageTab";
import qAppsLogo from "./images/qapps_logo.png";
import { useComponentLogic } from "./hooks";

/**
 *
 * `Topbar` is an Molecul Level component designed to show a Topbar with different elements
 *
 * @param {string} label - sets the label for the element
 * @param {function} onClick - function passed into the component to be called onClick.
 * @param {node} children - children componenets of the topbar
 * @param {object} client - client for the query

 **/

export const Topbar = ({ children, onLogoClick, onBackArrowClick, client }) => {
  useComponentLogic();

  return (
    <div className="tw-flex tw-items-center tw-bg-white tw-border tw-border-black-12 tw-shadow-card tw-w-full tw-h-[50px] tw-px-5 tw-justify-between">
      <div className="tw-pr-2.5 tw-border-r tw-border-gray-600 tw-mr-5">
        {onBackArrowClick ? (
          <button
            type="button"
            className="tw-border-none tw-bg-white tw-cursor-pointer"
            onClick={onBackArrowClick}
          >
            <i className="tw-not-italic tw-font-black tw-text-2xl tw-leading-6 tw-h-6 tw-w-6 tw-text-qc-blue-800/90 fa-solid fa-arrow-left"></i>
          </button>
        ) : (
          <img
            onClick={onLogoClick}
            src={qAppsLogo}
            className="tw-w-[100px] tw-min-w-[100px] tw-cursor-pointer"
            alt="Q-Apps Logo"
          />
        )}
      </div>
      {children && (
        <div className="tw-flex tw-items-center tw-w-full">{children}</div>
      )}
      <div className="tw-flex tw-items-center tw-gap-10 tw-ml-10">
        <a
          href="https://qcentrix.freshdesk.com/support/home"
          target="_blank"
          rel="noreferrer"
          className="tw-flex tw-justify-center tw-items-center tw-rounded-[50%] tw-border-2 tw-border-qc-blue-800/90 tw-bg-white tw-not-italic tw-font-normal tw-h-6 tw-w-6 tw-my-0 tw-cursor-pointer tw-no-underline"
        >
          <i className="tw-text-qc-blue-800/90 fas fa-question" />
        </a>
        <ManageTab client={client} />
        <UserTab />
      </div>
    </div>
  );
};

Topbar.propTypes = {
  children: PropTypes.node,
  onLogoClick: PropTypes.func,
  onBackArrowClick: PropTypes.func,
  client: PropTypes.object
};

Topbar.defaultProps = {
  children: null,
  onLogoClick: undefined,
  onBackArrowClick: undefined
};

export default Topbar;
