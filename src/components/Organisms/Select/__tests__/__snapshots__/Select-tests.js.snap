// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Select it renders component correctly 1`] = `
<div
  className="tw-font-sans tw-not-italic tw-font-normal tw-text-sm tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <ReactSelect
    className=""
    classNamePrefix="test"
    classNames={
      Object {
        "control": [Function],
      }
    }
    clearable={false}
    closeMenuOnSelect={true}
    components={
      Object {
        "ClearIndicator": [Function],
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
        "Menu": [Function],
        "MenuList": [Function],
        "Option": [Function],
      }
    }
    disabled={false}
    error={false}
    errorText="this is required"
    hideSelectedOptions={true}
    iconClass="fa-solid fa-caret-down"
    isClearable={false}
    isDisabled={false}
    isMulti={false}
    isOptionDisabled={[Function]}
    isPageable={true}
    menuPlacement="auto"
    menuPortalTarget={null}
    onChange={[Function]}
    onInputChange={[Function]}
    onPageChange={[Function]}
    optionDisabled={[Function]}
    options={
      Array [
        Object {
          "label": "Incorrect Location Error",
          "value": "ILE",
        },
      ]
    }
    page={1}
    placeholder="Mismatched category"
    styles={
      Object {
        "control": [Function],
        "option": [Function],
        "placeholder": [Function],
        "valueContainer": [Function],
      }
    }
    totalCount={null}
    value={null}
  />
</div>
`;
