import React from "react";

const CustomClearIndicator = () => (
  <div className="custom-clear-indicator">
    <i className="fa-solid fa-circle-xmark tw-p-2 tw-text-qcNeutrals-400" />
  </div>
);

const ClearIndicator = props => {
  const {
    children = <CustomClearIndicator />,
    innerProps: { ref, ...restInnerProps }
  } = props;

  return (
    <div {...restInnerProps} ref={ref}>
      <div>{children}</div>
    </div>
  );
};

export default ClearIndicator;
