import React, { useCallback } from "react";
import ReactSelect from "react-select";
import PropTypes from "prop-types";
import cn from "../../../utils/cn";
import Checkbox from "../../Molecules/Checkbox";
import classNames from "classnames";
import MenuList from "./MenuList";
import Menu from "./Menu";
import Label from "../../Molecules/Label";
import InputError from "../../Cells/Input/InputError";
import InputFeedback from "../../Cells/Input/InputFeedback";
import ClearIndicator from "./ClearIndicator";
import CustomDropdownIndicator from "./CustomDropdownIndicator";
import {
  __,
  always,
  concat,
  defaultTo,
  ifElse,
  includes,
  pipe,
  reject,
  uniq,
  when
} from "ramda";
import twMerge from "../../../utils/tailwind/twMerge";
import { isNotNullOrEmpty } from "../../../utils/fp/isNullOrEmpty";

const isMultiStyles = (isMulti, values) =>
  isMulti && isNotNullOrEmpty(values) ? "4px" : "0 16px 0 8px";

const Option = ({ children, ...props }) => {
  const { isMulti, isSelected, innerRef, innerProps } = props;

  const optionClass = classNames(
    "tw-flex tw-gap-x-2.5 tw-p-2.5 tw-items-center hover:tw-bg-qc-blue-25 hover:tw-text-qc-blue-800 tw-border-gray-400 [&:not(:last-child)]:tw-border-b",
    {
      "tw-bg-purple-25 tw-text-purple-800": isSelected,
      "tw-text-black-70": !isSelected
    }
  );

  return (
    <li ref={innerRef} {...innerProps} className={optionClass}>
      {isMulti && (
        <div>
          <Checkbox checked={isSelected} readOnly />
        </div>
      )}
      <div className="tw-text-sm tw-flex tw-items-center">{children}</div>
    </li>
  );
};

// const MultiValue = ({children, innerRef, innerProps}) => (
//   <div ref={innerRef} {...innerProps} className="tw-flex tw-h-9 tw-p-2.5 tw-items-center tw-rounded-[5px] tw-border tw-border-qc-blue-800 tw-bg-qc-blue-25 tw-text-[10px] tw-text-qc-blue-700">
//     {children}
//   </div>
// )

// const ValueContainer = ({children, innerRef, innerProps}) => (
//   <div ref={innerRef} {...innerProps} className="tw-flex tw-gap-x-2">
//     {children}
//   </div>
// )

let typingTimer;

/**
 * `Select` is a flexible and customizable select input component with multi-selection, searching,
 * and pagination capabilities.
 *
 * @component
 * @example
 *
 * @param {Object} props - The properties to configure the Select component.
 * @param {string} props.id - The unique identifier for the select input element.
 * @param {Array.<{ value: string, label: string }>} props.options - An array of objects representing the options for the select input.
 * @param {string} props.placeholder - The placeholder text displayed in the select input when no option is selected.
 * @param {string} props.label - The label for the select input.
 * @param {string} props.name - The name attribute for the select input.
 * @param {Object} props.value - The currently selected option(s). For multi-select, this should be an array of option objects.
 * @param {Function} props.onChange - Callback function triggered when the selection changes.
 * @param {Function} props.onInputChange - Callback function triggered when the input value changes.
 * @param {string} props.iconClass - CSS classes applied to the dropdown indicator icon.
 * @param {string} props.classNamePrefix - Prefix for CSS classes applied to inner elements of the select input.
 * @param {boolean} props.disabled - Flag indicating whether the select input is disabled.
 * @param {boolean} props.clearable - Flag indicating whether the select input can be cleared.
 * @param {Function} props.optionDisabled - Function determining if an option is disabled.
 * @param {boolean} props.error - Flag indicating whether the select input has an error.
 * @param {string} props.errorText - Text to display when the select input has an error.
 * @param {HTMLElement} props.menuPortalTarget - Element where the menu portal should render.
 * @param {string} props.menuPlacement - Placement of the menu relative to the select input.
 * @param {boolean} props.isMulti - Flag indicating whether multiple selections are allowed.
 * @param {boolean} props.isSearchable - Flag indicating whether the select input is searchable.
 * @param {boolean} props.isPageable - Flag indicating whether the select input supports pagination.
 * @param {string} props.containerClassName - Additional CSS classes for the container wrapping the select input.
 * @param {number} props.totalCount - Total count of options available for pagination purposes.
 * @param {number} props.page - Current page number for paginated lists.
 * @param {string} props.feedbackText - Feedback message to display below the select input.
 * @param {string} props.feedbackType - Type of feedback message ('neutral', 'success', or 'error').
 * @param {string} props.feedbackClassName - Additional CSS classes for the feedback message container.
 * @param {string} props.labelClassName - Additional CSS classes for the label element.
 * @param {boolean} props.smallPagination - Flag indicating whether the pagination controls should be smaller.
 * @param {string} [props.cacheKey='default'] - Key used for caching purposes.
 * @param {number} [props.rowsPerPage=25] - Number of rows per page for paginated lists.
 * @param {string} props.inputClassName - Additional CSS classes for the input element.
 */
export const Select = props => {
  const {
    id,
    options,
    placeholder,
    label,
    name,
    value,
    onChange,
    onInputChange,
    iconClass,
    classNamePrefix,
    disabled,
    clearable,
    optionDisabled,
    error,
    onPageChange,
    errorText,
    menuPortalTarget,
    menuPlacement,
    isMulti,
    isSearchable,
    isPageable,
    isLoading,
    containerClassName,
    totalCount,
    page,
    feedbackText,
    feedbackType,
    feedbackClassName,
    labelClassName,
    smallPagination,
    cacheKey = "default",
    rowsPerPage = 25,
    inputClassName,
    optional = false,
    placeholderStyles
  } = props;

  const customStyles = {
    control: base => ({
      ...base,
      minHeight: "35px !important"
    }),
    valueContainer: base => ({
      ...base,
      padding: isMultiStyles(isMulti, value)
    }),
    option: base => ({
      ...base,
      padding: "8px 16px 8px 10px"
    }),
    placeholder: base => ({
      ...base,
      color: "#D7D7D7",
      ...(placeholderStyles || {})
    })
  };

  const handleChange = useCallback(
    value => {
      if (onChange) onChange(value, name);
    },
    [name, onChange]
  );

  const handleInputChange = useCallback(
    value => {
      clearTimeout(typingTimer);
      typingTimer = setTimeout(() => {
        if (onInputChange) onInputChange(value?.length > 2 ? value : "", name);
      }, 500);
    },
    [name, onInputChange]
  );

  const handleSelectAllChange = allSelected => {
    pipe(
      defaultTo([]),
      ifElse(
        always(allSelected),
        pipe(concat(__, options), uniq),
        reject(includes(__, options))
      ),
      when(always(onChange), val => onChange(val, name))
    )(value);
  };

  const handlePageChange = useCallback(
    newPage => {
      if (onPageChange) onPageChange(newPage, name);
    },
    [name, onPageChange]
  );

  return (
    <div
      className={cn(
        "tw-font-sans tw-not-italic tw-font-normal tw-text-sm tw-flex tw-flex-col tw-group tw-gap-y-1",
        containerClassName
      )}
    >
      {label && (
        <Label
          htmlFor={id}
          error={error}
          feedbackType={feedbackType}
          disabled={disabled}
          className={labelClassName}
          optional={optional}
        >
          {label}
        </Label>
      )}
      <ReactSelect
        inputId={id}
        styles={customStyles}
        classNames={{
          control: state => {
            if (state.isFocused)
              return "tw-overflow-y-auto tw-border-[1.5px] !tw-border-purple-800 tw-outline-none !tw-shadow-none";
            if (error)
              return "tw-overflow-y-auto tw-border-[1.5px] !tw-border-error-700 tw-outline-none !tw-shadow-none";
            return twMerge(
              "tw-overflow-y-auto border-grey-300",
              inputClassName
            );
          }
        }}
        options={options}
        components={{
          /* eslint-disable-next-line react/display-name */
          DropdownIndicator: () => (
            <CustomDropdownIndicator
              icon={iconClass}
              containerClass={cn(
                "tw-text-qc-blue-800 tw-pr-3.5 tw-text-sm",
                disabled && "tw-text-black-29"
              )}
            />
          ),
          IndicatorSeparator: () => null,
          ClearIndicator,
          Option,
          Menu: menuProps => (
            <Menu
              {...menuProps}
              onPageChange={handlePageChange}
              totalCount={totalCount}
              isPageable={isPageable}
              page={page}
              smallPagination={smallPagination}
              cacheKey={cacheKey}
              rowsPerPage={rowsPerPage}
            />
          ),
          MenuList: menuListProps => (
            <MenuList
              {...menuListProps}
              isPageable={isPageable}
              onSelectAllChange={handleSelectAllChange}
            />
          )
          // MultiValue,
          // ValueContainer
        }}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onInputChange={handleInputChange}
        classNamePrefix={classNamePrefix}
        isDisabled={disabled}
        isClearable={clearable}
        isSearchable={isSearchable}
        isOptionDisabled={optionDisabled}
        menuPortalTarget={menuPortalTarget}
        menuPlacement={menuPlacement}
        isMulti={isMulti}
        closeMenuOnSelect={!isMulti}
        hideSelectedOptions={!isMulti}
        isLoading={isLoading}
        {...props}
      />
      {!disabled && error && errorText && <InputError errorText={errorText} />}
      {!disabled && !error && feedbackText && (
        <InputFeedback
          feedbackText={feedbackText}
          variant={feedbackType}
          feedbackClassName={feedbackClassName}
        />
      )}
    </div>
  );
};

Select.propTypes = {
  placeholder: PropTypes.string,
  label: PropTypes.string,
  options: PropTypes.array,
  value: PropTypes.object,
  onChange: PropTypes.func,
  onInputChange: PropTypes.func,
  onPageChange: PropTypes.func,
  optionDisabled: PropTypes.func,
  iconClass: PropTypes.string,
  classNamePrefix: PropTypes.string,
  disabled: PropTypes.bool,
  searchable: PropTypes.bool,
  error: PropTypes.bool,
  errorText: PropTypes.string,
  menuPortalTarget: PropTypes.element,
  menuPlacement: PropTypes.string,
  isMulti: PropTypes.bool,
  isSearchable: PropTypes.bool,
  isPageable: PropTypes.bool,
  containerClassName: PropTypes.string,
  totalCount: PropTypes.number,
  page: PropTypes.number,
  feedbackText: PropTypes.string,
  feedbackType: PropTypes.oneOf(["neutral", "success", "error"]),
  feedbackClassName: PropTypes.string,
  labelClassName: PropTypes.string,
  smallPagination: PropTypes.bool,
  cacheKey: PropTypes.string,
  rowsPerPage: PropTypes.number,
  inputClassName: PropTypes.string
};

Select.defaultProps = {
  options: [{ value: "ILE", label: "Incorrect Location Error" }],
  value: null,
  placeholder: "Mismatched category",
  onChange: () => {},
  onInputChange: () => {},
  onPageChange: () => {},
  optionDisabled: () => {},
  iconClass: "fa-solid fa-caret-down",
  classNamePrefix: "test",
  disabled: false,
  clearable: false,
  errorText: "this is required",
  error: false,
  menuPortalTarget: null,
  menuPlacement: "auto",
  isMulti: false,
  isPageable: true,
  className: "",
  totalCount: null,
  page: 1
};

export default Select;
