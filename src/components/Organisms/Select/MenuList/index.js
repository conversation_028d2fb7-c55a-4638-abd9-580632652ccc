import React from "react";
import Checkbox from "../../../Molecules/Checkbox";
import { all, any, path } from "ramda";

const selectedPath = ["props", "isSelected"];

export const MenuList = ({
  isMulti,
  children,
  innerRef,
  innerProps,
  isPageable,
  onSelectAllChange
}) => {
  const someSelected = any(path(selectedPath), children);
  const allSelected = someSelected && all(path(selectedPath), children);
  const handleClick = e => {
    if (onSelectAllChange) onSelectAllChange(!someSelected);
  };

  return (
    <ul
      ref={innerRef}
      {...innerProps}
      className="tw-flex tw-flex-col tw-max-h-[350px] tw-overflow-auto"
    >
      {isMulti && (
        <li
          className="tw-flex tw-gap-x-2.5 tw-p-2.5 tw-items-center tw-bg-gray-100 tw-text-black-70 hover:tw-text-qc-blue-800"
          onClick={handleClick}
        >
          <div>
            <Checkbox
              checked={allSelected}
              indeterminate={someSelected && !allSelected}
              readOnly
            />
          </div>
          <div className="tw-text-sm tw-flex tw-items-center">
            {`
          ${someSelected ? "Deselect" : "Select"} All 
          ${isPageable ? "on this page" : ""}`}
          </div>
        </li>
      )}
      {children}
    </ul>
  );
};

export default MenuList;
