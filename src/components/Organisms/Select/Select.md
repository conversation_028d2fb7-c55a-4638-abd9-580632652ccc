Select

```jsx
import { useState } from "react";

const options = [
  { value: 1, label: "Test1" },
  { value: 2, label: "Test2" },
  { value: 3, label: "Test3" },
  { value: 4, label: "Test4" },
  { value: 5, label: "Test5" },
  { value: 6, label: "Test6" },
  { value: 7, label: "Test7" },
  { value: 8, label: "Test8" },
  { value: 9, label: "Test9" },
  { value: 10, label: "Test10" },
  { value: 11, label: "Test11" },
  { value: 12, label: "Test12" },
  { value: 13, label: "Test13" },
  { value: 14, label: "Test14" },
  { value: 15, label: "Test15" },
  { value: 16, label: "Test16" },
  { value: 17, label: "Test17" },
  { value: 18, label: "Test18" },
  { value: 19, label: "Test19" },
  { value: 20, label: "Test20" },
  { value: 21, label: "Test21" },
  { value: 22, label: "Test22" },
  { value: 23, label: "Test23" },
  { value: 24, label: "Test24" },
  { value: 25, label: "Test25" },
  { value: 26, label: "Test26" },
  { value: 27, label: "Test27" },
  { value: 28, label: "Test28" },
  { value: 29, label: "Test29" },
  { value: 30, label: "Test30" },
  { value: 31, label: "Test31" },
  { value: 32, label: "Test32" },
  { value: 33, label: "Test33" }
];
const [option, setOption] = useState();
const [page, setPage] = useState(1);
const handlePageChange = newPage => {
  setPage(newPage);
};
const displayOptions = options.slice((page-1) * 25, page*25);

<>
  <div style={{ marginBottom: "10px" }}>
    <Select
      options={displayOptions}
      placeholder="Placeholder"
      value={option}
      onChange={value => {
        setOption(value);
      }}
      clearable={true}
      optionDisabled={value => value.value <= 2}
      onError={true}
      onPageChange={handlePageChange}
      totalCount={options.length}
      page={page}
      isSearchable
      label="Test Label"
      optional
      placeholderStyles={{
        color: "#484357"
      }}
    />
  </div>
  <div style={{ marginBottom: "10px" }}>
    <Select
      options={[
        { value: 1, label: "Test1" },
        { value: 2, label: "Test2" },
        { value: 3, label: "Test3" }
      ]}
      placeholder={"Placeholder"}
      value={option}
      onChange={value => {
        setOption(value);
      }}
      iconClass="fa-solid fa-chevron-down"
      classNamePrefix="react-select"
      isMulti
      isSearchable
    />
  </div>
  <div style={{ marginBottom: "10px" }}>
    <Select
      options={[
        { value: 1, label: "Test1" },
        { value: 2, label: "Test2" },
        { value: 3, label: "Test3" }
      ]}
      placeholder={"Placeholder"}
      value={option}
      onChange={value => {
        setOption(value);
      }}
      iconClass="fa-solid fa-chevron-down"
      disabled={true}
    />
  </div>
</>;
```
