// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dropdown renders component 1`] = `
<div
  className=" css-b62m3t-container"
  onKeyDown={[Function]}
>
  <span
    className="css-1f43avz-a11yText-A11yText"
    id="react-select-2-live-region"
  />
  <span
    aria-atomic="false"
    aria-live="polite"
    aria-relevant="additions text"
    className="css-1f43avz-a11yText-A11yText"
  />
  <div
    className="border-grey-300 css-1931yp4-control"
    onMouseDown={[Function]}
    onTouchEnd={[Function]}
  >
    <div
      className=" css-wxskv2-ValueContainer"
    >
      <div
        className=" css-1sdqpo4-placeholder"
        id="react-select-2-placeholder"
      >
        Select an option
      </div>
      <input
        aria-autocomplete="list"
        aria-describedby="react-select-2-placeholder"
        aria-expanded={false}
        aria-haspopup={true}
        aria-readonly={true}
        className="css-mohuvp-dummyInput-DummyInput"
        disabled={false}
        id="react-select-2-input"
        inputMode="none"
        onBlur={[Function]}
        onChange={[Function]}
        onFocus={[Function]}
        role="combobox"
        tabIndex={0}
        value=""
      />
    </div>
    <div
      className=" css-1hb7zxy-IndicatorsContainer"
    >
      <div
        aria-hidden="true"
        className=" css-1xc3v61-indicatorContainer"
        onMouseDown={[Function]}
        onTouchEnd={[Function]}
      >
        <i
          className="fa-solid fa-chevron-down tw-text-qc-blue-800"
        />
      </div>
    </div>
  </div>
</div>
`;
