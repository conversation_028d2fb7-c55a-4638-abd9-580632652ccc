import React from "react";
import PropTypes from "prop-types";
import Select, { components } from "react-select";
import classnames from "classnames";

/**
 * Custom Dropdown Indicator for the Select component.
 * @param {Object} props - The props passed to the component.
 * @returns {React.Element} The DropdownIndicator component.
 */
const DropdownIndicator = props => {
  const { isDisabled } = props;

  const iconClassName = classnames(
    "fa-solid fa-chevron-down",
    { "tw-text-qc-blue-800": !isDisabled },
    { "tw-text-black-29": isDisabled }
  );

  return (
    <components.DropdownIndicator {...props}>
      <i className={iconClassName} />
    </components.DropdownIndicator>
  );
};

/**
 * Dropdown component using react-select.
 * @param {Object} props - The props passed to the component.
 * @param {Array.<{label: string, value: (string|number)}>} props.options - The options for the dropdown.
 * @param {{label: string, value: (string|number)}|null} [props.value=null] props.value - The value of the dropdown, reflected by the selected option
 * @param {Function} props.onChange - The function to call when the dropdown value changes.
 * @param {boolean} [props.loading=false] - A boolean to show loading state.
 * @param {boolean} [props.disabled=false] - A boolean to disable the dropdown.
 * @param {string} [props.placeholder="Select an option"] - The placeholder text for the dropdown.
 * @returns {React.Element} The Dropdown component.
 */
export const Dropdown = ({
  options,
  onChange,
  loading = false,
  disabled = false,
  placeholder = "Select an option",
  value = null,
  menuPlacement,
  placeholderStyles
}) => (
  <Select
    isLoading={loading}
    isDisabled={disabled}
    placeholder={placeholder}
    options={options}
    isSearchable={false}
    classNames={{
      option: () =>
        "tw-border-b tw-border-gray-400 last:tw-border-b-0 tw-transition tw-ease-in-out first:tw-rounded-t-md last:tw-rounded-b-md",
      menuList: () => "!tw-p-0 tw-roundend-md",
      control: state => {
        if (state.isFocused)
          return "tw-border-[1.5px] !tw-border-purple-800 tw-outline-none !tw-shadow-none";
        return "border-grey-300";
      }
    }}
    styles={{
      option: (base, { isFocused, isDisabled }) => ({
        ...base,
        backgroundColor: isFocused ? "#F4FCFF" : null,
        color: isDisabled ? "#B5B5B5" : "rgba(0, 0, 0, 0.7)",
        cursor: isDisabled ? "not-allowed" : "pointer",
        padding: "8px 16px 8px 10px"
      }),
      control: base => ({
        ...base,
        fontSize: "14px",
        minHeight: "30px",
        cursor: "pointer"
      }),
      menuPortal: base => ({ ...base, zIndex: 9999 }),
      valueContainer: base => ({
        ...base,
        padding: "0 16px 0 8px"
      }),
      singleValue: base => ({
        ...base,
        margin: 0
      }),
      placeholder: base => ({
        ...base,
        margin: 0,
        color: "#C2C5D5",
        ...(placeholderStyles || {})
      })
    }}
    onChange={onChange}
    value={value}
    components={{
      DropdownIndicator,
      IndicatorSeparator: () => null
    }}
    menuPlacement={menuPlacement}
    menuPortalTarget={document.body}
    menuPosition="fixed"
  />
);

Dropdown.propTypes = {
  /**
   * The options for the dropdown.
   */
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string
    })
  ).isRequired,
  /**
   * The function to call when the dropdown value changes.
   */
  onChange: PropTypes.func.isRequired,
  /**
   * The placeholder text for the dropdown.
   */
  placeholder: PropTypes.string.isRequired,
  /**
   * A boolean to show loading state.
   */
  loading: PropTypes.bool,
  /**
   * A boolean to disable the dropdown.
   */
  disabled: PropTypes.bool,
  /**
   * The value of the dropdown, reflected by the selected option
   */
  value: PropTypes.oneOfType([
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string
    }),
    PropTypes.oneOf([null])
  ]),
  /**
   * The placement of the dropdown menu, will be a value of bottom, top or auto, if none selected will default to bottom
   */
  menuPlacement: PropTypes.oneOf(["bottom", "top", "auto"])
};

export default Dropdown;
