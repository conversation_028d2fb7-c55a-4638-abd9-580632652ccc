// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectAsync it renders component correctly 1`] = `
<div
  className="tw-font-sans tw-not-italic tw-font-normal tw-text-sm tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <AsyncSelect
    cacheOptions={true}
    classNames={
      Object {
        "control": [Function],
        "option": [Function],
      }
    }
    components={
      Object {
        "ClearIndicator": [Function],
        "DropdownIndicator": [Function],
        "IndicatorSeparator": [Function],
      }
    }
    disabled={false}
    iconClass="fa-solid fa-chevron-down"
    menuPosition="auto"
    placeholder={
      <div
        className="tw-font-sans tw-not-italic tw-text-sm tw-font-normal tw-text-qcNeutrals-400"
      />
    }
    styles={
      Object {
        "control": [Function],
        "input": [Function],
        "menuList": [Function],
        "option": [Function],
        "placeholder": [Function],
        "singleValue": [Function],
        "valueContainer": [Function],
      }
    }
  />
</div>
`;
