import React from "react";
import PropTypes from "prop-types";
import AsyncSelect from "react-select/async";
import Label from "../../Molecules/Label";
import InputError from "../../Cells/Input/InputError";
import InputFeedback from "components/Cells/Input/InputFeedback";
import CustomDropdownIndicator from "components/Organisms/Select/CustomDropdownIndicator";
import ClearIndicator from "../Select/ClearIndicator";
import cn from "../../../utils/cn";

/**
 * `SelectAsync` is an asynchronous select input component with customizable styling and feedback.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {string} props.id - The unique identifier for the select input.
 * @param {string} [props.label] - The label for the select input.
 * @param {string} [props.placeholder] - The placeholder text for the select input.
 * @param {function} props.loadOptions - A function that loads options asynchronously.
 * @param {Object} props.defaultValue - Default value for the select.
 * @param {boolean} [props.error] - If true, the select input is in an error state.
 * @param {string} [props.errorText] - The error text to display when in an error state.
 * @param {boolean} [props.disabled=false] - If true, the select input is disabled.
 * @param {'success' | 'neutral' | 'error'} [props.feedbackType] - The type of feedback.
 * @param {string} [props.feedbackText] - The feedback text to display.
 * @param {string} [props.labelClassName] - Additional class name for the label.
 * @param {string} [props.feedbackClassName] - Additional class name for the feedback.
 * @param {(boolean | object[])} [props.defaultOptions] - Determines when the remote request is initially fired.
 *   - If set to an array of options, it populates the initial set of options when opening the select.
 *   - If set to `true` or provided without an array, immediately fires the remote request described by `loadOptions`
 *     to get the initial values for the Select.
 * @param {function} props.onChange - Callback function triggered on select change.
 * @param {string} [props.iconClass="fa-solid fa-chevron-down"] - Class name for the select input icon.
 * @param {string} props.containerClassName - Classes to customize the container that wraps other elements.
 * @returns {JSX.Element} - The rendered SelectAsync component.
 */
const SelectAsync = ({
  id,
  label,
  placeholder,
  loadOptions,
  defaultValue,
  error,
  errorText,
  disabled = false,
  feedbackType,
  feedbackText,
  labelClassName,
  feedbackClassName,
  defaultOptions,
  onChange,
  iconClass = "fa-solid fa-chevron-down",
  containerClassName
}) => {
  const customStyles = {
    control: base => ({
      ...base,
      minHeight: "35px !important"
    }),
    menuList: base => ({
      ...base,
      paddingTop: 0,
      paddingBottom: 0,
      borderRadius: "4px"
    }),
    valueContainer: base => ({
      ...base,
      padding: "0 16px 0 8px"
    }),
    input: base => ({
      ...base,
      padding: "0",
      margin: "0"
    }),
    singleValue: base => ({
      ...base,
      margin: 0
    }),
    placeholder: base => ({
      ...base,
      margin: 0
    }),
    option: base => ({
      ...base,
      padding: "8px 16px 8px 10px"
    })
  };
  return (
    <div
      className={cn(
        "tw-font-sans tw-not-italic tw-font-normal tw-text-sm tw-flex tw-flex-col tw-group tw-gap-y-1",
        containerClassName
      )}
    >
      {label && (
        <Label
          htmlFor={id}
          error={error}
          feedbackType={feedbackType}
          disabled={disabled}
          className={`tw-block ${labelClassName}`}
        >
          {label}
        </Label>
      )}
      <AsyncSelect
        defaultValue={defaultValue}
        id={id}
        styles={customStyles}
        classNames={{
          control: state => {
            if (state.isFocused)
              return "tw-max-h-[35px] tw-border-[1.5px] !tw-border-purple-800 tw-outline-none !tw-shadow-none";
            if (error || feedbackType === "error")
              return "tw-max-h-[35px] tw-border-[1.5px] !tw-border-error-700 tw-outline-none !tw-shadow-none";
            return "tw-border-grey-300 ";
          },
          option: state => {
            if (state.isSelected) return "!tw-bg-purple-25 !tw-text-purple-800";
            if (state.isFocused) return "!tw-text-qc-blue-800";
            if (state.isDisabled) return "!tw-text-black-29";
            return "!tw-text-black-70";
          }
        }}
        components={{
          /* eslint-disable-next-line react/display-name */
          DropdownIndicator: () => (
            <CustomDropdownIndicator
              icon={iconClass}
              containerClass={cn(
                "tw-text-qc-blue-800 tw-pr-3.5 tw-text-sm",
                disabled && "tw-text-black-29"
              )}
            />
          ),
          IndicatorSeparator: () => null,
          ClearIndicator
        }}
        menuPosition="auto"
        iconClass={iconClass}
        onError={error}
        errorText={errorText}
        disabled={disabled}
        onChange={onChange}
        cacheOptions
        loadOptions={loadOptions}
        placeholder={
          <div className="tw-font-sans tw-not-italic tw-text-sm tw-font-normal tw-text-qcNeutrals-400">
            {placeholder}
          </div>
        }
        defaultOptions={defaultOptions}
      />
      {!disabled && error && errorText && <InputError errorText={errorText} />}
      {!disabled && !error && feedbackText && (
        <InputFeedback
          feedbackText={feedbackText}
          variant={feedbackType}
          feedbackClassName={feedbackClassName}
        />
      )}
    </div>
  );
};

SelectAsync.propTypes = {
  /**
   * The unique identifier for the select input.
   */
  id: PropTypes.string.isRequired,
  /**
   * The label for the select input.
   */
  label: PropTypes.string,
  /**
   * The placeholder text for the select input.
   */
  placeholder: PropTypes.string,
  /**
   * A function that loads options asynchronously.
   */
  loadOptions: PropTypes.func.isRequired,
  /**
   * Default value for the select.
   */
  defaultValue: PropTypes.object,
  /**
   * If true, the select input is in an error state.
   */
  error: PropTypes.bool,
  /**
   * The error text to display when in an error state.
   */
  errorText: PropTypes.string,
  /**
   * If true, the select input is disabled.
   */
  disabled: PropTypes.bool,
  /**
   * The type of feedback (success, neutral, or error).
   */
  feedbackType: PropTypes.oneOf(["success", "neutral", "error"]),
  /**
   * The feedback text to display.
   */
  feedbackText: PropTypes.string,
  /**
   * Additional class name for the label.
   */
  labelClassName: PropTypes.string,
  /**
   * Additional class name for the feedback.
   */
  feedbackClassName: PropTypes.string,
  /**
   * Determines when the remote request is initially fired.
   * - If set to an array of options, it populates the initial set of options when opening the select.
   * - If set to `true` or provided without an array, immediately fires the remote request described by `loadOptions`
   *   to get the initial values for the Select.
   */
  defaultOptions: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.arrayOf(PropTypes.object)
  ]),
  /**
   * Callback function triggered on select change.
   */
  onChange: PropTypes.func.isRequired,
  /**
   * Class name for the select input icon.
   */
  iconClass: PropTypes.string,
  /**
   * Classes to customize the container that wraps other elements.
   */
  containerClassName: PropTypes.string
};

export default SelectAsync;
