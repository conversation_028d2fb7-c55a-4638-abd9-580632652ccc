import React from "react";
import PropTypes from "prop-types";
import twMerge from "../../../../utils/tailwind/twMerge";
import classnames from "classnames";
import { motion } from "framer-motion";

const variants = {
  open: {
    y: 0,
    opacity: 1,
    transition: {
      y: { stiffness: 1000, velocity: -100 }
    }
  },
  closed: {
    y: 50,
    opacity: 0,
    transition: {
      y: { stiffness: 1000 }
    }
  }
};

/**
 * `TableRow` is an UI Component designed to display the row of a table.
 * @param {React.ReactNode} children - The table row content.
 * @param {boolean} selected - Whether the row is selected.
 * @param {string} className - Additional CSS tailwind classes to apply to the table row.
 * @param {Function} onClick - The function to call when the row is clicked.
 */

export const TableRow = ({
  children,
  selected,
  className,
  onClick,
  ...otherProps
}) => {
  const optionalClassName = classnames({
    "!tw-bg-purple-50 tw-text-purple-800 tw-border-purple-200 hover:tw-border-gray-200": selected
  });

  const mergedClassName = twMerge(
    "tw-border-y-[0.5px] tw-border-gray-200 even:tw-bg-gray-25 odd:tw-bg-white hover:!tw-bg-qc-blue-50 tw-cursor-pointer last:tw-rounded-b-[5px]",
    optionalClassName,
    className
  );

  return (
    <motion.tr
      onClick={onClick}
      className={mergedClassName}
      variants={variants}
      {...otherProps}
    >
      {children}
    </motion.tr>
  );
};

TableRow.propTypes = {
  /**
   * The table row content.
   */
  children: PropTypes.node.isRequired,
  /**
   * Whether the row is selected.
   */
  selected: PropTypes.bool,
  /**
   * Additional CSS tailwind classes to apply to the table row.
   */
  className: PropTypes.string,
  /**
   * The function to call when the row is clicked.
   */
  onClick: PropTypes.func
};

export default TableRow;
