import React, { forwardRef, useCallback } from "react";
import Select, { components } from "react-select";
import PropTypes from "prop-types";
import cn from "../../../utils/cn";
import Label from "../../Molecules/Label";
import InputError from "../../Cells/Input/InputError";
import InputFeedback from "../../Cells/Input/InputFeedback";
import twMerge from "../../../utils/tailwind/twMerge";
import { isNotNullOrEmpty } from "../../../utils/fp/isNullOrEmpty";

/**
 * Escapes special characters in a string so it can be used
 * safely within a regular expression.
 *
 * @param {string} string - The input string to escape
 * @returns {string} The input string with special regex characters escaped
 */
const makeStringRegexSafe = string =>
  string.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&");

/**
 * Highlights matching portions of a dropdown option label based on user input
 * @param {string} optionLabel - The option label to format
 * @param {string} userSearchInput - The user's search input to highlight
 * @returns {(string|JSX.Element)[]} Array of text and highlighted elements
 */
const highlightMatchesInOption = (userSearchInput, optionLabel) => {
  if (!userSearchInput || !optionLabel) {
    return optionLabel;
  }

  const safeSearchTerm = makeStringRegexSafe(userSearchInput);
  const highlightRegex = new RegExp(`(${safeSearchTerm})`, "gi");
  const labelSegments = optionLabel.split(highlightRegex);

  return labelSegments.map((labelSegment, index) =>
    highlightRegex.test(labelSegment) ? (
      <span
        key={`${labelSegment}-${index}`}
        className="tw-text-black tw-font-bold"
      >
        {labelSegment}
      </span>
    ) : (
      labelSegment
    )
  );
};
const isMultiStyles = (isMulti, values) =>
  isMulti && isNotNullOrEmpty(values) ? "4px" : "0 16px 0 8px";

// Custom components for highlight on search
const Option = props => {
  const userInput = props?.selectProps?.inputValue || "";
  const label = props?.data?.label || "";

  const formattedLabel = userInput
    ? highlightMatchesInOption(userInput, label)
    : label;

  return <components.Option {...props}>{formattedLabel}</components.Option>;
};

const CustomDropdownIndicator = ({ icon, containerClass }) => (
  <div className={containerClass}>
    <i className={`${icon ? icon : "fa-solid fa-caret-down"}`} />
  </div>
);

const CustomClearIndicator = props => {
  const { clearIndicatorClass } = props;

  return (
    <components.ClearIndicator {...props}>
      <div className="custom-clear-indicator">
        <i
          className={`fa-solid fa-circle-xmark 
            ${clearIndicatorClass ? clearIndicatorClass : ""}
            `}
        />
      </div>
    </components.ClearIndicator>
  );
};

/**
 * `InputDropdown` is an Atom Level component designed to replace the
 * default dropdown element.
 *
 * @component
 * @param {object} props - Component props.
 * @param {string} props.id - The HTML id attribute for the dropdown.
 * @param {array} props.options - An array of options for the dropdown.
 * @param {string} props.placeholder - Placeholder text for the dropdown.
 * @param {string} props.label - Optional label text for the dropdown.
 * @param {string} props.name - Name value.
 * @param {object} props.value - Selected value from the options array.
 * @param {function} props.onChange - Function passed to handle change.
 * @param {function} props.onInputChange - Function passed to handle input change.
 * @param {string} props.iconClass - Pass Font Awesome icon string here for dropdown indicator.
 * @param {string} props.classNamePrefix - Class name prefix for the dropdown.
 * @param {boolean} props.disabled - Disable select.
 * @param {boolean} props.clearable - Allow clearing the selected value.
 * @param {function} props.optionDisabled - Function to determine if an option should be disabled.
 * @param {boolean} props.error - Specifies whether there is an error in the dropdown.
 * @param {string} props.errorText - Text for the error message.
 * @param {Element} props.menuPortalTarget - Specify menuPortalTarget.
 * @param {string} props.menuPlacement - Placement of the dropdown menu.
 * @param {boolean} props.isMulti - Allow multiple selections.
 * @param {boolean} props.isLoading - Show loading indicator.
 * @param {string} props.containerClassName - Classes to customize the container that wraps other elements.
 * @param {string} props.feedbackText - The optional feedback text for the input.
 * @param {("neutral"|"success"|"error")} [props.feedbackType="neutral"] - Style variants for the feedback text.
 * @param {string} props.feedbackClassName - Classes to customize the optional InputFeedback.
 * @param {string} props.labelClassName - Label CSS classes.
 * @param {string} props.inputClassName - Input CSS classes.
 * @param {object} props.optionStyles - Option CSS classes.
 * @param {object} props.controlStyles - Control CSS classes.
 * @param {boolean} props.clearIndicatorStyles - clearIndicatorStyles CSS
 * @param {boolean} props.indicatorSeparatorStyles - indicatorSeparatorStyles CSS
 * @param {boolean} props.valueContainerStyles - valueContainerStyles CSS
 * @param {boolean} props.clearIndicatorStyles - clearIndicatorStyles CSS
 *
 * @returns {React.Element} Rendered component.
 **/

// eslint-disable-next-line react/display-name
export const InputDropdown = forwardRef((props, ref) => {
  const {
    menuStyles,
    indicatorSeparatorStyles = false,
    valueContainerStyles = false,
    clearIndicatorStyles = false,
    clearIndicatorClass,
    placeholderStyles,
    id,
    options,
    placeholder,
    label,
    name,
    value,
    onChange,
    onInputChange,
    iconClass,
    classNamePrefix,
    disabled,
    clearable,
    optionDisabled,
    error,
    errorText,
    menuPortalTarget,
    menuPlacement,
    isMulti,
    isLoading,
    containerClassName,
    feedbackText,
    feedbackType,
    feedbackClassName,
    labelClassName,
    inputClassName,
    optionStyles,
    controlStyles
  } = props;

  const handleChange = useCallback(
    value => {
      if (onChange) onChange(value, name);
    },
    [name, onChange]
  );

  const handleInputChange = useCallback(
    value => {
      if (onInputChange) onInputChange(value, name);
    },
    [name, onInputChange]
  );

  const customStyles = {
    menu: base => ({
      ...base,
      ...(menuStyles || {})
    }),
    menuList: base => ({
      ...base,
      paddingTop: 0,
      paddingBottom: 0,
      borderRadius: "4px"
    }),
    option: base => ({
      ...base,
      ...(optionStyles || {}),
      "&:not(:last-child)": {
        borderBottom: "1px solid #D7D7D7"
      },
      padding: "8px 16px 8px 10px"
    }),
    control: base => ({
      ...base,
      ...(controlStyles || {}),
      minHeight: "35px !important"
    }),
    clearIndicator: base => ({
      ...base,
      ...(clearIndicatorStyles || {})
    }),
    indicatorSeparator: base => ({
      ...base,
      display: indicatorSeparatorStyles ? "block" : "none",
      ...(indicatorSeparatorStyles || {})
    }),
    valueContainer: base => ({
      ...base,
      padding: isMultiStyles(isMulti, value),
      ...(valueContainerStyles || {})
    }),
    input: base => ({
      ...base,
      padding: "0",
      margin: "0"
    }),
    singleValue: base => ({
      ...base,
      margin: 0
    }),
    placeholder: base => ({
      ...base,
      margin: 0,
      ...(placeholderStyles || {})
    })
  };

  return (
    <div
      className={cn(
        "tw-group tw-flex tw-flex-col tw-gap-y-1 tw-font-sans tw-text-sm tw-font-normal tw-not-italic",
        containerClassName
      )}
    >
      {label && (
        <Label
          htmlFor={id}
          error={error}
          feedbackType={feedbackType}
          disabled={disabled}
          className={`${labelClassName} tw-block`}
        >
          {label}
        </Label>
      )}
      <Select
        ref={ref}
        styles={customStyles}
        inputId={id}
        classNames={{
          control: state => {
            if (state.isFocused)
              return "tw-border-[1.5px] !tw-border-purple-800 tw-outline-none !tw-shadow-none";
            if (error || feedbackType === "error")
              return "tw-border-[1.5px] !tw-border-error-700 tw-outline-none !tw-shadow-none";
            return twMerge("border-grey-300", inputClassName);
          },
          option: state => {
            if (state.isSelected) return "!tw-bg-purple-25 !tw-text-purple-800";
            if (state.isFocused) return "!tw-text-qc-blue-800";
            if (state.isDisabled) return "!tw-text-black-29";
            return "!tw-text-black-70";
          }
        }}
        options={options}
        placeholder={
          <div className="tw-font-sans tw-text-sm tw-font-normal tw-not-italic tw-text-qcNeutrals-400">
            {placeholder}
          </div>
        }
        components={{
          /* eslint-disable-next-line react/display-name */
          DropdownIndicator: () => (
            <CustomDropdownIndicator
              icon={iconClass}
              containerClass={cn(
                "tw-text-qc-blue-800 tw-pr-3.5 tw-text-sm",
                disabled && "tw-text-black-29"
              )}
              {...props}
            />
          ),
          ClearIndicator: props => (
            <CustomClearIndicator
              clearIndicatorClass={clearIndicatorClass}
              {...props}
            />
          ),
          Option
        }}
        value={value}
        onChange={handleChange}
        onInputChange={handleInputChange}
        classNamePrefix={classNamePrefix}
        isDisabled={disabled}
        isClearable={clearable}
        isOptionDisabled={optionDisabled}
        menuPortalTarget={menuPortalTarget}
        menuPlacement={menuPlacement}
        isMulti={isMulti}
        isLoading={isLoading}
      />
      {!disabled && error && errorText && <InputError errorText={errorText} />}
      {!disabled && !error && feedbackText && (
        <InputFeedback
          feedbackText={feedbackText}
          variant={feedbackType}
          feedbackClassName={feedbackClassName}
        />
      )}
    </div>
  );
});

InputDropdown.propTypes = {
  id: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.object),
  placeholder: PropTypes.string,
  label: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.object,
  onChange: PropTypes.func,
  onInputChange: PropTypes.func,
  iconClass: PropTypes.string,
  classNamePrefix: PropTypes.string,
  disabled: PropTypes.bool,
  clearable: PropTypes.bool,
  optionDisabled: PropTypes.func,
  error: PropTypes.bool,
  errorText: PropTypes.string,
  menuPortalTarget: PropTypes.node,
  menuPlacement: PropTypes.string,
  isMulti: PropTypes.bool,
  isLoading: PropTypes.bool,
  containerClassName: PropTypes.string,
  feedbackText: PropTypes.string,
  feedbackType: PropTypes.oneOf(["neutral", "success", "error"]),
  feedbackClassName: PropTypes.string,
  labelClassName: PropTypes.string,
  inputClassName: PropTypes.string,
  optionStyles: PropTypes.object,
  controlStyles: PropTypes.object,
  clearIndicatorClass: PropTypes.string,
  menuStyles: PropTypes.object,
  valueContainerStyles: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]),
  indicatorSeparatorStyles: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.object
  ])
};

InputDropdown.defaultProps = {
  options: [{ value: "ILE", label: "Incorrect Location Error" }],
  value: null,
  placeholder: "Mismatched category",
  onChange: () => {},
  onInputChange: () => {},
  optionDisabled: () => {},
  iconClass: "fa-solid fa-caret-down",
  classNamePrefix: "test",
  disabled: false,
  clearable: false,
  errorText: "this is required",
  error: false,
  menuPortalTarget: null,
  menuPlacement: "auto",
  isMulti: false,
  controlStyles: {},
  optionStyles: {}
};

export default InputDropdown;
