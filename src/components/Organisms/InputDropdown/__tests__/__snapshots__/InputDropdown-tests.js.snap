// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InputDropdown it renders component correctly 1`] = `
<div
  className="tw-group tw-flex tw-flex-col tw-gap-y-1 tw-font-sans tw-text-sm tw-font-normal tw-not-italic"
>
  <ReactSelect
    classNamePrefix="test"
    classNames={
      Object {
        "control": [Function],
        "option": [Function],
      }
    }
    components={
      Object {
        "ClearIndicator": [Function],
        "DropdownIndicator": [Function],
        "Option": [Function],
      }
    }
    isClearable={false}
    isDisabled={false}
    isMulti={false}
    isOptionDisabled={[Function]}
    menuPlacement="auto"
    menuPortalTarget={null}
    onChange={[Function]}
    onInputChange={[Function]}
    options={
      Array [
        Object {
          "label": "Incorrect Location Error",
          "value": "ILE",
        },
      ]
    }
    placeholder={
      <div
        className="tw-font-sans tw-text-sm tw-font-normal tw-not-italic tw-text-qcNeutrals-400"
      >
        Mismatched category
      </div>
    }
    styles={
      Object {
        "clearIndicator": [Function],
        "control": [Function],
        "indicatorSeparator": [Function],
        "input": [Function],
        "menu": [Function],
        "menuList": [Function],
        "option": [Function],
        "placeholder": [Function],
        "singleValue": [Function],
        "valueContainer": [Function],
      }
    }
    value={null}
  />
</div>
`;
