import React, { useMemo } from "react";
import PropTypes from "prop-types";
import { motion } from "framer-motion";
import { GET_AVAILABLE_APPS_DATA } from "./graphql/query";
import { useQuery } from "@apollo/client";
import { prop, filter } from "ramda";
import { serverURI } from "../../../helpers/base/constants";
import { QcIcon } from "./icons/QcIcon";
import cleanURL from "../../../utils/fp/cleanURL";
import "../../../styles/icon-font.css";
import FacilitySelector from "./FacilitySelector";
import { appName, version } from "helpers/base/constants";
import { useAnalytics } from "../../../hooks/useAnalytics";

const updateApps = data => {
  const { userApps = [] } = data;

  return filter(prop("enabled"), userApps);
};

/**
 * `SideNavMenu` is a component that renders a side navigation menu.
 * @param {function} onFacilityChange - The function to call when the facility is changed.
 **/
export const SideNavMenu = ({ onFacilityChange }) => {
  const { data = {} } = useQuery(GET_AVAILABLE_APPS_DATA);

  const apps = useMemo(() => updateApps(data), [data]);

  useAnalytics();

  return (
    <motion.nav
      className="tw-w-[50px] tw-bg-qc-blue-800 tw-overflow-hidden tw-h-inherit tw-flex tw-flex-col tw-justify-between"
      whileHover={{
        width: "310px",
        transition: {
          duration: 0.5,
          type: "spring",
          damping: 10,
          delay: 0.5
        },
        boxShadow: "-5px 0px 20px 0px rgba(0, 0, 0, 0.25)"
      }}
    >
      <ul>
        <li>
          <div className="tw-flex tw-items-center tw-h-12">
            <div className="tw-min-w-[50px] tw-mr-[20px] tw-flex tw-justify-center">
              <i className="tw-text-xl tw-text-gray-400 fa-light fa-location-dot" />
            </div>
            <div className="tw-flex-grow tw-self-start tw-pr-3 tw-mt-1">
              <FacilitySelector
                onFacilityChange={onFacilityChange}
                smallPagination={true}
              />
            </div>
          </div>
        </li>
        {apps.map(app => (
          <li key={app.key}>
            <a
              className="tw-flex tw-items-center tw-h-12 tw-text-sm tw-text-gray-400 tw-whitespace-nowrap hover:tw-border-r-4 hover:tw-border-qc-orange-800 hover:tw-bg-qc-blue-700 hover:tw-text-white"
              href={cleanURL(`${serverURI}${app.path}`)}
            >
              <div className="tw-min-w-[50px] tw-mr-[20px] tw-flex tw-justify-center">
                <i
                  className={`fa-kit icon-${app.key} tw-text-[30px] tw-text-inherit`}
                />
              </div>
              {app.name}
            </a>
          </li>
        ))}
      </ul>
      <div className="tw-flex tw-flex-grow-0 tw-gap-x-5 tw-justify-start tw-items-center">
        <div className="tw-min-w-[50px] tw-flex tw-justify-center">
          <QcIcon />
        </div>
        <div className="tw-flex-grow-0 tw-text-xs tw-font-normal tw-text-gray-400 tw-whitespace-nowrap tw-font-not-italic">
          version {appName} {version}
        </div>
      </div>
    </motion.nav>
  );
};

SideNavMenu.defaultProps = {
  onFacilityChange: () => {}
};

SideNavMenu.propTypes = {
  /**
   * The function to call when the facility is changed.
   */
  onFacilityChange: PropTypes.func
};

export default SideNavMenu;
