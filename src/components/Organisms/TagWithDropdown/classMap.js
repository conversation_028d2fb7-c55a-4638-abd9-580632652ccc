export const classMap = {
  danger: {
    text: "!tw-text-qcDanger-800",
    border: "!tw-border-qcDanger-800",
    background: "!tw-bg-qcDanger-50"
  },
  default: {
    text: "!tw-text-qcInfo-700",
    border: "!tw-border-qcInfo-700",
    background: "!tw-bg-qcInfo-50"
  },
  main: {
    text: "!tw-text-qcIris-800",
    border: "!tw-border-qcIris-800",
    background: "!tw-bg-qcIris-50"
  },
  neutral: {
    text: "!tw-text-qcNeutrals-600",
    border: "!tw-border-qcNeutrals-600",
    background: "!tw-bg-qcNeutrals-200"
  },
  success: {
    text: "!tw-text-qcGreen-800",
    border: "!tw-border-qcGreen-800",
    background: "!tw-bg-qcGreen-50"
  },
  warning: {
    text: "!tw-text-qcSunset-800",
    border: "!tw-border-qcSunset-800",
    background: "!tw-bg-qcSunset-50"
  }
};

export const borderlessClassMap = {
  danger: {
    text: "!tw-text-qcDanger-900",
    border: "!tw-border-qcDanger-900",
    background: "!tw-bg-qcDanger-200"
  },
  default: {
    text: "!tw-text-qcInfo-800",
    border: "!tw-border-qcInfo-800",
    background: "!tw-bg-qcInfo-100"
  },
  main: {
    text: "!tw-text-qcIris-800",
    border: "!tw-border-qcIris-800",
    background: "!tw-bg-qcIris-100"
  },
  neutral: {
    text: "!tw-text-qcNeutrals-800",
    border: "!tw-border-qcNeutrals-800",
    background: "!tw-bg-qcNeutrals-300"
  },
  success: {
    text: "!tw-text-qcGreen-900",
    border: "!tw-border-qcGreen-900",
    background: "!tw-bg-qcGreen-800"
  },
  warning: {
    text: "!tw-text-qcSunset-800",
    border: "!tw-border-qcSunset-800",
    background: "!tw-bg-qcSunset-100"
  }
};
