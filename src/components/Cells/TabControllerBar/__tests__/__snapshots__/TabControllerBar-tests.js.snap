// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TabControllerBar renders component 1`] = `
<div
  className="tw-flex tw-w-fit tw-p-1 tw-gap-1 tw-bg-qcNeutrals-300 tw-border tw-border-qcNeutrals-400 tw-rounded-md"
>
  <button
    className="disabled:tw-text-white disabled:hover:tw-cursor-default tw-text-midnightPurple hover:tw-cursor-pointer tw-min-w-[148px] tw-min-h-[32px] tw-flex tw-justify-center tw-items-center tw-py-2 hover:tw-bg-qcInfo-300 tw-px-4 tw-rounded-md tw-relative tw-transition"
    disabled={true}
    onClick={[MockFunction]}
  >
    <div
      className="tw-z-20 tw-flex tw-gap-2 tw-justify-center tw-items-center"
    >
      <i
        className="fa-solid fa-filters"
      />
      Filters
    </div>
    <span
      className="tw-absolute tw-inset-0 tw-z-10 tw-bg-oceanBlue tw-rounded-md"
      style={Object {}}
    />
  </button>
  <button
    className="disabled:tw-text-white disabled:hover:tw-cursor-default tw-text-midnightPurple hover:tw-cursor-pointer tw-min-w-[148px] tw-min-h-[32px] tw-flex tw-justify-center tw-items-center tw-py-2 hover:tw-bg-qcInfo-300 tw-px-4 tw-rounded-md tw-relative tw-transition"
    disabled={false}
    onClick={[MockFunction]}
  >
    <div
      className="tw-z-20 tw-flex tw-gap-2 tw-justify-center tw-items-center"
    >
      <i
        className="fa-solid fa-table-layout"
      />
      Columns
    </div>
  </button>
  <button
    className="disabled:tw-text-white disabled:hover:tw-cursor-default tw-text-midnightPurple hover:tw-cursor-pointer tw-min-w-[148px] tw-min-h-[32px] tw-flex tw-justify-center tw-items-center tw-py-2 hover:tw-bg-qcInfo-300 tw-px-4 tw-rounded-md tw-relative tw-transition"
    disabled={false}
    onClick={[MockFunction]}
  >
    <div
      className="tw-z-20 tw-flex tw-gap-2 tw-justify-center tw-items-center"
    >
      Other
    </div>
  </button>
</div>
`;
