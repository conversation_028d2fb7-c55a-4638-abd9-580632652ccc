import React from "react";
import { create } from "react-test-renderer";
import { TabControllerBar } from "..";
import { Tab } from "../../../Cells/Tab";

jest.mock("../../../../styles/tw-output.css", () => "");

const tabs = [
  {
    id: "filters",
    label: "Filters",
    icon: "fa-solid fa-filters",
    active: true
  },
  {
    id: "columns",
    label: "Columns",
    icon: "fa-solid fa-table-layout",
    active: false
  },
  { id: "other", label: "Other", active: false }
];

const render = () =>
  create(
    <TabControllerBar>
      {tabs.map(tab => (
        <Tab layoutId="controller" key={tab.id} tab={tab} onClick={jest.fn()} />
      ))}
    </TabControllerBar>
  );

describe("TabControllerBar", () => {
  test("renders component", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });
});
