import React, { ReactNode } from "react";
import PropTypes from "prop-types";
import twMerge from "../../../utils/tailwind/twMerge";
import "../../../styles/tw-output.css";

interface TabControllerBar {
  children: ReactNode;
  className?: string | null | undefined;
}

/**
 * `Tab Controller Bar` is a Cell Component designed to display a set of tabs.
 * @param {React.ReactNode} children - The tabs content.
 * @param {string} className - Additional CSS tailwind classes to apply to the tabs container.
 */

const defaultClassName =
  "tw-flex tw-w-fit tw-p-1 tw-gap-1 tw-bg-qcNeutrals-300 tw-border tw-border-qcNeutrals-400 tw-rounded-md";

export const TabControllerBar: React.FC<TabControllerBar> = ({
  children,
  className
}) => {
  const mergedClassName = twMerge(defaultClassName, className);

  return <div className={mergedClassName}>{children}</div>;
};

TabControllerBar.propTypes = {
  /**
   * The tab controller bar content.
   */
  children: PropTypes.node.isRequired,
  /**
   * Additional CSS tailwind classes to apply to the file tabs container.
   */
  className: PropTypes.string
};

export default TabControllerBar;
