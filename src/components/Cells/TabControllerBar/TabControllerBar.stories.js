import React from "react";
import TabControllerBar from "./";
import { assoc, ifElse, map, propEq } from "ramda";
import Tab from "../Tab";

/**
 * `TabControllerBar` is an UI Component designed to display tabs the user can select.
 */

export default {
  component: TabControllerBar,
  tags: ["autodocs"]
};

const defaultTabs = [
  {
    id: "filters",
    label: "Filters",
    icon: "fa-solid fa-filters",
    active: true
  },
  {
    id: "columns",
    label: "Columns",
    icon: "fa-solid fa-table-layout",
    active: false
  },
  { id: "other", label: "Other", icon: "fa-solid fa-mistletoe", active: false }
];

const defaultIconOnlyTabs = [
  { id: "filtersIcon", icon: "fa-solid fa-filters", active: true },
  { id: "columnsIcon", icon: "fa-solid fa-table-layout", active: false },
  { id: "otherIcon", icon: "fa-solid fa-mistletoe", active: false }
];

export const Default = () => {
  const [tabs, setTabs] = React.useState(defaultTabs);
  const [tabs2, setTabs2] = React.useState(defaultTabs);
  const [tabs3, setTabs3] = React.useState(defaultIconOnlyTabs);

  const handleTabClick = id => {
    const updatedState = map(
      ifElse(propEq("id", id), assoc("active", true), assoc("active", false))
    )(tabs);

    setTabs(updatedState);
  };
  const handleTabClick1 = id => {
    const updatedState = map(
      ifElse(propEq("id", id), assoc("active", true), assoc("active", false))
    )(tabs3);

    setTabs3(updatedState);
  };
  const handleTabClick2 = id => {
    const updatedState = map(
      ifElse(propEq("id", id), assoc("active", true), assoc("active", false))
    )(tabs2);

    setTabs2(updatedState);
  };

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <TabControllerBar>
        {tabs.map(tab => (
          <Tab
            key={tab.id}
            tab={tab}
            isActive={tab.active}
            onClick={() => handleTabClick(tab.id)}
          />
        ))}
      </TabControllerBar>
      <TabControllerBar>
        {tabs3.map(tab => (
          <Tab
            key={tab.id}
            layoutId="iconController"
            tab={tab}
            onClick={() => handleTabClick1(tab.id)}
            className="tw-min-w-[61px]"
          />
        ))}
      </TabControllerBar>
      <TabControllerBar className="tw-bg-qcGlacier-200 tw-border-qcIris-500">
        {tabs2.map(tab => (
          <Tab
            key={tab.id}
            layoutId="colorController"
            tab={tab}
            onClick={() => handleTabClick2(tab.id)}
            className="tw-text-qcGreen-700"
            activeTabClassName="tw-bg-qcIris-400"
          />
        ))}
      </TabControllerBar>
    </div>
  );
};
