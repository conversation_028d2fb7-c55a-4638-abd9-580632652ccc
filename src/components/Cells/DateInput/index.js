import React, { forwardRef } from "react";
import { T, always, cond, equals } from "ramda";
import { cva } from "class-variance-authority";
import DatePicker from "react-datepicker";
import PropTypes from "prop-types";
import "react-datepicker/dist/react-datepicker.css";
import dateFormats from "constants/dateFormats";
import { getCurrentVariant, inputVariants } from "../Input";
import Label from "../../Molecules/Label";
import InputError from "../Input/InputError";
import InputFeedback from "../Input/InputFeedback";
import cn from "../../../utils/cn";

const iconVariants = cva(
  "far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px]",
  {
    variants: {
      variant: {
        default: "tw-text-qc-blue-800",
        disabled: "tw-text-black-70",
        error: "tw-text-qc-blue-800"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

const inputContainerVariants = cva("tw-relative tw-w-full", {
  variants: {
    variant: {
      default: "",
      disabled: "disabled-datepicker tw-opacity-50",
      error: ""
    }
  },
  defaultVariants: {
    variant: "default"
  }
});

/**
 * DateInternalInput component.
 *
 * @component
 * @param {object} props - Component props.
 * @param {boolean} props.disabled - Disable the input.
 * @param {string} props.inputContainerClassName - Input container CSS classes.
 * @param {string} props.inputClassName - Input CSS classes.
 * @param {string} props.iconClassName - Icon CSS classes.
 * @param {object} ref - React ref to input.
 * @returns {React.Element} Rendered component.
 */
const DateInternalInput = forwardRef(
  (
    {
      disabled,
      inputContainerClassName,
      inputClassName,
      iconClassName,
      type,
      ...props
    },
    ref
  ) => (
    <div className={inputContainerClassName}>
      <input
        {...props}
        disabled={disabled}
        className={inputClassName}
        ref={ref}
        type={type}
      />
      <i className={iconClassName} />
    </div>
  )
);

DateInternalInput.displayName = "DateInternalInput";

DateInternalInput.propTypes = {
  disabled: PropTypes.bool,
  inputContainerClassName: PropTypes.string,
  inputClassName: PropTypes.string,
  iconClassName: PropTypes.string
};

const getDateFormats = type =>
  cond([
    [equals("quarter"), always(dateFormats.quarterFormats)],
    [equals("month"), always(dateFormats.monthFormats)],
    [T, always(dateFormats.dayFormats)]
  ])(type);

/**
 * `DateInput` is an Atom Level component designed to replace the default datepicker element.
 *
 * @component
 * @param {object} props - Component props.
 * @param {string} props.label - Label text.
 * @param {string} props.labelClassName - Label CSS classes.
 * @param {string} props.inputClassName - Input CSS classes.
 * @param {string} props.iconClassName - Icon CSS classes.
 * @param {string} props.inputContainerClassName - Input container CSS classes.
 * @param {string} props.wrapperClassName - Wrapper CSS classes.
 * @param {string} props.id - Input id.
 * @param {string} props.name - Input name.
 * @param {Date} props.value - Input value.
 * @param {function} props.onChange - Function to handle input change.
 * @param {string} props.placeholder - Input placeholder.
 * @param {("top"|"bottom"|"left"|"right")} [props.popperPlacement="bottom"] props.popperPlacement - Text to choose where calendar pops up: top, bottom, left, right.
 * @param {boolean} props.selectsStart - Pass "selectsStart" to let component know date input is part of a date range.
 * @param {Date} props.startDate - Start date.
 * @param {boolean} props.selectsEnd - Pass "selectsEnd" to let component know date input is part of a date range.
 * @param {Date} props.endDate - End date.
 * @param {boolean} props.showMonthYearPicker - Pass to let component know date input is part of a date range.
 * @param {boolean} props.showQuarterYearPicker - Pass to let component know date input is part of a date range.
 * @param {("day"|"month")} [props.dateFormat="day"] - Pass "month" to use month and year date formats. pass "day" to use month, day and year date formats
 * @param {boolean} props.disabled - Disable the input.
 * @param {boolean} props.error - Indicates if there is an error.
 * @param {string} props.errorText - Error text.
 * @param {object} ref - React ref to input.
 * @param {string} props.feedbackText - The optional feedback text for the input.
 * @param {("neutral"|"success"|"error")} [props.feedbackType="neutral"] Style variants for the feedback text.
 * @param {string} props.feedbackClassName - Classes to customize the optional InputFeedback.
 * @param {string} props.containerClassName - Classes to customize the container that wraps other elements.
 * @param {Date} props.minDate - Minimum date.
 * @param {Date} props.maxDate - Maximum date.
 * @returns {React.Element} Rendered component.
 */
export const DateInput = forwardRef(
  (
    {
      label,
      labelClassName,
      inputClassName,
      iconClassName,
      inputContainerClassName,
      wrapperClassName,
      id,
      name,
      value,
      onChange,
      placeholder,
      popperPlacement,
      selectsStart,
      startDate,
      selectsEnd,
      endDate,
      showMonthYearPicker,
      showQuarterYearPicker,
      dateFormat,
      disabled,
      error,
      errorText,
      feedbackText,
      feedbackType,
      feedbackClassName,
      containerClassName,
      minDate,
      maxDate,
      type
    },
    ref
  ) => {
    const currentVariant = getCurrentVariant({
      error: error || feedbackType === "error",
      disabled
    });

    return (
      <div
        className={cn(
          "tw-flex tw-flex-col tw-group tw-gap-y-1",
          containerClassName
        )}
      >
        {label && (
          <Label
            htmlFor={id}
            error={error}
            feedbackType={feedbackType}
            disabled={disabled}
            className={labelClassName}
          >
            {label}
          </Label>
        )}
        <DatePicker
          id={id}
          customInput={
            <DateInternalInput
              inputContainerClassName={cn(
                inputContainerVariants({ variant: currentVariant }),
                inputContainerClassName
              )}
              inputClassName={cn(
                inputVariants({ variant: currentVariant }),
                "tw-pr-8 tw-w-full",
                inputClassName
              )}
              iconClassName={cn(
                iconVariants({ variant: currentVariant }),
                iconClassName
              )}
              type={type || "text"}
              ref={ref}
            />
          }
          name={name}
          selected={value}
          placeholderText={placeholder}
          // accepts an array of date-fns compatible date formats to parse
          // the first dateformat in the array of date formats will be used format the output
          dateFormat={getDateFormats(dateFormat)}
          showQuarterYearPicker={showQuarterYearPicker}
          onChange={onChange}
          selectsStart={selectsStart}
          selectsEnd={selectsEnd}
          startDate={startDate}
          endDate={endDate}
          maxDate={endDate || maxDate}
          minDate={startDate || minDate}
          popperPlacement={popperPlacement}
          showMonthYearPicker={showMonthYearPicker}
          disabled={disabled}
          wrapperClassName={cn(wrapperClassName)}
        />
        {!disabled && error && errorText && (
          <InputError errorText={errorText} />
        )}
        {!disabled && !error && feedbackText && (
          <InputFeedback
            feedbackText={feedbackText}
            variant={feedbackType}
            feedbackClassName={feedbackClassName}
          />
        )}
      </div>
    );
  }
);

DateInput.displayName = "DateInput";

DateInput.propTypes = {
  /**
   * Label text.
   **/
  label: PropTypes.string,
  /**
   * CSS classes for the label.
   **/
  labelClassName: PropTypes.string,
  /**
   * CSS classes for the input.
   **/
  inputClassName: PropTypes.string,
  /**
   * CSS classes for the icon.
   **/
  iconClassName: PropTypes.string,
  /**
   * CSS classes for the input container.
   **/
  inputContainerClassName: PropTypes.string,
  /**
   * CSS classes for the wrapper.
   **/
  wrapperClassName: PropTypes.string,
  /**
   * Input id.
   **/
  id: PropTypes.string,
  /**
   * Input name.
   **/
  name: PropTypes.string,
  /**
   * Input value.
   **/
  value: PropTypes.instanceOf(Date),
  /**
   * Input placeholder.
   **/
  placeholder: PropTypes.string,
  /**
   * Date format. Can be "day", "month", or "quarter".
   **/
  dateFormat: PropTypes.oneOf(["day", "month", "quarter"]),
  /**
   * Function to handle input change.
   **/
  onChange: PropTypes.func,
  /**
   * Text to choose where calendar pops up: top, bottom, left, right.
   **/
  popperPlacement: PropTypes.oneOf(["top", "bottom", "left", "right"]),
  /**
   * Pass "selectsStart" to let component know date input is part of a date range.
   **/
  selectsStart: PropTypes.bool,
  /**
   * Pass "selectsEnd" to let component know date input is part of a date range.
   **/
  selectsEnd: PropTypes.bool,
  /**
   * Pass to let component know date input is part of a date range.
   **/
  showMonthYearPicker: PropTypes.bool,
  /**
   * Pass to let component know date input is part of a date range.
   **/
  showQuarterYearPicker: PropTypes.bool,
  /**
   * Disable the input.
   **/
  disabled: PropTypes.bool,
  /**
   * Start date.
   **/
  startDate: PropTypes.instanceOf(Date),
  /**
   * End date.
   **/
  endDate: PropTypes.instanceOf(Date),
  /**
   * Indicates if there is an error.
   **/
  error: PropTypes.bool,
  /**
   * Error text.
   **/
  errorText: PropTypes.string,
  /**
   * The optional feedback text for the input.
   **/
  feedbackText: PropTypes.string,
  /**
   * Style variants for the feedback text. Can be "neutral", "success", or "error".
   **/
  feedbackType: PropTypes.oneOf(["neutral", "success", "error"]),
  /**
   * Classes to customize the optional InputFeedback.
   **/
  feedbackClassName: PropTypes.string,
  /**
   * Classes to customize the container that wraps other elements.
   */
  containerClassName: PropTypes.string,
  /**
   * Minimum date.
   **/
  minDate: PropTypes.instanceOf(Date),
  /**
   * Maximum date.
   **/
  maxDate: PropTypes.instanceOf(Date)
};

DateInput.defaultProps = {
  placeholder: "mm/dd/yyyy",
  dateFormat: "day",
  popperPlacement: "bottom",
  selectsStart: false,
  selectsEnd: false,
  showMonthYearPicker: false,
  disabled: false,
  showQuarterYearPicker: false
};

export default DateInput;
