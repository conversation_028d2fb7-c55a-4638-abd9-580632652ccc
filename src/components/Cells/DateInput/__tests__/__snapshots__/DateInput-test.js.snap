// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateInput renders component 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="tw-relative tw-w-full"
      >
        <input
          className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
          disabled={false}
          onBlur={[Function]}
          onChange={[Function]}
          onClick={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mm/dd/yyyy"
          readOnly={false}
          type="text"
          value=""
        />
        <i
          className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component two components as date range picker 1`] = `
<div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="startDate"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/dd/yyyy"
            readOnly={false}
            type="text"
            value="07/01/2022"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="endDate"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/dd/yyyy"
            readOnly={false}
            type="text"
            value="07/31/2022"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component two components used as month range 1`] = `
<div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="startMonth"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/yy"
            readOnly={false}
            type="text"
            value="07/22"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="endMonth"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/yy"
            readOnly={false}
            type="text"
            value="07/22"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component two components used as quarter range 1`] = `
<div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="startQuarter"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/yy"
            readOnly={false}
            type="text"
            value="Q3/2022"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    className="tw-flex tw-flex-col tw-group tw-gap-y-1"
  >
    <div
      className="react-datepicker-wrapper"
    >
      <div
        className="react-datepicker__input-container"
      >
        <div
          className="tw-relative tw-w-full"
        >
          <input
            className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
            disabled={false}
            name="endQuarter"
            onBlur={[Function]}
            onChange={[Function]}
            onClick={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="mm/yy"
            readOnly={false}
            type="text"
            value="Q3/2022"
          />
          <i
            className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component with single date 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="tw-relative tw-w-full"
      >
        <input
          className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
          disabled={false}
          name="date"
          onBlur={[Function]}
          onChange={[Function]}
          onClick={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mm/dd/yyyy"
          readOnly={false}
          type="text"
          value="07/01/2022"
        />
        <i
          className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component with single month 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="tw-relative tw-w-full"
      >
        <input
          className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
          disabled={false}
          name="month"
          onBlur={[Function]}
          onChange={[Function]}
          onClick={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mm/yy"
          readOnly={false}
          type="text"
          value="07/22"
        />
        <i
          className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders component with single quarter 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="tw-relative tw-w-full"
      >
        <input
          className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-pr-8 tw-w-full"
          disabled={false}
          name="quarter"
          onBlur={[Function]}
          onChange={[Function]}
          onClick={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mm/yy"
          readOnly={false}
          type="text"
          value="Q3/2022"
        />
        <i
          className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-qc-blue-800"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`DateInput renders disabled component 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <div
    className="react-datepicker-wrapper"
  >
    <div
      className="react-datepicker__input-container"
    >
      <div
        className="tw-relative tw-w-full disabled-datepicker tw-opacity-50"
      >
        <input
          className="tw-max-h-[35px] tw-rounded-md tw-border tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-gray-700 tw-bg-gray-100 tw-cursor-not-allowed tw-text-gray-600 tw-pr-8 tw-w-full"
          disabled={true}
          name="date"
          onBlur={[Function]}
          onChange={[Function]}
          onClick={[Function]}
          onFocus={[Function]}
          onKeyDown={[Function]}
          placeholder="mm/dd/yyyy"
          readOnly={false}
          type="text"
          value=""
        />
        <i
          className="far fa-calendar tw-absolute tw-top-1/2 tw-transform tw-translate-y-[-50%] tw-right-[10px] tw-text-black-70"
        />
      </div>
    </div>
  </div>
</div>
`;
