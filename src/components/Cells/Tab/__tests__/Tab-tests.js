import React from "react";
import { create } from "react-test-renderer";
import { Tab } from "../index";

const render = props =>
  create(
    <Tab
      tab={{
        id: "filters",
        label: "Filters",
        icon: "fa-solid fa-filters",
        active: true
      }}
      onClick={jest.fn()}
      layoutId="testController"
      {...props}
    />
  );

describe("Tab", () => {
  test("renders component", () => {
    const component = render();

    expect(component).toMatchSnapshot();
  });

  test("renders component with disabled=false", () => {
    const component = render({
      tab: {
        id: "columns",
        label: "Columns",
        icon: "fa-solid fa-table-layout",
        active: false
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component without icon", () => {
    const component = render({
      tab: {
        id: "columns",
        label: "Columns",
        active: false
      }
    });

    expect(component).toMatchSnapshot();
  });

  test("renders component without icon or label", () => {
    const component = render({
      tab: {
        id: "columns",
        active: false
      }
    });

    expect(component).toMatchSnapshot();
  });
});
