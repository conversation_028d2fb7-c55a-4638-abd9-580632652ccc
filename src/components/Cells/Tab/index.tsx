import React from "react";
import PropTypes from "prop-types";
import { motion } from "framer-motion";
import twMerge from "../../../utils/tailwind/twMerge";

interface TabObject {
  id: string;
  label?: string | null | undefined;
  icon?: string | null | undefined;
  active: boolean;
}

interface Tab {
  layoutId: string;
  tab: TabObject;
  onClick: () => void;
  className?: string;
  activeTabClassName?: string;
}

/**
 *
 * `Tab` is an Molecular Level component designed to be the default tab design.
 *
 * @param {string} tab - Specifies tab specific properties.
 * @param {string} layoutId - specifies the layout id for animations to work.
 * @param {function} onClick - Function passed into the component to be called onClick.
 * @param {string} className - Additional CSS tailwind classes to apply to the button container.
 * @param {string} activeTabClassName - Additional CSS tailwind classes to apply to the active tab span.
 *
 **/
const defaultClassName =
  "disabled:tw-text-white disabled:hover:tw-cursor-default tw-text-midnightPurple hover:tw-cursor-pointer  tw-min-w-[148px] tw-min-h-[32px] tw-flex tw-justify-center tw-items-center tw-py-2 hover:tw-bg-qcInfo-300 tw-px-4 tw-rounded-md tw-relative tw-transition";

const defaultActiveTabClassName =
  "tw-absolute tw-inset-0 tw-z-10 tw-bg-oceanBlue tw-rounded-md";

export const Tab: React.FC<Tab> = ({
  layoutId = "tabController",
  tab = { id: "first", active: false },
  onClick,
  className,
  activeTabClassName
}) => {
  const mergedClassName = twMerge(defaultClassName, className);
  const activeTabMergedClassName = twMerge(
    defaultActiveTabClassName,
    activeTabClassName
  );

  const isActive = tab.active;

  return (
    <button onClick={onClick} disabled={isActive} className={mergedClassName}>
      <div className="tw-z-20 tw-flex tw-gap-2 tw-justify-center tw-items-center">
        {tab?.icon && <i className={tab.icon} />}
        {tab?.label && tab.label}
      </div>

      {isActive && (
        <motion.span
          layoutId={layoutId}
          className={activeTabMergedClassName}
          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
        />
      )}
    </button>
  );
};

Tab.propTypes = {
  /**
   * Type specifing what tab to use
   **/
  tab: PropTypes.shape({
    id: PropTypes.string.isRequired,
    label: PropTypes.string,
    icon: PropTypes.string,
    active: PropTypes.bool.isRequired
  }).isRequired,
  /**
   * Function passed into the component to be called onClick
   **/
  onClick: PropTypes.func.isRequired,
  /**
   * Additional CSS tailwind classes to apply to the button container.
   */
  className: PropTypes.string,
  /**
   * Additional CSS tailwind classes to apply to the active tab span.
   */
  activeTabClassName: PropTypes.string,
  /**
   * Specifies the layout id for animations to work.
   */
  layoutId: PropTypes.string.isRequired
};

Tab.defaultProps = {
  tab: { id: "one", label: "", active: false, icon: "" },
  layoutId: "tabController",
  onClick: () => {},
  className: "",
  activeTabClassName: ""
};

export default Tab;
