// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextArea renders component 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <label
    className="group-focus-within:tw-text-purple-800 tw-text-sm tw-font-semibold tw-flex tw-gap-[5px] tw-text-black-70"
  >
    test
  </label>
  <textarea
    className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-black-29 tw-min-h-[80px] tw-p-2.5"
    name="test"
    placeholder="test"
  />
</div>
`;

exports[`TextArea renders component with disabled 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <textarea
    className="tw-max-h-[35px] tw-rounded-md tw-border tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-gray-700 tw-bg-gray-100 tw-cursor-not-allowed tw-text-gray-600 tw-min-h-[80px] tw-p-2.5"
    disabled={true}
    name="test"
    placeholder="test"
  />
</div>
`;

exports[`TextArea renders component with error 1`] = `
<div
  className="tw-flex tw-flex-col tw-group tw-gap-y-1"
>
  <textarea
    className="tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400 tw-border-error-700 tw-min-h-[80px] tw-p-2.5"
    name="test"
    placeholder="test"
  />
  <p
    className="tw-italic tw-text-xs tw-text-error-700"
  >
    error
  </p>
</div>
`;
