import React, { forwardRef } from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";
import cn from "../../../utils/cn";
import InputFeedback from "./InputFeedback";
import InputError from "./InputError";
import Label from "../../Molecules/Label";

export const inputVariants = cva(
  "tw-max-h-[35px] tw-rounded-md tw-border tw-bg-white tw-p-2 tw-outline-none focus:tw-border-purple-800 focus:tw-border tw-text-sm placeholder:tw-text-qcNuetrals-400",
  {
    variants: {
      variant: {
        default: "tw-border-black-29",
        disabled:
          "tw-border-gray-700 tw-bg-gray-100 tw-cursor-not-allowed tw-text-gray-600",
        error: "tw-border-error-700"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);

export const getCurrentVariant = ({ error, disabled }) => {
  if (disabled) return "disabled";
  if (error) return "error";
  return "default";
};

/**
 * `Input` is a UI Component designed to display a text input.
 *
 * @component
 * @param {object} props - Component props.
 * @param {string} props.id - The id of the input.
 * @param {string} props.name - The name of the input.
 * @param {string} props.value - The value of the input.
 * @param {Function} props.onChange - The function to call when the input value changes.
 * @param {string} props.placeholder - The placeholder of the input.
 * @param {string} props.inputClassName - Classes to customize the underlying HTML input element.
 * @param {string} props.labelClassName - Classes to customize the optional underlying HTML label element.
 * @param {boolean} props.disabled - Whether the input is disabled.
 * @param {boolean} props.error - Whether the input has an error.
 * @param {string} props.errorText - The error text to display.
 * @param {string} props.label - The optional label for the input.
 * @param {string} props.optional - Whether the input is optional or required (Adds text to Label component).
 * @param {string} props.feedbackText - The optional feedback text for the input.
 * @param {("neutral"|"success"|"error")} [props.feedbackType="neutral"] Style variants for the feedback text.
 * @param {string} props.feedbackClassName - Classes to customize the optional InputFeedback.
 * @param {string} props.containerClassName - Classes to customize the container that wraps other elements.
 * @returns {React.Element} Rendered component.
 */
export const Input = forwardRef(
  (
    {
      inputClassName,
      id,
      disabled,
      error,
      errorText,
      label,
      labelClassName,
      feedbackText,
      feedbackType,
      feedbackClassName,
      containerClassName,
      optional = false,
      ...props
    },
    ref
  ) => {
    const currentVariant = getCurrentVariant({
      error: error || feedbackType === "error",
      disabled
    });

    return (
      <div
        className={cn(
          "tw-flex tw-flex-col tw-group tw-gap-y-1",
          containerClassName
        )}
      >
        {label && (
          <Label
            htmlFor={id}
            error={error}
            feedbackType={feedbackType}
            disabled={disabled}
            className={labelClassName}
            optional={optional}
          >
            {label}
          </Label>
        )}
        <input
          id={id}
          disabled={disabled}
          className={cn(
            inputVariants({ variant: currentVariant }),
            inputClassName
          )}
          ref={ref}
          {...props}
        />
        {!disabled && error && errorText && (
          <InputError errorText={errorText} />
        )}
        {!disabled && !error && feedbackText && (
          <InputFeedback
            feedbackText={feedbackText}
            variant={feedbackType}
            feedbackClassName={feedbackClassName}
          />
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

Input.propTypes = {
  /**
   * The id of the input.
   */
  id: PropTypes.string,
  /**
   * The name of the input.
   */
  name: PropTypes.string,
  /**
   * The value of the input.
   */
  value: PropTypes.string,
  /**
   * The function to call when the input value changes.
   */
  onChange: PropTypes.func,
  /**
   * The placeholder of the input.
   */
  placeholder: PropTypes.string,
  /**
   * Classes to customize the underlying HTML input element.
   */
  inputClassName: PropTypes.string,
  /**
   * Classes to customize the optional underlying HTML label element.
   */
  labelClassName: PropTypes.string,
  /**
   * Whether the input is disabled.
   */
  disabled: PropTypes.bool,
  /**
   * Whether the input has an error.
   */
  error: PropTypes.bool,
  /**
   * The error text to display.
   */
  errorText: PropTypes.string,
  /**
   * The optional label for the input.
   */
  label: PropTypes.string,
  /**
   * The optional feedback text for the input.
   */
  feedbackText: PropTypes.string,
  /**
   * Style variants for the feedback text.
   */
  feedbackType: PropTypes.oneOf(["neutral", "success", "error"]),
  /**
   * Classes to customize the optional InputFeedback.
   */
  feedbackClassName: PropTypes.string,
  /**
   * Classes to customize the container that wraps other elements.
   */
  containerClassName: PropTypes.string
};

export default Input;
