version: 2
jobs:
  build:
    working_directory: ~/q-centrix/web-client
    resource_class: small
    parallelism: 1
    shell: /bin/bash --login
    environment:
      CIRCLE_ARTIFACTS: /tmp/circleci-artifacts
      CIRCLE_TEST_REPORTS: /tmp/circleci-test-results
    docker:
      - image: cimg/node:22.15
    steps:
      - checkout
      - run: mkdir -p $CIRCLE_ARTIFACTS $CIRCLE_TEST_REPORTS
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies
          command: yarn install --network-concurrency 1
      - save_cache:
          name: Save Yarn Package Cache
          key: yarn-packages-{{ checksum "yarn.lock" }}
          paths:
            - ./node_modules
      # Test
      - run: yarn validate
      - store_test_results:
          path: /tmp/circleci-test-results
      # Save artifacts
      - store_artifacts:
          path: /tmp/circleci-artifacts
      - store_artifacts:
          path: /tmp/circleci-test-results
  deployment:
    working_directory: ~/q-centrix/web-client
    parallelism: 1
    shell: /bin/bash --login
    docker:
      - image: cimg/node:22.15
    steps:
      - checkout
      - restore_cache:
          keys:
            - yarn-packages-{{ checksum "yarn.lock" }}
      - run: yarn install --network-concurrency 1
      - run: ./deploy.sh
workflows:
  version: 2
  build-test-and-deployment:
    jobs:
      - build:
          filters:
            tags:
              only: /.*/
      - deployment:
          requires:
            - build
          filters:
            tags:
              only: /^v[0-9]+(\.[0-9]+)*/
            branches:
              ignore: /.*/
