window.initPendo = function(api<PERSON><PERSON>, user, facility) {   
  (function(p,e,n,d,o){var v,w,x,y,z;o=p[d]=p[d]||{};o._q=[];
  v=['initialize','identify','updateOptions','pageLoad','track'];for(w=0,x=v.length;w<x;++w)(function(m){
      o[m]=o[m]||function(){o._q[m===v[0]?'unshift':'push']([m].concat([].slice.call(arguments,0)));};})(v[w]);
      y=e.createElement(n);y.async=!0;y.src='https://cdn.pendo.io/agent/static/'+apiKey+'/pendo.js';
      z=e.getElementsByTagName(n)[0];z.parentNode.insertBefore(y,z);})(window,document,'script','pendo');

  const { id, name, environmentPrefix = "", definitiveFacilityId } = facility;

  let defaultAccountId = "";

  pendo.initialize({
    sanitizeUrl: function(u) {
      // Code to remove query string from urls
      // sent to Pendo
      var p = document.createElement("a");
      p.href = u;
      p.search = "";
      return p.href;
    },
    visitor: {
      id:    environmentPrefix + user.id, // Required if user is logged in
      email: user.email,     // Recommended if using Pendo Feedback, or NPS Em
      // role:         // Optional

      // You can add any additional visitor level key-values here,
      // as long as it's not one of the above reserved names.
    },

    account: {
      id: environmentPrefix ? environmentPrefix + id : definitiveFacilityId ? definitiveFacilityId : "na_" + id, // Highly recommended
      name             // Optional
      // is_paying:    // Recommended if using Pendo Feedback
      // monthly_value:// Recommended if using Pendo Feedback
      // planLevel:    // Optional
      // planPrice:    // Optional
      // creationDate: // Optional

      // You can add any additional account level key-values here,
      // as long as it's not one of the above reserved names.
    },
    events: {
      ready: () => {
        defaultAccountId = pendo.accountId;
      },
      guidesLoaded: () => {
        if (user.email.includes("@q-centrix.com")) {
          if (pendo.accountId !== defaultAccountId) {
              const feedbackGuide = pendo.findGuideById(
                "2yuG5XoxnrznNAGX7cjB2erHV3k@LZ8T9BcAMGJOPrFiabf4pdIRduo"
              );
  
              if (feedbackGuide) {
                feedbackGuide.steps[0].before("teardown", function() {
                  pendo.identify(pendo.visitorId, defaultAccountId);
                });
              }
          }

          window.addEventListener("message", function(event) {
            if (
              event.data.message == "loaded-receptive-widget" &&
              event.origin == "https://portal.feedback.us.pendo.io"
            ) {
              pendo.identify(pendo.visitorId, "**********");
            }
          });
        }
      }
    }
  });
}; 
