name: build and deploy
on:
  workflow_dispatch:
  push:
    tags:
      - '*' 
jobs:
  build_and_deploy:
    name: build project and deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: '18.0.0'
      - name: set github access
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{secrets.QCX_CI_SSH_KEY}}
      - name: install dependencies
        run: | 
          rm -rf node_modules
          yarn install --network-concurrency 1
      - name: build project
        run: CI=false yarn build-no-hashes
      - name: deploy
        uses: staevs/s3-deploy-action@a051ac37a3868bc6f9ba19182a86dfecd89d590c
        if: success()
        with:
          args: --follow-symlinks --delete --no-progress
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.QCX_CI_AWS_ACCESS_KEY_ID }}
          AWS_REGION: 'us-east-1' 
          AWS_S3_BUCKET: ${{ secrets.QAPPS_CORE_PROD_S3_BUCKET }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.QCX_CI_AWS_SECRET_ACCESS_KEY }}
          CLOUDFRONT_DISTRIBUTION_ID: ${{ secrets.QAPPS_CORE_PROD_CLOUDFRONT_DISTRIBUTION_ID }}
          S3_SOURCE_DIR: 'build'
          DESTINATION_DIR: 'web-client/${GITHUB_REF#refs/tags/}'
