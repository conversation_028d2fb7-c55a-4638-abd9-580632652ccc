version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    # Disable version updates and only check security updates
    # since <PERSON><PERSON><PERSON> does the rest for us
    open-pull-requests-limit: 0
    reviewers:
      - "q-centrix/security-reviewers"
    groups:
      patch:
        applies-to: security-updates
        update-types:
          - "patch"
      minor:
        applies-to: security-updates
        update-types:
          - "minor"
      major:
        applies-to: security-updates
        update-types:
          - "major"
